/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/kyc/issue-claim/route";
exports.ids = ["app/api/kyc/issue-claim/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkyc%2Fissue-claim%2Froute&page=%2Fapi%2Fkyc%2Fissue-claim%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkyc%2Fissue-claim%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkyc%2Fissue-claim%2Froute&page=%2Fapi%2Fkyc%2Fissue-claim%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkyc%2Fissue-claim%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_tokendev_newroo_admin_panel_src_app_api_kyc_issue_claim_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/kyc/issue-claim/route.ts */ \"(rsc)/./src/app/api/kyc/issue-claim/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/kyc/issue-claim/route\",\n        pathname: \"/api/kyc/issue-claim\",\n        filename: \"route\",\n        bundlePath: \"app/api/kyc/issue-claim/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api\\\\kyc\\\\issue-claim\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_tokendev_newroo_admin_panel_src_app_api_kyc_issue_claim_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkyc%2Fissue-claim%2Froute&page=%2Fapi%2Fkyc%2Fissue-claim%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkyc%2Fissue-claim%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/kyc/issue-claim/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/kyc/issue-claim/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/address/checks.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/providers/provider-jsonrpc.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/wallet/wallet.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/abi/abi-coder.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/contract/contract.js\");\n\n\nconst KYC_CLAIMS_MODULE_ABI = [\n    \"function issueCustomClaim(address user, uint256 claimType, bytes calldata data, string calldata uri, uint256 expiresAt) external returns (bytes32)\",\n    \"function issueKYCClaim(address user, bytes calldata data) external returns (bytes32)\"\n];\nconst CLAIM_REGISTRY_ABI = [\n    \"function issueClaim(address subject, uint256 claimType, bytes calldata signature, bytes calldata data, string calldata uri, uint256 expiresAt) external returns (bytes32)\"\n];\nasync function POST(request) {\n    try {\n        const { userAddress, topicId, data = '', uri = '', expiresAt = 0 } = await request.json();\n        if (!userAddress || !topicId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'User address and topic ID are required'\n            }, {\n                status: 400\n            });\n        }\n        // Validate address\n        if (!ethers__WEBPACK_IMPORTED_MODULE_1__.isAddress(userAddress)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid user address format'\n            }, {\n                status: 400\n            });\n        }\n        // Get environment variables\n        const rpcUrl = process.env.AMOY_RPC_URL;\n        const privateKey = process.env.CONTRACT_ADMIN_PRIVATE_KEY;\n        const kycModuleAddress = process.env.AMOY_KYC_CLAIMS_MODULE_ADDRESS;\n        const claimRegistryAddress = process.env.CLAIM_REGISTRY_ADDRESS;\n        if (!rpcUrl || !privateKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Server configuration error'\n            }, {\n                status: 500\n            });\n        }\n        // Setup provider and signer\n        const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.JsonRpcProvider(rpcUrl);\n        const signer = new ethers__WEBPACK_IMPORTED_MODULE_3__.Wallet(privateKey, provider);\n        // Encode claim data\n        const encodedData = data ? ethers__WEBPACK_IMPORTED_MODULE_4__.AbiCoder.defaultAbiCoder().encode([\n            'string'\n        ], [\n            data\n        ]) : '0x';\n        let txHash;\n        let claimId;\n        if (kycModuleAddress && ethers__WEBPACK_IMPORTED_MODULE_1__.isAddress(kycModuleAddress)) {\n            // Use KYC Claims Module if available\n            console.log('Using KYC Claims Module for claim issuance');\n            const kycModule = new ethers__WEBPACK_IMPORTED_MODULE_5__.Contract(kycModuleAddress, KYC_CLAIMS_MODULE_ABI, signer);\n            try {\n                let tx;\n                if (topicId === '10101010000001') {\n                    // Special handling for KYC verification claim\n                    tx = await kycModule.issueKYCClaim(userAddress, encodedData);\n                } else {\n                    // Custom claim\n                    tx = await kycModule.issueCustomClaim(userAddress, topicId, encodedData, uri, expiresAt);\n                }\n                const receipt = await tx.wait();\n                txHash = tx.hash;\n                // Extract claim ID from events\n                const claimEvent = receipt.logs.find((log)=>{\n                    try {\n                        const parsed = kycModule.interface.parseLog(log);\n                        return parsed?.name === 'ClaimIssued';\n                    } catch  {\n                        return false;\n                    }\n                });\n                if (claimEvent) {\n                    const parsedEvent = kycModule.interface.parseLog(claimEvent);\n                    claimId = parsedEvent?.args?.claimId || 'Unknown';\n                } else {\n                    claimId = 'Generated';\n                }\n                console.log('Claim issued via KYC Claims Module:', txHash);\n            } catch (moduleError) {\n                console.log('KYC Claims Module failed, falling back to direct ClaimRegistry call:', moduleError);\n                if (!claimRegistryAddress || !ethers__WEBPACK_IMPORTED_MODULE_1__.isAddress(claimRegistryAddress)) {\n                    throw new Error('ClaimRegistry address not configured');\n                }\n                // Fallback to direct ClaimRegistry call\n                const claimRegistry = new ethers__WEBPACK_IMPORTED_MODULE_5__.Contract(claimRegistryAddress, CLAIM_REGISTRY_ABI, signer);\n                const tx = await claimRegistry.issueClaim(userAddress, topicId, '0x', encodedData, uri, expiresAt);\n                const receipt = await tx.wait();\n                txHash = tx.hash;\n                claimId = 'Generated via ClaimRegistry';\n                console.log('Claim issued via direct ClaimRegistry call:', txHash);\n            }\n        } else {\n            // Direct ClaimRegistry call\n            if (!claimRegistryAddress || !ethers__WEBPACK_IMPORTED_MODULE_1__.isAddress(claimRegistryAddress)) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'ClaimRegistry address not configured'\n                }, {\n                    status: 500\n                });\n            }\n            console.log('Using direct ClaimRegistry call for claim issuance');\n            const claimRegistry = new ethers__WEBPACK_IMPORTED_MODULE_5__.Contract(claimRegistryAddress, CLAIM_REGISTRY_ABI, signer);\n            const tx = await claimRegistry.issueClaim(userAddress, topicId, '0x', encodedData, uri, expiresAt);\n            const receipt = await tx.wait();\n            txHash = tx.hash;\n            claimId = 'Generated via ClaimRegistry';\n            console.log('Claim issued via direct ClaimRegistry call:', txHash);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Claim issued successfully',\n            txHash,\n            claimId,\n            userAddress,\n            topicId,\n            data\n        });\n    } catch (error) {\n        console.error('Error issuing claim:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: `Failed to issue claim: ${error.message}`\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/kyc/issue-claim/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ethers","vendor-chunks/@noble","vendor-chunks/@adraffy","vendor-chunks/aes-js"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkyc%2Fissue-claim%2Froute&page=%2Fapi%2Fkyc%2Fissue-claim%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkyc%2Fissue-claim%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();