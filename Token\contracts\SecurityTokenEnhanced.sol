// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";

/**
 * @title SecurityTokenEnhanced
 * @dev Enhanced security token with critical audit fixes
 * Size-optimized while maintaining essential security features
 */
contract SecurityTokenEnhanced is ERC20, AccessControl, ReentrancyGuard {
    // Constants
    bytes32 public constant AGENT_ROLE = keccak256("AGENT_ROLE");
    bytes32 public constant TRANSFER_MANAGER_ROLE = keccak256("TRANSFER_MANAGER_ROLE");
    
    // Core properties
    uint8 private _decimals;
    uint256 public maxSupply;
    string public tokenPrice;
    string public bonusTiers;
    string public tokenImageUrl;
    
    // ERC-3643 compliance
    address public identityRegistry;
    address public compliance;
    
    // Agent management (simplified)
    address[] public agents;
    mapping(address => bool) public isAgent;
    
    // Agreement tracking
    mapping(address => uint256) private _agreementAcceptances;
    
    // Security features
    bool private _emergencyPaused;
    mapping(bytes4 => bool) private _functionPaused;
    
    // Events
    event AgentAdded(address indexed agent);
    event AgentRemoved(address indexed agent);
    event AgreementAccepted(address indexed account, uint256 timestamp);
    event EmergencyPaused(address indexed admin);
    event EmergencyUnpaused(address indexed admin);
    
    // Modifiers
    modifier onlyAgent() {
        require(hasRole(AGENT_ROLE, _msgSender()), "Not agent");
        _;
    }
    
    modifier whenNotPaused() {
        require(!_emergencyPaused, "Emergency paused");
        require(!_functionPaused[msg.sig], "Function paused");
        _;
    }
    
    constructor(
        string memory name,
        string memory symbol,
        uint8 decimals_,
        uint256 maxSupply_,
        address admin,
        string memory tokenPrice_,
        string memory bonusTiers_,
        string memory tokenImageUrl_,
        address identityRegistry_,
        address compliance_
    ) ERC20(name, symbol) {
        require(admin != address(0), "Invalid admin");
        require(maxSupply_ > 0, "Invalid max supply");
        require(decimals_ <= 18, "Invalid decimals");
        
        _decimals = decimals_;
        maxSupply = maxSupply_;
        tokenPrice = tokenPrice_;
        bonusTiers = bonusTiers_;
        tokenImageUrl = tokenImageUrl_;
        identityRegistry = identityRegistry_;
        compliance = compliance_;
        
        _grantRole(DEFAULT_ADMIN_ROLE, admin);
        _grantRole(AGENT_ROLE, admin);
        _grantRole(TRANSFER_MANAGER_ROLE, admin);
        
        agents.push(admin);
        isAgent[admin] = true;
        
        emit AgentAdded(admin);
    }
    
    function decimals() public view override returns (uint8) {
        return _decimals;
    }
    
    function mint(address to, uint256 amount) external onlyAgent nonReentrant whenNotPaused {
        require(to != address(0), "Invalid recipient");
        require(amount > 0, "Invalid amount");
        require(totalSupply() + amount <= maxSupply, "Exceeds max supply");
        
        _mint(to, amount);
    }
    
    function forcedTransfer(address from, address to, uint256 amount)
        external
        onlyRole(TRANSFER_MANAGER_ROLE)
        nonReentrant
        whenNotPaused
        returns (bool)
    {
        require(from != address(0) && to != address(0), "Invalid addresses");
        require(amount > 0, "Invalid amount");
        require(balanceOf(from) >= amount, "Insufficient balance");
        
        _transfer(from, to, amount);
        return true;
    }
    
    function addAgent(address agent) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(agent != address(0), "Invalid agent");
        require(!isAgent[agent], "Already agent");
        require(agents.length < 50, "Too many agents");
        
        _grantRole(AGENT_ROLE, agent);
        agents.push(agent);
        isAgent[agent] = true;
        
        emit AgentAdded(agent);
    }
    
    function removeAgent(address agent) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(isAgent[agent], "Not agent");
        require(agents.length > 1, "Cannot remove last agent");
        
        _revokeRole(AGENT_ROLE, agent);
        isAgent[agent] = false;
        
        // Remove from array
        for (uint256 i = 0; i < agents.length; i++) {
            if (agents[i] == agent) {
                agents[i] = agents[agents.length - 1];
                agents.pop();
                break;
            }
        }
        
        emit AgentRemoved(agent);
    }
    
    function getAllAgents() external view returns (address[] memory) {
        return agents;
    }
    
    function getAgentCount() external view returns (uint256) {
        return agents.length;
    }
    
    function getAgentAt(uint256 index) external view returns (address) {
        require(index < agents.length, "Index out of bounds");
        return agents[index];
    }
    
    function acceptAgreement() external {
        address account = _msgSender();
        require(_agreementAcceptances[account] == 0, "Already accepted");
        
        _agreementAcceptances[account] = block.timestamp;
        emit AgreementAccepted(account, block.timestamp);
    }
    
    function hasAcceptedAgreement(address account) external view returns (bool) {
        return _agreementAcceptances[account] > 0;
    }
    
    function getAgreementAcceptanceTimestamp(address account) external view returns (uint256) {
        return _agreementAcceptances[account];
    }
    
    // Emergency controls
    function emergencyPause() external onlyRole(DEFAULT_ADMIN_ROLE) {
        _emergencyPaused = true;
        emit EmergencyPaused(_msgSender());
    }
    
    function emergencyUnpause() external onlyRole(DEFAULT_ADMIN_ROLE) {
        _emergencyPaused = false;
        emit EmergencyUnpaused(_msgSender());
    }
    
    function pauseFunction(bytes4 functionSelector) external onlyRole(DEFAULT_ADMIN_ROLE) {
        _functionPaused[functionSelector] = true;
    }
    
    function unpauseFunction(bytes4 functionSelector) external onlyRole(DEFAULT_ADMIN_ROLE) {
        _functionPaused[functionSelector] = false;
    }
    
    function isEmergencyPaused() external view returns (bool) {
        return _emergencyPaused;
    }
    
    function isFunctionPaused(bytes4 functionSelector) external view returns (bool) {
        return _functionPaused[functionSelector];
    }
    
    function version() external pure returns (string memory) {
        return "3.0.0-enhanced";
    }
}
