{"/api/clients/route": "app/api/clients/route.js", "/api/clients/[id]/route": "app/api/clients/[id]/route.js", "/api/identity/route": "app/api/identity/route.js", "/api/qualification-progress/route": "app/api/qualification-progress/route.js", "/api/tokens/route": "app/api/tokens/route.js", "/api/whitelist/check/route": "app/api/whitelist/check/route.js", "/tokens/[address]/page": "app/tokens/[address]/page.js", "/orders/page": "app/orders/page.js", "/page": "app/page.js", "/clients/page": "app/clients/page.js", "/clients/[id]/page": "app/clients/[id]/page.js"}