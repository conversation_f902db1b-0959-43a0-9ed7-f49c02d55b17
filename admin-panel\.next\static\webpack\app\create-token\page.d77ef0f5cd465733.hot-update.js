"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/create-token/page",{

/***/ "(app-pages-browser)/./src/app/create-token/hooks/useTokenDeployment.ts":
/*!**********************************************************!*\
  !*** ./src/app/create-token/hooks/useTokenDeployment.ts ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTokenDeployment: () => (/* binding */ useTokenDeployment)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/constants/addresses.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/address/checks.js\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../config */ \"(app-pages-browser)/./src/config.ts\");\n/* harmony import */ var _contracts_SecurityTokenFactory_json__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../contracts/SecurityTokenFactory.json */ \"(app-pages-browser)/./src/contracts/SecurityTokenFactory.json\");\n/* harmony import */ var _contracts_SecurityToken_json__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../contracts/SecurityToken.json */ \"(app-pages-browser)/./src/contracts/SecurityToken.json\");\n/* harmony import */ var _useERC3643Integration__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./useERC3643Integration */ \"(app-pages-browser)/./src/app/create-token/hooks/useERC3643Integration.ts\");\n\n\n\n\n\n\n/**\r\n * Custom hook for token deployment logic\r\n *\r\n * Encapsulates all the token deployment functionality including state management,\r\n * transaction handling, and error handling\r\n */ function useTokenDeployment(network, factoryAddress, hasDeployerRole, kycSupported) {\n    // State management\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [deployedToken, setDeployedToken] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [transactionHash, setTransactionHash] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [deploymentStep, setDeploymentStep] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('idle');\n    // ERC-3643 integration\n    const { setupERC3643Compliance, isERC3643Available } = (0,_useERC3643Integration__WEBPACK_IMPORTED_MODULE_4__.useERC3643Integration)();\n    /**\r\n   * Save token data to database\r\n   */ const saveTokenToDatabase = async (deployedToken, formData, transactionHash, blockNumber, network)=>{\n        // Fetch totalSupply from the blockchain\n        let totalSupply = '0';\n        try {\n            // Create a new provider instance for blockchain calls\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_5__.BrowserProvider(window.ethereum);\n            const token = new ethers__WEBPACK_IMPORTED_MODULE_6__.Contract(deployedToken.address, _contracts_SecurityToken_json__WEBPACK_IMPORTED_MODULE_3__.abi, provider);\n            const totalSupplyRaw = await token.totalSupply();\n            totalSupply = deployedToken.decimals === 0 ? totalSupplyRaw.toString() : ethers__WEBPACK_IMPORTED_MODULE_7__.formatUnits(totalSupplyRaw, deployedToken.decimals);\n        } catch (error) {\n            console.warn('Could not fetch totalSupply from blockchain, using default 0:', error);\n        }\n        const tokenData = {\n            address: deployedToken.address,\n            transactionHash: transactionHash,\n            blockNumber: blockNumber,\n            network: network,\n            name: deployedToken.name,\n            symbol: deployedToken.symbol,\n            decimals: deployedToken.decimals,\n            maxSupply: deployedToken.maxSupply,\n            totalSupply: totalSupply,\n            tokenType: formData.tokenType,\n            tokenPrice: deployedToken.tokenPrice,\n            currency: deployedToken.currency,\n            bonusTiers: deployedToken.bonusTiers,\n            tokenImageUrl: deployedToken.tokenImageUrl,\n            whitelistAddress: deployedToken.whitelistAddress,\n            adminAddress: deployedToken.admin,\n            hasKYC: deployedToken.hasKYC,\n            selectedClaims: formData.selectedClaims,\n            isActive: true,\n            deployedBy: deployedToken.admin,\n            deploymentNotes: \"\".concat(formData.tokenType, \" token deployed via admin panel\")\n        };\n        const response = await fetch('/api/tokens', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify(tokenData)\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(\"Database save failed: \".concat(errorData.error || 'Unknown error'));\n        }\n        return await response.json();\n    };\n    /**\r\n   * Deploy a new token with the provided form data\r\n   */ const deployToken = async (formData)=>{\n        setIsSubmitting(true);\n        setError(null);\n        setSuccess(null);\n        setDeployedToken(null);\n        setTransactionHash(null);\n        setDeploymentStep('preparing');\n        try {\n            // Validate form data\n            validateFormData(formData);\n            // Get network configuration\n            const networkConfig = (0,_config__WEBPACK_IMPORTED_MODULE_1__.getNetworkConfig)(network);\n            if (!factoryAddress) {\n                throw new Error(\"No factory address configured for network: \".concat(network));\n            }\n            if (!window.ethereum) {\n                throw new Error('Please install MetaMask to use this feature!');\n            }\n            setDeploymentStep('connecting');\n            // Get provider and signer\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_5__.BrowserProvider(window.ethereum);\n            const signer = await provider.getSigner();\n            // Verify network connection\n            await verifyNetworkConnection(provider, network);\n            // Connect to the factory contract\n            const factory = new ethers__WEBPACK_IMPORTED_MODULE_6__.Contract(factoryAddress, _contracts_SecurityTokenFactory_json__WEBPACK_IMPORTED_MODULE_2__.abi, signer);\n            console.log(\"Connected to factory at:\", factoryAddress);\n            // Verify deployer role\n            await verifyDeployerRole(factory, signer, hasDeployerRole);\n            // Verify KYC support\n            await verifyKYCSupport(factory, formData.enableKYC);\n            // Check if token symbol already exists\n            await checkTokenSymbolAvailability(factory, formData.symbol);\n            // Convert maxSupply to the appropriate unit based on decimals\n            const maxSupplyWei = formData.decimals === 0 ? BigInt(formData.maxSupply) : ethers__WEBPACK_IMPORTED_MODULE_7__.parseUnits(formData.maxSupply, formData.decimals);\n            setDeploymentStep('deploying');\n            console.log(\"Deploying token with params:\", {\n                name: formData.name,\n                symbol: formData.symbol,\n                decimals: formData.decimals,\n                maxSupply: formData.maxSupply,\n                admin: formData.ownerAddress,\n                tokenPrice: formData.tokenPrice,\n                bonusTiers: formData.bonusTiers,\n                enableKYC: formData.enableKYC\n            });\n            // Create the transaction\n            const tx = await createDeployTransaction(factory, formData, maxSupplyWei, network, kycSupported);\n            setTransactionHash(tx.hash);\n            console.log(\"Transaction hash:\", tx.hash);\n            setDeploymentStep('confirming');\n            // Wait for the transaction to be mined\n            const receipt = await tx.wait();\n            console.log(\"Transaction mined in block:\", receipt.blockNumber);\n            setDeploymentStep('fetching');\n            // Get the token address\n            const tokenAddress = await factory.getTokenAddressBySymbol(formData.symbol);\n            if (tokenAddress && tokenAddress !== ethers__WEBPACK_IMPORTED_MODULE_8__.ZeroAddress) {\n                // Create token deployment result\n                const deploymentResult = await getDeploymentResult(tokenAddress, provider, formData);\n                setDeployedToken(deploymentResult);\n                // Save token to database\n                try {\n                    await saveTokenToDatabase(deploymentResult, formData, tx.hash, receipt.blockNumber.toString(), network);\n                    console.log(\"Token successfully saved to database\");\n                } catch (dbError) {\n                    console.warn(\"Failed to save token to database:\", dbError);\n                // Don't fail the deployment if database save fails\n                }\n                // Setup ERC-3643 compliance if available\n                if (isERC3643Available()) {\n                    setDeploymentStep('setting_up_compliance');\n                    console.log(\"🏛️ Setting up ERC-3643 compliance...\");\n                    try {\n                        const complianceResult = await setupERC3643Compliance(tokenAddress, formData.ownerAddress, signer, {\n                            name: formData.name,\n                            symbol: formData.symbol,\n                            tokenType: formData.tokenType,\n                            country: formData.issuerCountry || 'US',\n                            selectedClaims: formData.selectedClaims\n                        });\n                        if (complianceResult.errors.length > 0) {\n                            console.warn(\"⚠️ Some ERC-3643 setup steps failed:\", complianceResult.errors);\n                        // Don't fail deployment, just warn\n                        } else {\n                            console.log(\"✅ ERC-3643 compliance setup completed successfully\");\n                        }\n                    } catch (complianceError) {\n                        console.warn(\"⚠️ ERC-3643 compliance setup failed:\", complianceError);\n                    // Don't fail deployment, just warn\n                    }\n                } else {\n                    console.log(\"ℹ️ ERC-3643 contracts not available, skipping compliance setup\");\n                }\n                setDeploymentStep('completed');\n                setSuccess('Token \"'.concat(formData.name, '\" (').concat(formData.symbol, \") successfully deployed!\"));\n            } else {\n                throw new Error(\"Token deployment failed: Could not retrieve token address\");\n            }\n        } catch (err) {\n            handleDeploymentError(err, network);\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    /**\r\n   * Validate form data before deployment\r\n   */ const validateFormData = (formData)=>{\n        if (!formData.name || !formData.symbol || !formData.maxSupply || !formData.ownerAddress) {\n            throw new Error('Please fill in all required fields');\n        }\n        if (!ethers__WEBPACK_IMPORTED_MODULE_9__.isAddress(formData.ownerAddress)) {\n            throw new Error('Invalid owner address');\n        }\n        if (formData.decimals < 0 || formData.decimals > 18) {\n            throw new Error('Decimals must be between 0 and 18');\n        }\n    };\n    /**\r\n   * Verify that the wallet is connected to the correct network\r\n   */ const verifyNetworkConnection = async (provider, network)=>{\n        const chainId = (await provider.getNetwork()).chainId;\n        if (network === 'amoy' && chainId.toString() !== '80002') {\n            throw new Error('Please connect your wallet to the Amoy network (Chain ID: 80002)');\n        } else if (network === 'polygon' && chainId.toString() !== '137') {\n            throw new Error('Please connect your wallet to the Polygon network (Chain ID: 137)');\n        }\n    };\n    /**\r\n   * Verify that the connected wallet has DEPLOYER_ROLE\r\n   */ const verifyDeployerRole = async (factory, signer, hasRole)=>{\n        if (!hasRole) {\n            const DEPLOYER_ROLE = await factory.DEPLOYER_ROLE();\n            const hasDeployerRole = await factory.hasRole(DEPLOYER_ROLE, await signer.getAddress());\n            if (!hasDeployerRole) {\n                throw new Error(\"Your wallet does not have the DEPLOYER_ROLE required to create tokens\");\n            }\n        }\n    };\n    /**\r\n   * Check if token symbol is available\r\n   */ const checkTokenSymbolAvailability = async (factory, symbol)=>{\n        try {\n            const existingTokenAddress = await factory.getTokenAddressBySymbol(symbol);\n            if (existingTokenAddress !== ethers__WEBPACK_IMPORTED_MODULE_8__.ZeroAddress) {\n                throw new Error('Token with symbol \"'.concat(symbol, '\" already exists at address ').concat(existingTokenAddress, \". Please choose a different symbol.\"));\n            }\n        } catch (err) {\n            if (err.message.includes(\"already exists\")) {\n                throw err; // Re-throw our custom error\n            }\n            // If it's a different error, log it but don't fail the deployment\n            console.warn(\"Could not check token symbol availability:\", err.message);\n        }\n    };\n    /**\r\n   * Verify KYC support in the factory contract\r\n   */ const verifyKYCSupport = async (factory, enableKYC)=>{\n        if (!enableKYC) return; // Skip verification if KYC is not enabled\n        try {\n            // Check if this is the enhanced factory with built-in KYC\n            const factoryAddress = await factory.getAddress();\n            const isEnhancedFactory = factoryAddress === \"******************************************\" || factoryAddress === \"******************************************\" || factoryAddress === \"******************************************\";\n            if (isEnhancedFactory) {\n                console.log(\"✅ Enhanced factory with built-in KYC support detected\");\n                return; // Enhanced factory has built-in KYC, no need for further checks\n            }\n            // Fallback to old KYC detection method for other factories\n            try {\n                const kycImplementation = await factory.whitelistWithKYCImplementation();\n                if (!kycImplementation || kycImplementation === ethers__WEBPACK_IMPORTED_MODULE_8__.ZeroAddress) {\n                    throw new Error(\"KYC implementation address is not set in the factory contract.\");\n                }\n                return true;\n            } catch (err) {\n                // Enhanced factory doesn't have this function - it always supports KYC\n                console.log(\"Enhanced factory detected - KYC support is built-in\");\n                return true;\n            }\n        } catch (err) {\n            if (err.message.includes(\"whitelistWithKYCImplementation\")) {\n                throw new Error(\"The deployed factory contract doesn't support the KYC functionality. Please deploy an updated factory contract.\");\n            }\n            throw err;\n        }\n    };\n    /**\r\n   * Create the deployment transaction with the appropriate gas settings\r\n   */ const createDeployTransaction = async (factory, formData, maxSupplyWei, network, supportsKYC)=>{\n        // Determine if KYC is supported\n        let canUseKYC = supportsKYC;\n        try {\n            // Check if this is the enhanced factory with built-in KYC\n            const factoryAddress = await factory.getAddress();\n            const isEnhancedFactory = factoryAddress === \"******************************************\" || factoryAddress === \"******************************************\" || factoryAddress === \"******************************************\";\n            if (isEnhancedFactory) {\n                canUseKYC = true;\n                console.log(\"✅ Using enhanced factory with built-in KYC support\");\n            } else {\n                // Fallback to old KYC detection method\n                await factory.whitelistWithKYCImplementation();\n                const hasKYCFunction = factory.interface.fragments.some((fragment)=>fragment.type === \"function\" && 'name' in fragment && fragment.name === \"deploySecurityTokenWithOptions\");\n                canUseKYC = hasKYCFunction;\n            }\n        } catch (err) {\n            console.log(\"KYC functionality not supported in this factory contract, using standard deployment\");\n            canUseKYC = false;\n            // If KYC was enabled but not supported, warn the user\n            if (formData.enableKYC) {\n                console.warn(\"KYC requested but not supported by the contract. Proceeding with standard token deployment.\");\n            }\n        }\n        // Optimized gas settings for Amoy testnet\n        if (network === 'amoy') {\n            // Optimized gas settings for Amoy testnet - 5M gas limit, 50 gwei gas price\n            const gasLimit = BigInt(5000000);\n            const gasPrice = ethers__WEBPACK_IMPORTED_MODULE_7__.parseUnits(\"50\", \"gwei\");\n            console.log(\"Using optimized gas settings for Amoy testnet:\");\n            console.log(\"Gas limit:\", gasLimit.toString());\n            console.log(\"Gas price:\", ethers__WEBPACK_IMPORTED_MODULE_7__.formatUnits(gasPrice, \"gwei\"), \"gwei\");\n            // Call the appropriate function based on factory type\n            const factoryAddress = await factory.getAddress();\n            const isEnhancedFactory = factoryAddress === \"******************************************\" || factoryAddress === \"******************************************\" || factoryAddress === \"******************************************\" || factoryAddress === \"******************************************\" || factoryAddress === \"******************************************\"; // Final factory\n            if (isEnhancedFactory) {\n                console.log(\"Calling enhanced factory deploySecurityToken with built-in KYC\");\n                return await factory.deploySecurityToken(formData.name, formData.symbol, formData.decimals, maxSupplyWei, formData.ownerAddress, \"\".concat(formData.tokenPrice, \" \").concat(formData.currency), formData.bonusTiers, \"\".concat(formData.tokenType, \" token deployed via admin panel\"), formData.tokenImageUrl || \"\", {\n                    gasLimit,\n                    gasPrice\n                });\n            } else if (canUseKYC) {\n                console.log(\"Calling deploySecurityTokenWithOptions with KYC:\", formData.enableKYC);\n                return await factory.deploySecurityTokenWithOptions(formData.name, formData.symbol, formData.decimals, maxSupplyWei, formData.ownerAddress, \"\".concat(formData.tokenPrice, \" \").concat(formData.currency), formData.bonusTiers, \"\".concat(formData.tokenType, \" token deployed via admin panel\"), formData.tokenImageUrl || \"\", formData.enableKYC, {\n                    gasLimit,\n                    gasPrice\n                });\n            } else {\n                console.log(\"Calling deploySecurityToken (no KYC support)\");\n                return await factory.deploySecurityToken(formData.name, formData.symbol, formData.decimals, maxSupplyWei, formData.ownerAddress, \"\".concat(formData.tokenPrice, \" \").concat(formData.currency), formData.bonusTiers, \"\".concat(formData.tokenType, \" token deployed via admin panel\"), formData.tokenImageUrl || \"\", {\n                    gasLimit,\n                    gasPrice\n                });\n            }\n        } else {\n            // For other networks, try to estimate gas\n            let gasLimit;\n            try {\n                // Estimate gas based on factory type\n                let gasEstimate;\n                const factoryAddress = await factory.getAddress();\n                const isEnhancedFactory = factoryAddress === \"******************************************\" || factoryAddress === \"******************************************\" || factoryAddress === \"******************************************\";\n                if (isEnhancedFactory || !canUseKYC) {\n                    gasEstimate = await factory.deploySecurityToken.estimateGas(formData.name, formData.symbol, formData.decimals, maxSupplyWei, formData.ownerAddress, \"\".concat(formData.tokenPrice, \" \").concat(formData.currency), formData.bonusTiers, \"\".concat(formData.tokenType, \" token deployed via admin panel\"), formData.tokenImageUrl || \"\");\n                } else {\n                    gasEstimate = await factory.deploySecurityTokenWithOptions.estimateGas(formData.name, formData.symbol, formData.decimals, maxSupplyWei, formData.ownerAddress, \"\".concat(formData.tokenPrice, \" \").concat(formData.currency), formData.bonusTiers, \"\".concat(formData.tokenType, \" token deployed via admin panel\"), formData.tokenImageUrl || \"\", formData.enableKYC);\n                }\n                console.log(\"Gas estimate:\", gasEstimate.toString());\n                // Add 50% to the gas estimate to be safer\n                gasLimit = gasEstimate * BigInt(150) / BigInt(100);\n                console.log(\"Using calculated gas limit:\", gasLimit.toString());\n            } catch (estimateErr) {\n                console.error(\"Gas estimation failed, using fixed limit:\", estimateErr);\n                // Fallback to fixed gas limit if estimation fails\n                gasLimit = BigInt(2000000); // Use 2M for non-Amoy networks\n                console.log(\"Using fallback gas limit:\", gasLimit.toString());\n            }\n            // Call without specific gas price for networks that calculate it properly\n            const factoryAddress = await factory.getAddress();\n            const isEnhancedFactory = factoryAddress === \"******************************************\" || factoryAddress === \"******************************************\" || factoryAddress === \"******************************************\";\n            if (isEnhancedFactory) {\n                return await factory.deploySecurityToken(formData.name, formData.symbol, formData.decimals, maxSupplyWei, formData.ownerAddress, \"\".concat(formData.tokenPrice, \" \").concat(formData.currency), formData.bonusTiers, \"\".concat(formData.tokenType, \" token deployed via admin panel\"), formData.tokenImageUrl || \"\", {\n                    gasLimit\n                });\n            } else if (canUseKYC) {\n                return await factory.deploySecurityTokenWithOptions(formData.name, formData.symbol, formData.decimals, maxSupplyWei, formData.ownerAddress, \"\".concat(formData.tokenPrice, \" \").concat(formData.currency), formData.bonusTiers, \"\".concat(formData.tokenType, \" token deployed via admin panel\"), formData.tokenImageUrl || \"\", formData.enableKYC, {\n                    gasLimit\n                });\n            } else {\n                return await factory.deploySecurityToken(formData.name, formData.symbol, formData.decimals, maxSupplyWei, formData.ownerAddress, \"\".concat(formData.tokenPrice, \" \").concat(formData.currency), formData.bonusTiers, \"\".concat(formData.tokenType, \" token deployed via admin panel\"), formData.tokenImageUrl || \"\", {\n                    gasLimit\n                });\n            }\n        }\n    };\n    /**\r\n   * Get deployment result after successful transaction\r\n   */ const getDeploymentResult = async (tokenAddress, provider, formData)=>{\n        console.log(\"Token successfully deployed at:\", tokenAddress);\n        // Connect to the token contract\n        const token = new ethers__WEBPACK_IMPORTED_MODULE_6__.Contract(tokenAddress, _contracts_SecurityToken_json__WEBPACK_IMPORTED_MODULE_3__.abi, provider);\n        // Get identity registry address (whitelist contract)\n        const whitelistAddress = await token.identityRegistry();\n        // Get token decimals from the contract\n        const tokenDecimals = await token.decimals();\n        const decimalsNumber = Number(tokenDecimals);\n        // Format maxSupply based on decimals\n        const maxSupplyRaw = await token.maxSupply();\n        const maxSupplyFormatted = decimalsNumber === 0 ? maxSupplyRaw.toString() : ethers__WEBPACK_IMPORTED_MODULE_7__.formatUnits(maxSupplyRaw, tokenDecimals);\n        // Try to get the image URL from the contract if supported\n        let tokenImageUrl = formData.tokenImageUrl;\n        try {\n            // Check if the token contract supports tokenImageUrl function\n            if (token.interface.fragments.some((fragment)=>fragment.type === \"function\" && 'name' in fragment && fragment.name === \"tokenImageUrl\")) {\n                tokenImageUrl = await token.tokenImageUrl();\n            }\n        } catch (error) {\n            console.log(\"Token contract doesn't support image URL, using form data\");\n        }\n        // Create deployed token object\n        return {\n            address: tokenAddress,\n            name: await token.name(),\n            symbol: await token.symbol(),\n            decimals: decimalsNumber,\n            maxSupply: maxSupplyFormatted,\n            whitelistAddress: whitelistAddress,\n            admin: formData.ownerAddress,\n            tokenPrice: \"\".concat(formData.tokenPrice, \" \").concat(formData.currency),\n            currency: formData.currency,\n            bonusTiers: formData.bonusTiers,\n            hasKYC: formData.enableKYC,\n            tokenImageUrl: tokenImageUrl\n        };\n    };\n    /**\r\n   * Handle deployment errors with detailed messages\r\n   */ const handleDeploymentError = (err, network)=>{\n        console.error('Error creating token:', err);\n        // Extract more details from the error for debugging\n        const errorDetails = typeof err === 'object' ? JSON.stringify({\n            code: err.code,\n            message: err.message,\n            data: err.data,\n            info: err.info\n        }, null, 2) : String(err);\n        // Special handling for specific contract errors\n        if (err.message.includes(\"transaction execution reverted\")) {\n            // This is likely a contract validation error\n            setError(\"Transaction failed: The contract rejected the transaction. This could be due to:\\n\\n• Token symbol already exists - try a different symbol\\n• Invalid parameters (empty name/symbol, zero max supply, etc.)\\n• Access control issues\\n\\nPlease check your inputs and try again with a unique token symbol.\\n\\nTechnical details: \".concat(err.message));\n        } else if (err.message.includes(\"gas required exceeds allowance\") || err.message.includes(\"intrinsic gas too low\") || err.message.includes(\"Internal JSON-RPC error\")) {\n            // For Amoy testnet specifically, provide CLI alternative\n            if (network === 'amoy') {\n                // Create a CLI command template - actual values will be filled in by the UI\n                const cliCommand = '# For Windows PowerShell:\\ncd Token\\n$env:NETWORK=\"amoy\"\\n$env:TOKEN_NAME=\"YourTokenName\"\\n$env:TOKEN_SYMBOL=\"YTS\"\\n$env:TOKEN_DECIMALS=\"0\"\\n$env:MAX_SUPPLY=\"1000000\"\\n$env:ADMIN_ADDRESS=\"0xYourAddress\"\\n$env:TOKEN_PRICE=\"10 USD\"\\n$env:BONUS_TIERS=\"Tier 1: 5%, Tier 2: 10%\"\\nnpx hardhat run scripts/02-deploy-token.js --network amoy';\n                setError(\"Gas estimation failed on Amoy testnet. This is a common issue with this network.\\n\\nYou can try using this command line script instead:\\n\\n\".concat(cliCommand));\n            } else {\n                setError(\"Transaction failed due to gas calculation issues: \".concat(err.message, \"\\n\\nDetails: \").concat(errorDetails));\n            }\n        } else if (err.message.includes(\"Internal JSON-RPC error\") || err.message.includes(\"could not coalesce error\")) {\n            setError(\"Transaction failed. This is likely due to gas calculation issues on the Amoy testnet. Try using the command line script instead.\");\n        } else {\n            setError(\"Transaction failed: \".concat(err.message, \"\\n\\nDetails: \").concat(errorDetails));\n        }\n        setDeploymentStep('failed');\n    };\n    return {\n        isSubmitting,\n        error,\n        success,\n        deployedToken,\n        transactionHash,\n        deploymentStep,\n        deployToken\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/create-token/hooks/useTokenDeployment.ts\n"));

/***/ })

});