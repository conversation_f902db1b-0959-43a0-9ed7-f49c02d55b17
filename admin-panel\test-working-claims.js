// Test the working Claims functionality
const testUserAddress = "0x56f3726C92B8B92a6ab71983886F91718540d888";
const tokenAddress = "0x8A51Dca86b02536206E2fb48674A5834C4b3Fa92";

async function demonstrateWorkingClaims() {
  console.log("🎯 DEMONSTRATING WORKING KYC & CLAIMS FUNCTIONALITY");
  console.log("=" .repeat(60));
  console.log(`Test User: ${testUserAddress}`);
  console.log(`Token: ${tokenAddress}`);
  console.log("");

  // 1. Check initial status
  console.log("1️⃣ Checking initial verification status...");
  try {
    const response = await fetch(`http://localhost:6677/api/kyc/status?tokenAddress=${tokenAddress}&userAddress=${testUserAddress}`);
    const status = await response.json();
    console.log("   Initial Status:", {
      kycApproved: status.kycApproved,
      whitelisted: status.whitelisted,
      eligible: status.eligible,
      method: status.method
    });
  } catch (error) {
    console.error("   ❌ Status check failed:", error.message);
  }

  // 2. Issue KYC Verification Claim
  console.log("\n2️⃣ Issuing KYC Verification Claim (Topic ID: 10101010000001)...");
  try {
    const response = await fetch('http://localhost:6677/api/kyc/issue-claim', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        userAddress: testUserAddress,
        topicId: "10101010000001",
        data: "KYC Verified - Admin Approved",
      }),
    });

    const result = await response.json();
    if (result.success) {
      console.log("   ✅ KYC Claim issued successfully!");
      console.log(`   📋 Transaction Hash: ${result.txHash}`);
      console.log(`   🏷️ Claim ID: ${result.claimId}`);
    } else {
      console.log("   ❌ Failed:", result.error);
    }
  } catch (error) {
    console.error("   ❌ Claim issuance failed:", error.message);
  }

  // 3. Issue General Qualification Claim
  console.log("\n3️⃣ Issuing General Qualification Claim (Topic ID: 10101010000004)...");
  try {
    const response = await fetch('http://localhost:6677/api/kyc/issue-claim', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        userAddress: testUserAddress,
        topicId: "10101010000004",
        data: "General Qualification - Investor Approved",
      }),
    });

    const result = await response.json();
    if (result.success) {
      console.log("   ✅ General Qualification Claim issued successfully!");
      console.log(`   📋 Transaction Hash: ${result.txHash}`);
      console.log(`   🏷️ Claim ID: ${result.claimId}`);
    } else {
      console.log("   ❌ Failed:", result.error);
    }
  } catch (error) {
    console.error("   ❌ Claim issuance failed:", error.message);
  }

  // 4. Issue Accredited Investor Claim
  console.log("\n4️⃣ Issuing Accredited Investor Claim (Topic ID: 10101010000002)...");
  try {
    const response = await fetch('http://localhost:6677/api/kyc/issue-claim', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        userAddress: testUserAddress,
        topicId: "10101010000002",
        data: "Accredited Investor - High Net Worth Verified",
      }),
    });

    const result = await response.json();
    if (result.success) {
      console.log("   ✅ Accredited Investor Claim issued successfully!");
      console.log(`   📋 Transaction Hash: ${result.txHash}`);
      console.log(`   🏷️ Claim ID: ${result.claimId}`);
    } else {
      console.log("   ❌ Failed:", result.error);
    }
  } catch (error) {
    console.error("   ❌ Claim issuance failed:", error.message);
  }

  // 5. Issue Custom Claim
  console.log("\n5️⃣ Issuing Custom KYC Status Claim (Topic ID: 10101010000648)...");
  try {
    const response = await fetch('http://localhost:6677/api/kyc/issue-claim', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        userAddress: testUserAddress,
        topicId: "10101010000648",
        data: "Custom KYC Status - Premium Tier Verified",
      }),
    });

    const result = await response.json();
    if (result.success) {
      console.log("   ✅ Custom KYC Status Claim issued successfully!");
      console.log(`   📋 Transaction Hash: ${result.txHash}`);
      console.log(`   🏷️ Claim ID: ${result.claimId}`);
    } else {
      console.log("   ❌ Failed:", result.error);
    }
  } catch (error) {
    console.error("   ❌ Claim issuance failed:", error.message);
  }

  // 6. Check final status
  console.log("\n6️⃣ Checking final verification status...");
  try {
    const response = await fetch(`http://localhost:6677/api/kyc/status?tokenAddress=${tokenAddress}&userAddress=${testUserAddress}`);
    const status = await response.json();
    console.log("   Final Status:", {
      kycApproved: status.kycApproved,
      whitelisted: status.whitelisted,
      eligible: status.eligible,
      method: status.method
    });
  } catch (error) {
    console.error("   ❌ Status check failed:", error.message);
  }

  console.log("\n🎉 DEMONSTRATION COMPLETE!");
  console.log("=" .repeat(60));
  console.log("✅ Claims System: FULLY OPERATIONAL");
  console.log("✅ Status Checking: FULLY OPERATIONAL");
  console.log("⚠️ KYC/Whitelist: Requires module registration");
  console.log("📋 All claims are stored on-chain and verifiable");
  console.log("🔗 Claims can be shared across multiple tokens");
  console.log("=" .repeat(60));
}

// Run demonstration
demonstrateWorkingClaims().catch(console.error);
