"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/modular-tokens/page",{

/***/ "(app-pages-browser)/./src/app/modular-tokens/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/modular-tokens/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ModularTokensPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useModularToken__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../hooks/useModularToken */ \"(app-pages-browser)/./src/hooks/useModularToken.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n// Import custom hook\n\n// Contract addresses from environment\nconst SECURITY_TOKEN_CORE_ADDRESS = \"0xA4f70C1953165be63409e1F0Ff11c2D03522E8C2\";\nconst UPGRADE_MANAGER_ADDRESS = \"0xb7a53dC340C2E5e823002B174629e2a44B8ed815\";\nfunction ModularTokensPage() {\n    _s();\n    // Use the custom hook for modular token functionality\n    const { provider, signer, tokenInfo, upgradeInfo, pendingUpgrades, upgradeHistory, loading, error, initializeProvider, mintTokens, togglePause, scheduleUpgrade, toggleEmergencyMode, refreshData, setError } = (0,_hooks_useModularToken__WEBPACK_IMPORTED_MODULE_2__.useModularToken)();\n    // Local state for UI\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mintAmount, setMintAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [mintAddress, setMintAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [upgradeDescription, setUpgradeDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newImplementationAddress, setNewImplementationAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [actionLoading, setActionLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    // KYC & Claims state\n    const [kycUserAddress, setKycUserAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [claimUserAddress, setClaimUserAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [claimTopicId, setClaimTopicId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('10101010000001');\n    const [claimData, setClaimData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [checkUserAddress, setCheckUserAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [verificationStatus, setVerificationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Clear success message after 5 seconds\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"ModularTokensPage.useEffect\": ()=>{\n            if (success) {\n                const timer = setTimeout({\n                    \"ModularTokensPage.useEffect.timer\": ()=>setSuccess(null)\n                }[\"ModularTokensPage.useEffect.timer\"], 5000);\n                return ({\n                    \"ModularTokensPage.useEffect\": ()=>clearTimeout(timer)\n                })[\"ModularTokensPage.useEffect\"];\n            }\n        }\n    }[\"ModularTokensPage.useEffect\"], [\n        success\n    ]);\n    // Clear error message after 10 seconds\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"ModularTokensPage.useEffect\": ()=>{\n            if (error) {\n                const timer = setTimeout({\n                    \"ModularTokensPage.useEffect.timer\": ()=>setError(null)\n                }[\"ModularTokensPage.useEffect.timer\"], 10000);\n                return ({\n                    \"ModularTokensPage.useEffect\": ()=>clearTimeout(timer)\n                })[\"ModularTokensPage.useEffect\"];\n            }\n        }\n    }[\"ModularTokensPage.useEffect\"], [\n        error,\n        setError\n    ]);\n    const handleMint = async ()=>{\n        if (!mintAddress || !mintAmount) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            await mintTokens(mintAddress, mintAmount);\n            setSuccess(\"Successfully minted \".concat(mintAmount, \" tokens to \").concat(mintAddress));\n            setMintAmount('');\n            setMintAddress('');\n            await refreshData(); // Refresh token info\n        } catch (error) {\n            console.error('Error minting tokens:', error);\n            setError(\"Failed to mint tokens: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handlePauseToggle = async ()=>{\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            await togglePause();\n            setSuccess(\"Token \".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'unpaused' : 'paused', \" successfully\"));\n            await refreshData(); // Refresh token info\n        } catch (error) {\n            console.error('Error toggling pause:', error);\n            setError(\"Failed to \".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'unpause' : 'pause', \" token: \").concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleScheduleUpgrade = async ()=>{\n        if (!newImplementationAddress || !upgradeDescription) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            await scheduleUpgrade(newImplementationAddress, upgradeDescription);\n            setSuccess('Upgrade scheduled successfully! It will be executable after the 48-hour timelock.');\n            setNewImplementationAddress('');\n            setUpgradeDescription('');\n            await refreshData(); // Refresh upgrade info\n        } catch (error) {\n            console.error('Error scheduling upgrade:', error);\n            setError(\"Failed to schedule upgrade: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleEmergencyModeToggle = async ()=>{\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            await toggleEmergencyMode();\n            setSuccess(\"Emergency mode \".concat((upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? 'deactivated' : 'activated', \" successfully\"));\n            await refreshData(); // Refresh upgrade info\n        } catch (error) {\n            console.error('Error toggling emergency mode:', error);\n            setError(\"Failed to \".concat((upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? 'deactivate' : 'activate', \" emergency mode: \").concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    // Utility functions\n    const formatAddress = (address)=>{\n        return \"\".concat(address.slice(0, 6), \"...\").concat(address.slice(-4));\n    };\n    const formatTimestamp = (timestamp)=>{\n        return new Date(timestamp * 1000).toLocaleString();\n    };\n    const getTimeUntilExecution = (executeTime)=>{\n        const now = Math.floor(Date.now() / 1000);\n        const timeLeft = executeTime - now;\n        if (timeLeft <= 0) return 'Ready to execute';\n        const hours = Math.floor(timeLeft / 3600);\n        const minutes = Math.floor(timeLeft % 3600 / 60);\n        return \"\".concat(hours, \"h \").concat(minutes, \"m remaining\");\n    };\n    if (!provider || !signer) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold mb-2\",\n                        children: \"Connect Wallet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: \"Please connect your MetaMask wallet to manage modular tokens.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: initializeProvider,\n                        className: \"bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\",\n                        children: \"Connect Wallet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                lineNumber: 165,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n            lineNumber: 164,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold\",\n                                children: \"Modular Token Management\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Manage your modular ERC-3643 security tokens and upgrades\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat((upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'),\n                                children: (upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? \"Emergency Mode Active\" : \"Normal Mode\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, this),\n                            tokenInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(tokenInfo.paused ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'),\n                                children: tokenInfo.paused ? \"Paused\" : \"Active\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: refreshData,\n                                disabled: loading,\n                                className: \"bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-1 px-3 rounded text-sm disabled:opacity-50\",\n                                children: [\n                                    loading ? '🔄' : '↻',\n                                    \" Refresh\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                lineNumber: 183,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-red-600 mr-2\",\n                            children: \"⚠️\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                    lineNumber: 219,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                lineNumber: 218,\n                columnNumber: 9\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-green-600 mr-2\",\n                            children: \"✅\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: success\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                    lineNumber: 228,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                lineNumber: 227,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"-mb-px flex space-x-8\",\n                            children: [\n                                'overview',\n                                'operations',\n                                'kyc-claims',\n                                'upgrades',\n                                'history'\n                            ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(tab),\n                                    className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === tab ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                    children: tab === 'kyc-claims' ? 'KYC & Claims' : tab.charAt(0).toUpperCase() + tab.slice(1).replace('-', ' ')\n                                }, tab, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 9\n                    }, this),\n                    activeTab === 'overview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg shadow p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium mb-2\",\n                                                children: \"Token Information\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-4\",\n                                                children: \"Current token details and status\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: tokenInfo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Name:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 266,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: tokenInfo.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 267,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Symbol:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 270,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: tokenInfo.symbol\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 271,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Version:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 274,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: tokenInfo.version\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 275,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Total Supply:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 278,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: tokenInfo.totalSupply\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 279,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Max Supply:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 282,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: tokenInfo.maxSupply\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 283,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 281,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Decimals:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 286,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: tokenInfo.decimals\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 287,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 285,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Status:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 290,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(tokenInfo.paused ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'),\n                                                                    children: tokenInfo.paused ? \"Paused\" : \"Active\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 291,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Loading token information...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 303,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg shadow p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium mb-2\",\n                                                children: \"Contract Addresses\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-4\",\n                                                children: \"Deployed contract addresses on Amoy testnet\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"SecurityTokenCore:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 314,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500 font-mono\",\n                                                                children: SECURITY_TOKEN_CORE_ADDRESS\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 315,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"UpgradeManager:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 320,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500 font-mono\",\n                                                                children: UPGRADE_MANAGER_ADDRESS\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 321,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"pt-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>window.open(\"https://amoy.polygonscan.com/address/\".concat(SECURITY_TOKEN_CORE_ADDRESS), '_blank'),\n                                                            className: \"bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-1 px-3 rounded text-sm\",\n                                                            children: \"View on PolygonScan\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 13\n                            }, this),\n                            (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.metadata) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium mb-2\",\n                                        children: \"Token Metadata\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-4\",\n                                        children: \"Additional token information and configuration\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Price:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: tokenInfo.metadata.tokenPrice\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Bonus Tiers:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: tokenInfo.metadata.bonusTiers\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 346,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Details:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 mt-1\",\n                                                        children: tokenInfo.metadata.tokenDetails\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 352,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 350,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'operations' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium mb-2\",\n                                            children: \"Mint Tokens\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"Mint new tokens to a specified address\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"mintAddress\",\n                                                            className: \"block text-sm font-medium text-gray-700\",\n                                                            children: \"Recipient Address\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 370,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            id: \"mintAddress\",\n                                                            type: \"text\",\n                                                            placeholder: \"0x...\",\n                                                            value: mintAddress,\n                                                            onChange: (e)=>setMintAddress(e.target.value),\n                                                            className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 371,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"mintAmount\",\n                                                            className: \"block text-sm font-medium text-gray-700\",\n                                                            children: \"Amount\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 381,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            id: \"mintAmount\",\n                                                            type: \"number\",\n                                                            placeholder: \"1000\",\n                                                            value: mintAmount,\n                                                            onChange: (e)=>setMintAmount(e.target.value),\n                                                            className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 382,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleMint,\n                                                    disabled: actionLoading || !mintAddress || !mintAmount,\n                                                    className: \"w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50\",\n                                                    children: [\n                                                        actionLoading ? '🔄 ' : '',\n                                                        \"Mint Tokens\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium mb-2\",\n                                            children: \"Token Controls\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"Pause/unpause token transfers and emergency controls\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handlePauseToggle,\n                                                    disabled: actionLoading,\n                                                    className: \"w-full font-bold py-2 px-4 rounded disabled:opacity-50 \".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'bg-green-600 hover:bg-green-700 text-white' : 'bg-red-600 hover:bg-red-700 text-white'),\n                                                    children: [\n                                                        actionLoading ? '🔄 ' : '',\n                                                        (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'Unpause Token' : 'Pause Token'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleEmergencyModeToggle,\n                                                    disabled: actionLoading,\n                                                    className: \"w-full font-bold py-2 px-4 rounded disabled:opacity-50 \".concat((upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? 'bg-green-600 hover:bg-green-700 text-white' : 'bg-red-600 hover:bg-red-700 text-white'),\n                                                    children: [\n                                                        actionLoading ? '🔄 ' : '',\n                                                        (upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: \"✅ Deactivate Emergency Mode\"\n                                                        }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: \"⚡ Activate Emergency Mode\"\n                                                        }, void 0, false)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 404,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                    lineNumber: 401,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                            lineNumber: 364,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                        lineNumber: 363,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'kyc-claims' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-600 text-xl\",\n                                                children: \"\\uD83D\\uDD10\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 445,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-medium text-blue-800\",\n                                                    children: \"On-Chain KYC & Claims System\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 448,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-1 text-sm text-blue-700\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"Manage KYC verification and custom claims using Tokeny-style Topic IDs:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 452,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"mt-1 list-disc list-inside space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: \"KYC Verification:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 454,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \" Topic ID 10101010000001\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 454,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: \"General Qualification:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 455,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \" Topic ID 10101010000004\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 455,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: \"Accredited Investor:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 456,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \" Topic ID 10101010000002\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 456,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: \"Custom Claims:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 457,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \" Like 10101010000648 for specific KYC status\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 457,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 453,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 451,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 447,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                    lineNumber: 443,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg shadow p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium mb-2\",\n                                                children: \"KYC Management\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 466,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-4\",\n                                                children: \"Approve KYC and add users to whitelist\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 467,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"kycUserAddress\",\n                                                                className: \"block text-sm font-medium text-gray-700\",\n                                                                children: \"User Address\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 470,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                id: \"kycUserAddress\",\n                                                                type: \"text\",\n                                                                placeholder: \"0x...\",\n                                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 471,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 469,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                disabled: actionLoading,\n                                                                className: \"bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50\",\n                                                                children: [\n                                                                    actionLoading ? '🔄 ' : '✅ ',\n                                                                    \"Approve KYC\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 479,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                disabled: actionLoading,\n                                                                className: \"bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50\",\n                                                                children: [\n                                                                    actionLoading ? '🔄 ' : '📋 ',\n                                                                    \"Add to Whitelist\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 485,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 478,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 468,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 465,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg shadow p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium mb-2\",\n                                                children: \"Custom Claims\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 496,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-4\",\n                                                children: \"Issue custom claims with specific Topic IDs\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 497,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"claimUserAddress\",\n                                                                className: \"block text-sm font-medium text-gray-700\",\n                                                                children: \"User Address\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 500,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                id: \"claimUserAddress\",\n                                                                type: \"text\",\n                                                                placeholder: \"0x...\",\n                                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 501,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 499,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"claimTopicId\",\n                                                                className: \"block text-sm font-medium text-gray-700\",\n                                                                children: \"Topic ID\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 509,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                id: \"claimTopicId\",\n                                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"10101010000001\",\n                                                                        children: \"10101010000001 - KYC Verification\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 514,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"10101010000002\",\n                                                                        children: \"10101010000002 - Accredited Investor\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 515,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"10101010000003\",\n                                                                        children: \"10101010000003 - Jurisdiction Compliance\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 516,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"10101010000004\",\n                                                                        children: \"10101010000004 - General Qualification\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 517,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"10101010000648\",\n                                                                        children: \"10101010000648 - Custom KYC Status\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 518,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 510,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 508,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"claimData\",\n                                                                className: \"block text-sm font-medium text-gray-700\",\n                                                                children: \"Claim Data (Optional)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 522,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                id: \"claimData\",\n                                                                placeholder: \"Additional claim information...\",\n                                                                rows: 2,\n                                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 523,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 521,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        disabled: actionLoading,\n                                                        className: \"w-full bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50\",\n                                                        children: [\n                                                            actionLoading ? '🔄 ' : '🏷️ ',\n                                                            \"Issue Custom Claim\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 530,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 498,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 464,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium mb-2\",\n                                        children: \"Verification Status Checker\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 541,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-4\",\n                                        children: \"Check KYC and claims status for any user\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 542,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"checkUserAddress\",\n                                                                className: \"block text-sm font-medium text-gray-700\",\n                                                                children: \"User Address\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 546,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                id: \"checkUserAddress\",\n                                                                type: \"text\",\n                                                                placeholder: \"0x...\",\n                                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 547,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 545,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-end\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            disabled: actionLoading,\n                                                            className: \"bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50\",\n                                                            children: [\n                                                                actionLoading ? '🔄 ' : '🔍 ',\n                                                                \"Check Status\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 555,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 554,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 544,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border rounded-lg p-4 bg-gray-50\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"Status results will appear here after checking a user address.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 567,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-2 space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"KYC Approved:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 570,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-400\",\n                                                                            children: \"-\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 571,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 569,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Whitelisted:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 574,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-400\",\n                                                                            children: \"-\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 575,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 573,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Eligible:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 578,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-400\",\n                                                                            children: \"-\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 579,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 577,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Method:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 582,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-400\",\n                                                                            children: \"-\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 583,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 581,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 568,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 566,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 565,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 543,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 540,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium mb-2\",\n                                        children: \"Token Claims Configuration\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 592,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-4\",\n                                        children: \"Configure which claims are required for this token\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 593,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"checkbox\",\n                                                                    className: \"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 598,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"ml-2 text-sm\",\n                                                                    children: \"Enable KYC Verification\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 599,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 597,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 596,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"checkbox\",\n                                                                    className: \"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 604,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"ml-2 text-sm\",\n                                                                    children: \"Enable Claims System\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 605,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 603,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 602,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 595,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"Required Claims\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 611,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"checkbox\",\n                                                                        className: \"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 614,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 text-sm\",\n                                                                        children: \"KYC Verification (10101010000001)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 615,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 613,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"checkbox\",\n                                                                        className: \"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 618,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 text-sm\",\n                                                                        children: \"General Qualification (10101010000004)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 619,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 617,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"checkbox\",\n                                                                        className: \"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 622,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 text-sm\",\n                                                                        children: \"Accredited Investor (10101010000002)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 623,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 621,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 612,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 610,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                disabled: actionLoading,\n                                                className: \"w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50\",\n                                                children: [\n                                                    actionLoading ? '🔄 ' : '⚙️ ',\n                                                    \"Update Token Configuration\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 628,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 594,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 591,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                        lineNumber: 441,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'upgrades' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium mb-2\",\n                                            children: \"Schedule Upgrade\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 643,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"Schedule a new implementation upgrade with 48-hour timelock\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 644,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"newImplementation\",\n                                                            className: \"block text-sm font-medium text-gray-700\",\n                                                            children: \"New Implementation Address\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 649,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            id: \"newImplementation\",\n                                                            type: \"text\",\n                                                            placeholder: \"0x...\",\n                                                            value: newImplementationAddress,\n                                                            onChange: (e)=>setNewImplementationAddress(e.target.value),\n                                                            className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 650,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 648,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"upgradeDescription\",\n                                                            className: \"block text-sm font-medium text-gray-700\",\n                                                            children: \"Upgrade Description\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 660,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            id: \"upgradeDescription\",\n                                                            placeholder: \"Describe the changes in this upgrade...\",\n                                                            value: upgradeDescription,\n                                                            onChange: (e)=>setUpgradeDescription(e.target.value),\n                                                            rows: 3,\n                                                            className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 661,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 659,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleScheduleUpgrade,\n                                                    disabled: actionLoading || !newImplementationAddress || !upgradeDescription,\n                                                    className: \"w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50\",\n                                                    children: [\n                                                        actionLoading ? '🔄 ' : '⏰ ',\n                                                        \"Schedule Upgrade (48h Timelock)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 670,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 647,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                    lineNumber: 642,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium mb-2\",\n                                            children: \"Pending Upgrades\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 682,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"Upgrades waiting for timelock expiration or execution\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 683,\n                                            columnNumber: 17\n                                        }, this),\n                                        pendingUpgrades.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 text-center py-4\",\n                                            children: \"No pending upgrades\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 687,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: pendingUpgrades.map((upgrade)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border rounded-lg p-3 space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-start\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: upgrade.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 695,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(upgrade.executed ? 'bg-green-100 text-green-800' : upgrade.cancelled ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'),\n                                                                    children: upgrade.executed ? \"Executed\" : upgrade.cancelled ? \"Cancelled\" : \"Pending\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 698,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 694,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-500 space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        \"Implementation: \",\n                                                                        upgrade.newImplementation.slice(0, 10),\n                                                                        \"...\",\n                                                                        upgrade.newImplementation.slice(-8)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 709,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        \"Execute Time: \",\n                                                                        new Date(upgrade.executeTime * 1000).toLocaleString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 710,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: getTimeUntilExecution(upgrade.executeTime)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 711,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 708,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, upgrade.upgradeId, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 693,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 691,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                    lineNumber: 681,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                            lineNumber: 641,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                        lineNumber: 640,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'history' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium mb-2\",\n                                    children: \"Upgrade History\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                    lineNumber: 725,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: \"Complete history of all upgrades performed on this token\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                    lineNumber: 726,\n                                    columnNumber: 15\n                                }, this),\n                                upgradeHistory.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 text-center py-4\",\n                                    children: \"No upgrade history available\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                    lineNumber: 730,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: upgradeHistory.map((record, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border rounded-lg p-4 space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium\",\n                                                                    children: record.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 739,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: [\n                                                                        new Date(record.timestamp * 1000).toLocaleString(),\n                                                                        \" by \",\n                                                                        record.executor.slice(0, 10),\n                                                                        \"...\",\n                                                                        record.executor.slice(-8)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 740,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 738,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(record.isEmergency ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'),\n                                                                    children: record.isEmergency ? \"Emergency\" : \"Scheduled\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 745,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                record.version && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800\",\n                                                                    children: [\n                                                                        \"v\",\n                                                                        record.version\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 751,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 744,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 737,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-500 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                \"From: \",\n                                                                record.oldImplementation.slice(0, 10),\n                                                                \"...\",\n                                                                record.oldImplementation.slice(-8)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 758,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                \"To: \",\n                                                                record.newImplementation.slice(0, 10),\n                                                                \"...\",\n                                                                record.newImplementation.slice(-8)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 759,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 757,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 736,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                    lineNumber: 734,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                            lineNumber: 724,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                        lineNumber: 723,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                lineNumber: 235,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n        lineNumber: 182,\n        columnNumber: 5\n    }, this);\n}\n_s(ModularTokensPage, \"QAYwK3XwuosE+3JgQGWcjchibQU=\", false, function() {\n    return [\n        _hooks_useModularToken__WEBPACK_IMPORTED_MODULE_2__.useModularToken\n    ];\n});\n_c = ModularTokensPage;\nvar _c;\n$RefreshReg$(_c, \"ModularTokensPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modular-tokens/page.tsx\n"));

/***/ })

});