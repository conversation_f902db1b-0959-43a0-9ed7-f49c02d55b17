globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/clients/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./src/components/Header.tsx":{"*":{"id":"(ssr)/./src/components/Header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Providers.tsx":{"*":{"id":"(ssr)/./src/components/Providers.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/create-token/page.tsx":{"*":{"id":"(ssr)/./src/app/create-token/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/tokens/[address]/page.tsx":{"*":{"id":"(ssr)/./src/app/tokens/[address]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/tokens/page.tsx":{"*":{"id":"(ssr)/./src/app/tokens/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/clients/[id]/page.tsx":{"*":{"id":"(ssr)/./src/app/clients/[id]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(ssr)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/clients/page.tsx":{"*":{"id":"(ssr)/./src/app/clients/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/transfer-controls/page.tsx":{"*":{"id":"(ssr)/./src/app/transfer-controls/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/debug-token/page.tsx":{"*":{"id":"(ssr)/./src/app/debug-token/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"D:\\github\\tokendev-newroo\\admin-panel\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\github\\tokendev-newroo\\admin-panel\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\github\\tokendev-newroo\\admin-panel\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\github\\tokendev-newroo\\admin-panel\\src\\components\\Header.tsx":{"id":"(app-pages-browser)/./src/components/Header.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\github\\tokendev-newroo\\admin-panel\\src\\components\\Providers.tsx":{"id":"(app-pages-browser)/./src/components/Providers.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\github\\tokendev-newroo\\admin-panel\\src\\app\\create-token\\page.tsx":{"id":"(app-pages-browser)/./src/app/create-token/page.tsx","name":"*","chunks":[],"async":false},"D:\\github\\tokendev-newroo\\admin-panel\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\github\\tokendev-newroo\\admin-panel\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\github\\tokendev-newroo\\admin-panel\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\github\\tokendev-newroo\\admin-panel\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\github\\tokendev-newroo\\admin-panel\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\github\\tokendev-newroo\\admin-panel\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\github\\tokendev-newroo\\admin-panel\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\github\\tokendev-newroo\\admin-panel\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\github\\tokendev-newroo\\admin-panel\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\github\\tokendev-newroo\\admin-panel\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\github\\tokendev-newroo\\admin-panel\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\github\\tokendev-newroo\\admin-panel\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\github\\tokendev-newroo\\admin-panel\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\github\\tokendev-newroo\\admin-panel\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\github\\tokendev-newroo\\admin-panel\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\github\\tokendev-newroo\\admin-panel\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\github\\tokendev-newroo\\admin-panel\\src\\app\\tokens\\[address]\\page.tsx":{"id":"(app-pages-browser)/./src/app/tokens/[address]/page.tsx","name":"*","chunks":[],"async":false},"D:\\github\\tokendev-newroo\\admin-panel\\src\\app\\tokens\\page.tsx":{"id":"(app-pages-browser)/./src/app/tokens/page.tsx","name":"*","chunks":[],"async":false},"D:\\github\\tokendev-newroo\\admin-panel\\src\\app\\clients\\[id]\\page.tsx":{"id":"(app-pages-browser)/./src/app/clients/[id]/page.tsx","name":"*","chunks":[],"async":false},"D:\\github\\tokendev-newroo\\admin-panel\\src\\app\\page.tsx":{"id":"(app-pages-browser)/./src/app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\github\\tokendev-newroo\\admin-panel\\src\\app\\clients\\page.tsx":{"id":"(app-pages-browser)/./src/app/clients/page.tsx","name":"*","chunks":["app/clients/page","static/chunks/app/clients/page.js"],"async":false},"D:\\github\\tokendev-newroo\\admin-panel\\src\\app\\transfer-controls\\page.tsx":{"id":"(app-pages-browser)/./src/app/transfer-controls/page.tsx","name":"*","chunks":[],"async":false},"D:\\github\\tokendev-newroo\\admin-panel\\src\\app\\debug-token\\page.tsx":{"id":"(app-pages-browser)/./src/app/debug-token/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"D:\\github\\tokendev-newroo\\admin-panel\\src\\":[],"D:\\github\\tokendev-newroo\\admin-panel\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"D:\\github\\tokendev-newroo\\admin-panel\\src\\app\\page":[],"D:\\github\\tokendev-newroo\\admin-panel\\src\\app\\clients\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Header.tsx":{"*":{"id":"(rsc)/./src/components/Header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Providers.tsx":{"*":{"id":"(rsc)/./src/components/Providers.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/create-token/page.tsx":{"*":{"id":"(rsc)/./src/app/create-token/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/tokens/[address]/page.tsx":{"*":{"id":"(rsc)/./src/app/tokens/[address]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/tokens/page.tsx":{"*":{"id":"(rsc)/./src/app/tokens/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/clients/[id]/page.tsx":{"*":{"id":"(rsc)/./src/app/clients/[id]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(rsc)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/clients/page.tsx":{"*":{"id":"(rsc)/./src/app/clients/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/transfer-controls/page.tsx":{"*":{"id":"(rsc)/./src/app/transfer-controls/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/debug-token/page.tsx":{"*":{"id":"(rsc)/./src/app/debug-token/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}