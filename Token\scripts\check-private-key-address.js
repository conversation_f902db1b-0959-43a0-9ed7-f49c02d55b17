const { ethers } = require("hardhat");

async function main() {
  const privateKey = "94692a030d151928530ddf7ae9f1ff07ad43d187b96135c8953fac16183619c1";
  
  console.log("🔍 Checking Private Key Address");
  console.log("=" .repeat(50));
  
  try {
    const wallet = new ethers.Wallet(privateKey);
    console.log("Private Key:", privateKey);
    console.log("Address:", wallet.address);
    
    // Check if this matches the deployer address
    const [deployer] = await ethers.getSigners();
    console.log("Deployer Address:", deployer.address);
    
    if (wallet.address.toLowerCase() === deployer.address.toLowerCase()) {
      console.log("✅ MATCH: Admin panel wallet matches deployer wallet");
    } else {
      console.log("❌ MISMATCH: Admin panel wallet does NOT match deployer wallet");
      console.log("This explains why you don't have permissions in the admin panel!");
    }
    
  } catch (error) {
    console.error("❌ Error:", error.message);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
