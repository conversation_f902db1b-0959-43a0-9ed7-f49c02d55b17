'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, CheckCircle, AlertCircle, Clock, Zap, ExternalLink, RefreshCw } from 'lucide-react';

// Import custom hook
import { useModularToken } from '@/hooks/useModularToken';

// Contract addresses from environment
const SECURITY_TOKEN_CORE_ADDRESS = process.env.NEXT_PUBLIC_AMOY_SECURITY_TOKEN_CORE_ADDRESS;
const UPGRADE_MANAGER_ADDRESS = process.env.NEXT_PUBLIC_AMOY_UPGRADE_MANAGER_ADDRESS;

export default function ModularTokensPage() {
  // Use the custom hook for modular token functionality
  const {
    provider,
    signer,
    tokenInfo,
    upgradeInfo,
    pendingUpgrades,
    upgradeHistory,
    loading,
    error,
    initializeProvider,
    mintTokens,
    togglePause,
    scheduleUpgrade,
    toggleEmergencyMode,
    refreshData,
    setError
  } = useModularToken();

  // Local state for UI
  const [success, setSuccess] = useState<string | null>(null);
  const [mintAmount, setMintAmount] = useState('');
  const [mintAddress, setMintAddress] = useState('');
  const [upgradeDescription, setUpgradeDescription] = useState('');
  const [newImplementationAddress, setNewImplementationAddress] = useState('');
  const [actionLoading, setActionLoading] = useState(false);

  // Clear success message after 5 seconds
  React.useEffect(() => {
    if (success) {
      const timer = setTimeout(() => setSuccess(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [success]);

  // Clear error message after 10 seconds
  React.useEffect(() => {
    if (error) {
      const timer = setTimeout(() => setError(null), 10000);
      return () => clearTimeout(timer);
    }
  }, [error, setError]);

  const handleMint = async () => {
    if (!mintAddress || !mintAmount) return;

    setActionLoading(true);
    setError(null);
    setSuccess(null);

    try {
      await mintTokens(mintAddress, mintAmount);
      setSuccess(`Successfully minted ${mintAmount} tokens to ${mintAddress}`);
      setMintAmount('');
      setMintAddress('');
      await refreshData(); // Refresh token info
    } catch (error: any) {
      console.error('Error minting tokens:', error);
      setError(`Failed to mint tokens: ${error.message}`);
    } finally {
      setActionLoading(false);
    }
  };

  const handlePauseToggle = async () => {
    setActionLoading(true);
    setError(null);
    setSuccess(null);

    try {
      await togglePause();
      setSuccess(`Token ${tokenInfo?.paused ? 'unpaused' : 'paused'} successfully`);
      await refreshData(); // Refresh token info
    } catch (error: any) {
      console.error('Error toggling pause:', error);
      setError(`Failed to ${tokenInfo?.paused ? 'unpause' : 'pause'} token: ${error.message}`);
    } finally {
      setActionLoading(false);
    }
  };

  const handleScheduleUpgrade = async () => {
    if (!newImplementationAddress || !upgradeDescription) return;

    setActionLoading(true);
    setError(null);
    setSuccess(null);

    try {
      await scheduleUpgrade(newImplementationAddress, upgradeDescription);
      setSuccess('Upgrade scheduled successfully! It will be executable after the 48-hour timelock.');
      setNewImplementationAddress('');
      setUpgradeDescription('');
      await refreshData(); // Refresh upgrade info
    } catch (error: any) {
      console.error('Error scheduling upgrade:', error);
      setError(`Failed to schedule upgrade: ${error.message}`);
    } finally {
      setActionLoading(false);
    }
  };

  const handleEmergencyModeToggle = async () => {
    setActionLoading(true);
    setError(null);
    setSuccess(null);

    try {
      await toggleEmergencyMode();
      setSuccess(`Emergency mode ${upgradeInfo?.emergencyModeActive ? 'deactivated' : 'activated'} successfully`);
      await refreshData(); // Refresh upgrade info
    } catch (error: any) {
      console.error('Error toggling emergency mode:', error);
      setError(`Failed to ${upgradeInfo?.emergencyModeActive ? 'deactivate' : 'activate'} emergency mode: ${error.message}`);
    } finally {
      setActionLoading(false);
    }
  };

  // Utility functions
  const formatAddress = (address: string) => {
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  const getTimeUntilExecution = (executeTime: number) => {
    const now = Math.floor(Date.now() / 1000);
    const timeLeft = executeTime - now;

    if (timeLeft <= 0) return 'Ready to execute';

    const hours = Math.floor(timeLeft / 3600);
    const minutes = Math.floor((timeLeft % 3600) / 60);

    return `${hours}h ${minutes}m remaining`;
  };

  const formatAddress = (address: string) => {
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  const getTimeUntilExecution = (executeTime: number) => {
    const now = Math.floor(Date.now() / 1000);
    const timeLeft = executeTime - now;

    if (timeLeft <= 0) return 'Ready to execute';

    const hours = Math.floor(timeLeft / 3600);
    const minutes = Math.floor((timeLeft % 3600) / 60);

    return `${hours}h ${minutes}m remaining`;
  };

  if (!provider || !signer) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardHeader>
            <CardTitle>Connect Wallet</CardTitle>
            <CardDescription>
              Please connect your MetaMask wallet to manage modular tokens.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={initializeProvider}>Connect Wallet</Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Modular Token Management</h1>
          <p className="text-muted-foreground">
            Manage your modular ERC-3643 security tokens and upgrades
          </p>
        </div>
        <div className="flex gap-2">
          <Badge variant={upgradeInfo?.emergencyModeActive ? "destructive" : "secondary"}>
            {upgradeInfo?.emergencyModeActive ? "Emergency Mode Active" : "Normal Mode"}
          </Badge>
          {tokenInfo && (
            <Badge variant={tokenInfo.paused ? "destructive" : "default"}>
              {tokenInfo.paused ? "Paused" : "Active"}
            </Badge>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={refreshData}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="operations">Token Operations</TabsTrigger>
          <TabsTrigger value="upgrades">Upgrade Management</TabsTrigger>
          <TabsTrigger value="history">Upgrade History</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Token Information</CardTitle>
                <CardDescription>Current token details and status</CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                {tokenInfo ? (
                  <>
                    <div className="flex justify-between">
                      <span className="font-medium">Name:</span>
                      <span>{tokenInfo.name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="font-medium">Symbol:</span>
                      <span>{tokenInfo.symbol}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="font-medium">Version:</span>
                      <span>{tokenInfo.version}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="font-medium">Total Supply:</span>
                      <span>{tokenInfo.totalSupply}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="font-medium">Max Supply:</span>
                      <span>{tokenInfo.maxSupply}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="font-medium">Decimals:</span>
                      <span>{tokenInfo.decimals}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="font-medium">Status:</span>
                      <Badge variant={tokenInfo.paused ? "destructive" : "default"}>
                        {tokenInfo.paused ? "Paused" : "Active"}
                      </Badge>
                    </div>
                  </>
                ) : (
                  <div className="flex items-center space-x-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span>Loading token information...</span>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Contract Addresses</CardTitle>
                <CardDescription>Deployed contract addresses on Amoy testnet</CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                <div>
                  <span className="font-medium">SecurityTokenCore:</span>
                  <div className="text-sm text-muted-foreground font-mono">
                    {SECURITY_TOKEN_CORE_ADDRESS}
                  </div>
                </div>
                <div>
                  <span className="font-medium">UpgradeManager:</span>
                  <div className="text-sm text-muted-foreground font-mono">
                    {UPGRADE_MANAGER_ADDRESS}
                  </div>
                </div>
                <div className="pt-2">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => window.open(`https://amoy.polygonscan.com/address/${SECURITY_TOKEN_CORE_ADDRESS}`, '_blank')}
                  >
                    View on PolygonScan
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {tokenInfo?.metadata && (
            <Card>
              <CardHeader>
                <CardTitle>Token Metadata</CardTitle>
                <CardDescription>Additional token information and configuration</CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between">
                  <span className="font-medium">Price:</span>
                  <span>{tokenInfo.metadata.tokenPrice}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">Bonus Tiers:</span>
                  <span>{tokenInfo.metadata.bonusTiers}</span>
                </div>
                <div>
                  <span className="font-medium">Details:</span>
                  <p className="text-sm text-muted-foreground mt-1">
                    {tokenInfo.metadata.tokenDetails}
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="operations" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Mint Tokens</CardTitle>
                <CardDescription>Mint new tokens to a specified address</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="mintAddress">Recipient Address</Label>
                  <Input
                    id="mintAddress"
                    placeholder="0x..."
                    value={mintAddress}
                    onChange={(e) => setMintAddress(e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="mintAmount">Amount</Label>
                  <Input
                    id="mintAmount"
                    type="number"
                    placeholder="1000"
                    value={mintAmount}
                    onChange={(e) => setMintAmount(e.target.value)}
                  />
                </div>
                <Button
                  onClick={handleMint}
                  disabled={actionLoading || !mintAddress || !mintAmount}
                  className="w-full"
                >
                  {actionLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
                  Mint Tokens
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Token Controls</CardTitle>
                <CardDescription>Pause/unpause token transfers and emergency controls</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button
                  onClick={handlePauseToggle}
                  disabled={actionLoading}
                  variant={tokenInfo?.paused ? "default" : "destructive"}
                  className="w-full"
                >
                  {actionLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
                  {tokenInfo?.paused ? 'Unpause Token' : 'Pause Token'}
                </Button>

                <Button
                  onClick={handleEmergencyModeToggle}
                  disabled={actionLoading}
                  variant={upgradeInfo?.emergencyModeActive ? "default" : "destructive"}
                  className="w-full"
                >
                  {actionLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
                  {upgradeInfo?.emergencyModeActive ? (
                    <>
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Deactivate Emergency Mode
                    </>
                  ) : (
                    <>
                      <Zap className="h-4 w-4 mr-2" />
                      Activate Emergency Mode
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="upgrades" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Schedule Upgrade</CardTitle>
                <CardDescription>
                  Schedule a new implementation upgrade with 48-hour timelock
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="newImplementation">New Implementation Address</Label>
                  <Input
                    id="newImplementation"
                    placeholder="0x..."
                    value={newImplementationAddress}
                    onChange={(e) => setNewImplementationAddress(e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="upgradeDescription">Upgrade Description</Label>
                  <Textarea
                    id="upgradeDescription"
                    placeholder="Describe the changes in this upgrade..."
                    value={upgradeDescription}
                    onChange={(e) => setUpgradeDescription(e.target.value)}
                  />
                </div>
                <Button
                  onClick={handleScheduleUpgrade}
                  disabled={actionLoading || !newImplementationAddress || !upgradeDescription}
                  className="w-full"
                >
                  {actionLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
                  <Clock className="h-4 w-4 mr-2" />
                  Schedule Upgrade (48h Timelock)
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Pending Upgrades</CardTitle>
                <CardDescription>
                  Upgrades waiting for timelock expiration or execution
                </CardDescription>
              </CardHeader>
              <CardContent>
                {pendingUpgrades.length === 0 ? (
                  <p className="text-muted-foreground text-center py-4">
                    No pending upgrades
                  </p>
                ) : (
                  <div className="space-y-3">
                    {pendingUpgrades.map((upgrade) => (
                      <div key={upgrade.upgradeId} className="border rounded-lg p-3 space-y-2">
                        <div className="flex justify-between items-start">
                          <div className="text-sm font-medium">
                            {upgrade.description}
                          </div>
                          <Badge variant={
                            upgrade.executed ? "default" : 
                            upgrade.cancelled ? "destructive" : 
                            "secondary"
                          }>
                            {upgrade.executed ? "Executed" : 
                             upgrade.cancelled ? "Cancelled" : 
                             "Pending"}
                          </Badge>
                        </div>
                        <div className="text-xs text-muted-foreground space-y-1">
                          <div>Implementation: {formatAddress(upgrade.newImplementation)}</div>
                          <div>Execute Time: {formatTimestamp(upgrade.executeTime)}</div>
                          <div>{getTimeUntilExecution(upgrade.executeTime)}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Upgrade History</CardTitle>
              <CardDescription>
                Complete history of all upgrades performed on this token
              </CardDescription>
            </CardHeader>
            <CardContent>
              {upgradeHistory.length === 0 ? (
                <p className="text-muted-foreground text-center py-4">
                  No upgrade history available
                </p>
              ) : (
                <div className="space-y-4">
                  {upgradeHistory.map((record, index) => (
                    <div key={index} className="border rounded-lg p-4 space-y-2">
                      <div className="flex justify-between items-start">
                        <div className="space-y-1">
                          <div className="font-medium">{record.description}</div>
                          <div className="text-sm text-muted-foreground">
                            {formatTimestamp(record.timestamp)} by {formatAddress(record.executor)}
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <Badge variant={record.isEmergency ? "destructive" : "default"}>
                            {record.isEmergency ? "Emergency" : "Scheduled"}
                          </Badge>
                          {record.version && (
                            <Badge variant="outline">v{record.version}</Badge>
                          )}
                        </div>
                      </div>
                      <div className="text-xs text-muted-foreground space-y-1">
                        <div>From: {formatAddress(record.oldImplementation)}</div>
                        <div>To: {formatAddress(record.newImplementation)}</div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
