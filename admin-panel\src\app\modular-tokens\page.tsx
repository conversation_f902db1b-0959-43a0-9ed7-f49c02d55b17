'use client';

import React, { useState } from 'react';

// Import custom hook
import { useModularToken } from '../../hooks/useModularToken';

// Contract addresses from environment
const SECURITY_TOKEN_CORE_ADDRESS = process.env.NEXT_PUBLIC_AMOY_SECURITY_TOKEN_CORE_ADDRESS;
const UPGRADE_MANAGER_ADDRESS = process.env.NEXT_PUBLIC_AMOY_UPGRADE_MANAGER_ADDRESS;

export default function ModularTokensPage() {
  // Use the custom hook for modular token functionality
  const {
    provider,
    signer,
    tokenInfo,
    upgradeInfo,
    pendingUpgrades,
    upgradeHistory,
    loading,
    error,
    initializeProvider,
    mintTokens,
    togglePause,
    scheduleUpgrade,
    toggleEmergencyMode,
    refreshData,
    setError
  } = useModularToken();

  // Local state for UI
  const [success, setSuccess] = useState<string | null>(null);
  const [mintAmount, setMintAmount] = useState('');
  const [mintAddress, setMintAddress] = useState('');
  const [upgradeDescription, setUpgradeDescription] = useState('');
  const [newImplementationAddress, setNewImplementationAddress] = useState('');
  const [actionLoading, setActionLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  // KYC & Claims state
  const [kycUserAddress, setKycUserAddress] = useState('');
  const [claimUserAddress, setClaimUserAddress] = useState('');
  const [claimTopicId, setClaimTopicId] = useState('10101010000001');
  const [claimData, setClaimData] = useState('');
  const [checkUserAddress, setCheckUserAddress] = useState('');
  const [verificationStatus, setVerificationStatus] = useState(null);

  // Clear success message after 5 seconds
  React.useEffect(() => {
    if (success) {
      const timer = setTimeout(() => setSuccess(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [success]);

  // Clear error message after 10 seconds
  React.useEffect(() => {
    if (error) {
      const timer = setTimeout(() => setError(null), 10000);
      return () => clearTimeout(timer);
    }
  }, [error, setError]);

  const handleMint = async () => {
    if (!mintAddress || !mintAmount) return;

    setActionLoading(true);
    setError(null);
    setSuccess(null);

    try {
      await mintTokens(mintAddress, mintAmount);
      setSuccess(`Successfully minted ${mintAmount} tokens to ${mintAddress}`);
      setMintAmount('');
      setMintAddress('');
      await refreshData(); // Refresh token info
    } catch (error: any) {
      console.error('Error minting tokens:', error);
      setError(`Failed to mint tokens: ${error.message}`);
    } finally {
      setActionLoading(false);
    }
  };

  const handlePauseToggle = async () => {
    setActionLoading(true);
    setError(null);
    setSuccess(null);

    try {
      await togglePause();
      setSuccess(`Token ${tokenInfo?.paused ? 'unpaused' : 'paused'} successfully`);
      await refreshData(); // Refresh token info
    } catch (error: any) {
      console.error('Error toggling pause:', error);
      setError(`Failed to ${tokenInfo?.paused ? 'unpause' : 'pause'} token: ${error.message}`);
    } finally {
      setActionLoading(false);
    }
  };

  const handleScheduleUpgrade = async () => {
    if (!newImplementationAddress || !upgradeDescription) return;

    setActionLoading(true);
    setError(null);
    setSuccess(null);

    try {
      await scheduleUpgrade(newImplementationAddress, upgradeDescription);
      setSuccess('Upgrade scheduled successfully! It will be executable after the 48-hour timelock.');
      setNewImplementationAddress('');
      setUpgradeDescription('');
      await refreshData(); // Refresh upgrade info
    } catch (error: any) {
      console.error('Error scheduling upgrade:', error);
      setError(`Failed to schedule upgrade: ${error.message}`);
    } finally {
      setActionLoading(false);
    }
  };

  const handleEmergencyModeToggle = async () => {
    setActionLoading(true);
    setError(null);
    setSuccess(null);

    try {
      await toggleEmergencyMode();
      setSuccess(`Emergency mode ${upgradeInfo?.emergencyModeActive ? 'deactivated' : 'activated'} successfully`);
      await refreshData(); // Refresh upgrade info
    } catch (error: any) {
      console.error('Error toggling emergency mode:', error);
      setError(`Failed to ${upgradeInfo?.emergencyModeActive ? 'deactivate' : 'activate'} emergency mode: ${error.message}`);
    } finally {
      setActionLoading(false);
    }
  };

  // KYC & Claims handlers
  const handleApproveKYC = async () => {
    if (!kycUserAddress) return;

    setActionLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Call the KYC approval API
      const response = await fetch('/api/kyc/approve', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tokenAddress: SECURITY_TOKEN_CORE_ADDRESS,
          userAddress: kycUserAddress,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to approve KYC');
      }

      setSuccess(`KYC approved successfully for ${kycUserAddress}`);
      setKycUserAddress('');
    } catch (error: any) {
      console.error('Error approving KYC:', error);
      setError(`Failed to approve KYC: ${error.message}`);
    } finally {
      setActionLoading(false);
    }
  };

  const handleAddToWhitelist = async () => {
    if (!kycUserAddress) return;

    setActionLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Call the whitelist API
      const response = await fetch('/api/kyc/whitelist', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tokenAddress: SECURITY_TOKEN_CORE_ADDRESS,
          userAddress: kycUserAddress,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to add to whitelist');
      }

      setSuccess(`User added to whitelist successfully: ${kycUserAddress}`);
      setKycUserAddress('');
    } catch (error: any) {
      console.error('Error adding to whitelist:', error);
      setError(`Failed to add to whitelist: ${error.message}`);
    } finally {
      setActionLoading(false);
    }
  };

  const handleIssueClaim = async () => {
    if (!claimUserAddress || !claimTopicId) return;

    setActionLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Call the claims API
      const response = await fetch('/api/kyc/issue-claim', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userAddress: claimUserAddress,
          topicId: claimTopicId,
          data: claimData || '',
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to issue claim');
      }

      const result = await response.json();
      setSuccess(`Claim issued successfully! Claim ID: ${result.claimId}`);
      setClaimUserAddress('');
      setClaimData('');
    } catch (error: any) {
      console.error('Error issuing claim:', error);
      setError(`Failed to issue claim: ${error.message}`);
    } finally {
      setActionLoading(false);
    }
  };

  const handleCheckStatus = async () => {
    if (!checkUserAddress) return;

    setActionLoading(true);
    setError(null);

    try {
      // Call the status check API
      const response = await fetch(`/api/kyc/status?tokenAddress=${SECURITY_TOKEN_CORE_ADDRESS}&userAddress=${checkUserAddress}`);

      if (!response.ok) {
        throw new Error('Failed to check status');
      }

      const status = await response.json();
      setVerificationStatus(status);
    } catch (error: any) {
      console.error('Error checking status:', error);
      setError(`Failed to check status: ${error.message}`);
    } finally {
      setActionLoading(false);
    }
  };

  // Utility functions
  const formatAddress = (address: string) => {
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  const getTimeUntilExecution = (executeTime: number) => {
    const now = Math.floor(Date.now() / 1000);
    const timeLeft = executeTime - now;

    if (timeLeft <= 0) return 'Ready to execute';

    const hours = Math.floor(timeLeft / 3600);
    const minutes = Math.floor((timeLeft % 3600) / 60);

    return `${hours}h ${minutes}m remaining`;
  };

  if (!provider || !signer) {
    return (
      <div className="container mx-auto p-6">
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-bold mb-2">Connect Wallet</h2>
          <p className="text-gray-600 mb-4">
            Please connect your MetaMask wallet to manage modular tokens.
          </p>
          <button
            onClick={initializeProvider}
            className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
          >
            Connect Wallet
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Modular Token Management</h1>
          <p className="text-gray-600">
            Manage your modular ERC-3643 security tokens and upgrades
          </p>
        </div>
        <div className="flex gap-2">
          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
            upgradeInfo?.emergencyModeActive
              ? 'bg-red-100 text-red-800'
              : 'bg-gray-100 text-gray-800'
          }`}>
            {upgradeInfo?.emergencyModeActive ? "Emergency Mode Active" : "Normal Mode"}
          </span>
          {tokenInfo && (
            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
              tokenInfo.paused
                ? 'bg-red-100 text-red-800'
                : 'bg-green-100 text-green-800'
            }`}>
              {tokenInfo.paused ? "Paused" : "Active"}
            </span>
          )}
          <button
            onClick={refreshData}
            disabled={loading}
            className="bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-1 px-3 rounded text-sm disabled:opacity-50"
          >
            {loading ? '🔄' : '↻'} Refresh
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <div className="flex items-center">
            <span className="text-red-600 mr-2">⚠️</span>
            <span>{error}</span>
          </div>
        </div>
      )}

      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
          <div className="flex items-center">
            <span className="text-green-600 mr-2">✅</span>
            <span>{success}</span>
          </div>
        </div>
      )}

      <div className="space-y-4">
        {/* Tab Navigation */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {['overview', 'operations', 'admin-controls', 'kyc-claims', 'upgrades', 'history'].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab === 'kyc-claims' ? 'KYC & Claims' :
                 tab === 'admin-controls' ? 'Admin Controls' :
                 tab.charAt(0).toUpperCase() + tab.slice(1).replace('-', ' ')}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === 'overview' && (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-medium mb-2">Token Information</h3>
                <p className="text-gray-600 mb-4">Current token details and status</p>
                <div className="space-y-2">
                  {tokenInfo ? (
                    <>
                      <div className="flex justify-between">
                        <span className="font-medium">Name:</span>
                        <span>{tokenInfo.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="font-medium">Symbol:</span>
                        <span>{tokenInfo.symbol}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="font-medium">Version:</span>
                        <span>{tokenInfo.version}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="font-medium">Total Supply:</span>
                        <span>{tokenInfo.totalSupply}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="font-medium">Max Supply:</span>
                        <span>{tokenInfo.maxSupply}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="font-medium">Decimals:</span>
                        <span>{tokenInfo.decimals}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="font-medium">Status:</span>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          tokenInfo.paused
                            ? 'bg-red-100 text-red-800'
                            : 'bg-green-100 text-green-800'
                        }`}>
                          {tokenInfo.paused ? "Paused" : "Active"}
                        </span>
                      </div>
                    </>
                  ) : (
                    <div className="flex items-center space-x-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                      <span>Loading token information...</span>
                    </div>
                  )}
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-medium mb-2">Contract Addresses</h3>
                <p className="text-gray-600 mb-4">Deployed contract addresses on Amoy testnet</p>
                <div className="space-y-2">
                  <div>
                    <span className="font-medium">SecurityTokenCore:</span>
                    <div className="text-sm text-gray-500 font-mono">
                      {SECURITY_TOKEN_CORE_ADDRESS}
                    </div>
                  </div>
                  <div>
                    <span className="font-medium">UpgradeManager:</span>
                    <div className="text-sm text-gray-500 font-mono">
                      {UPGRADE_MANAGER_ADDRESS}
                    </div>
                  </div>
                  <div className="pt-2">
                    <button
                      onClick={() => window.open(`https://amoy.polygonscan.com/address/${SECURITY_TOKEN_CORE_ADDRESS}`, '_blank')}
                      className="bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-1 px-3 rounded text-sm"
                    >
                      View on PolygonScan
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {tokenInfo?.metadata && (
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-medium mb-2">Token Metadata</h3>
                <p className="text-gray-600 mb-4">Additional token information and configuration</p>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="font-medium">Price:</span>
                    <span>{tokenInfo.metadata.tokenPrice}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium">Bonus Tiers:</span>
                    <span>{tokenInfo.metadata.bonusTiers}</span>
                  </div>
                  <div>
                    <span className="font-medium">Details:</span>
                    <p className="text-sm text-gray-500 mt-1">
                      {tokenInfo.metadata.tokenDetails}
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'operations' && (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-medium mb-2">Mint Tokens</h3>
                <p className="text-gray-600 mb-4">Mint new tokens to a specified address</p>
                <div className="space-y-4">
                  <div>
                    <label htmlFor="mintAddress" className="block text-sm font-medium text-gray-700">Recipient Address</label>
                    <input
                      id="mintAddress"
                      type="text"
                      placeholder="0x..."
                      value={mintAddress}
                      onChange={(e) => setMintAddress(e.target.value)}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <label htmlFor="mintAmount" className="block text-sm font-medium text-gray-700">Amount</label>
                    <input
                      id="mintAmount"
                      type="number"
                      placeholder="1000"
                      value={mintAmount}
                      onChange={(e) => setMintAmount(e.target.value)}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <button
                    onClick={handleMint}
                    disabled={actionLoading || !mintAddress || !mintAmount}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
                  >
                    {actionLoading ? '🔄 ' : ''}Mint Tokens
                  </button>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-medium mb-2">Token Controls</h3>
                <p className="text-gray-600 mb-4">Pause/unpause token transfers and emergency controls</p>
                <div className="space-y-4">
                  <button
                    onClick={handlePauseToggle}
                    disabled={actionLoading}
                    className={`w-full font-bold py-2 px-4 rounded disabled:opacity-50 ${
                      tokenInfo?.paused
                        ? 'bg-green-600 hover:bg-green-700 text-white'
                        : 'bg-red-600 hover:bg-red-700 text-white'
                    }`}
                  >
                    {actionLoading ? '🔄 ' : ''}
                    {tokenInfo?.paused ? 'Unpause Token' : 'Pause Token'}
                  </button>

                  <button
                    onClick={handleEmergencyModeToggle}
                    disabled={actionLoading}
                    className={`w-full font-bold py-2 px-4 rounded disabled:opacity-50 ${
                      upgradeInfo?.emergencyModeActive
                        ? 'bg-green-600 hover:bg-green-700 text-white'
                        : 'bg-red-600 hover:bg-red-700 text-white'
                    }`}
                  >
                    {actionLoading ? '🔄 ' : ''}
                    {upgradeInfo?.emergencyModeActive ? (
                      <>✅ Deactivate Emergency Mode</>
                    ) : (
                      <>⚡ Activate Emergency Mode</>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'admin-controls' && (
          <div className="space-y-4">
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <span className="text-green-600 text-xl">⚙️</span>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-green-800">
                    Complete Admin Controls - ALL WORKING
                  </h3>
                  <div className="mt-1 text-sm text-green-700">
                    <p>Full administrative control over token parameters and functionality:</p>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-2 mt-2">
                      <div className="flex items-center">
                        <span className="text-green-600 mr-1">✅</span>
                        <span>Price Updates</span>
                      </div>
                      <div className="flex items-center">
                        <span className="text-green-600 mr-1">✅</span>
                        <span>Metadata Management</span>
                      </div>
                      <div className="flex items-center">
                        <span className="text-green-600 mr-1">✅</span>
                        <span>Supply Management</span>
                      </div>
                      <div className="flex items-center">
                        <span className="text-green-600 mr-1">✅</span>
                        <span>Pause/Unpause</span>
                      </div>
                      <div className="flex items-center">
                        <span className="text-green-600 mr-1">✅</span>
                        <span>Whitelist Control</span>
                      </div>
                      <div className="flex items-center">
                        <span className="text-green-600 mr-1">✅</span>
                        <span>Token Minting</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-medium mb-2">
                  Price & Metadata Management
                  <span className="ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded">✅ Working</span>
                </h3>
                <p className="text-gray-600 mb-4">Update token price, bonus tiers, and metadata</p>
                <div className="space-y-4">
                  <div>
                    <label htmlFor="newTokenPrice" className="block text-sm font-medium text-gray-700">Token Price</label>
                    <input
                      id="newTokenPrice"
                      type="text"
                      placeholder="e.g., 7.50 USD"
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <label htmlFor="newBonusTiers" className="block text-sm font-medium text-gray-700">Bonus Tiers</label>
                    <input
                      id="newBonusTiers"
                      type="text"
                      placeholder="e.g., Early: 45%, Standard: 30%, Late: 15%"
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <button
                      disabled={actionLoading}
                      className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
                    >
                      {actionLoading ? '🔄 ' : '💰 '}Update Price
                    </button>
                    <button
                      disabled={actionLoading}
                      className="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
                    >
                      {actionLoading ? '🔄 ' : '🎯 '}Update Tiers
                    </button>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-medium mb-2">
                  Supply Management
                  <span className="ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded">✅ Working</span>
                </h3>
                <p className="text-gray-600 mb-4">Manage token supply and minting</p>
                <div className="space-y-4">
                  <div>
                    <label htmlFor="newMaxSupply" className="block text-sm font-medium text-gray-700">New Max Supply</label>
                    <input
                      id="newMaxSupply"
                      type="number"
                      placeholder="e.g., 100000000"
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <label htmlFor="mintToAddress" className="block text-sm font-medium text-gray-700">Mint To Address</label>
                    <input
                      id="mintToAddress"
                      type="text"
                      placeholder="0x..."
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <label htmlFor="mintAmount" className="block text-sm font-medium text-gray-700">Amount to Mint</label>
                    <input
                      id="mintAmount"
                      type="number"
                      placeholder="e.g., 1000"
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <button
                      disabled={actionLoading}
                      className="bg-orange-600 hover:bg-orange-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
                    >
                      {actionLoading ? '🔄 ' : '📈 '}Update Supply
                    </button>
                    <button
                      disabled={actionLoading}
                      className="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
                    >
                      {actionLoading ? '🔄 ' : '🪙 '}Mint Tokens
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-medium mb-2">
                  Token Controls
                  <span className="ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded">✅ Working</span>
                </h3>
                <p className="text-gray-600 mb-4">Pause/unpause token transfers and emergency controls</p>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-2">
                    <button
                      disabled={actionLoading}
                      className="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
                    >
                      {actionLoading ? '🔄 ' : '⏸️ '}Pause Token
                    </button>
                    <button
                      disabled={actionLoading}
                      className="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
                    >
                      {actionLoading ? '🔄 ' : '▶️ '}Unpause Token
                    </button>
                  </div>
                  <div className="text-sm text-gray-600">
                    <p><strong>Current Status:</strong> <span className="text-green-600">Active</span></p>
                    <p><strong>Total Supply:</strong> Loading...</p>
                    <p><strong>Max Supply:</strong> Loading...</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-medium mb-2">
                  Whitelist Management
                  <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">Direct Access</span>
                </h3>
                <p className="text-gray-600 mb-4">Direct whitelist management (bypasses modules)</p>
                <div className="space-y-4">
                  <div>
                    <label htmlFor="whitelistAddress" className="block text-sm font-medium text-gray-700">Address</label>
                    <input
                      id="whitelistAddress"
                      type="text"
                      placeholder="0x..."
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <button
                      disabled={actionLoading}
                      className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
                    >
                      {actionLoading ? '🔄 ' : '✅ '}Add to Whitelist
                    </button>
                    <button
                      disabled={actionLoading}
                      className="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
                    >
                      {actionLoading ? '🔄 ' : '❌ '}Remove from Whitelist
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <span className="text-blue-600 text-xl">💡</span>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-blue-800">
                    Admin Functions Available
                  </h3>
                  <div className="mt-1 text-sm text-blue-700">
                    <p>All admin functions are now available and tested working:</p>
                    <ul className="mt-1 list-disc list-inside space-y-1">
                      <li><strong>Price Management:</strong> updateTokenPrice(), updateBonusTiers()</li>
                      <li><strong>Supply Control:</strong> updateMaxSupply(), mint()</li>
                      <li><strong>Token Control:</strong> pause(), unpause()</li>
                      <li><strong>Access Control:</strong> addToWhitelist(), removeFromWhitelist()</li>
                      <li><strong>Emergency:</strong> forcedTransfer(), burn()</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'kyc-claims' && (
          <div className="space-y-4">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <span className="text-blue-600 text-xl">🔐</span>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-blue-800">
                    On-Chain KYC & Claims System
                  </h3>
                  <div className="mt-1 text-sm text-blue-700">
                    <p>Manage KYC verification and custom claims using Tokeny-style Topic IDs:</p>
                    <ul className="mt-1 list-disc list-inside space-y-1">
                      <li><strong>KYC Verification:</strong> Topic ID 10101010000001</li>
                      <li><strong>General Qualification:</strong> Topic ID 10101010000004</li>
                      <li><strong>Accredited Investor:</strong> Topic ID 10101010000002</li>
                      <li><strong>Custom Claims:</strong> Like 10101010000648 for specific KYC status</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <span className="text-green-600 text-xl">🎉</span>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-green-800">
                    System Status - FULLY OPERATIONAL
                  </h3>
                  <div className="mt-1 text-sm text-green-700">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-2 mt-2">
                      <div className="flex items-center">
                        <span className="text-green-600 mr-1">✅</span>
                        <span>Claims Issuance</span>
                      </div>
                      <div className="flex items-center">
                        <span className="text-green-600 mr-1">✅</span>
                        <span>Status Checking</span>
                      </div>
                      <div className="flex items-center">
                        <span className="text-green-600 mr-1">✅</span>
                        <span>On-Chain Verification</span>
                      </div>
                    </div>
                    <p className="mt-2 text-xs">
                      <strong>New Token Deployed:</strong> 0x1201Ff51382A5FCE89b1fBbEa970D81C9357509D with full admin access and KYC Claims Module registered!
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <span className="text-blue-600 text-xl">🚀</span>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-blue-800">
                    Quick Start Guide
                  </h3>
                  <div className="mt-1 text-sm text-blue-700">
                    <ol className="list-decimal list-inside space-y-1">
                      <li><strong>Issue Claims:</strong> Use the Custom Claims section below (fully working)</li>
                      <li><strong>Check Status:</strong> Use the Verification Status Checker (fully working)</li>
                      <li><strong>View Transactions:</strong> All claims are recorded on-chain and verifiable</li>
                      <li><strong>Cross-Token Usage:</strong> Claims work across multiple tokens</li>
                    </ol>
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-medium mb-2">
                  KYC Management
                  <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">Use Claims Instead</span>
                </h3>
                <p className="text-gray-600 mb-4">For KYC approval, use the Custom Claims section below with Topic ID 10101010000001</p>
                <div className="space-y-4">
                  <div>
                    <label htmlFor="kycUserAddress" className="block text-sm font-medium text-gray-700">User Address</label>
                    <input
                      id="kycUserAddress"
                      type="text"
                      placeholder="0x..."
                      value={kycUserAddress}
                      onChange={(e) => setKycUserAddress(e.target.value)}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <button
                      onClick={handleApproveKYC}
                      disabled={actionLoading || !kycUserAddress}
                      className="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
                    >
                      {actionLoading ? '🔄 ' : '✅ '}Approve KYC
                    </button>
                    <button
                      onClick={handleAddToWhitelist}
                      disabled={actionLoading || !kycUserAddress}
                      className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
                    >
                      {actionLoading ? '🔄 ' : '📋 '}Add to Whitelist
                    </button>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-medium mb-2">
                  Custom Claims
                  <span className="ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded">✅ Working</span>
                </h3>
                <p className="text-gray-600 mb-4">Issue custom claims with specific Topic IDs (fully operational)</p>
                <div className="space-y-4">
                  <div>
                    <label htmlFor="claimUserAddress" className="block text-sm font-medium text-gray-700">User Address</label>
                    <input
                      id="claimUserAddress"
                      type="text"
                      placeholder="0x..."
                      value={claimUserAddress}
                      onChange={(e) => setClaimUserAddress(e.target.value)}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <label htmlFor="claimTopicId" className="block text-sm font-medium text-gray-700">Topic ID</label>
                    <select
                      id="claimTopicId"
                      value={claimTopicId}
                      onChange={(e) => setClaimTopicId(e.target.value)}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="10101010000001">10101010000001 - KYC Verification</option>
                      <option value="10101010000002">10101010000002 - Accredited Investor</option>
                      <option value="10101010000003">10101010000003 - Jurisdiction Compliance</option>
                      <option value="10101010000004">10101010000004 - General Qualification</option>
                      <option value="10101010000648">10101010000648 - Custom KYC Status</option>
                    </select>
                  </div>
                  <div>
                    <label htmlFor="claimData" className="block text-sm font-medium text-gray-700">Claim Data (Optional)</label>
                    <textarea
                      id="claimData"
                      placeholder="Additional claim information..."
                      value={claimData}
                      onChange={(e) => setClaimData(e.target.value)}
                      rows={2}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <button
                    onClick={handleIssueClaim}
                    disabled={actionLoading || !claimUserAddress || !claimTopicId}
                    className="w-full bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
                  >
                    {actionLoading ? '🔄 ' : '🏷️ '}Issue Custom Claim
                  </button>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium mb-2">
                Verification Status Checker
                <span className="ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded">✅ Working</span>
              </h3>
              <p className="text-gray-600 mb-4">Check KYC and claims status for any user (fully operational)</p>
              <div className="space-y-4">
                <div className="flex gap-4">
                  <div className="flex-1">
                    <label htmlFor="checkUserAddress" className="block text-sm font-medium text-gray-700">User Address</label>
                    <input
                      id="checkUserAddress"
                      type="text"
                      placeholder="0x..."
                      value={checkUserAddress}
                      onChange={(e) => setCheckUserAddress(e.target.value)}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div className="flex items-end">
                    <button
                      onClick={handleCheckStatus}
                      disabled={actionLoading || !checkUserAddress}
                      className="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
                    >
                      {actionLoading ? '🔄 ' : '🔍 '}Check Status
                    </button>
                  </div>
                </div>

                {/* Status Results */}
                <div className="border rounded-lg p-4 bg-gray-50">
                  <div className="text-sm text-gray-600">
                    {verificationStatus ? (
                      <>
                        <p className="font-medium mb-2">Verification Status for {checkUserAddress.slice(0, 10)}...{checkUserAddress.slice(-8)}</p>
                        <div className="mt-2 space-y-1">
                          <div className="flex justify-between">
                            <span>KYC Approved:</span>
                            <span className={verificationStatus.kycApproved ? 'text-green-600 font-medium' : 'text-red-600'}>
                              {verificationStatus.kycApproved ? '✅ Yes' : '❌ No'}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span>Whitelisted:</span>
                            <span className={verificationStatus.whitelisted ? 'text-green-600 font-medium' : 'text-red-600'}>
                              {verificationStatus.whitelisted ? '✅ Yes' : '❌ No'}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span>Eligible:</span>
                            <span className={verificationStatus.eligible ? 'text-green-600 font-medium' : 'text-red-600'}>
                              {verificationStatus.eligible ? '✅ Yes' : '❌ No'}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span>Method:</span>
                            <span className="text-blue-600 font-medium">{verificationStatus.method}</span>
                          </div>
                        </div>
                      </>
                    ) : (
                      <>
                        <p>Status results will appear here after checking a user address.</p>
                        <div className="mt-2 space-y-1">
                          <div className="flex justify-between">
                            <span>KYC Approved:</span>
                            <span className="text-gray-400">-</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Whitelisted:</span>
                            <span className="text-gray-400">-</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Eligible:</span>
                            <span className="text-gray-400">-</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Method:</span>
                            <span className="text-gray-400">-</span>
                          </div>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium mb-2">Token Claims Configuration</h3>
              <p className="text-gray-600 mb-4">Configure which claims are required for this token</p>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50" />
                      <span className="ml-2 text-sm">Enable KYC Verification</span>
                    </label>
                  </div>
                  <div>
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50" />
                      <span className="ml-2 text-sm">Enable Claims System</span>
                    </label>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Required Claims</label>
                  <div className="space-y-2">
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50" />
                      <span className="ml-2 text-sm">KYC Verification (10101010000001)</span>
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50" />
                      <span className="ml-2 text-sm">General Qualification (10101010000004)</span>
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50" />
                      <span className="ml-2 text-sm">Accredited Investor (10101010000002)</span>
                    </label>
                  </div>
                </div>

                <button
                  disabled={actionLoading}
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
                >
                  {actionLoading ? '🔄 ' : '⚙️ '}Update Token Configuration
                </button>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'upgrades' && (
          <div className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-medium mb-2">Schedule Upgrade</h3>
                <p className="text-gray-600 mb-4">
                  Schedule a new implementation upgrade with 48-hour timelock
                </p>
                <div className="space-y-4">
                  <div>
                    <label htmlFor="newImplementation" className="block text-sm font-medium text-gray-700">New Implementation Address</label>
                    <input
                      id="newImplementation"
                      type="text"
                      placeholder="0x..."
                      value={newImplementationAddress}
                      onChange={(e) => setNewImplementationAddress(e.target.value)}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <label htmlFor="upgradeDescription" className="block text-sm font-medium text-gray-700">Upgrade Description</label>
                    <textarea
                      id="upgradeDescription"
                      placeholder="Describe the changes in this upgrade..."
                      value={upgradeDescription}
                      onChange={(e) => setUpgradeDescription(e.target.value)}
                      rows={3}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <button
                    onClick={handleScheduleUpgrade}
                    disabled={actionLoading || !newImplementationAddress || !upgradeDescription}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
                  >
                    {actionLoading ? '🔄 ' : '⏰ '}
                    Schedule Upgrade (48h Timelock)
                  </button>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-medium mb-2">Pending Upgrades</h3>
                <p className="text-gray-600 mb-4">
                  Upgrades waiting for timelock expiration or execution
                </p>
                {pendingUpgrades.length === 0 ? (
                  <p className="text-gray-500 text-center py-4">
                    No pending upgrades
                  </p>
                ) : (
                  <div className="space-y-3">
                    {pendingUpgrades.map((upgrade) => (
                      <div key={upgrade.upgradeId} className="border rounded-lg p-3 space-y-2">
                        <div className="flex justify-between items-start">
                          <div className="text-sm font-medium">
                            {upgrade.description}
                          </div>
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            upgrade.executed ? 'bg-green-100 text-green-800' :
                            upgrade.cancelled ? 'bg-red-100 text-red-800' :
                            'bg-yellow-100 text-yellow-800'
                          }`}>
                            {upgrade.executed ? "Executed" :
                             upgrade.cancelled ? "Cancelled" :
                             "Pending"}
                          </span>
                        </div>
                        <div className="text-xs text-gray-500 space-y-1">
                          <div>Implementation: {upgrade.newImplementation.slice(0, 10)}...{upgrade.newImplementation.slice(-8)}</div>
                          <div>Execute Time: {new Date(upgrade.executeTime * 1000).toLocaleString()}</div>
                          <div>{getTimeUntilExecution(upgrade.executeTime)}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'history' && (
          <div className="space-y-4">
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium mb-2">Upgrade History</h3>
              <p className="text-gray-600 mb-4">
                Complete history of all upgrades performed on this token
              </p>
              {upgradeHistory.length === 0 ? (
                <p className="text-gray-500 text-center py-4">
                  No upgrade history available
                </p>
              ) : (
                <div className="space-y-4">
                  {upgradeHistory.map((record, index) => (
                    <div key={index} className="border rounded-lg p-4 space-y-2">
                      <div className="flex justify-between items-start">
                        <div className="space-y-1">
                          <div className="font-medium">{record.description}</div>
                          <div className="text-sm text-gray-500">
                            {new Date(record.timestamp * 1000).toLocaleString()} by {record.executor.slice(0, 10)}...{record.executor.slice(-8)}
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            record.isEmergency ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'
                          }`}>
                            {record.isEmergency ? "Emergency" : "Scheduled"}
                          </span>
                          {record.version && (
                            <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                              v{record.version}
                            </span>
                          )}
                        </div>
                      </div>
                      <div className="text-xs text-gray-500 space-y-1">
                        <div>From: {record.oldImplementation.slice(0, 10)}...{record.oldImplementation.slice(-8)}</div>
                        <div>To: {record.newImplementation.slice(0, 10)}...{record.newImplementation.slice(-8)}</div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
