{"_format": "hh-sol-artifact-1", "contractName": "TransferModule", "sourceName": "contracts/modules/TransferModule.sol", "abi": [{"inputs": [{"internalType": "address", "name": "_securityToken", "type": "address"}, {"internalType": "address", "name": "_admin", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "fee", "type": "uint256"}], "name": "TransferFeeCollected", "type": "event"}, {"anonymous": false, "inputs": [], "name": "TransferFeesDisabled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "feePercentage", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "feeCollector", "type": "address"}], "name": "TransferFeesEnabled", "type": "event"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "calculateTransferFee", "outputs": [{"internalType": "uint256", "name": "fee", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "disableTransferFees", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_feePercentage", "type": "uint256"}, {"internalType": "address", "name": "_feeCollector", "type": "address"}], "name": "enableTransferFees", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "feeCollector", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getTransferFeeInfo", "outputs": [{"internalType": "bool", "name": "enabled", "type": "bool"}, {"internalType": "uint256", "name": "percentage", "type": "uint256"}, {"internalType": "address", "name": "collector", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "processTransfer", "outputs": [{"internalType": "uint256", "name": "actualAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "securityToken", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "transferFeePercentage", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "transferFeesEnabled", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}