'use client';

import { useState } from 'react';
import { ethers } from 'ethers';
import SecurityTokenABI from '../../contracts/SecurityToken.json';

export default function DebugTokenPage() {
  const [tokenAddress, setTokenAddress] = useState('******************************************');
  const [results, setResults] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const debugToken = async () => {
    if (!tokenAddress) return;
    
    setLoading(true);
    setResults(null);
    
    try {
      console.log("🔍 Starting token debug for:", tokenAddress);
      
      // Create provider
      const provider = new ethers.JsonRpcProvider('https://rpc-amoy.polygon.technology/');
      
      // Connect to token contract
      const tokenContract = new ethers.Contract(
        tokenAddress,
        SecurityTokenABI.abi,
        provider
      );
      
      const debugResults: any = {
        tokenAddress,
        timestamp: new Date().toISOString()
      };
      
      // Test basic functions
      try {
        debugResults.name = await tokenContract.name();
        console.log("✅ Name:", debugResults.name);
      } catch (err: any) {
        debugResults.nameError = err.message;
        console.log("❌ Name error:", err.message);
      }
      
      try {
        debugResults.symbol = await tokenContract.symbol();
        console.log("✅ Symbol:", debugResults.symbol);
      } catch (err: any) {
        debugResults.symbolError = err.message;
        console.log("❌ Symbol error:", err.message);
      }
      
      // Test version function
      try {
        debugResults.version = await tokenContract.version();
        debugResults.isEnhanced = debugResults.version && debugResults.version.includes("enhanced");
        console.log("✅ Version:", debugResults.version);
        console.log("✅ Is Enhanced:", debugResults.isEnhanced);
      } catch (err: any) {
        debugResults.versionError = err.message;
        debugResults.isEnhanced = false;
        console.log("❌ Version error:", err.message);
      }
      
      // Test identityRegistry function
      try {
        debugResults.identityRegistry = await tokenContract.identityRegistry();
        debugResults.hasIdentityRegistry = debugResults.identityRegistry !== ethers.ZeroAddress;
        console.log("✅ Identity Registry:", debugResults.identityRegistry);
        console.log("✅ Has Identity Registry:", debugResults.hasIdentityRegistry);
      } catch (err: any) {
        debugResults.identityRegistryError = err.message;
        debugResults.hasIdentityRegistry = false;
        console.log("❌ Identity Registry error:", err.message);
      }
      
      // Test isWhitelisted function
      try {
        await tokenContract.isWhitelisted(ethers.ZeroAddress);
        debugResults.hasBuiltInWhitelist = true;
        console.log("✅ Has built-in whitelist function");
      } catch (err: any) {
        debugResults.hasBuiltInWhitelist = false;
        debugResults.whitelistError = err.message;
        console.log("❌ No built-in whitelist function:", err.message);
      }
      
      // Calculate final KYC status
      debugResults.finalHasKYC = debugResults.hasIdentityRegistry || debugResults.hasBuiltInWhitelist || debugResults.isEnhanced;
      console.log("✅ Final hasKYC:", debugResults.finalHasKYC);
      
      // Test agent functions
      try {
        debugResults.agentCount = await tokenContract.getAgentCount();
        console.log("✅ Agent count:", debugResults.agentCount.toString());
      } catch (err: any) {
        debugResults.agentError = err.message;
        console.log("❌ Agent error:", err.message);
      }
      
      setResults(debugResults);
      
    } catch (error: any) {
      console.error("Debug error:", error);
      setResults({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Token Debug Tool</h1>
      
      <div className="mb-4">
        <label className="block text-sm font-medium mb-2">Token Address:</label>
        <input
          type="text"
          value={tokenAddress}
          onChange={(e) => setTokenAddress(e.target.value)}
          className="w-full p-2 border border-gray-300 rounded"
          placeholder="0x..."
        />
      </div>
      
      <button
        onClick={debugToken}
        disabled={loading || !tokenAddress}
        className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-400"
      >
        {loading ? 'Debugging...' : 'Debug Token'}
      </button>
      
      {results && (
        <div className="mt-6 p-4 bg-gray-50 rounded">
          <h2 className="text-lg font-semibold mb-4">Debug Results</h2>
          <pre className="text-sm overflow-x-auto">
            {JSON.stringify(results, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}
