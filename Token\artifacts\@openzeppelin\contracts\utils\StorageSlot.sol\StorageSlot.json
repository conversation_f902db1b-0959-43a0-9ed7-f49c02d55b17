{"_format": "hh-sol-artifact-1", "contractName": "StorageSlot", "sourceName": "@openzeppelin/contracts/utils/StorageSlot.sol", "abi": [], "bytecode": "0x60808060405234601757603a9081601d823930815050f35b600080fdfe600080fdfea2646970667358221220c947ff118484a580a410faf32a52de718626e498b7fbd3bb2334b055d6bfc76f64736f6c63430008160033", "deployedBytecode": "0x600080fdfea2646970667358221220c947ff118484a580a410faf32a52de718626e498b7fbd3bb2334b055d6bfc76f64736f6c63430008160033", "linkReferences": {}, "deployedLinkReferences": {}}