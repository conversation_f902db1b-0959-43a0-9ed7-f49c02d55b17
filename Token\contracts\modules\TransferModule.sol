// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";

/**
 * @title TransferModule
 * @dev Handles transfer fees and compliance checks
 */
contract TransferModule is AccessControl, ReentrancyGuard {
    // Constants
    uint256 private constant MAX_FEE_PERCENTAGE = 1000; // 10%
    uint256 private constant FEE_PRECISION = 10000; // 100%
    
    // State variables
    address public securityToken;
    bool public transferFeesEnabled;
    uint256 public transferFeePercentage;
    address public feeCollector;
    
    // Events
    event TransferFeesEnabled(uint256 feePercentage, address feeCollector);
    event TransferFeesDisabled();
    event TransferFeeCollected(address indexed from, address indexed to, uint256 amount, uint256 fee);
    
    // Modifiers
    modifier onlySecurityToken() {
        require(msg.sender == securityToken, "TransferModule: caller is not the security token");
        _;
    }
    
    modifier onlyTokenAdmin() {
        require(hasRole(DEFAULT_ADMIN_ROLE, msg.sender), "TransferModule: caller is not admin");
        _;
    }
    
    /**
     * @dev Constructor
     */
    constructor(address _securityToken, address _admin) {
        require(_securityToken != address(0), "TransferModule: invalid token address");
        require(_admin != address(0), "TransferModule: invalid admin address");
        
        securityToken = _securityToken;
        _grantRole(DEFAULT_ADMIN_ROLE, _admin);
    }
    
    /**
     * @dev Enable transfer fees
     */
    function enableTransferFees(uint256 _feePercentage, address _feeCollector) 
        external 
        onlyTokenAdmin 
        nonReentrant 
    {
        require(_feePercentage <= MAX_FEE_PERCENTAGE, "TransferModule: fee too high");
        require(_feeCollector != address(0), "TransferModule: invalid collector");
        
        transferFeesEnabled = true;
        transferFeePercentage = _feePercentage;
        feeCollector = _feeCollector;
        
        emit TransferFeesEnabled(_feePercentage, _feeCollector);
    }
    
    /**
     * @dev Disable transfer fees
     */
    function disableTransferFees() external onlyTokenAdmin {
        transferFeesEnabled = false;
        transferFeePercentage = 0;
        feeCollector = address(0);
        
        emit TransferFeesDisabled();
    }
    
    /**
     * @dev Calculate transfer fee
     */
    function calculateTransferFee(uint256 amount) external view returns (uint256 fee) {
        if (transferFeesEnabled) {
            return (amount * transferFeePercentage) / FEE_PRECISION;
        }
        return 0;
    }
    
    /**
     * @dev Process transfer with fees
     */
    function processTransfer(address from, address to, uint256 amount) 
        external 
        onlySecurityToken 
        nonReentrant 
        returns (uint256 actualAmount) 
    {
        require(from != address(0), "TransferModule: transfer from zero address");
        require(to != address(0), "TransferModule: transfer to zero address");
        require(amount > 0, "TransferModule: transfer amount must be positive");
        
        uint256 fee = 0;
        if (transferFeesEnabled && feeCollector != address(0)) {
            fee = (amount * transferFeePercentage) / FEE_PRECISION;
            
            if (fee > 0) {
                emit TransferFeeCollected(from, to, amount, fee);
            }
        }
        
        actualAmount = amount - fee;
        return actualAmount;
    }
    
    /**
     * @dev Get transfer fee information
     */
    function getTransferFeeInfo() external view returns (bool enabled, uint256 percentage, address collector) {
        return (transferFeesEnabled, transferFeePercentage, feeCollector);
    }
}
