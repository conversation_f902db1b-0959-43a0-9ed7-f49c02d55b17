// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol";
import "@openzeppelin/contracts-upgradeable/token/ERC20/extensions/ERC20BurnableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";
import "./interfaces/ICompleteWhitelist.sol";
import "./interfaces/ISecurityToken.sol";
import "./IdentityRegistry.sol";
import "./Compliance.sol";

/**
 * @title SecurityToken
 * @dev An ERC-3643 compliant security token with compliance enforcement
 * This implementation follows T-REX (Token for Regulated EXchanges) standard
 */
contract SecurityToken is
    Initializable,
    ERC20Upgradeable,
    ERC20BurnableUpgradeable,
    PausableUpgradeable,
    AccessControlUpgradeable,
    UUPSUpgradeable,
    ReentrancyGuardUpgradeable,
    ISecurityToken
{
    // Roles
    bytes32 public constant AGENT_ROLE = keccak256("AGENT_ROLE");
    bytes32 public constant TRANSFER_MANAGER_ROLE = keccak256("TRANSFER_MANAGER_ROLE");

    // Max supply of the token
    uint256 private _maxSupply;

    // Token decimals
    uint8 private _decimals;

    // Optional token metadata
    string private _tokenPrice;
    string private _bonusTiers;
    string private _tokenDetails;
    string private _tokenImageUrl;

    // Identity Registry and Compliance contracts
    IdentityRegistry private _identityRegistry;
    Compliance private _compliance;

    // Legacy whitelist interface for backward compatibility
    ICompleteWhitelist private _legacyWhitelist;

    // Flag to indicate a privileged transfer to bypass certain checks
    bool private _forcedTransferInProgress;

    // Flag to indicate an approved transfer is in progress
    bool private _approvedTransferInProgress;

    // Version tracking for the implementation
    string private constant _TOKEN_VERSION = "3.0.0"; // Updated for ERC-3643 compliance

    // Store all agent addresses for enumeration
    address[] private _agentList;
    // Mapping to track agent index in the _agentList array (+1 for index 0 meaning not found)
    mapping(address => uint256) private _agentListIndex;

    // Maximum number of agents allowed (security limit)
    uint256 private constant MAX_AGENTS = 50;

    // Constants for fee calculations
    uint256 private constant FEE_DENOMINATOR = 10000; // 100% = 10000 basis points
    uint256 private constant MAX_FEE_PERCENTAGE = 10000; // 100% maximum fee

    // Advanced Transfer Control Features

    // 1. Conditional Transfers (Approval Flow)
    bool private _conditionalTransfersEnabled;
    mapping(bytes32 => bool) private _transferApprovals; // keccak256(from, to, amount, nonce) => approved
    mapping(address => uint256) private _transferNonces; // address => nonce for unique transfer IDs

    // 2. Transfer Whitelisting (additional layer)
    bool private _transferWhitelistEnabled;
    mapping(address => bool) private _transferWhitelisted; // addresses allowed to initiate transfers

    // 3. Transfer Fees
    bool private _transferFeesEnabled;
    uint256 private _transferFeePercentage; // basis points (100 = 1%)
    address private _feeCollector; // must be whitelisted investor

    // Agreement Management
    mapping(address => uint256) private _agreementAcceptances; // address => timestamp of acceptance

    // Emergency controls removed to reduce contract size

    // Additional events for ERC-3643 compliance
    event ComplianceUpdated(address indexed oldCompliance, address indexed newCompliance);

    // Emergency control events removed to reduce contract size

    /**
     * @dev Modifier to make a function callable only by a transfer manager
     */
    modifier onlyTransferManager() {
        require(hasRole(TRANSFER_MANAGER_ROLE, _msgSender()) || hasRole(DEFAULT_ADMIN_ROLE, _msgSender()),
            "SecurityToken: caller is not a transfer manager or admin");
        _;
    }

    // Emergency control modifiers removed to reduce contract size

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers();
    }

    /**
     * @dev Initialize the token with the given parameters
     * @param name_ The name of the token
     * @param symbol_ The symbol of the token
     * @param decimals_ The number of decimals for the token (0-18)
     * @param maxSupply_ The maximum supply of the token
     * @param identityRegistry_ The address of the identity registry contract
     * @param compliance_ The address of the compliance contract
     * @param admin_ The address to be granted DEFAULT_ADMIN_ROLE
     * @param tokenPrice_ Optional token price metadata
     * @param bonusTiers_ Optional bonus tiers metadata
     * @param tokenDetails_ Optional additional token details
     * @param tokenImageUrl_ Optional token image/logo URL
     */
    function initialize(
        string memory name_,
        string memory symbol_,
        uint8 decimals_,
        uint256 maxSupply_,
        address identityRegistry_,
        address compliance_,
        address admin_,
        string memory tokenPrice_,
        string memory bonusTiers_,
        string memory tokenDetails_,
        string memory tokenImageUrl_
    ) public initializer {
        __ERC20_init(name_, symbol_);
        __ERC20Burnable_init();
        __Pausable_init();
        __AccessControl_init();
        __UUPSUpgradeable_init();
        __ReentrancyGuard_init();

        require(identityRegistry_ != address(0), "SecurityToken: identity registry cannot be zero address");
        require(compliance_ != address(0), "SecurityToken: compliance cannot be zero address");
        require(admin_ != address(0), "SecurityToken: admin cannot be zero address");
        require(maxSupply_ > 0, "SecurityToken: max supply must be positive");
        require(decimals_ <= 18, "SecurityToken: decimals must be 18 or less");

        _maxSupply = maxSupply_;
        _decimals = decimals_;
        _tokenPrice = tokenPrice_;
        _bonusTiers = bonusTiers_;
        _tokenDetails = tokenDetails_;
        _tokenImageUrl = tokenImageUrl_;

        // Set up new ERC-3643 contracts
        _identityRegistry = IdentityRegistry(identityRegistry_);
        _compliance = Compliance(compliance_);

        // Set legacy whitelist for backward compatibility
        _legacyWhitelist = ICompleteWhitelist(identityRegistry_);

        _setupRoles(admin_);

        // Set token address in compliance contract
        _compliance.setTokenAddress(address(this));

        emit IdentityRegistryUpdated(address(0), identityRegistry_);
        emit TokenMetadataUpdated(tokenPrice_, bonusTiers_, tokenDetails_);
        emit MaxSupplyUpdated(0, maxSupply_);
    }



    /**
     * @dev Setup roles for the admin
     * @param admin_ The address to grant roles to
     */
    function _setupRoles(address admin_) private {
        _grantRole(DEFAULT_ADMIN_ROLE, admin_);
        _grantRole(TRANSFER_MANAGER_ROLE, admin_);

        // Setup agent role and tracking
        _grantRole(AGENT_ROLE, admin_);
        _agentList.push(admin_);
        _agentListIndex[admin_] = _agentList.length; // Index + 1

        emit AgentAdded(admin_);
    }

    /**
     * @dev Pauses all token transfers
     */
    function pause() external override onlyRole(DEFAULT_ADMIN_ROLE) {
        _pause();
    }

    /**
     * @dev Unpauses all token transfers
     */
    function unpause() external override onlyRole(DEFAULT_ADMIN_ROLE) {
        _unpause();
    }

    /**
     * @dev Mints new tokens to a specified address
     * @param to The address to receive the minted tokens
     * @param amount The amount of tokens to mint
     */
    function mint(address to, uint256 amount) external override onlyRole(AGENT_ROLE) nonReentrant {
        require(to != address(0), "SecurityToken: mint to the zero address");
        require(amount > 0, "SecurityToken: mint amount must be positive");
        require(totalSupply() + amount <= _maxSupply, "SecurityToken: exceeds max supply");

        // Verify compliance using new system
        require(_identityRegistry.isVerified(to), "SecurityToken: recipient not verified");
        require(_identityRegistry.isWhitelisted(to), "SecurityToken: recipient not whitelisted");
        require(!_identityRegistry.isFrozen(to), "SecurityToken: recipient frozen");

        _mint(to, amount);

        // Notify compliance contract of token creation
        _compliance.created(to, amount);
    }

    /**
     * @dev Force transfer tokens from one address to another (for compliance enforcement)
     * This will bypass the frozen checks but still requires the recipient to be whitelisted
     * @param from The address to transfer tokens from
     * @param to The address to transfer tokens to
     * @param amount The amount of tokens to transfer
     * @return bool True if the transfer is successful
     */
    function forcedTransfer(address from, address to, uint256 amount)
        external
        override
        onlyTransferManager
        nonReentrant
        returns (bool)
    {
        require(from != address(0), "SecurityToken: transfer from the zero address");
        require(to != address(0), "SecurityToken: transfer to the zero address");
        require(amount > 0, "SecurityToken: transfer amount must be positive");
        require(amount <= balanceOf(from), "SecurityToken: insufficient balance");

        // Require recipient to be verified and whitelisted, but allow transfers from/to frozen addresses
        require(_identityRegistry.isVerified(to), "SecurityToken: recipient not verified");
        require(_identityRegistry.isWhitelisted(to), "SecurityToken: recipient not whitelisted");

        // Set the forced transfer flag to bypass frozen checks
        _forcedTransferInProgress = true;

        // Perform the transfer
        _transfer(from, to, amount);

        // Reset the forced transfer flag
        _forcedTransferInProgress = false;

        // Emit both forced transfer and standard transfer events for transparency
        emit ForcedTransfer(from, to, amount);
        emit Transfer(from, to, amount);
        return true;
    }

    /**
     * @dev Update the identity registry contract address
     * @param newIdentityRegistry The address of the new identity registry
     */
    function updateIdentityRegistry(address newIdentityRegistry)
        external
        override
        onlyRole(DEFAULT_ADMIN_ROLE)
    {
        require(newIdentityRegistry != address(0), "SecurityToken: registry cannot be zero address");
        require(newIdentityRegistry != address(_identityRegistry), "SecurityToken: new registry is the same as current");

        emit IdentityRegistryUpdated(address(_identityRegistry), newIdentityRegistry);
        _identityRegistry = IdentityRegistry(newIdentityRegistry);
        _legacyWhitelist = ICompleteWhitelist(newIdentityRegistry);
    }

    /**
     * @dev Update the compliance contract address
     * @param newCompliance The address of the new compliance contract
     */
    function updateCompliance(address newCompliance)
        external
        onlyRole(DEFAULT_ADMIN_ROLE)
    {
        require(newCompliance != address(0), "SecurityToken: compliance cannot be zero address");
        require(newCompliance != address(_compliance), "SecurityToken: new compliance is the same as current");

        address oldCompliance = address(_compliance);
        _compliance = Compliance(newCompliance);

        // Set token address in new compliance contract
        _compliance.setTokenAddress(address(this));

        emit ComplianceUpdated(oldCompliance, newCompliance);
    }

    /**
     * @dev Update token metadata
     * @param tokenPrice_ New token price metadata
     * @param bonusTiers_ New bonus tiers metadata
     * @param tokenDetails_ New token details
     */
    function updateTokenMetadata(
        string memory tokenPrice_,
        string memory bonusTiers_,
        string memory tokenDetails_
    ) external override onlyRole(DEFAULT_ADMIN_ROLE) {
        _tokenPrice = tokenPrice_;
        _bonusTiers = bonusTiers_;
        _tokenDetails = tokenDetails_;

        emit TokenMetadataUpdated(tokenPrice_, bonusTiers_, tokenDetails_);
    }

    /**
     * @dev Update token image URL
     * @param tokenImageUrl_ New token image URL
     */
    function updateTokenImageUrl(string memory tokenImageUrl_) external onlyRole(DEFAULT_ADMIN_ROLE) {
        _tokenImageUrl = tokenImageUrl_;
        emit TokenImageUpdated(tokenImageUrl_);
    }

    /**
     * @dev Update max supply
     * @param newMaxSupply New maximum supply
     */
    function updateMaxSupply(uint256 newMaxSupply) external override onlyRole(DEFAULT_ADMIN_ROLE) {
        require(newMaxSupply > 0, "SecurityToken: max supply must be positive");
        require(newMaxSupply >= totalSupply(), "SecurityToken: new max supply below current total supply");

        emit MaxSupplyUpdated(_maxSupply, newMaxSupply);
        _maxSupply = newMaxSupply;
    }

    /**
     * @dev Returns the token price metadata
     * @return string The token price metadata
     */
    function tokenPrice() external view override returns (string memory) {
        return _tokenPrice;
    }

    /**
     * @dev Returns the bonus tiers metadata
     * @return string The bonus tiers metadata
     */
    function bonusTiers() external view override returns (string memory) {
        return _bonusTiers;
    }

    /**
     * @dev Returns the token details metadata
     * @return string The token details metadata
     */
    function tokenDetails() external view override returns (string memory) {
        return _tokenDetails;
    }

    /**
     * @dev Returns the token image URL
     * @return string The token image URL
     */
    function tokenImageUrl() external view returns (string memory) {
        return _tokenImageUrl;
    }

    /**
     * @dev Returns the maximum supply of the token
     * @return uint256 The maximum supply
     */
    function maxSupply() external view override returns (uint256) {
        return _maxSupply;
    }

    // Advanced Transfer Control View Functions

    /**
     * @dev Check if conditional transfers are enabled
     * @return bool True if conditional transfers are enabled
     */
    function conditionalTransfersEnabled() external view override returns (bool) {
        return _conditionalTransfersEnabled;
    }

    /**
     * @dev Check if transfer whitelisting is enabled
     * @return bool True if transfer whitelisting is enabled
     */
    function transferWhitelistEnabled() external view override returns (bool) {
        return _transferWhitelistEnabled;
    }

    /**
     * @dev Check if an address is transfer whitelisted
     * @param account The address to check
     * @return bool True if the address is transfer whitelisted
     */
    function isTransferWhitelisted(address account) external view override returns (bool) {
        return _transferWhitelisted[account];
    }

    /**
     * @dev Check if transfer fees are enabled
     * @return bool True if transfer fees are enabled
     */
    function transferFeesEnabled() external view override returns (bool) {
        return _transferFeesEnabled;
    }

    /**
     * @dev Get transfer fee configuration
     * @return feePercentage Fee percentage in basis points
     * @return feeCollector Address that collects fees
     */
    function getTransferFeeConfig() external view override returns (uint256 feePercentage, address feeCollector) {
        return (_transferFeePercentage, _feeCollector);
    }

    /**
     * @dev Get the next nonce for an address
     * @param account The address to get nonce for
     * @return uint256 The next nonce
     */
    function getTransferNonce(address account) external view override returns (uint256) {
        return _transferNonces[account];
    }

    /**
     * @dev Returns the identity registry contract address
     * @return address The identity registry contract address
     */
    function identityRegistry() external view override returns (address) {
        return address(_identityRegistry);
    }

    /**
     * @dev Returns the compliance contract address
     * @return address The compliance contract address
     */
    function compliance() external view returns (address) {
        return address(_compliance);
    }

    /**
     * @dev Returns the token version
     * @return string The token version
     */
    function version() external pure override returns (string memory) {
        return _TOKEN_VERSION;
    }

    /**
     * @dev Returns the number of decimals used to get its user representation
     * @return uint8 The number of decimals
     */
    function decimals() public view override(ERC20Upgradeable, IERC20Metadata) returns (uint8) {
        return _decimals;
    }

    // Advanced Transfer Control Configuration Functions

    /**
     * @dev Enable/disable conditional transfers (approval flow)
     * @param enabled Whether to enable conditional transfers
     */
    function setConditionalTransfers(bool enabled) external override onlyRole(DEFAULT_ADMIN_ROLE) {
        _conditionalTransfersEnabled = enabled;
        emit ConditionalTransfersUpdated(enabled);
    }

    /**
     * @dev Enable/disable transfer whitelisting
     * @param enabled Whether to enable transfer whitelisting
     */
    function setTransferWhitelist(bool enabled) external override onlyRole(DEFAULT_ADMIN_ROLE) {
        _transferWhitelistEnabled = enabled;
        emit TransferWhitelistUpdated(enabled);
    }

    /**
     * @dev Add/remove an address from transfer whitelist
     * @param account The address to update
     * @param whitelisted Whether to whitelist the address
     */
    function setTransferWhitelistAddress(address account, bool whitelisted) external override onlyRole(AGENT_ROLE) {
        require(account != address(0), "SecurityToken: cannot whitelist zero address");
        _transferWhitelisted[account] = whitelisted;
        emit TransferWhitelistAddressUpdated(account, whitelisted);
    }

    /**
     * @dev Enable/disable transfer fees
     * @param enabled Whether to enable transfer fees
     * @param feePercentage Fee percentage in basis points (100 = 1%)
     * @param feeCollector Address to collect fees (must be whitelisted)
     */
    function setTransferFees(bool enabled, uint256 feePercentage, address feeCollector) external override onlyRole(DEFAULT_ADMIN_ROLE) {
        if (enabled) {
            require(feePercentage <= MAX_FEE_PERCENTAGE, "SecurityToken: fee percentage cannot exceed 100%");
            require(feeCollector != address(0), "SecurityToken: fee collector cannot be zero address");
            require(_identityRegistry.isWhitelisted(feeCollector), "SecurityToken: fee collector must be whitelisted");
        }

        _transferFeesEnabled = enabled;
        _transferFeePercentage = feePercentage;
        _feeCollector = feeCollector;

        emit TransferFeesUpdated(enabled, feePercentage, feeCollector);
    }

    // Conditional Transfer Functions

    /**
     * @dev Approve a specific transfer in the approval flow
     * @param from The address to transfer from
     * @param to The address to transfer to
     * @param amount The amount to transfer
     * @param nonce The nonce for this transfer
     */
    function approveTransfer(address from, address to, uint256 amount, uint256 nonce) external override onlyRole(AGENT_ROLE) {
        require(from != address(0), "SecurityToken: from cannot be zero address");
        require(to != address(0), "SecurityToken: to cannot be zero address");
        require(amount > 0, "SecurityToken: amount must be positive");
        require(nonce == _transferNonces[from], "SecurityToken: invalid nonce");

        bytes32 transferId = keccak256(abi.encodePacked(from, to, amount, nonce));
        _transferApprovals[transferId] = true;

        emit TransferApproved(transferId, from, to, amount);
    }

    /**
     * @dev Execute a pre-approved transfer
     * @param to The address to transfer to
     * @param amount The amount to transfer
     * @param nonce The nonce for this transfer
     */
    function executeApprovedTransfer(address to, uint256 amount, uint256 nonce) external override nonReentrant {
        address from = _msgSender();
        require(to != address(0), "SecurityToken: to cannot be zero address");
        require(amount > 0, "SecurityToken: amount must be positive");
        require(nonce == _transferNonces[from], "SecurityToken: invalid nonce");

        bytes32 transferId = keccak256(abi.encodePacked(from, to, amount, nonce));
        require(_transferApprovals[transferId], "SecurityToken: transfer not approved");

        // Clear the approval and increment nonce
        delete _transferApprovals[transferId];
        _transferNonces[from]++;

        // Set the approved transfer flag
        _approvedTransferInProgress = true;

        // Execute the transfer
        _transfer(from, to, amount);

        // Reset the approved transfer flag
        _approvedTransferInProgress = false;
    }

    /**
     * @dev Check if a transfer is valid according to transfer rules and compliance
     * @param from Source address
     * @param to Destination address
     * @param amount Amount of tokens to transfer
     * @return bool True if the transfer is valid
     */
    function canTransfer(address from, address to, uint256 amount)
        external
        view
        override
        returns (bool)
    {
        if (paused()) {
            return false;
        }

        if (amount == 0 || from == address(0) || to == address(0)) {
            return false;
        }

        if (balanceOf(from) < amount) {
            return false;
        }

        // Use compliance contract for comprehensive checks
        return _compliance.canTransfer(from, to, amount);
    }

    /**
     * @dev Hook that is called before any transfer of tokens.
     * This includes minting and burning.
     */
    function _update(
        address from,
        address to,
        uint256 amount
    ) internal override whenNotPaused {
        // Skip compliance checks for minting and burning
        if (from != address(0) && to != address(0)) {
            // Use compliance contract for comprehensive checks (unless forced transfer)
            if (!_forcedTransferInProgress) {
                require(_compliance.canTransfer(from, to, amount), "SecurityToken: transfer not compliant");
            } else {
                // For forced transfers, only check basic identity requirements
                require(_identityRegistry.isVerified(from), "SecurityToken: sender not verified");
                require(_identityRegistry.isVerified(to), "SecurityToken: recipient not verified");
                require(_identityRegistry.isWhitelisted(to), "SecurityToken: recipient not whitelisted");
            }

            // Advanced Transfer Controls (only for regular transfers, not forced transfers)
            if (!_forcedTransferInProgress) {
                // 1. Transfer Whitelisting Check
                if (_transferWhitelistEnabled) {
                    require(_transferWhitelisted[from], "SecurityToken: sender not transfer whitelisted");
                }

                // 2. Conditional Transfers Check
                if (_conditionalTransfersEnabled) {
                    // For conditional transfers, we need to check if this transfer was pre-approved
                    // This check is bypassed for executeApprovedTransfer function calls
                    // We use a simple flag to detect if we're in an approved transfer context
                    require(_isApprovedTransferInProgress(), "SecurityToken: transfer requires approval");
                }

                // 3. Transfer Fees Processing
                if (_transferFeesEnabled && _transferFeePercentage > 0 && _feeCollector != address(0)) {
                    // Solidity 0.8+ has built-in overflow protection
                    uint256 feeAmount = (amount * _transferFeePercentage) / FEE_DENOMINATOR;
                    if (feeAmount > 0) {
                        // Validate fee calculation to prevent overflow
                        require(feeAmount <= amount, "SecurityToken: fee amount exceeds transfer amount");

                        // Reduce the amount by the fee first to prevent reentrancy issues
                        uint256 originalAmount = amount;
                        amount = amount - feeAmount;

                        // Execute main transfer first
                        super._update(from, to, amount);

                        // Then transfer fee to collector (separate transaction to prevent reentrancy)
                        super._update(from, _feeCollector, feeAmount);

                        emit TransferFeeCollected(from, to, originalAmount, feeAmount);

                        // Return early to avoid double execution of super._update
                        return;
                    }
                }
            }
        } else if (to != address(0)) {
            // For minting, check recipient using identity registry
            require(_identityRegistry.isVerified(to), "SecurityToken: recipient not verified");
            require(_identityRegistry.isWhitelisted(to), "SecurityToken: recipient not whitelisted");

            // Check if recipient is frozen, unless it's a forced transfer
            if (!_forcedTransferInProgress) {
                require(!_identityRegistry.isFrozen(to), "SecurityToken: recipient frozen");
            }
        } else if (from != address(0)) {
            // For burning, notify compliance contract
            _compliance.destroyed(from, amount);
        }

        super._update(from, to, amount);

        // Notify compliance contract of transfer (for regular transfers)
        if (from != address(0) && to != address(0)) {
            _compliance.transferred(from, to, amount);
        }
    }

    /**
     * @dev Internal function to check if we're in an approved transfer context
     * This is a simple implementation that can be enhanced based on specific needs
     */
    function _isApprovedTransferInProgress() internal view returns (bool) {
        return _approvedTransferInProgress;
    }

    /**
     * @dev Function to authorize an upgrade
     * @param newImplementation The address of the new implementation
     */
    function _authorizeUpgrade(address newImplementation)
        internal
        override
        onlyRole(DEFAULT_ADMIN_ROLE)
    {}

    /**
     * @dev Delegate whitelist management functions to the identity registry
     */

    /**
     * @dev Add an address to the whitelist
     * @param account The address to add to the whitelist
     */
    function addToWhitelist(address account) external override onlyRole(AGENT_ROLE) {
        _identityRegistry.addToWhitelist(account);
    }

    /**
     * @dev Batch add addresses to the whitelist
     * @param accounts The addresses to add to the whitelist
     */
    function batchAddToWhitelist(address[] calldata accounts) external override onlyRole(AGENT_ROLE) {
        _identityRegistry.batchAddToWhitelist(accounts);
    }

    /**
     * @dev Remove an address from the whitelist
     * @param account The address to remove from the whitelist
     */
    function removeFromWhitelist(address account) external override onlyRole(AGENT_ROLE) {
        _identityRegistry.removeFromWhitelist(account);
    }

    /**
     * @dev Check if an address is whitelisted
     * @param account The address to check
     * @return bool True if the address is whitelisted, false otherwise
     */
    function isWhitelisted(address account) external view override returns (bool) {
        return _identityRegistry.isWhitelisted(account);
    }

    /**
     * @dev Check if an address is verified (has registered identity)
     * @param account The address to check
     * @return bool True if the address is verified
     */
    function isVerified(address account) external view returns (bool) {
        return _identityRegistry.isVerified(account);
    }

    /**
     * @dev Get investor country
     * @param account The address to check
     * @return uint16 ISO-3166 country code
     */
    function investorCountry(address account) external view returns (uint16) {
        return _identityRegistry.investorCountry(account);
    }

    /**
     * @dev Freeze an address
     * @param account The address to freeze
     */
    function freezeAddress(address account) external override onlyRole(AGENT_ROLE) {
        _identityRegistry.freezeAddress(account);
    }

    /**
     * @dev Unfreeze an address
     * @param account The address to unfreeze
     */
    function unfreezeAddress(address account) external override onlyRole(AGENT_ROLE) {
        _identityRegistry.unfreezeAddress(account);
    }

    /**
     * @dev Add a new agent to the token
     * @param agent Address to add as an agent
     */
    function addAgent(address agent) external override onlyRole(DEFAULT_ADMIN_ROLE) {
        require(agent != address(0), "SecurityToken: agent cannot be zero address");
        require(!hasRole(AGENT_ROLE, agent), "SecurityToken: address is already an agent");
        require(_agentList.length < MAX_AGENTS, "SecurityToken: maximum number of agents reached");
        require(agent != _msgSender(), "SecurityToken: cannot add yourself as agent through this function");

        // Additional security check: ensure agent is not a contract unless explicitly allowed
        if (agent.code.length > 0) {
            // Allow known contract types (could be extended with a whitelist)
            require(hasRole(DEFAULT_ADMIN_ROLE, _msgSender()), "SecurityToken: only admin can add contract agents");
        }

        grantRole(AGENT_ROLE, agent);
    }

    /**
     * @dev Remove an agent from the token
     * @param agent Address to remove as an agent
     */
    function removeAgent(address agent) external override onlyRole(DEFAULT_ADMIN_ROLE) {
        require(hasRole(AGENT_ROLE, agent), "SecurityToken: address is not an agent");
        require(agent != _msgSender(), "SecurityToken: cannot remove yourself as agent");

        revokeRole(AGENT_ROLE, agent);
    }

    /**
     * @dev Get the total number of agents
     * @return uint256 Number of agents
     */
    function getAgentCount() external view override returns (uint256) {
        return _agentList.length;
    }

    /**
     * @dev Get agent address by index
     * @param index Index in the agent list
     * @return address Agent address
     */
    function getAgentAt(uint256 index) external view override returns (address) {
        require(index < _agentList.length, "SecurityToken: index out of bounds");
        return _agentList[index];
    }

    /**
     * @dev Get all agents
     * @return address[] Array of agent addresses
     */
    function getAllAgents() external view returns (address[] memory) {
        return _agentList;
    }

    /**
     * @dev Check if an address is an agent
     * @param account Address to check
     * @return bool True if the address is an agent
     */
    function isAgent(address account) external view returns (bool) {
        return hasRole(AGENT_ROLE, account);
    }

    /**
     * @dev Overrides grantRole to keep track of agents
     * Note: This implementation must maintain the same signature as the parent
     */
    function grantRole(bytes32 role, address account) public virtual override(AccessControlUpgradeable) {
        super.grantRole(role, account);

        // If the role is AGENT_ROLE and the account is not already in the agent list
        if (role == AGENT_ROLE && _agentListIndex[account] == 0) {
            _agentList.push(account);
            _agentListIndex[account] = _agentList.length; // Index + 1
            emit AgentAdded(account);
        }
    }

    /**
     * @dev Overrides revokeRole to keep track of agents
     * Note: This implementation must maintain the same signature as the parent
     */
    function revokeRole(bytes32 role, address account) public virtual override(AccessControlUpgradeable) {
        super.revokeRole(role, account);

        // If the role is AGENT_ROLE and the account is in the agent list
        if (role == AGENT_ROLE && _agentListIndex[account] > 0) {
            _removeAgentFromList(account);
        }
    }

    /**
     * @dev Internal function to safely remove agent from list
     * @param account The agent address to remove
     */
    function _removeAgentFromList(address account) internal {
        uint256 indexPlusOne = _agentListIndex[account];
        require(indexPlusOne > 0, "SecurityToken: agent not in list");

        // Get the actual index (subtract 1 because we store index+1)
        uint256 index = indexPlusOne - 1;
        uint256 lastIndex = _agentList.length - 1;

        // Ensure we don't have an empty array
        require(_agentList.length > 0, "SecurityToken: agent list is empty");
        require(index <= lastIndex, "SecurityToken: invalid agent index");

        // If not the last element, swap with the last one
        if (index != lastIndex) {
            address lastAgent = _agentList[lastIndex];
            _agentList[index] = lastAgent;
            _agentListIndex[lastAgent] = index + 1;
        }

        // Remove the last element
        _agentList.pop();
        delete _agentListIndex[account];

        emit AgentRemoved(account);
    }

    // Agreement Management Functions

    /**
     * @dev Accept the agreement for the calling address
     */
    function acceptAgreement() external override {
        address account = _msgSender();
        require(_agreementAcceptances[account] == 0, "SecurityToken: agreement already accepted");

        _agreementAcceptances[account] = block.timestamp;
        emit AgreementAccepted(account, block.timestamp);
    }

    /**
     * @dev Check if an address has accepted the agreement
     * @param account The address to check
     * @return bool True if the address has accepted the agreement
     */
    function hasAcceptedAgreement(address account) external view override returns (bool) {
        return _agreementAcceptances[account] > 0;
    }

    /**
     * @dev Get the timestamp when an address accepted the agreement
     * @param account The address to check
     * @return uint256 The timestamp when the agreement was accepted (0 if not accepted)
     */
    function getAgreementAcceptanceTimestamp(address account) external view override returns (uint256) {
        return _agreementAcceptances[account];
    }

    // Emergency controls removed to reduce contract size for deployment
}