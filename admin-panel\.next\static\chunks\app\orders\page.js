/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/orders/page"],{

/***/ "(app-pages-browser)/./node_modules/.prisma/client/index-browser.js":
/*!******************************************************!*\
  !*** ./node_modules/.prisma/client/index-browser.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n/* !!! This is code generated by Prisma. Do not edit directly. !!!\n/* eslint-disable */\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nconst {\n  Decimal,\n  objectEnumValues,\n  makeStrictEnum,\n  Public,\n  getRuntime,\n  skip\n} = __webpack_require__(/*! @prisma/client/runtime/index-browser.js */ \"(app-pages-browser)/./node_modules/@prisma/client/runtime/index-browser.js\")\n\n\nconst Prisma = {}\n\nexports.Prisma = Prisma\nexports.$Enums = {}\n\n/**\n * Prisma Client JS version: 6.8.2\n * Query Engine version: 2060c79ba17c6bb9f5823312b6f6b7f4a845738e\n */\nPrisma.prismaVersion = {\n  client: \"6.8.2\",\n  engine: \"2060c79ba17c6bb9f5823312b6f6b7f4a845738e\"\n}\n\nPrisma.PrismaClientKnownRequestError = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)};\nPrisma.PrismaClientUnknownRequestError = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.PrismaClientRustPanicError = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.PrismaClientInitializationError = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.PrismaClientValidationError = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.Decimal = Decimal\n\n/**\n * Re-export of sql-template-tag\n */\nPrisma.sql = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.empty = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.join = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.raw = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.validator = Public.validator\n\n/**\n* Extensions\n*/\nPrisma.getExtensionContext = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.defineExtension = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\n\n/**\n * Shorthand utilities for JSON filtering\n */\nPrisma.DbNull = objectEnumValues.instances.DbNull\nPrisma.JsonNull = objectEnumValues.instances.JsonNull\nPrisma.AnyNull = objectEnumValues.instances.AnyNull\n\nPrisma.NullTypes = {\n  DbNull: objectEnumValues.classes.DbNull,\n  JsonNull: objectEnumValues.classes.JsonNull,\n  AnyNull: objectEnumValues.classes.AnyNull\n}\n\n\n\n/**\n * Enums\n */\n\nexports.Prisma.TransactionIsolationLevel = makeStrictEnum({\n  ReadUncommitted: 'ReadUncommitted',\n  ReadCommitted: 'ReadCommitted',\n  RepeatableRead: 'RepeatableRead',\n  Serializable: 'Serializable'\n});\n\nexports.Prisma.ClientScalarFieldEnum = {\n  id: 'id',\n  firstName: 'firstName',\n  lastName: 'lastName',\n  gender: 'gender',\n  nationality: 'nationality',\n  birthday: 'birthday',\n  birthPlace: 'birthPlace',\n  identificationType: 'identificationType',\n  passportNumber: 'passportNumber',\n  idCardNumber: 'idCardNumber',\n  documentExpiration: 'documentExpiration',\n  phoneNumber: 'phoneNumber',\n  email: 'email',\n  occupation: 'occupation',\n  sectorOfActivity: 'sectorOfActivity',\n  pepStatus: 'pepStatus',\n  pepDetails: 'pepDetails',\n  street: 'street',\n  buildingNumber: 'buildingNumber',\n  city: 'city',\n  state: 'state',\n  country: 'country',\n  zipCode: 'zipCode',\n  sourceOfWealth: 'sourceOfWealth',\n  bankAccountNumber: 'bankAccountNumber',\n  sourceOfFunds: 'sourceOfFunds',\n  taxIdentificationNumber: 'taxIdentificationNumber',\n  kycStatus: 'kycStatus',\n  kycCompletedAt: 'kycCompletedAt',\n  kycNotes: 'kycNotes',\n  walletAddress: 'walletAddress',\n  walletSignature: 'walletSignature',\n  walletVerifiedAt: 'walletVerifiedAt',\n  isWhitelisted: 'isWhitelisted',\n  whitelistedAt: 'whitelistedAt',\n  agreementAccepted: 'agreementAccepted',\n  agreementAcceptedAt: 'agreementAcceptedAt',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt',\n  createdBy: 'createdBy',\n  updatedBy: 'updatedBy'\n};\n\nexports.Prisma.ClientDocumentScalarFieldEnum = {\n  id: 'id',\n  clientId: 'clientId',\n  documentType: 'documentType',\n  fileName: 'fileName',\n  originalFileName: 'originalFileName',\n  fileSize: 'fileSize',\n  mimeType: 'mimeType',\n  filePath: 'filePath',\n  status: 'status',\n  verifiedAt: 'verifiedAt',\n  verifiedBy: 'verifiedBy',\n  rejectionReason: 'rejectionReason',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt'\n};\n\nexports.Prisma.ClientTransactionScalarFieldEnum = {\n  id: 'id',\n  clientId: 'clientId',\n  transactionHash: 'transactionHash',\n  tokenAddress: 'tokenAddress',\n  transactionType: 'transactionType',\n  amount: 'amount',\n  fromAddress: 'fromAddress',\n  toAddress: 'toAddress',\n  status: 'status',\n  blockNumber: 'blockNumber',\n  gasUsed: 'gasUsed',\n  gasPrice: 'gasPrice',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt'\n};\n\nexports.Prisma.TokenScalarFieldEnum = {\n  id: 'id',\n  address: 'address',\n  transactionHash: 'transactionHash',\n  blockNumber: 'blockNumber',\n  network: 'network',\n  name: 'name',\n  symbol: 'symbol',\n  decimals: 'decimals',\n  maxSupply: 'maxSupply',\n  totalSupply: 'totalSupply',\n  tokenType: 'tokenType',\n  tokenPrice: 'tokenPrice',\n  currency: 'currency',\n  bonusTiers: 'bonusTiers',\n  tokenImageUrl: 'tokenImageUrl',\n  whitelistAddress: 'whitelistAddress',\n  adminAddress: 'adminAddress',\n  hasKYC: 'hasKYC',\n  isActive: 'isActive',\n  selectedClaims: 'selectedClaims',\n  deployedBy: 'deployedBy',\n  deploymentNotes: 'deploymentNotes',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt'\n};\n\nexports.Prisma.TokenClientApprovalScalarFieldEnum = {\n  id: 'id',\n  tokenId: 'tokenId',\n  clientId: 'clientId',\n  approvalStatus: 'approvalStatus',\n  kycApproved: 'kycApproved',\n  whitelistApproved: 'whitelistApproved',\n  approvedBy: 'approvedBy',\n  approvedAt: 'approvedAt',\n  rejectedReason: 'rejectedReason',\n  notes: 'notes',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt'\n};\n\nexports.Prisma.OrderScalarFieldEnum = {\n  id: 'id',\n  tokenId: 'tokenId',\n  clientId: 'clientId',\n  status: 'status',\n  tokensOrdered: 'tokensOrdered',\n  tokensConfirmed: 'tokensConfirmed',\n  amountToPay: 'amountToPay',\n  confirmedPayment: 'confirmedPayment',\n  tokenPrice: 'tokenPrice',\n  paymentReference: 'paymentReference',\n  transactionHash: 'transactionHash',\n  blockNumber: 'blockNumber',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt'\n};\n\nexports.Prisma.QualificationProgressScalarFieldEnum = {\n  id: 'id',\n  clientId: 'clientId',\n  tokenId: 'tokenId',\n  claimType: 'claimType',\n  countrySelected: 'countrySelected',\n  countryValue: 'countryValue',\n  agreementAccepted: 'agreementAccepted',\n  profileCompleted: 'profileCompleted',\n  walletConnected: 'walletConnected',\n  kycCompleted: 'kycCompleted',\n  currentStep: 'currentStep',\n  completedSteps: 'completedSteps',\n  totalSteps: 'totalSteps',\n  countryCompletedAt: 'countryCompletedAt',\n  agreementCompletedAt: 'agreementCompletedAt',\n  profileCompletedAt: 'profileCompletedAt',\n  walletCompletedAt: 'walletCompletedAt',\n  kycCompletedAt: 'kycCompletedAt',\n  qualificationStatus: 'qualificationStatus',\n  approvedBy: 'approvedBy',\n  approvedAt: 'approvedAt',\n  rejectedReason: 'rejectedReason',\n  lastActiveStep: 'lastActiveStep',\n  notes: 'notes',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt'\n};\n\nexports.Prisma.TokenAgreementScalarFieldEnum = {\n  id: 'id',\n  clientId: 'clientId',\n  tokenId: 'tokenId',\n  tokenSymbol: 'tokenSymbol',\n  tokenAddress: 'tokenAddress',\n  accepted: 'accepted',\n  agreementVersion: 'agreementVersion',\n  agreementText: 'agreementText',\n  acceptedAt: 'acceptedAt',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt'\n};\n\nexports.Prisma.SortOrder = {\n  asc: 'asc',\n  desc: 'desc'\n};\n\nexports.Prisma.QueryMode = {\n  default: 'default',\n  insensitive: 'insensitive'\n};\n\nexports.Prisma.NullsOrder = {\n  first: 'first',\n  last: 'last'\n};\nexports.Gender = exports.$Enums.Gender = {\n  MALE: 'MALE',\n  FEMALE: 'FEMALE',\n  OTHER: 'OTHER',\n  PREFER_NOT_TO_SAY: 'PREFER_NOT_TO_SAY'\n};\n\nexports.IdentificationType = exports.$Enums.IdentificationType = {\n  PASSPORT: 'PASSPORT',\n  ID_CARD: 'ID_CARD',\n  DRIVERS_LICENSE: 'DRIVERS_LICENSE',\n  OTHER: 'OTHER'\n};\n\nexports.PEPStatus = exports.$Enums.PEPStatus = {\n  NOT_PEP: 'NOT_PEP',\n  DOMESTIC_PEP: 'DOMESTIC_PEP',\n  FOREIGN_PEP: 'FOREIGN_PEP',\n  INTERNATIONAL_ORG_PEP: 'INTERNATIONAL_ORG_PEP',\n  FAMILY_MEMBER: 'FAMILY_MEMBER',\n  CLOSE_ASSOCIATE: 'CLOSE_ASSOCIATE'\n};\n\nexports.KYCStatus = exports.$Enums.KYCStatus = {\n  PENDING: 'PENDING',\n  IN_REVIEW: 'IN_REVIEW',\n  APPROVED: 'APPROVED',\n  REJECTED: 'REJECTED',\n  EXPIRED: 'EXPIRED'\n};\n\nexports.DocumentType = exports.$Enums.DocumentType = {\n  PASSPORT: 'PASSPORT',\n  ID_CARD: 'ID_CARD',\n  DRIVERS_LICENSE: 'DRIVERS_LICENSE',\n  PROOF_OF_ADDRESS: 'PROOF_OF_ADDRESS',\n  BANK_STATEMENT: 'BANK_STATEMENT',\n  INCOME_PROOF: 'INCOME_PROOF',\n  OTHER: 'OTHER'\n};\n\nexports.DocumentStatus = exports.$Enums.DocumentStatus = {\n  PENDING: 'PENDING',\n  APPROVED: 'APPROVED',\n  REJECTED: 'REJECTED'\n};\n\nexports.TransactionType = exports.$Enums.TransactionType = {\n  MINT: 'MINT',\n  TRANSFER: 'TRANSFER',\n  BURN: 'BURN',\n  FREEZE: 'FREEZE',\n  UNFREEZE: 'UNFREEZE',\n  WHITELIST_ADD: 'WHITELIST_ADD',\n  WHITELIST_REMOVE: 'WHITELIST_REMOVE'\n};\n\nexports.TransactionStatus = exports.$Enums.TransactionStatus = {\n  PENDING: 'PENDING',\n  CONFIRMED: 'CONFIRMED',\n  FAILED: 'FAILED',\n  CANCELLED: 'CANCELLED'\n};\n\nexports.ApprovalStatus = exports.$Enums.ApprovalStatus = {\n  PENDING: 'PENDING',\n  APPROVED: 'APPROVED',\n  REJECTED: 'REJECTED'\n};\n\nexports.OrderStatus = exports.$Enums.OrderStatus = {\n  PENDING_APPROVAL: 'PENDING_APPROVAL',\n  CONFIRMED: 'CONFIRMED',\n  MINTED: 'MINTED',\n  CANCELLED: 'CANCELLED'\n};\n\nexports.QualificationStatus = exports.$Enums.QualificationStatus = {\n  PENDING: 'PENDING',\n  APPROVED: 'APPROVED',\n  REJECTED: 'REJECTED',\n  FORCE_APPROVED: 'FORCE_APPROVED'\n};\n\nexports.Prisma.ModelName = {\n  Client: 'Client',\n  ClientDocument: 'ClientDocument',\n  ClientTransaction: 'ClientTransaction',\n  Token: 'Token',\n  TokenClientApproval: 'TokenClientApproval',\n  Order: 'Order',\n  QualificationProgress: 'QualificationProgress',\n  TokenAgreement: 'TokenAgreement'\n};\n\n/**\n * This is a stub Prisma Client that will error at runtime if called.\n */\nclass PrismaClient {\n  constructor() {\n    return new Proxy(this, {\n      get(target, prop) {\n        let message\n        const runtime = getRuntime()\n        if (runtime.isEdge) {\n          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:\n- Use Prisma Accelerate: https://pris.ly/d/accelerate\n- Use Driver Adapters: https://pris.ly/d/driver-adapters\n`;\n        } else {\n          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'\n        }\n\n        message += `\nIf this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`\n\n        throw new Error(message)\n      }\n    })\n  }\n}\n\nexports.PrismaClient = PrismaClient\n\nObject.assign(exports, Prisma)\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucHJpc21hL2NsaWVudC9pbmRleC1icm93c2VyLmpzIiwibWFwcGluZ3MiOiI7QUFDQTtBQUNBOztBQUVBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQzs7QUFFN0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxFQUFFLEVBQUUsbUJBQU8sQ0FBQywySEFBeUM7OztBQUdyRDs7QUFFQSxjQUFjO0FBQ2QsY0FBYzs7QUFFZDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxpSkFBaUosWUFBWTtBQUM3SjtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1KQUFtSixZQUFZO0FBQy9KO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOElBQThJLFlBQVk7QUFDMUo7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtSkFBbUosWUFBWTtBQUMvSjtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtJQUErSSxZQUFZO0FBQzNKO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEhBQTBILFlBQVk7QUFDdEk7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5SEFBeUgsWUFBWTtBQUNySTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdIQUF3SCxZQUFZO0FBQ3BJO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUhBQXVILFlBQVk7QUFDbkk7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrSkFBa0osWUFBWTtBQUM5SjtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhJQUE4SSxZQUFZO0FBQzFKO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7Ozs7QUFJQTtBQUNBO0FBQ0E7O0FBRUEsd0NBQXdDO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQzs7QUFFRCxvQ0FBb0M7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLDRDQUE0QztBQUM1QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsK0NBQStDO0FBQy9DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxtQ0FBbUM7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsaURBQWlEO0FBQ2pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLG1DQUFtQztBQUNuQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsbURBQW1EO0FBQ25EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSw0Q0FBNEM7QUFDNUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHdCQUF3QjtBQUN4QjtBQUNBO0FBQ0E7O0FBRUEsd0JBQXdCO0FBQ3hCO0FBQ0E7QUFDQTs7QUFFQSx5QkFBeUI7QUFDekI7QUFDQTtBQUNBO0FBQ0EsY0FBYyxHQUFHLHFCQUFxQjtBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLDBCQUEwQixHQUFHLGlDQUFpQztBQUM5RDtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLGlCQUFpQixHQUFHLHdCQUF3QjtBQUM1QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxpQkFBaUIsR0FBRyx3QkFBd0I7QUFDNUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLG9CQUFvQixHQUFHLDJCQUEyQjtBQUNsRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHNCQUFzQixHQUFHLDZCQUE2QjtBQUN0RDtBQUNBO0FBQ0E7QUFDQTs7QUFFQSx1QkFBdUIsR0FBRyw4QkFBOEI7QUFDeEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSx5QkFBeUIsR0FBRyxnQ0FBZ0M7QUFDNUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxzQkFBc0IsR0FBRyw2QkFBNkI7QUFDdEQ7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsbUJBQW1CLEdBQUcsMEJBQTBCO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsMkJBQTJCLEdBQUcsa0NBQWtDO0FBQ2hFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsd0JBQXdCO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdFQUFnRSxtQkFBbUI7QUFDbkY7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7O0FBRUEsb0JBQW9COztBQUVwQiIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcdG9rZW5kZXYtbmV3cm9vXFxhZG1pbi1wYW5lbFxcbm9kZV9tb2R1bGVzXFwucHJpc21hXFxjbGllbnRcXGluZGV4LWJyb3dzZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXG4vKiAhISEgVGhpcyBpcyBjb2RlIGdlbmVyYXRlZCBieSBQcmlzbWEuIERvIG5vdCBlZGl0IGRpcmVjdGx5LiAhISFcbi8qIGVzbGludC1kaXNhYmxlICovXG5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcblxuY29uc3Qge1xuICBEZWNpbWFsLFxuICBvYmplY3RFbnVtVmFsdWVzLFxuICBtYWtlU3RyaWN0RW51bSxcbiAgUHVibGljLFxuICBnZXRSdW50aW1lLFxuICBza2lwXG59ID0gcmVxdWlyZSgnQHByaXNtYS9jbGllbnQvcnVudGltZS9pbmRleC1icm93c2VyLmpzJylcblxuXG5jb25zdCBQcmlzbWEgPSB7fVxuXG5leHBvcnRzLlByaXNtYSA9IFByaXNtYVxuZXhwb3J0cy4kRW51bXMgPSB7fVxuXG4vKipcbiAqIFByaXNtYSBDbGllbnQgSlMgdmVyc2lvbjogNi44LjJcbiAqIFF1ZXJ5IEVuZ2luZSB2ZXJzaW9uOiAyMDYwYzc5YmExN2M2YmI5ZjU4MjMzMTJiNmY2YjdmNGE4NDU3MzhlXG4gKi9cblByaXNtYS5wcmlzbWFWZXJzaW9uID0ge1xuICBjbGllbnQ6IFwiNi44LjJcIixcbiAgZW5naW5lOiBcIjIwNjBjNzliYTE3YzZiYjlmNTgyMzMxMmI2ZjZiN2Y0YTg0NTczOGVcIlxufVxuXG5QcmlzbWEuUHJpc21hQ2xpZW50S25vd25SZXF1ZXN0RXJyb3IgPSAoKSA9PiB7XG4gIGNvbnN0IHJ1bnRpbWVOYW1lID0gZ2V0UnVudGltZSgpLnByZXR0eU5hbWU7XG4gIHRocm93IG5ldyBFcnJvcihgUHJpc21hQ2xpZW50S25vd25SZXF1ZXN0RXJyb3IgaXMgdW5hYmxlIHRvIHJ1biBpbiB0aGlzIGJyb3dzZXIgZW52aXJvbm1lbnQsIG9yIGhhcyBiZWVuIGJ1bmRsZWQgZm9yIHRoZSBicm93c2VyIChydW5uaW5nIGluICR7cnVudGltZU5hbWV9KS5cbkluIGNhc2UgdGhpcyBlcnJvciBpcyB1bmV4cGVjdGVkIGZvciB5b3UsIHBsZWFzZSByZXBvcnQgaXQgaW4gaHR0cHM6Ly9wcmlzLmx5L3ByaXNtYS1wcmlzbWEtYnVnLXJlcG9ydGAsXG4pfTtcblByaXNtYS5QcmlzbWFDbGllbnRVbmtub3duUmVxdWVzdEVycm9yID0gKCkgPT4ge1xuICBjb25zdCBydW50aW1lTmFtZSA9IGdldFJ1bnRpbWUoKS5wcmV0dHlOYW1lO1xuICB0aHJvdyBuZXcgRXJyb3IoYFByaXNtYUNsaWVudFVua25vd25SZXF1ZXN0RXJyb3IgaXMgdW5hYmxlIHRvIHJ1biBpbiB0aGlzIGJyb3dzZXIgZW52aXJvbm1lbnQsIG9yIGhhcyBiZWVuIGJ1bmRsZWQgZm9yIHRoZSBicm93c2VyIChydW5uaW5nIGluICR7cnVudGltZU5hbWV9KS5cbkluIGNhc2UgdGhpcyBlcnJvciBpcyB1bmV4cGVjdGVkIGZvciB5b3UsIHBsZWFzZSByZXBvcnQgaXQgaW4gaHR0cHM6Ly9wcmlzLmx5L3ByaXNtYS1wcmlzbWEtYnVnLXJlcG9ydGAsXG4pfVxuUHJpc21hLlByaXNtYUNsaWVudFJ1c3RQYW5pY0Vycm9yID0gKCkgPT4ge1xuICBjb25zdCBydW50aW1lTmFtZSA9IGdldFJ1bnRpbWUoKS5wcmV0dHlOYW1lO1xuICB0aHJvdyBuZXcgRXJyb3IoYFByaXNtYUNsaWVudFJ1c3RQYW5pY0Vycm9yIGlzIHVuYWJsZSB0byBydW4gaW4gdGhpcyBicm93c2VyIGVudmlyb25tZW50LCBvciBoYXMgYmVlbiBidW5kbGVkIGZvciB0aGUgYnJvd3NlciAocnVubmluZyBpbiAke3J1bnRpbWVOYW1lfSkuXG5JbiBjYXNlIHRoaXMgZXJyb3IgaXMgdW5leHBlY3RlZCBmb3IgeW91LCBwbGVhc2UgcmVwb3J0IGl0IGluIGh0dHBzOi8vcHJpcy5seS9wcmlzbWEtcHJpc21hLWJ1Zy1yZXBvcnRgLFxuKX1cblByaXNtYS5QcmlzbWFDbGllbnRJbml0aWFsaXphdGlvbkVycm9yID0gKCkgPT4ge1xuICBjb25zdCBydW50aW1lTmFtZSA9IGdldFJ1bnRpbWUoKS5wcmV0dHlOYW1lO1xuICB0aHJvdyBuZXcgRXJyb3IoYFByaXNtYUNsaWVudEluaXRpYWxpemF0aW9uRXJyb3IgaXMgdW5hYmxlIHRvIHJ1biBpbiB0aGlzIGJyb3dzZXIgZW52aXJvbm1lbnQsIG9yIGhhcyBiZWVuIGJ1bmRsZWQgZm9yIHRoZSBicm93c2VyIChydW5uaW5nIGluICR7cnVudGltZU5hbWV9KS5cbkluIGNhc2UgdGhpcyBlcnJvciBpcyB1bmV4cGVjdGVkIGZvciB5b3UsIHBsZWFzZSByZXBvcnQgaXQgaW4gaHR0cHM6Ly9wcmlzLmx5L3ByaXNtYS1wcmlzbWEtYnVnLXJlcG9ydGAsXG4pfVxuUHJpc21hLlByaXNtYUNsaWVudFZhbGlkYXRpb25FcnJvciA9ICgpID0+IHtcbiAgY29uc3QgcnVudGltZU5hbWUgPSBnZXRSdW50aW1lKCkucHJldHR5TmFtZTtcbiAgdGhyb3cgbmV3IEVycm9yKGBQcmlzbWFDbGllbnRWYWxpZGF0aW9uRXJyb3IgaXMgdW5hYmxlIHRvIHJ1biBpbiB0aGlzIGJyb3dzZXIgZW52aXJvbm1lbnQsIG9yIGhhcyBiZWVuIGJ1bmRsZWQgZm9yIHRoZSBicm93c2VyIChydW5uaW5nIGluICR7cnVudGltZU5hbWV9KS5cbkluIGNhc2UgdGhpcyBlcnJvciBpcyB1bmV4cGVjdGVkIGZvciB5b3UsIHBsZWFzZSByZXBvcnQgaXQgaW4gaHR0cHM6Ly9wcmlzLmx5L3ByaXNtYS1wcmlzbWEtYnVnLXJlcG9ydGAsXG4pfVxuUHJpc21hLkRlY2ltYWwgPSBEZWNpbWFsXG5cbi8qKlxuICogUmUtZXhwb3J0IG9mIHNxbC10ZW1wbGF0ZS10YWdcbiAqL1xuUHJpc21hLnNxbCA9ICgpID0+IHtcbiAgY29uc3QgcnVudGltZU5hbWUgPSBnZXRSdW50aW1lKCkucHJldHR5TmFtZTtcbiAgdGhyb3cgbmV3IEVycm9yKGBzcWx0YWcgaXMgdW5hYmxlIHRvIHJ1biBpbiB0aGlzIGJyb3dzZXIgZW52aXJvbm1lbnQsIG9yIGhhcyBiZWVuIGJ1bmRsZWQgZm9yIHRoZSBicm93c2VyIChydW5uaW5nIGluICR7cnVudGltZU5hbWV9KS5cbkluIGNhc2UgdGhpcyBlcnJvciBpcyB1bmV4cGVjdGVkIGZvciB5b3UsIHBsZWFzZSByZXBvcnQgaXQgaW4gaHR0cHM6Ly9wcmlzLmx5L3ByaXNtYS1wcmlzbWEtYnVnLXJlcG9ydGAsXG4pfVxuUHJpc21hLmVtcHR5ID0gKCkgPT4ge1xuICBjb25zdCBydW50aW1lTmFtZSA9IGdldFJ1bnRpbWUoKS5wcmV0dHlOYW1lO1xuICB0aHJvdyBuZXcgRXJyb3IoYGVtcHR5IGlzIHVuYWJsZSB0byBydW4gaW4gdGhpcyBicm93c2VyIGVudmlyb25tZW50LCBvciBoYXMgYmVlbiBidW5kbGVkIGZvciB0aGUgYnJvd3NlciAocnVubmluZyBpbiAke3J1bnRpbWVOYW1lfSkuXG5JbiBjYXNlIHRoaXMgZXJyb3IgaXMgdW5leHBlY3RlZCBmb3IgeW91LCBwbGVhc2UgcmVwb3J0IGl0IGluIGh0dHBzOi8vcHJpcy5seS9wcmlzbWEtcHJpc21hLWJ1Zy1yZXBvcnRgLFxuKX1cblByaXNtYS5qb2luID0gKCkgPT4ge1xuICBjb25zdCBydW50aW1lTmFtZSA9IGdldFJ1bnRpbWUoKS5wcmV0dHlOYW1lO1xuICB0aHJvdyBuZXcgRXJyb3IoYGpvaW4gaXMgdW5hYmxlIHRvIHJ1biBpbiB0aGlzIGJyb3dzZXIgZW52aXJvbm1lbnQsIG9yIGhhcyBiZWVuIGJ1bmRsZWQgZm9yIHRoZSBicm93c2VyIChydW5uaW5nIGluICR7cnVudGltZU5hbWV9KS5cbkluIGNhc2UgdGhpcyBlcnJvciBpcyB1bmV4cGVjdGVkIGZvciB5b3UsIHBsZWFzZSByZXBvcnQgaXQgaW4gaHR0cHM6Ly9wcmlzLmx5L3ByaXNtYS1wcmlzbWEtYnVnLXJlcG9ydGAsXG4pfVxuUHJpc21hLnJhdyA9ICgpID0+IHtcbiAgY29uc3QgcnVudGltZU5hbWUgPSBnZXRSdW50aW1lKCkucHJldHR5TmFtZTtcbiAgdGhyb3cgbmV3IEVycm9yKGByYXcgaXMgdW5hYmxlIHRvIHJ1biBpbiB0aGlzIGJyb3dzZXIgZW52aXJvbm1lbnQsIG9yIGhhcyBiZWVuIGJ1bmRsZWQgZm9yIHRoZSBicm93c2VyIChydW5uaW5nIGluICR7cnVudGltZU5hbWV9KS5cbkluIGNhc2UgdGhpcyBlcnJvciBpcyB1bmV4cGVjdGVkIGZvciB5b3UsIHBsZWFzZSByZXBvcnQgaXQgaW4gaHR0cHM6Ly9wcmlzLmx5L3ByaXNtYS1wcmlzbWEtYnVnLXJlcG9ydGAsXG4pfVxuUHJpc21hLnZhbGlkYXRvciA9IFB1YmxpYy52YWxpZGF0b3JcblxuLyoqXG4qIEV4dGVuc2lvbnNcbiovXG5QcmlzbWEuZ2V0RXh0ZW5zaW9uQ29udGV4dCA9ICgpID0+IHtcbiAgY29uc3QgcnVudGltZU5hbWUgPSBnZXRSdW50aW1lKCkucHJldHR5TmFtZTtcbiAgdGhyb3cgbmV3IEVycm9yKGBFeHRlbnNpb25zLmdldEV4dGVuc2lvbkNvbnRleHQgaXMgdW5hYmxlIHRvIHJ1biBpbiB0aGlzIGJyb3dzZXIgZW52aXJvbm1lbnQsIG9yIGhhcyBiZWVuIGJ1bmRsZWQgZm9yIHRoZSBicm93c2VyIChydW5uaW5nIGluICR7cnVudGltZU5hbWV9KS5cbkluIGNhc2UgdGhpcyBlcnJvciBpcyB1bmV4cGVjdGVkIGZvciB5b3UsIHBsZWFzZSByZXBvcnQgaXQgaW4gaHR0cHM6Ly9wcmlzLmx5L3ByaXNtYS1wcmlzbWEtYnVnLXJlcG9ydGAsXG4pfVxuUHJpc21hLmRlZmluZUV4dGVuc2lvbiA9ICgpID0+IHtcbiAgY29uc3QgcnVudGltZU5hbWUgPSBnZXRSdW50aW1lKCkucHJldHR5TmFtZTtcbiAgdGhyb3cgbmV3IEVycm9yKGBFeHRlbnNpb25zLmRlZmluZUV4dGVuc2lvbiBpcyB1bmFibGUgdG8gcnVuIGluIHRoaXMgYnJvd3NlciBlbnZpcm9ubWVudCwgb3IgaGFzIGJlZW4gYnVuZGxlZCBmb3IgdGhlIGJyb3dzZXIgKHJ1bm5pbmcgaW4gJHtydW50aW1lTmFtZX0pLlxuSW4gY2FzZSB0aGlzIGVycm9yIGlzIHVuZXhwZWN0ZWQgZm9yIHlvdSwgcGxlYXNlIHJlcG9ydCBpdCBpbiBodHRwczovL3ByaXMubHkvcHJpc21hLXByaXNtYS1idWctcmVwb3J0YCxcbil9XG5cbi8qKlxuICogU2hvcnRoYW5kIHV0aWxpdGllcyBmb3IgSlNPTiBmaWx0ZXJpbmdcbiAqL1xuUHJpc21hLkRiTnVsbCA9IG9iamVjdEVudW1WYWx1ZXMuaW5zdGFuY2VzLkRiTnVsbFxuUHJpc21hLkpzb25OdWxsID0gb2JqZWN0RW51bVZhbHVlcy5pbnN0YW5jZXMuSnNvbk51bGxcblByaXNtYS5BbnlOdWxsID0gb2JqZWN0RW51bVZhbHVlcy5pbnN0YW5jZXMuQW55TnVsbFxuXG5QcmlzbWEuTnVsbFR5cGVzID0ge1xuICBEYk51bGw6IG9iamVjdEVudW1WYWx1ZXMuY2xhc3Nlcy5EYk51bGwsXG4gIEpzb25OdWxsOiBvYmplY3RFbnVtVmFsdWVzLmNsYXNzZXMuSnNvbk51bGwsXG4gIEFueU51bGw6IG9iamVjdEVudW1WYWx1ZXMuY2xhc3Nlcy5BbnlOdWxsXG59XG5cblxuXG4vKipcbiAqIEVudW1zXG4gKi9cblxuZXhwb3J0cy5QcmlzbWEuVHJhbnNhY3Rpb25Jc29sYXRpb25MZXZlbCA9IG1ha2VTdHJpY3RFbnVtKHtcbiAgUmVhZFVuY29tbWl0dGVkOiAnUmVhZFVuY29tbWl0dGVkJyxcbiAgUmVhZENvbW1pdHRlZDogJ1JlYWRDb21taXR0ZWQnLFxuICBSZXBlYXRhYmxlUmVhZDogJ1JlcGVhdGFibGVSZWFkJyxcbiAgU2VyaWFsaXphYmxlOiAnU2VyaWFsaXphYmxlJ1xufSk7XG5cbmV4cG9ydHMuUHJpc21hLkNsaWVudFNjYWxhckZpZWxkRW51bSA9IHtcbiAgaWQ6ICdpZCcsXG4gIGZpcnN0TmFtZTogJ2ZpcnN0TmFtZScsXG4gIGxhc3ROYW1lOiAnbGFzdE5hbWUnLFxuICBnZW5kZXI6ICdnZW5kZXInLFxuICBuYXRpb25hbGl0eTogJ25hdGlvbmFsaXR5JyxcbiAgYmlydGhkYXk6ICdiaXJ0aGRheScsXG4gIGJpcnRoUGxhY2U6ICdiaXJ0aFBsYWNlJyxcbiAgaWRlbnRpZmljYXRpb25UeXBlOiAnaWRlbnRpZmljYXRpb25UeXBlJyxcbiAgcGFzc3BvcnROdW1iZXI6ICdwYXNzcG9ydE51bWJlcicsXG4gIGlkQ2FyZE51bWJlcjogJ2lkQ2FyZE51bWJlcicsXG4gIGRvY3VtZW50RXhwaXJhdGlvbjogJ2RvY3VtZW50RXhwaXJhdGlvbicsXG4gIHBob25lTnVtYmVyOiAncGhvbmVOdW1iZXInLFxuICBlbWFpbDogJ2VtYWlsJyxcbiAgb2NjdXBhdGlvbjogJ29jY3VwYXRpb24nLFxuICBzZWN0b3JPZkFjdGl2aXR5OiAnc2VjdG9yT2ZBY3Rpdml0eScsXG4gIHBlcFN0YXR1czogJ3BlcFN0YXR1cycsXG4gIHBlcERldGFpbHM6ICdwZXBEZXRhaWxzJyxcbiAgc3RyZWV0OiAnc3RyZWV0JyxcbiAgYnVpbGRpbmdOdW1iZXI6ICdidWlsZGluZ051bWJlcicsXG4gIGNpdHk6ICdjaXR5JyxcbiAgc3RhdGU6ICdzdGF0ZScsXG4gIGNvdW50cnk6ICdjb3VudHJ5JyxcbiAgemlwQ29kZTogJ3ppcENvZGUnLFxuICBzb3VyY2VPZldlYWx0aDogJ3NvdXJjZU9mV2VhbHRoJyxcbiAgYmFua0FjY291bnROdW1iZXI6ICdiYW5rQWNjb3VudE51bWJlcicsXG4gIHNvdXJjZU9mRnVuZHM6ICdzb3VyY2VPZkZ1bmRzJyxcbiAgdGF4SWRlbnRpZmljYXRpb25OdW1iZXI6ICd0YXhJZGVudGlmaWNhdGlvbk51bWJlcicsXG4gIGt5Y1N0YXR1czogJ2t5Y1N0YXR1cycsXG4gIGt5Y0NvbXBsZXRlZEF0OiAna3ljQ29tcGxldGVkQXQnLFxuICBreWNOb3RlczogJ2t5Y05vdGVzJyxcbiAgd2FsbGV0QWRkcmVzczogJ3dhbGxldEFkZHJlc3MnLFxuICB3YWxsZXRTaWduYXR1cmU6ICd3YWxsZXRTaWduYXR1cmUnLFxuICB3YWxsZXRWZXJpZmllZEF0OiAnd2FsbGV0VmVyaWZpZWRBdCcsXG4gIGlzV2hpdGVsaXN0ZWQ6ICdpc1doaXRlbGlzdGVkJyxcbiAgd2hpdGVsaXN0ZWRBdDogJ3doaXRlbGlzdGVkQXQnLFxuICBhZ3JlZW1lbnRBY2NlcHRlZDogJ2FncmVlbWVudEFjY2VwdGVkJyxcbiAgYWdyZWVtZW50QWNjZXB0ZWRBdDogJ2FncmVlbWVudEFjY2VwdGVkQXQnLFxuICBjcmVhdGVkQXQ6ICdjcmVhdGVkQXQnLFxuICB1cGRhdGVkQXQ6ICd1cGRhdGVkQXQnLFxuICBjcmVhdGVkQnk6ICdjcmVhdGVkQnknLFxuICB1cGRhdGVkQnk6ICd1cGRhdGVkQnknXG59O1xuXG5leHBvcnRzLlByaXNtYS5DbGllbnREb2N1bWVudFNjYWxhckZpZWxkRW51bSA9IHtcbiAgaWQ6ICdpZCcsXG4gIGNsaWVudElkOiAnY2xpZW50SWQnLFxuICBkb2N1bWVudFR5cGU6ICdkb2N1bWVudFR5cGUnLFxuICBmaWxlTmFtZTogJ2ZpbGVOYW1lJyxcbiAgb3JpZ2luYWxGaWxlTmFtZTogJ29yaWdpbmFsRmlsZU5hbWUnLFxuICBmaWxlU2l6ZTogJ2ZpbGVTaXplJyxcbiAgbWltZVR5cGU6ICdtaW1lVHlwZScsXG4gIGZpbGVQYXRoOiAnZmlsZVBhdGgnLFxuICBzdGF0dXM6ICdzdGF0dXMnLFxuICB2ZXJpZmllZEF0OiAndmVyaWZpZWRBdCcsXG4gIHZlcmlmaWVkQnk6ICd2ZXJpZmllZEJ5JyxcbiAgcmVqZWN0aW9uUmVhc29uOiAncmVqZWN0aW9uUmVhc29uJyxcbiAgY3JlYXRlZEF0OiAnY3JlYXRlZEF0JyxcbiAgdXBkYXRlZEF0OiAndXBkYXRlZEF0J1xufTtcblxuZXhwb3J0cy5QcmlzbWEuQ2xpZW50VHJhbnNhY3Rpb25TY2FsYXJGaWVsZEVudW0gPSB7XG4gIGlkOiAnaWQnLFxuICBjbGllbnRJZDogJ2NsaWVudElkJyxcbiAgdHJhbnNhY3Rpb25IYXNoOiAndHJhbnNhY3Rpb25IYXNoJyxcbiAgdG9rZW5BZGRyZXNzOiAndG9rZW5BZGRyZXNzJyxcbiAgdHJhbnNhY3Rpb25UeXBlOiAndHJhbnNhY3Rpb25UeXBlJyxcbiAgYW1vdW50OiAnYW1vdW50JyxcbiAgZnJvbUFkZHJlc3M6ICdmcm9tQWRkcmVzcycsXG4gIHRvQWRkcmVzczogJ3RvQWRkcmVzcycsXG4gIHN0YXR1czogJ3N0YXR1cycsXG4gIGJsb2NrTnVtYmVyOiAnYmxvY2tOdW1iZXInLFxuICBnYXNVc2VkOiAnZ2FzVXNlZCcsXG4gIGdhc1ByaWNlOiAnZ2FzUHJpY2UnLFxuICBjcmVhdGVkQXQ6ICdjcmVhdGVkQXQnLFxuICB1cGRhdGVkQXQ6ICd1cGRhdGVkQXQnXG59O1xuXG5leHBvcnRzLlByaXNtYS5Ub2tlblNjYWxhckZpZWxkRW51bSA9IHtcbiAgaWQ6ICdpZCcsXG4gIGFkZHJlc3M6ICdhZGRyZXNzJyxcbiAgdHJhbnNhY3Rpb25IYXNoOiAndHJhbnNhY3Rpb25IYXNoJyxcbiAgYmxvY2tOdW1iZXI6ICdibG9ja051bWJlcicsXG4gIG5ldHdvcms6ICduZXR3b3JrJyxcbiAgbmFtZTogJ25hbWUnLFxuICBzeW1ib2w6ICdzeW1ib2wnLFxuICBkZWNpbWFsczogJ2RlY2ltYWxzJyxcbiAgbWF4U3VwcGx5OiAnbWF4U3VwcGx5JyxcbiAgdG90YWxTdXBwbHk6ICd0b3RhbFN1cHBseScsXG4gIHRva2VuVHlwZTogJ3Rva2VuVHlwZScsXG4gIHRva2VuUHJpY2U6ICd0b2tlblByaWNlJyxcbiAgY3VycmVuY3k6ICdjdXJyZW5jeScsXG4gIGJvbnVzVGllcnM6ICdib251c1RpZXJzJyxcbiAgdG9rZW5JbWFnZVVybDogJ3Rva2VuSW1hZ2VVcmwnLFxuICB3aGl0ZWxpc3RBZGRyZXNzOiAnd2hpdGVsaXN0QWRkcmVzcycsXG4gIGFkbWluQWRkcmVzczogJ2FkbWluQWRkcmVzcycsXG4gIGhhc0tZQzogJ2hhc0tZQycsXG4gIGlzQWN0aXZlOiAnaXNBY3RpdmUnLFxuICBzZWxlY3RlZENsYWltczogJ3NlbGVjdGVkQ2xhaW1zJyxcbiAgZGVwbG95ZWRCeTogJ2RlcGxveWVkQnknLFxuICBkZXBsb3ltZW50Tm90ZXM6ICdkZXBsb3ltZW50Tm90ZXMnLFxuICBjcmVhdGVkQXQ6ICdjcmVhdGVkQXQnLFxuICB1cGRhdGVkQXQ6ICd1cGRhdGVkQXQnXG59O1xuXG5leHBvcnRzLlByaXNtYS5Ub2tlbkNsaWVudEFwcHJvdmFsU2NhbGFyRmllbGRFbnVtID0ge1xuICBpZDogJ2lkJyxcbiAgdG9rZW5JZDogJ3Rva2VuSWQnLFxuICBjbGllbnRJZDogJ2NsaWVudElkJyxcbiAgYXBwcm92YWxTdGF0dXM6ICdhcHByb3ZhbFN0YXR1cycsXG4gIGt5Y0FwcHJvdmVkOiAna3ljQXBwcm92ZWQnLFxuICB3aGl0ZWxpc3RBcHByb3ZlZDogJ3doaXRlbGlzdEFwcHJvdmVkJyxcbiAgYXBwcm92ZWRCeTogJ2FwcHJvdmVkQnknLFxuICBhcHByb3ZlZEF0OiAnYXBwcm92ZWRBdCcsXG4gIHJlamVjdGVkUmVhc29uOiAncmVqZWN0ZWRSZWFzb24nLFxuICBub3RlczogJ25vdGVzJyxcbiAgY3JlYXRlZEF0OiAnY3JlYXRlZEF0JyxcbiAgdXBkYXRlZEF0OiAndXBkYXRlZEF0J1xufTtcblxuZXhwb3J0cy5QcmlzbWEuT3JkZXJTY2FsYXJGaWVsZEVudW0gPSB7XG4gIGlkOiAnaWQnLFxuICB0b2tlbklkOiAndG9rZW5JZCcsXG4gIGNsaWVudElkOiAnY2xpZW50SWQnLFxuICBzdGF0dXM6ICdzdGF0dXMnLFxuICB0b2tlbnNPcmRlcmVkOiAndG9rZW5zT3JkZXJlZCcsXG4gIHRva2Vuc0NvbmZpcm1lZDogJ3Rva2Vuc0NvbmZpcm1lZCcsXG4gIGFtb3VudFRvUGF5OiAnYW1vdW50VG9QYXknLFxuICBjb25maXJtZWRQYXltZW50OiAnY29uZmlybWVkUGF5bWVudCcsXG4gIHRva2VuUHJpY2U6ICd0b2tlblByaWNlJyxcbiAgcGF5bWVudFJlZmVyZW5jZTogJ3BheW1lbnRSZWZlcmVuY2UnLFxuICB0cmFuc2FjdGlvbkhhc2g6ICd0cmFuc2FjdGlvbkhhc2gnLFxuICBibG9ja051bWJlcjogJ2Jsb2NrTnVtYmVyJyxcbiAgY3JlYXRlZEF0OiAnY3JlYXRlZEF0JyxcbiAgdXBkYXRlZEF0OiAndXBkYXRlZEF0J1xufTtcblxuZXhwb3J0cy5QcmlzbWEuUXVhbGlmaWNhdGlvblByb2dyZXNzU2NhbGFyRmllbGRFbnVtID0ge1xuICBpZDogJ2lkJyxcbiAgY2xpZW50SWQ6ICdjbGllbnRJZCcsXG4gIHRva2VuSWQ6ICd0b2tlbklkJyxcbiAgY2xhaW1UeXBlOiAnY2xhaW1UeXBlJyxcbiAgY291bnRyeVNlbGVjdGVkOiAnY291bnRyeVNlbGVjdGVkJyxcbiAgY291bnRyeVZhbHVlOiAnY291bnRyeVZhbHVlJyxcbiAgYWdyZWVtZW50QWNjZXB0ZWQ6ICdhZ3JlZW1lbnRBY2NlcHRlZCcsXG4gIHByb2ZpbGVDb21wbGV0ZWQ6ICdwcm9maWxlQ29tcGxldGVkJyxcbiAgd2FsbGV0Q29ubmVjdGVkOiAnd2FsbGV0Q29ubmVjdGVkJyxcbiAga3ljQ29tcGxldGVkOiAna3ljQ29tcGxldGVkJyxcbiAgY3VycmVudFN0ZXA6ICdjdXJyZW50U3RlcCcsXG4gIGNvbXBsZXRlZFN0ZXBzOiAnY29tcGxldGVkU3RlcHMnLFxuICB0b3RhbFN0ZXBzOiAndG90YWxTdGVwcycsXG4gIGNvdW50cnlDb21wbGV0ZWRBdDogJ2NvdW50cnlDb21wbGV0ZWRBdCcsXG4gIGFncmVlbWVudENvbXBsZXRlZEF0OiAnYWdyZWVtZW50Q29tcGxldGVkQXQnLFxuICBwcm9maWxlQ29tcGxldGVkQXQ6ICdwcm9maWxlQ29tcGxldGVkQXQnLFxuICB3YWxsZXRDb21wbGV0ZWRBdDogJ3dhbGxldENvbXBsZXRlZEF0JyxcbiAga3ljQ29tcGxldGVkQXQ6ICdreWNDb21wbGV0ZWRBdCcsXG4gIHF1YWxpZmljYXRpb25TdGF0dXM6ICdxdWFsaWZpY2F0aW9uU3RhdHVzJyxcbiAgYXBwcm92ZWRCeTogJ2FwcHJvdmVkQnknLFxuICBhcHByb3ZlZEF0OiAnYXBwcm92ZWRBdCcsXG4gIHJlamVjdGVkUmVhc29uOiAncmVqZWN0ZWRSZWFzb24nLFxuICBsYXN0QWN0aXZlU3RlcDogJ2xhc3RBY3RpdmVTdGVwJyxcbiAgbm90ZXM6ICdub3RlcycsXG4gIGNyZWF0ZWRBdDogJ2NyZWF0ZWRBdCcsXG4gIHVwZGF0ZWRBdDogJ3VwZGF0ZWRBdCdcbn07XG5cbmV4cG9ydHMuUHJpc21hLlRva2VuQWdyZWVtZW50U2NhbGFyRmllbGRFbnVtID0ge1xuICBpZDogJ2lkJyxcbiAgY2xpZW50SWQ6ICdjbGllbnRJZCcsXG4gIHRva2VuSWQ6ICd0b2tlbklkJyxcbiAgdG9rZW5TeW1ib2w6ICd0b2tlblN5bWJvbCcsXG4gIHRva2VuQWRkcmVzczogJ3Rva2VuQWRkcmVzcycsXG4gIGFjY2VwdGVkOiAnYWNjZXB0ZWQnLFxuICBhZ3JlZW1lbnRWZXJzaW9uOiAnYWdyZWVtZW50VmVyc2lvbicsXG4gIGFncmVlbWVudFRleHQ6ICdhZ3JlZW1lbnRUZXh0JyxcbiAgYWNjZXB0ZWRBdDogJ2FjY2VwdGVkQXQnLFxuICBjcmVhdGVkQXQ6ICdjcmVhdGVkQXQnLFxuICB1cGRhdGVkQXQ6ICd1cGRhdGVkQXQnXG59O1xuXG5leHBvcnRzLlByaXNtYS5Tb3J0T3JkZXIgPSB7XG4gIGFzYzogJ2FzYycsXG4gIGRlc2M6ICdkZXNjJ1xufTtcblxuZXhwb3J0cy5QcmlzbWEuUXVlcnlNb2RlID0ge1xuICBkZWZhdWx0OiAnZGVmYXVsdCcsXG4gIGluc2Vuc2l0aXZlOiAnaW5zZW5zaXRpdmUnXG59O1xuXG5leHBvcnRzLlByaXNtYS5OdWxsc09yZGVyID0ge1xuICBmaXJzdDogJ2ZpcnN0JyxcbiAgbGFzdDogJ2xhc3QnXG59O1xuZXhwb3J0cy5HZW5kZXIgPSBleHBvcnRzLiRFbnVtcy5HZW5kZXIgPSB7XG4gIE1BTEU6ICdNQUxFJyxcbiAgRkVNQUxFOiAnRkVNQUxFJyxcbiAgT1RIRVI6ICdPVEhFUicsXG4gIFBSRUZFUl9OT1RfVE9fU0FZOiAnUFJFRkVSX05PVF9UT19TQVknXG59O1xuXG5leHBvcnRzLklkZW50aWZpY2F0aW9uVHlwZSA9IGV4cG9ydHMuJEVudW1zLklkZW50aWZpY2F0aW9uVHlwZSA9IHtcbiAgUEFTU1BPUlQ6ICdQQVNTUE9SVCcsXG4gIElEX0NBUkQ6ICdJRF9DQVJEJyxcbiAgRFJJVkVSU19MSUNFTlNFOiAnRFJJVkVSU19MSUNFTlNFJyxcbiAgT1RIRVI6ICdPVEhFUidcbn07XG5cbmV4cG9ydHMuUEVQU3RhdHVzID0gZXhwb3J0cy4kRW51bXMuUEVQU3RhdHVzID0ge1xuICBOT1RfUEVQOiAnTk9UX1BFUCcsXG4gIERPTUVTVElDX1BFUDogJ0RPTUVTVElDX1BFUCcsXG4gIEZPUkVJR05fUEVQOiAnRk9SRUlHTl9QRVAnLFxuICBJTlRFUk5BVElPTkFMX09SR19QRVA6ICdJTlRFUk5BVElPTkFMX09SR19QRVAnLFxuICBGQU1JTFlfTUVNQkVSOiAnRkFNSUxZX01FTUJFUicsXG4gIENMT1NFX0FTU09DSUFURTogJ0NMT1NFX0FTU09DSUFURSdcbn07XG5cbmV4cG9ydHMuS1lDU3RhdHVzID0gZXhwb3J0cy4kRW51bXMuS1lDU3RhdHVzID0ge1xuICBQRU5ESU5HOiAnUEVORElORycsXG4gIElOX1JFVklFVzogJ0lOX1JFVklFVycsXG4gIEFQUFJPVkVEOiAnQVBQUk9WRUQnLFxuICBSRUpFQ1RFRDogJ1JFSkVDVEVEJyxcbiAgRVhQSVJFRDogJ0VYUElSRUQnXG59O1xuXG5leHBvcnRzLkRvY3VtZW50VHlwZSA9IGV4cG9ydHMuJEVudW1zLkRvY3VtZW50VHlwZSA9IHtcbiAgUEFTU1BPUlQ6ICdQQVNTUE9SVCcsXG4gIElEX0NBUkQ6ICdJRF9DQVJEJyxcbiAgRFJJVkVSU19MSUNFTlNFOiAnRFJJVkVSU19MSUNFTlNFJyxcbiAgUFJPT0ZfT0ZfQUREUkVTUzogJ1BST09GX09GX0FERFJFU1MnLFxuICBCQU5LX1NUQVRFTUVOVDogJ0JBTktfU1RBVEVNRU5UJyxcbiAgSU5DT01FX1BST09GOiAnSU5DT01FX1BST09GJyxcbiAgT1RIRVI6ICdPVEhFUidcbn07XG5cbmV4cG9ydHMuRG9jdW1lbnRTdGF0dXMgPSBleHBvcnRzLiRFbnVtcy5Eb2N1bWVudFN0YXR1cyA9IHtcbiAgUEVORElORzogJ1BFTkRJTkcnLFxuICBBUFBST1ZFRDogJ0FQUFJPVkVEJyxcbiAgUkVKRUNURUQ6ICdSRUpFQ1RFRCdcbn07XG5cbmV4cG9ydHMuVHJhbnNhY3Rpb25UeXBlID0gZXhwb3J0cy4kRW51bXMuVHJhbnNhY3Rpb25UeXBlID0ge1xuICBNSU5UOiAnTUlOVCcsXG4gIFRSQU5TRkVSOiAnVFJBTlNGRVInLFxuICBCVVJOOiAnQlVSTicsXG4gIEZSRUVaRTogJ0ZSRUVaRScsXG4gIFVORlJFRVpFOiAnVU5GUkVFWkUnLFxuICBXSElURUxJU1RfQUREOiAnV0hJVEVMSVNUX0FERCcsXG4gIFdISVRFTElTVF9SRU1PVkU6ICdXSElURUxJU1RfUkVNT1ZFJ1xufTtcblxuZXhwb3J0cy5UcmFuc2FjdGlvblN0YXR1cyA9IGV4cG9ydHMuJEVudW1zLlRyYW5zYWN0aW9uU3RhdHVzID0ge1xuICBQRU5ESU5HOiAnUEVORElORycsXG4gIENPTkZJUk1FRDogJ0NPTkZJUk1FRCcsXG4gIEZBSUxFRDogJ0ZBSUxFRCcsXG4gIENBTkNFTExFRDogJ0NBTkNFTExFRCdcbn07XG5cbmV4cG9ydHMuQXBwcm92YWxTdGF0dXMgPSBleHBvcnRzLiRFbnVtcy5BcHByb3ZhbFN0YXR1cyA9IHtcbiAgUEVORElORzogJ1BFTkRJTkcnLFxuICBBUFBST1ZFRDogJ0FQUFJPVkVEJyxcbiAgUkVKRUNURUQ6ICdSRUpFQ1RFRCdcbn07XG5cbmV4cG9ydHMuT3JkZXJTdGF0dXMgPSBleHBvcnRzLiRFbnVtcy5PcmRlclN0YXR1cyA9IHtcbiAgUEVORElOR19BUFBST1ZBTDogJ1BFTkRJTkdfQVBQUk9WQUwnLFxuICBDT05GSVJNRUQ6ICdDT05GSVJNRUQnLFxuICBNSU5URUQ6ICdNSU5URUQnLFxuICBDQU5DRUxMRUQ6ICdDQU5DRUxMRUQnXG59O1xuXG5leHBvcnRzLlF1YWxpZmljYXRpb25TdGF0dXMgPSBleHBvcnRzLiRFbnVtcy5RdWFsaWZpY2F0aW9uU3RhdHVzID0ge1xuICBQRU5ESU5HOiAnUEVORElORycsXG4gIEFQUFJPVkVEOiAnQVBQUk9WRUQnLFxuICBSRUpFQ1RFRDogJ1JFSkVDVEVEJyxcbiAgRk9SQ0VfQVBQUk9WRUQ6ICdGT1JDRV9BUFBST1ZFRCdcbn07XG5cbmV4cG9ydHMuUHJpc21hLk1vZGVsTmFtZSA9IHtcbiAgQ2xpZW50OiAnQ2xpZW50JyxcbiAgQ2xpZW50RG9jdW1lbnQ6ICdDbGllbnREb2N1bWVudCcsXG4gIENsaWVudFRyYW5zYWN0aW9uOiAnQ2xpZW50VHJhbnNhY3Rpb24nLFxuICBUb2tlbjogJ1Rva2VuJyxcbiAgVG9rZW5DbGllbnRBcHByb3ZhbDogJ1Rva2VuQ2xpZW50QXBwcm92YWwnLFxuICBPcmRlcjogJ09yZGVyJyxcbiAgUXVhbGlmaWNhdGlvblByb2dyZXNzOiAnUXVhbGlmaWNhdGlvblByb2dyZXNzJyxcbiAgVG9rZW5BZ3JlZW1lbnQ6ICdUb2tlbkFncmVlbWVudCdcbn07XG5cbi8qKlxuICogVGhpcyBpcyBhIHN0dWIgUHJpc21hIENsaWVudCB0aGF0IHdpbGwgZXJyb3IgYXQgcnVudGltZSBpZiBjYWxsZWQuXG4gKi9cbmNsYXNzIFByaXNtYUNsaWVudCB7XG4gIGNvbnN0cnVjdG9yKCkge1xuICAgIHJldHVybiBuZXcgUHJveHkodGhpcywge1xuICAgICAgZ2V0KHRhcmdldCwgcHJvcCkge1xuICAgICAgICBsZXQgbWVzc2FnZVxuICAgICAgICBjb25zdCBydW50aW1lID0gZ2V0UnVudGltZSgpXG4gICAgICAgIGlmIChydW50aW1lLmlzRWRnZSkge1xuICAgICAgICAgIG1lc3NhZ2UgPSBgUHJpc21hQ2xpZW50IGlzIG5vdCBjb25maWd1cmVkIHRvIHJ1biBpbiAke3J1bnRpbWUucHJldHR5TmFtZX0uIEluIG9yZGVyIHRvIHJ1biBQcmlzbWEgQ2xpZW50IG9uIGVkZ2UgcnVudGltZSwgZWl0aGVyOlxuLSBVc2UgUHJpc21hIEFjY2VsZXJhdGU6IGh0dHBzOi8vcHJpcy5seS9kL2FjY2VsZXJhdGVcbi0gVXNlIERyaXZlciBBZGFwdGVyczogaHR0cHM6Ly9wcmlzLmx5L2QvZHJpdmVyLWFkYXB0ZXJzXG5gO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIG1lc3NhZ2UgPSAnUHJpc21hQ2xpZW50IGlzIHVuYWJsZSB0byBydW4gaW4gdGhpcyBicm93c2VyIGVudmlyb25tZW50LCBvciBoYXMgYmVlbiBidW5kbGVkIGZvciB0aGUgYnJvd3NlciAocnVubmluZyBpbiBgJyArIHJ1bnRpbWUucHJldHR5TmFtZSArICdgKS4nXG4gICAgICAgIH1cblxuICAgICAgICBtZXNzYWdlICs9IGBcbklmIHRoaXMgaXMgdW5leHBlY3RlZCwgcGxlYXNlIG9wZW4gYW4gaXNzdWU6IGh0dHBzOi8vcHJpcy5seS9wcmlzbWEtcHJpc21hLWJ1Zy1yZXBvcnRgXG5cbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKG1lc3NhZ2UpXG4gICAgICB9XG4gICAgfSlcbiAgfVxufVxuXG5leHBvcnRzLlByaXNtYUNsaWVudCA9IFByaXNtYUNsaWVudFxuXG5PYmplY3QuYXNzaWduKGV4cG9ydHMsIFByaXNtYSlcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.prisma/client/index-browser.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@prisma/client/index-browser.js":
/*!******************************************************!*\
  !*** ./node_modules/@prisma/client/index-browser.js ***!
  \******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("const prisma = __webpack_require__(/*! .prisma/client/index-browser */ \"(app-pages-browser)/./node_modules/.prisma/client/index-browser.js\")\n\nmodule.exports = prisma\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AcHJpc21hL2NsaWVudC9pbmRleC1icm93c2VyLmpzIiwibWFwcGluZ3MiOiJBQUFBLGVBQWUsbUJBQU8sQ0FBQyx3R0FBOEI7O0FBRXJEIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFx0b2tlbmRldi1uZXdyb29cXGFkbWluLXBhbmVsXFxub2RlX21vZHVsZXNcXEBwcmlzbWFcXGNsaWVudFxcaW5kZXgtYnJvd3Nlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBwcmlzbWEgPSByZXF1aXJlKCcucHJpc21hL2NsaWVudC9pbmRleC1icm93c2VyJylcblxubW9kdWxlLmV4cG9ydHMgPSBwcmlzbWFcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@prisma/client/index-browser.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@prisma/client/runtime/index-browser.js":
/*!**************************************************************!*\
  !*** ./node_modules/@prisma/client/runtime/index-browser.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("var pe=Object.defineProperty;var Xe=Object.getOwnPropertyDescriptor;var Ke=Object.getOwnPropertyNames;var Qe=Object.prototype.hasOwnProperty;var Ye=e=>{throw TypeError(e)};var Oe=(e,n)=>{for(var i in n)pe(e,i,{get:n[i],enumerable:!0})},xe=(e,n,i,t)=>{if(n&&typeof n==\"object\"||typeof n==\"function\")for(let r of Ke(n))!Qe.call(e,r)&&r!==i&&pe(e,r,{get:()=>n[r],enumerable:!(t=Xe(n,r))||t.enumerable});return e};var ze=e=>xe(pe({},\"__esModule\",{value:!0}),e);var ne=(e,n,i)=>n.has(e)?Ye(\"Cannot add the same private member more than once\"):n instanceof WeakSet?n.add(e):n.set(e,i);var ii={};Oe(ii,{Decimal:()=>Je,Public:()=>ge,getRuntime:()=>_e,makeStrictEnum:()=>qe,objectEnumValues:()=>Ae});module.exports=ze(ii);var ge={};Oe(ge,{validator:()=>Re});function Re(...e){return n=>n}var ie=Symbol(),me=new WeakMap,we=class{constructor(n){n===ie?me.set(this,\"Prisma.\".concat(this._getName())):me.set(this,\"new Prisma.\".concat(this._getNamespace(),\".\").concat(this._getName(),\"()\"))}_getName(){return this.constructor.name}toString(){return me.get(this)}},G=class extends we{_getNamespace(){return\"NullTypes\"}},Ne,J=class extends G{constructor(){super(...arguments);ne(this,Ne)}};Ne=new WeakMap;ke(J,\"DbNull\");var ve,X=class extends G{constructor(){super(...arguments);ne(this,ve)}};ve=new WeakMap;ke(X,\"JsonNull\");var Ee,K=class extends G{constructor(){super(...arguments);ne(this,Ee)}};Ee=new WeakMap;ke(K,\"AnyNull\");var Ae={classes:{DbNull:J,JsonNull:X,AnyNull:K},instances:{DbNull:new J(ie),JsonNull:new X(ie),AnyNull:new K(ie)}};function ke(e,n){Object.defineProperty(e,\"name\",{value:n,configurable:!0})}var ye=new Set([\"toJSON\",\"$$typeof\",\"asymmetricMatch\",Symbol.iterator,Symbol.toStringTag,Symbol.isConcatSpreadable,Symbol.toPrimitive]);function qe(e){return new Proxy(e,{get(n,i){if(i in n)return n[i];if(!ye.has(i))throw new TypeError(\"Invalid enum value: \".concat(String(i)))}})}var en=()=>{var e,n;return((n=(e=globalThis.process)==null?void 0:e.release)==null?void 0:n.name)===\"node\"},nn=()=>{var e,n;return!!globalThis.Bun||!!((n=(e=globalThis.process)==null?void 0:e.versions)!=null&&n.bun)},tn=()=>!!globalThis.Deno,rn=()=>typeof globalThis.Netlify==\"object\",sn=()=>typeof globalThis.EdgeRuntime==\"object\",on=()=>{var e;return((e=globalThis.navigator)==null?void 0:e.userAgent)===\"Cloudflare-Workers\"};function un(){var i;return(i=[[rn,\"netlify\"],[sn,\"edge-light\"],[on,\"workerd\"],[tn,\"deno\"],[nn,\"bun\"],[en,\"node\"]].flatMap(t=>t[0]()?[t[1]]:[]).at(0))!=null?i:\"\"}var fn={node:\"Node.js\",workerd:\"Cloudflare Workers\",deno:\"Deno and Deno Deploy\",netlify:\"Netlify Edge Functions\",\"edge-light\":\"Edge Runtime (Vercel Edge Functions, Vercel Edge Middleware, Next.js (Pages Router) Edge API Routes, Next.js (App Router) Edge Route Handlers or Next.js Middleware)\"};function _e(){let e=un();return{id:e,prettyName:fn[e]||e,isEdge:[\"workerd\",\"deno\",\"netlify\",\"edge-light\"].includes(e)}}var V=9e15,H=1e9,Se=\"0123456789abcdef\",se=\"2.3025850929940456840179914546843642076011014886287729760333279009675726096773524802359972050895982983419677840422862486334095254650828067566662873690987816894829072083255546808437998948262331985283935053089653777326288461633662222876982198867465436674744042432743651550489343149393914796194044002221051017141748003688084012647080685567743216228355220114804663715659121373450747856947683463616792101806445070648000277502684916746550586856935673420670581136429224554405758925724208241314695689016758940256776311356919292033376587141660230105703089634572075440370847469940168269282808481184289314848524948644871927809676271275775397027668605952496716674183485704422507197965004714951050492214776567636938662976979522110718264549734772662425709429322582798502585509785265383207606726317164309505995087807523710333101197857547331541421808427543863591778117054309827482385045648019095610299291824318237525357709750539565187697510374970888692180205189339507238539205144634197265287286965110862571492198849978748873771345686209167058\",oe=\"3.1415926535897932384626433832795028841971693993751058209749445923078164062862089986280348253421170679821480865132823066470938446095505822317253594081284811174502841027019385211055596446229489549303819644288109756659334461284756482337867831652712019091456485669234603486104543266482133936072602491412737245870066063155881748815209209628292540917153643678925903600113305305488204665213841469519415116094330572703657595919530921861173819326117931051185480744623799627495673518857527248912279381830119491298336733624406566430860213949463952247371907021798609437027705392171762931767523846748184676694051320005681271452635608277857713427577896091736371787214684409012249534301465495853710507922796892589235420199561121290219608640344181598136297747713099605187072113499999983729780499510597317328160963185950244594553469083026425223082533446850352619311881710100031378387528865875332083814206171776691473035982534904287554687311595628638823537875937519577818577805321712268066130019278766111959092164201989380952572010654858632789\",Me={precision:20,rounding:4,modulo:1,toExpNeg:-7,toExpPos:21,minE:-V,maxE:V,crypto:!1},Le,Z,w=!0,fe=\"[DecimalError] \",$=fe+\"Invalid argument: \",Ie=fe+\"Precision limit exceeded\",Ze=fe+\"crypto unavailable\",Ue=\"[object Decimal]\",R=Math.floor,C=Math.pow,cn=/^0b([01]+(\\.[01]*)?|\\.[01]+)(p[+-]?\\d+)?$/i,ln=/^0x([0-9a-f]+(\\.[0-9a-f]*)?|\\.[0-9a-f]+)(p[+-]?\\d+)?$/i,an=/^0o([0-7]+(\\.[0-7]*)?|\\.[0-7]+)(p[+-]?\\d+)?$/i,Be=/^(\\d+(\\.\\d*)?|\\.\\d+)(e[+-]?\\d+)?$/i,D=1e7,m=7,dn=9007199254740991,hn=se.length-1,Ce=oe.length-1,h={toStringTag:Ue};h.absoluteValue=h.abs=function(){var e=new this.constructor(this);return e.s<0&&(e.s=1),p(e)};h.ceil=function(){return p(new this.constructor(this),this.e+1,2)};h.clampedTo=h.clamp=function(e,n){var i,t=this,r=t.constructor;if(e=new r(e),n=new r(n),!e.s||!n.s)return new r(NaN);if(e.gt(n))throw Error($+n);return i=t.cmp(e),i<0?e:t.cmp(n)>0?n:new r(t)};h.comparedTo=h.cmp=function(e){var n,i,t,r,s=this,o=s.d,u=(e=new s.constructor(e)).d,c=s.s,f=e.s;if(!o||!u)return!c||!f?NaN:c!==f?c:o===u?0:!o^c<0?1:-1;if(!o[0]||!u[0])return o[0]?c:u[0]?-f:0;if(c!==f)return c;if(s.e!==e.e)return s.e>e.e^c<0?1:-1;for(t=o.length,r=u.length,n=0,i=t<r?t:r;n<i;++n)if(o[n]!==u[n])return o[n]>u[n]^c<0?1:-1;return t===r?0:t>r^c<0?1:-1};h.cosine=h.cos=function(){var e,n,i=this,t=i.constructor;return i.d?i.d[0]?(e=t.precision,n=t.rounding,t.precision=e+Math.max(i.e,i.sd())+m,t.rounding=1,i=pn(t,We(t,i)),t.precision=e,t.rounding=n,p(Z==2||Z==3?i.neg():i,e,n,!0)):new t(1):new t(NaN)};h.cubeRoot=h.cbrt=function(){var e,n,i,t,r,s,o,u,c,f,l=this,a=l.constructor;if(!l.isFinite()||l.isZero())return new a(l);for(w=!1,s=l.s*C(l.s*l,1/3),!s||Math.abs(s)==1/0?(i=b(l.d),e=l.e,(s=(e-i.length+1)%3)&&(i+=s==1||s==-2?\"0\":\"00\"),s=C(i,1/3),e=R((e+1)/3)-(e%3==(e<0?-1:2)),s==1/0?i=\"5e\"+e:(i=s.toExponential(),i=i.slice(0,i.indexOf(\"e\")+1)+e),t=new a(i),t.s=l.s):t=new a(s.toString()),o=(e=a.precision)+3;;)if(u=t,c=u.times(u).times(u),f=c.plus(l),t=k(f.plus(l).times(u),f.plus(c),o+2,1),b(u.d).slice(0,o)===(i=b(t.d)).slice(0,o))if(i=i.slice(o-3,o+1),i==\"9999\"||!r&&i==\"4999\"){if(!r&&(p(u,e+1,0),u.times(u).times(u).eq(l))){t=u;break}o+=4,r=1}else{(!+i||!+i.slice(1)&&i.charAt(0)==\"5\")&&(p(t,e+1,1),n=!t.times(t).times(t).eq(l));break}return w=!0,p(t,e,a.rounding,n)};h.decimalPlaces=h.dp=function(){var e,n=this.d,i=NaN;if(n){if(e=n.length-1,i=(e-R(this.e/m))*m,e=n[e],e)for(;e%10==0;e/=10)i--;i<0&&(i=0)}return i};h.dividedBy=h.div=function(e){return k(this,new this.constructor(e))};h.dividedToIntegerBy=h.divToInt=function(e){var n=this,i=n.constructor;return p(k(n,new i(e),0,1,1),i.precision,i.rounding)};h.equals=h.eq=function(e){return this.cmp(e)===0};h.floor=function(){return p(new this.constructor(this),this.e+1,3)};h.greaterThan=h.gt=function(e){return this.cmp(e)>0};h.greaterThanOrEqualTo=h.gte=function(e){var n=this.cmp(e);return n==1||n===0};h.hyperbolicCosine=h.cosh=function(){var e,n,i,t,r,s=this,o=s.constructor,u=new o(1);if(!s.isFinite())return new o(s.s?1/0:NaN);if(s.isZero())return u;i=o.precision,t=o.rounding,o.precision=i+Math.max(s.e,s.sd())+4,o.rounding=1,r=s.d.length,r<32?(e=Math.ceil(r/3),n=(1/le(4,e)).toString()):(e=16,n=\"2.3283064365386962890625e-10\"),s=j(o,1,s.times(n),new o(1),!0);for(var c,f=e,l=new o(8);f--;)c=s.times(s),s=u.minus(c.times(l.minus(c.times(l))));return p(s,o.precision=i,o.rounding=t,!0)};h.hyperbolicSine=h.sinh=function(){var e,n,i,t,r=this,s=r.constructor;if(!r.isFinite()||r.isZero())return new s(r);if(n=s.precision,i=s.rounding,s.precision=n+Math.max(r.e,r.sd())+4,s.rounding=1,t=r.d.length,t<3)r=j(s,2,r,r,!0);else{e=1.4*Math.sqrt(t),e=e>16?16:e|0,r=r.times(1/le(5,e)),r=j(s,2,r,r,!0);for(var o,u=new s(5),c=new s(16),f=new s(20);e--;)o=r.times(r),r=r.times(u.plus(o.times(c.times(o).plus(f))))}return s.precision=n,s.rounding=i,p(r,n,i,!0)};h.hyperbolicTangent=h.tanh=function(){var e,n,i=this,t=i.constructor;return i.isFinite()?i.isZero()?new t(i):(e=t.precision,n=t.rounding,t.precision=e+7,t.rounding=1,k(i.sinh(),i.cosh(),t.precision=e,t.rounding=n)):new t(i.s)};h.inverseCosine=h.acos=function(){var e=this,n=e.constructor,i=e.abs().cmp(1),t=n.precision,r=n.rounding;return i!==-1?i===0?e.isNeg()?F(n,t,r):new n(0):new n(NaN):e.isZero()?F(n,t+4,r).times(.5):(n.precision=t+6,n.rounding=1,e=new n(1).minus(e).div(e.plus(1)).sqrt().atan(),n.precision=t,n.rounding=r,e.times(2))};h.inverseHyperbolicCosine=h.acosh=function(){var e,n,i=this,t=i.constructor;return i.lte(1)?new t(i.eq(1)?0:NaN):i.isFinite()?(e=t.precision,n=t.rounding,t.precision=e+Math.max(Math.abs(i.e),i.sd())+4,t.rounding=1,w=!1,i=i.times(i).minus(1).sqrt().plus(i),w=!0,t.precision=e,t.rounding=n,i.ln()):new t(i)};h.inverseHyperbolicSine=h.asinh=function(){var e,n,i=this,t=i.constructor;return!i.isFinite()||i.isZero()?new t(i):(e=t.precision,n=t.rounding,t.precision=e+2*Math.max(Math.abs(i.e),i.sd())+6,t.rounding=1,w=!1,i=i.times(i).plus(1).sqrt().plus(i),w=!0,t.precision=e,t.rounding=n,i.ln())};h.inverseHyperbolicTangent=h.atanh=function(){var e,n,i,t,r=this,s=r.constructor;return r.isFinite()?r.e>=0?new s(r.abs().eq(1)?r.s/0:r.isZero()?r:NaN):(e=s.precision,n=s.rounding,t=r.sd(),Math.max(t,e)<2*-r.e-1?p(new s(r),e,n,!0):(s.precision=i=t-r.e,r=k(r.plus(1),new s(1).minus(r),i+e,1),s.precision=e+4,s.rounding=1,r=r.ln(),s.precision=e,s.rounding=n,r.times(.5))):new s(NaN)};h.inverseSine=h.asin=function(){var e,n,i,t,r=this,s=r.constructor;return r.isZero()?new s(r):(n=r.abs().cmp(1),i=s.precision,t=s.rounding,n!==-1?n===0?(e=F(s,i+4,t).times(.5),e.s=r.s,e):new s(NaN):(s.precision=i+6,s.rounding=1,r=r.div(new s(1).minus(r.times(r)).sqrt().plus(1)).atan(),s.precision=i,s.rounding=t,r.times(2)))};h.inverseTangent=h.atan=function(){var e,n,i,t,r,s,o,u,c,f=this,l=f.constructor,a=l.precision,d=l.rounding;if(f.isFinite()){if(f.isZero())return new l(f);if(f.abs().eq(1)&&a+4<=Ce)return o=F(l,a+4,d).times(.25),o.s=f.s,o}else{if(!f.s)return new l(NaN);if(a+4<=Ce)return o=F(l,a+4,d).times(.5),o.s=f.s,o}for(l.precision=u=a+10,l.rounding=1,i=Math.min(28,u/m+2|0),e=i;e;--e)f=f.div(f.times(f).plus(1).sqrt().plus(1));for(w=!1,n=Math.ceil(u/m),t=1,c=f.times(f),o=new l(f),r=f;e!==-1;)if(r=r.times(c),s=o.minus(r.div(t+=2)),r=r.times(c),o=s.plus(r.div(t+=2)),o.d[n]!==void 0)for(e=n;o.d[e]===s.d[e]&&e--;);return i&&(o=o.times(2<<i-1)),w=!0,p(o,l.precision=a,l.rounding=d,!0)};h.isFinite=function(){return!!this.d};h.isInteger=h.isInt=function(){return!!this.d&&R(this.e/m)>this.d.length-2};h.isNaN=function(){return!this.s};h.isNegative=h.isNeg=function(){return this.s<0};h.isPositive=h.isPos=function(){return this.s>0};h.isZero=function(){return!!this.d&&this.d[0]===0};h.lessThan=h.lt=function(e){return this.cmp(e)<0};h.lessThanOrEqualTo=h.lte=function(e){return this.cmp(e)<1};h.logarithm=h.log=function(e){var n,i,t,r,s,o,u,c,f=this,l=f.constructor,a=l.precision,d=l.rounding,g=5;if(e==null)e=new l(10),n=!0;else{if(e=new l(e),i=e.d,e.s<0||!i||!i[0]||e.eq(1))return new l(NaN);n=e.eq(10)}if(i=f.d,f.s<0||!i||!i[0]||f.eq(1))return new l(i&&!i[0]?-1/0:f.s!=1?NaN:i?0:1/0);if(n)if(i.length>1)s=!0;else{for(r=i[0];r%10===0;)r/=10;s=r!==1}if(w=!1,u=a+g,o=B(f,u),t=n?ue(l,u+10):B(e,u),c=k(o,t,u,1),Q(c.d,r=a,d))do if(u+=10,o=B(f,u),t=n?ue(l,u+10):B(e,u),c=k(o,t,u,1),!s){+b(c.d).slice(r+1,r+15)+1==1e14&&(c=p(c,a+1,0));break}while(Q(c.d,r+=10,d));return w=!0,p(c,a,d)};h.minus=h.sub=function(e){var n,i,t,r,s,o,u,c,f,l,a,d,g=this,v=g.constructor;if(e=new v(e),!g.d||!e.d)return!g.s||!e.s?e=new v(NaN):g.d?e.s=-e.s:e=new v(e.d||g.s!==e.s?g:NaN),e;if(g.s!=e.s)return e.s=-e.s,g.plus(e);if(f=g.d,d=e.d,u=v.precision,c=v.rounding,!f[0]||!d[0]){if(d[0])e.s=-e.s;else if(f[0])e=new v(g);else return new v(c===3?-0:0);return w?p(e,u,c):e}if(i=R(e.e/m),l=R(g.e/m),f=f.slice(),s=l-i,s){for(a=s<0,a?(n=f,s=-s,o=d.length):(n=d,i=l,o=f.length),t=Math.max(Math.ceil(u/m),o)+2,s>t&&(s=t,n.length=1),n.reverse(),t=s;t--;)n.push(0);n.reverse()}else{for(t=f.length,o=d.length,a=t<o,a&&(o=t),t=0;t<o;t++)if(f[t]!=d[t]){a=f[t]<d[t];break}s=0}for(a&&(n=f,f=d,d=n,e.s=-e.s),o=f.length,t=d.length-o;t>0;--t)f[o++]=0;for(t=d.length;t>s;){if(f[--t]<d[t]){for(r=t;r&&f[--r]===0;)f[r]=D-1;--f[r],f[t]+=D}f[t]-=d[t]}for(;f[--o]===0;)f.pop();for(;f[0]===0;f.shift())--i;return f[0]?(e.d=f,e.e=ce(f,i),w?p(e,u,c):e):new v(c===3?-0:0)};h.modulo=h.mod=function(e){var n,i=this,t=i.constructor;return e=new t(e),!i.d||!e.s||e.d&&!e.d[0]?new t(NaN):!e.d||i.d&&!i.d[0]?p(new t(i),t.precision,t.rounding):(w=!1,t.modulo==9?(n=k(i,e.abs(),0,3,1),n.s*=e.s):n=k(i,e,0,t.modulo,1),n=n.times(e),w=!0,i.minus(n))};h.naturalExponential=h.exp=function(){return be(this)};h.naturalLogarithm=h.ln=function(){return B(this)};h.negated=h.neg=function(){var e=new this.constructor(this);return e.s=-e.s,p(e)};h.plus=h.add=function(e){var n,i,t,r,s,o,u,c,f,l,a=this,d=a.constructor;if(e=new d(e),!a.d||!e.d)return!a.s||!e.s?e=new d(NaN):a.d||(e=new d(e.d||a.s===e.s?a:NaN)),e;if(a.s!=e.s)return e.s=-e.s,a.minus(e);if(f=a.d,l=e.d,u=d.precision,c=d.rounding,!f[0]||!l[0])return l[0]||(e=new d(a)),w?p(e,u,c):e;if(s=R(a.e/m),t=R(e.e/m),f=f.slice(),r=s-t,r){for(r<0?(i=f,r=-r,o=l.length):(i=l,t=s,o=f.length),s=Math.ceil(u/m),o=s>o?s+1:o+1,r>o&&(r=o,i.length=1),i.reverse();r--;)i.push(0);i.reverse()}for(o=f.length,r=l.length,o-r<0&&(r=o,i=l,l=f,f=i),n=0;r;)n=(f[--r]=f[r]+l[r]+n)/D|0,f[r]%=D;for(n&&(f.unshift(n),++t),o=f.length;f[--o]==0;)f.pop();return e.d=f,e.e=ce(f,t),w?p(e,u,c):e};h.precision=h.sd=function(e){var n,i=this;if(e!==void 0&&e!==!!e&&e!==1&&e!==0)throw Error($+e);return i.d?(n=$e(i.d),e&&i.e+1>n&&(n=i.e+1)):n=NaN,n};h.round=function(){var e=this,n=e.constructor;return p(new n(e),e.e+1,n.rounding)};h.sine=h.sin=function(){var e,n,i=this,t=i.constructor;return i.isFinite()?i.isZero()?new t(i):(e=t.precision,n=t.rounding,t.precision=e+Math.max(i.e,i.sd())+m,t.rounding=1,i=mn(t,We(t,i)),t.precision=e,t.rounding=n,p(Z>2?i.neg():i,e,n,!0)):new t(NaN)};h.squareRoot=h.sqrt=function(){var e,n,i,t,r,s,o=this,u=o.d,c=o.e,f=o.s,l=o.constructor;if(f!==1||!u||!u[0])return new l(!f||f<0&&(!u||u[0])?NaN:u?o:1/0);for(w=!1,f=Math.sqrt(+o),f==0||f==1/0?(n=b(u),(n.length+c)%2==0&&(n+=\"0\"),f=Math.sqrt(n),c=R((c+1)/2)-(c<0||c%2),f==1/0?n=\"5e\"+c:(n=f.toExponential(),n=n.slice(0,n.indexOf(\"e\")+1)+c),t=new l(n)):t=new l(f.toString()),i=(c=l.precision)+3;;)if(s=t,t=s.plus(k(o,s,i+2,1)).times(.5),b(s.d).slice(0,i)===(n=b(t.d)).slice(0,i))if(n=n.slice(i-3,i+1),n==\"9999\"||!r&&n==\"4999\"){if(!r&&(p(s,c+1,0),s.times(s).eq(o))){t=s;break}i+=4,r=1}else{(!+n||!+n.slice(1)&&n.charAt(0)==\"5\")&&(p(t,c+1,1),e=!t.times(t).eq(o));break}return w=!0,p(t,c,l.rounding,e)};h.tangent=h.tan=function(){var e,n,i=this,t=i.constructor;return i.isFinite()?i.isZero()?new t(i):(e=t.precision,n=t.rounding,t.precision=e+10,t.rounding=1,i=i.sin(),i.s=1,i=k(i,new t(1).minus(i.times(i)).sqrt(),e+10,0),t.precision=e,t.rounding=n,p(Z==2||Z==4?i.neg():i,e,n,!0)):new t(NaN)};h.times=h.mul=function(e){var n,i,t,r,s,o,u,c,f,l=this,a=l.constructor,d=l.d,g=(e=new a(e)).d;if(e.s*=l.s,!d||!d[0]||!g||!g[0])return new a(!e.s||d&&!d[0]&&!g||g&&!g[0]&&!d?NaN:!d||!g?e.s/0:e.s*0);for(i=R(l.e/m)+R(e.e/m),c=d.length,f=g.length,c<f&&(s=d,d=g,g=s,o=c,c=f,f=o),s=[],o=c+f,t=o;t--;)s.push(0);for(t=f;--t>=0;){for(n=0,r=c+t;r>t;)u=s[r]+g[t]*d[r-t-1]+n,s[r--]=u%D|0,n=u/D|0;s[r]=(s[r]+n)%D|0}for(;!s[--o];)s.pop();return n?++i:s.shift(),e.d=s,e.e=ce(s,i),w?p(e,a.precision,a.rounding):e};h.toBinary=function(e,n){return Pe(this,2,e,n)};h.toDecimalPlaces=h.toDP=function(e,n){var i=this,t=i.constructor;return i=new t(i),e===void 0?i:(q(e,0,H),n===void 0?n=t.rounding:q(n,0,8),p(i,e+i.e+1,n))};h.toExponential=function(e,n){var i,t=this,r=t.constructor;return e===void 0?i=L(t,!0):(q(e,0,H),n===void 0?n=r.rounding:q(n,0,8),t=p(new r(t),e+1,n),i=L(t,!0,e+1)),t.isNeg()&&!t.isZero()?\"-\"+i:i};h.toFixed=function(e,n){var i,t,r=this,s=r.constructor;return e===void 0?i=L(r):(q(e,0,H),n===void 0?n=s.rounding:q(n,0,8),t=p(new s(r),e+r.e+1,n),i=L(t,!1,e+t.e+1)),r.isNeg()&&!r.isZero()?\"-\"+i:i};h.toFraction=function(e){var n,i,t,r,s,o,u,c,f,l,a,d,g=this,v=g.d,N=g.constructor;if(!v)return new N(g);if(f=i=new N(1),t=c=new N(0),n=new N(t),s=n.e=$e(v)-g.e-1,o=s%m,n.d[0]=C(10,o<0?m+o:o),e==null)e=s>0?n:f;else{if(u=new N(e),!u.isInt()||u.lt(f))throw Error($+u);e=u.gt(n)?s>0?n:f:u}for(w=!1,u=new N(b(v)),l=N.precision,N.precision=s=v.length*m*2;a=k(u,n,0,1,1),r=i.plus(a.times(t)),r.cmp(e)!=1;)i=t,t=r,r=f,f=c.plus(a.times(r)),c=r,r=n,n=u.minus(a.times(r)),u=r;return r=k(e.minus(i),t,0,1,1),c=c.plus(r.times(f)),i=i.plus(r.times(t)),c.s=f.s=g.s,d=k(f,t,s,1).minus(g).abs().cmp(k(c,i,s,1).minus(g).abs())<1?[f,t]:[c,i],N.precision=l,w=!0,d};h.toHexadecimal=h.toHex=function(e,n){return Pe(this,16,e,n)};h.toNearest=function(e,n){var i=this,t=i.constructor;if(i=new t(i),e==null){if(!i.d)return i;e=new t(1),n=t.rounding}else{if(e=new t(e),n===void 0?n=t.rounding:q(n,0,8),!i.d)return e.s?i:e;if(!e.d)return e.s&&(e.s=i.s),e}return e.d[0]?(w=!1,i=k(i,e,0,n,1).times(e),w=!0,p(i)):(e.s=i.s,i=e),i};h.toNumber=function(){return+this};h.toOctal=function(e,n){return Pe(this,8,e,n)};h.toPower=h.pow=function(e){var n,i,t,r,s,o,u=this,c=u.constructor,f=+(e=new c(e));if(!u.d||!e.d||!u.d[0]||!e.d[0])return new c(C(+u,f));if(u=new c(u),u.eq(1))return u;if(t=c.precision,s=c.rounding,e.eq(1))return p(u,t,s);if(n=R(e.e/m),n>=e.d.length-1&&(i=f<0?-f:f)<=dn)return r=He(c,u,i,t),e.s<0?new c(1).div(r):p(r,t,s);if(o=u.s,o<0){if(n<e.d.length-1)return new c(NaN);if((e.d[n]&1)==0&&(o=1),u.e==0&&u.d[0]==1&&u.d.length==1)return u.s=o,u}return i=C(+u,f),n=i==0||!isFinite(i)?R(f*(Math.log(\"0.\"+b(u.d))/Math.LN10+u.e+1)):new c(i+\"\").e,n>c.maxE+1||n<c.minE-1?new c(n>0?o/0:0):(w=!1,c.rounding=u.s=1,i=Math.min(12,(n+\"\").length),r=be(e.times(B(u,t+i)),t),r.d&&(r=p(r,t+5,1),Q(r.d,t,s)&&(n=t+10,r=p(be(e.times(B(u,n+i)),n),n+5,1),+b(r.d).slice(t+1,t+15)+1==1e14&&(r=p(r,t+1,0)))),r.s=o,w=!0,c.rounding=s,p(r,t,s))};h.toPrecision=function(e,n){var i,t=this,r=t.constructor;return e===void 0?i=L(t,t.e<=r.toExpNeg||t.e>=r.toExpPos):(q(e,1,H),n===void 0?n=r.rounding:q(n,0,8),t=p(new r(t),e,n),i=L(t,e<=t.e||t.e<=r.toExpNeg,e)),t.isNeg()&&!t.isZero()?\"-\"+i:i};h.toSignificantDigits=h.toSD=function(e,n){var i=this,t=i.constructor;return e===void 0?(e=t.precision,n=t.rounding):(q(e,1,H),n===void 0?n=t.rounding:q(n,0,8)),p(new t(i),e,n)};h.toString=function(){var e=this,n=e.constructor,i=L(e,e.e<=n.toExpNeg||e.e>=n.toExpPos);return e.isNeg()&&!e.isZero()?\"-\"+i:i};h.truncated=h.trunc=function(){return p(new this.constructor(this),this.e+1,1)};h.valueOf=h.toJSON=function(){var e=this,n=e.constructor,i=L(e,e.e<=n.toExpNeg||e.e>=n.toExpPos);return e.isNeg()?\"-\"+i:i};function b(e){var n,i,t,r=e.length-1,s=\"\",o=e[0];if(r>0){for(s+=o,n=1;n<r;n++)t=e[n]+\"\",i=m-t.length,i&&(s+=U(i)),s+=t;o=e[n],t=o+\"\",i=m-t.length,i&&(s+=U(i))}else if(o===0)return\"0\";for(;o%10===0;)o/=10;return s+o}function q(e,n,i){if(e!==~~e||e<n||e>i)throw Error($+e)}function Q(e,n,i,t){var r,s,o,u;for(s=e[0];s>=10;s/=10)--n;return--n<0?(n+=m,r=0):(r=Math.ceil((n+1)/m),n%=m),s=C(10,m-n),u=e[r]%s|0,t==null?n<3?(n==0?u=u/100|0:n==1&&(u=u/10|0),o=i<4&&u==99999||i>3&&u==49999||u==5e4||u==0):o=(i<4&&u+1==s||i>3&&u+1==s/2)&&(e[r+1]/s/100|0)==C(10,n-2)-1||(u==s/2||u==0)&&(e[r+1]/s/100|0)==0:n<4?(n==0?u=u/1e3|0:n==1?u=u/100|0:n==2&&(u=u/10|0),o=(t||i<4)&&u==9999||!t&&i>3&&u==4999):o=((t||i<4)&&u+1==s||!t&&i>3&&u+1==s/2)&&(e[r+1]/s/1e3|0)==C(10,n-3)-1,o}function te(e,n,i){for(var t,r=[0],s,o=0,u=e.length;o<u;){for(s=r.length;s--;)r[s]*=n;for(r[0]+=Se.indexOf(e.charAt(o++)),t=0;t<r.length;t++)r[t]>i-1&&(r[t+1]===void 0&&(r[t+1]=0),r[t+1]+=r[t]/i|0,r[t]%=i)}return r.reverse()}function pn(e,n){var i,t,r;if(n.isZero())return n;t=n.d.length,t<32?(i=Math.ceil(t/3),r=(1/le(4,i)).toString()):(i=16,r=\"2.3283064365386962890625e-10\"),e.precision+=i,n=j(e,1,n.times(r),new e(1));for(var s=i;s--;){var o=n.times(n);n=o.times(o).minus(o).times(8).plus(1)}return e.precision-=i,n}var k=function(){function e(t,r,s){var o,u=0,c=t.length;for(t=t.slice();c--;)o=t[c]*r+u,t[c]=o%s|0,u=o/s|0;return u&&t.unshift(u),t}function n(t,r,s,o){var u,c;if(s!=o)c=s>o?1:-1;else for(u=c=0;u<s;u++)if(t[u]!=r[u]){c=t[u]>r[u]?1:-1;break}return c}function i(t,r,s,o){for(var u=0;s--;)t[s]-=u,u=t[s]<r[s]?1:0,t[s]=u*o+t[s]-r[s];for(;!t[0]&&t.length>1;)t.shift()}return function(t,r,s,o,u,c){var f,l,a,d,g,v,N,A,M,_,E,P,x,I,ae,z,W,de,T,y,ee=t.constructor,he=t.s==r.s?1:-1,O=t.d,S=r.d;if(!O||!O[0]||!S||!S[0])return new ee(!t.s||!r.s||(O?S&&O[0]==S[0]:!S)?NaN:O&&O[0]==0||!S?he*0:he/0);for(c?(g=1,l=t.e-r.e):(c=D,g=m,l=R(t.e/g)-R(r.e/g)),T=S.length,W=O.length,M=new ee(he),_=M.d=[],a=0;S[a]==(O[a]||0);a++);if(S[a]>(O[a]||0)&&l--,s==null?(I=s=ee.precision,o=ee.rounding):u?I=s+(t.e-r.e)+1:I=s,I<0)_.push(1),v=!0;else{if(I=I/g+2|0,a=0,T==1){for(d=0,S=S[0],I++;(a<W||d)&&I--;a++)ae=d*c+(O[a]||0),_[a]=ae/S|0,d=ae%S|0;v=d||a<W}else{for(d=c/(S[0]+1)|0,d>1&&(S=e(S,d,c),O=e(O,d,c),T=S.length,W=O.length),z=T,E=O.slice(0,T),P=E.length;P<T;)E[P++]=0;y=S.slice(),y.unshift(0),de=S[0],S[1]>=c/2&&++de;do d=0,f=n(S,E,T,P),f<0?(x=E[0],T!=P&&(x=x*c+(E[1]||0)),d=x/de|0,d>1?(d>=c&&(d=c-1),N=e(S,d,c),A=N.length,P=E.length,f=n(N,E,A,P),f==1&&(d--,i(N,T<A?y:S,A,c))):(d==0&&(f=d=1),N=S.slice()),A=N.length,A<P&&N.unshift(0),i(E,N,P,c),f==-1&&(P=E.length,f=n(S,E,T,P),f<1&&(d++,i(E,T<P?y:S,P,c))),P=E.length):f===0&&(d++,E=[0]),_[a++]=d,f&&E[0]?E[P++]=O[z]||0:(E=[O[z]],P=1);while((z++<W||E[0]!==void 0)&&I--);v=E[0]!==void 0}_[0]||_.shift()}if(g==1)M.e=l,Le=v;else{for(a=1,d=_[0];d>=10;d/=10)a++;M.e=a+l*g-1,p(M,u?s+M.e+1:s,o,v)}return M}}();function p(e,n,i,t){var r,s,o,u,c,f,l,a,d,g=e.constructor;e:if(n!=null){if(a=e.d,!a)return e;for(r=1,u=a[0];u>=10;u/=10)r++;if(s=n-r,s<0)s+=m,o=n,l=a[d=0],c=l/C(10,r-o-1)%10|0;else if(d=Math.ceil((s+1)/m),u=a.length,d>=u)if(t){for(;u++<=d;)a.push(0);l=c=0,r=1,s%=m,o=s-m+1}else break e;else{for(l=u=a[d],r=1;u>=10;u/=10)r++;s%=m,o=s-m+r,c=o<0?0:l/C(10,r-o-1)%10|0}if(t=t||n<0||a[d+1]!==void 0||(o<0?l:l%C(10,r-o-1)),f=i<4?(c||t)&&(i==0||i==(e.s<0?3:2)):c>5||c==5&&(i==4||t||i==6&&(s>0?o>0?l/C(10,r-o):0:a[d-1])%10&1||i==(e.s<0?8:7)),n<1||!a[0])return a.length=0,f?(n-=e.e+1,a[0]=C(10,(m-n%m)%m),e.e=-n||0):a[0]=e.e=0,e;if(s==0?(a.length=d,u=1,d--):(a.length=d+1,u=C(10,m-s),a[d]=o>0?(l/C(10,r-o)%C(10,o)|0)*u:0),f)for(;;)if(d==0){for(s=1,o=a[0];o>=10;o/=10)s++;for(o=a[0]+=u,u=1;o>=10;o/=10)u++;s!=u&&(e.e++,a[0]==D&&(a[0]=1));break}else{if(a[d]+=u,a[d]!=D)break;a[d--]=0,u=1}for(s=a.length;a[--s]===0;)a.pop()}return w&&(e.e>g.maxE?(e.d=null,e.e=NaN):e.e<g.minE&&(e.e=0,e.d=[0])),e}function L(e,n,i){if(!e.isFinite())return je(e);var t,r=e.e,s=b(e.d),o=s.length;return n?(i&&(t=i-o)>0?s=s.charAt(0)+\".\"+s.slice(1)+U(t):o>1&&(s=s.charAt(0)+\".\"+s.slice(1)),s=s+(e.e<0?\"e\":\"e+\")+e.e):r<0?(s=\"0.\"+U(-r-1)+s,i&&(t=i-o)>0&&(s+=U(t))):r>=o?(s+=U(r+1-o),i&&(t=i-r-1)>0&&(s=s+\".\"+U(t))):((t=r+1)<o&&(s=s.slice(0,t)+\".\"+s.slice(t)),i&&(t=i-o)>0&&(r+1===o&&(s+=\".\"),s+=U(t))),s}function ce(e,n){var i=e[0];for(n*=m;i>=10;i/=10)n++;return n}function ue(e,n,i){if(n>hn)throw w=!0,i&&(e.precision=i),Error(Ie);return p(new e(se),n,1,!0)}function F(e,n,i){if(n>Ce)throw Error(Ie);return p(new e(oe),n,i,!0)}function $e(e){var n=e.length-1,i=n*m+1;if(n=e[n],n){for(;n%10==0;n/=10)i--;for(n=e[0];n>=10;n/=10)i++}return i}function U(e){for(var n=\"\";e--;)n+=\"0\";return n}function He(e,n,i,t){var r,s=new e(1),o=Math.ceil(t/m+4);for(w=!1;;){if(i%2&&(s=s.times(n),De(s.d,o)&&(r=!0)),i=R(i/2),i===0){i=s.d.length-1,r&&s.d[i]===0&&++s.d[i];break}n=n.times(n),De(n.d,o)}return w=!0,s}function Te(e){return e.d[e.d.length-1]&1}function Ve(e,n,i){for(var t,r,s=new e(n[0]),o=0;++o<n.length;){if(r=new e(n[o]),!r.s){s=r;break}t=s.cmp(r),(t===i||t===0&&s.s===i)&&(s=r)}return s}function be(e,n){var i,t,r,s,o,u,c,f=0,l=0,a=0,d=e.constructor,g=d.rounding,v=d.precision;if(!e.d||!e.d[0]||e.e>17)return new d(e.d?e.d[0]?e.s<0?0:1/0:1:e.s?e.s<0?0:e:NaN);for(n==null?(w=!1,c=v):c=n,u=new d(.03125);e.e>-2;)e=e.times(u),a+=5;for(t=Math.log(C(2,a))/Math.LN10*2+5|0,c+=t,i=s=o=new d(1),d.precision=c;;){if(s=p(s.times(e),c,1),i=i.times(++l),u=o.plus(k(s,i,c,1)),b(u.d).slice(0,c)===b(o.d).slice(0,c)){for(r=a;r--;)o=p(o.times(o),c,1);if(n==null)if(f<3&&Q(o.d,c-t,g,f))d.precision=c+=10,i=s=u=new d(1),l=0,f++;else return p(o,d.precision=v,g,w=!0);else return d.precision=v,o}o=u}}function B(e,n){var i,t,r,s,o,u,c,f,l,a,d,g=1,v=10,N=e,A=N.d,M=N.constructor,_=M.rounding,E=M.precision;if(N.s<0||!A||!A[0]||!N.e&&A[0]==1&&A.length==1)return new M(A&&!A[0]?-1/0:N.s!=1?NaN:A?0:N);if(n==null?(w=!1,l=E):l=n,M.precision=l+=v,i=b(A),t=i.charAt(0),Math.abs(s=N.e)<15e14){for(;t<7&&t!=1||t==1&&i.charAt(1)>3;)N=N.times(e),i=b(N.d),t=i.charAt(0),g++;s=N.e,t>1?(N=new M(\"0.\"+i),s++):N=new M(t+\".\"+i.slice(1))}else return f=ue(M,l+2,E).times(s+\"\"),N=B(new M(t+\".\"+i.slice(1)),l-v).plus(f),M.precision=E,n==null?p(N,E,_,w=!0):N;for(a=N,c=o=N=k(N.minus(1),N.plus(1),l,1),d=p(N.times(N),l,1),r=3;;){if(o=p(o.times(d),l,1),f=c.plus(k(o,new M(r),l,1)),b(f.d).slice(0,l)===b(c.d).slice(0,l))if(c=c.times(2),s!==0&&(c=c.plus(ue(M,l+2,E).times(s+\"\"))),c=k(c,new M(g),l,1),n==null)if(Q(c.d,l-v,_,u))M.precision=l+=v,f=o=N=k(a.minus(1),a.plus(1),l,1),d=p(N.times(N),l,1),r=u=1;else return p(c,M.precision=E,_,w=!0);else return M.precision=E,c;c=f,r+=2}}function je(e){return String(e.s*e.s/0)}function re(e,n){var i,t,r;for((i=n.indexOf(\".\"))>-1&&(n=n.replace(\".\",\"\")),(t=n.search(/e/i))>0?(i<0&&(i=t),i+=+n.slice(t+1),n=n.substring(0,t)):i<0&&(i=n.length),t=0;n.charCodeAt(t)===48;t++);for(r=n.length;n.charCodeAt(r-1)===48;--r);if(n=n.slice(t,r),n){if(r-=t,e.e=i=i-t-1,e.d=[],t=(i+1)%m,i<0&&(t+=m),t<r){for(t&&e.d.push(+n.slice(0,t)),r-=m;t<r;)e.d.push(+n.slice(t,t+=m));n=n.slice(t),t=m-n.length}else t-=r;for(;t--;)n+=\"0\";e.d.push(+n),w&&(e.e>e.constructor.maxE?(e.d=null,e.e=NaN):e.e<e.constructor.minE&&(e.e=0,e.d=[0]))}else e.e=0,e.d=[0];return e}function gn(e,n){var i,t,r,s,o,u,c,f,l;if(n.indexOf(\"_\")>-1){if(n=n.replace(/(\\d)_(?=\\d)/g,\"$1\"),Be.test(n))return re(e,n)}else if(n===\"Infinity\"||n===\"NaN\")return+n||(e.s=NaN),e.e=NaN,e.d=null,e;if(ln.test(n))i=16,n=n.toLowerCase();else if(cn.test(n))i=2;else if(an.test(n))i=8;else throw Error($+n);for(s=n.search(/p/i),s>0?(c=+n.slice(s+1),n=n.substring(2,s)):n=n.slice(2),s=n.indexOf(\".\"),o=s>=0,t=e.constructor,o&&(n=n.replace(\".\",\"\"),u=n.length,s=u-s,r=He(t,new t(i),s,s*2)),f=te(n,i,D),l=f.length-1,s=l;f[s]===0;--s)f.pop();return s<0?new t(e.s*0):(e.e=ce(f,l),e.d=f,w=!1,o&&(e=k(e,r,u*4)),c&&(e=e.times(Math.abs(c)<54?C(2,c):Y.pow(2,c))),w=!0,e)}function mn(e,n){var i,t=n.d.length;if(t<3)return n.isZero()?n:j(e,2,n,n);i=1.4*Math.sqrt(t),i=i>16?16:i|0,n=n.times(1/le(5,i)),n=j(e,2,n,n);for(var r,s=new e(5),o=new e(16),u=new e(20);i--;)r=n.times(n),n=n.times(s.plus(r.times(o.times(r).minus(u))));return n}function j(e,n,i,t,r){var s,o,u,c,f=1,l=e.precision,a=Math.ceil(l/m);for(w=!1,c=i.times(i),u=new e(t);;){if(o=k(u.times(c),new e(n++*n++),l,1),u=r?t.plus(o):t.minus(o),t=k(o.times(c),new e(n++*n++),l,1),o=u.plus(t),o.d[a]!==void 0){for(s=a;o.d[s]===u.d[s]&&s--;);if(s==-1)break}s=u,u=t,t=o,o=s,f++}return w=!0,o.d.length=a+1,o}function le(e,n){for(var i=e;--n;)i*=e;return i}function We(e,n){var i,t=n.s<0,r=F(e,e.precision,1),s=r.times(.5);if(n=n.abs(),n.lte(s))return Z=t?4:1,n;if(i=n.divToInt(r),i.isZero())Z=t?3:2;else{if(n=n.minus(i.times(r)),n.lte(s))return Z=Te(i)?t?2:3:t?4:1,n;Z=Te(i)?t?1:4:t?3:2}return n.minus(r).abs()}function Pe(e,n,i,t){var r,s,o,u,c,f,l,a,d,g=e.constructor,v=i!==void 0;if(v?(q(i,1,H),t===void 0?t=g.rounding:q(t,0,8)):(i=g.precision,t=g.rounding),!e.isFinite())l=je(e);else{for(l=L(e),o=l.indexOf(\".\"),v?(r=2,n==16?i=i*4-3:n==8&&(i=i*3-2)):r=n,o>=0&&(l=l.replace(\".\",\"\"),d=new g(1),d.e=l.length-o,d.d=te(L(d),10,r),d.e=d.d.length),a=te(l,10,r),s=c=a.length;a[--c]==0;)a.pop();if(!a[0])l=v?\"0p+0\":\"0\";else{if(o<0?s--:(e=new g(e),e.d=a,e.e=s,e=k(e,d,i,t,0,r),a=e.d,s=e.e,f=Le),o=a[i],u=r/2,f=f||a[i+1]!==void 0,f=t<4?(o!==void 0||f)&&(t===0||t===(e.s<0?3:2)):o>u||o===u&&(t===4||f||t===6&&a[i-1]&1||t===(e.s<0?8:7)),a.length=i,f)for(;++a[--i]>r-1;)a[i]=0,i||(++s,a.unshift(1));for(c=a.length;!a[c-1];--c);for(o=0,l=\"\";o<c;o++)l+=Se.charAt(a[o]);if(v){if(c>1)if(n==16||n==8){for(o=n==16?4:3,--c;c%o;c++)l+=\"0\";for(a=te(l,r,n),c=a.length;!a[c-1];--c);for(o=1,l=\"1.\";o<c;o++)l+=Se.charAt(a[o])}else l=l.charAt(0)+\".\"+l.slice(1);l=l+(s<0?\"p\":\"p+\")+s}else if(s<0){for(;++s;)l=\"0\"+l;l=\"0.\"+l}else if(++s>c)for(s-=c;s--;)l+=\"0\";else s<c&&(l=l.slice(0,s)+\".\"+l.slice(s))}l=(n==16?\"0x\":n==2?\"0b\":n==8?\"0o\":\"\")+l}return e.s<0?\"-\"+l:l}function De(e,n){if(e.length>n)return e.length=n,!0}function wn(e){return new this(e).abs()}function Nn(e){return new this(e).acos()}function vn(e){return new this(e).acosh()}function En(e,n){return new this(e).plus(n)}function kn(e){return new this(e).asin()}function Sn(e){return new this(e).asinh()}function Mn(e){return new this(e).atan()}function Cn(e){return new this(e).atanh()}function bn(e,n){e=new this(e),n=new this(n);var i,t=this.precision,r=this.rounding,s=t+4;return!e.s||!n.s?i=new this(NaN):!e.d&&!n.d?(i=F(this,s,1).times(n.s>0?.25:.75),i.s=e.s):!n.d||e.isZero()?(i=n.s<0?F(this,t,r):new this(0),i.s=e.s):!e.d||n.isZero()?(i=F(this,s,1).times(.5),i.s=e.s):n.s<0?(this.precision=s,this.rounding=1,i=this.atan(k(e,n,s,1)),n=F(this,s,1),this.precision=t,this.rounding=r,i=e.s<0?i.minus(n):i.plus(n)):i=this.atan(k(e,n,s,1)),i}function Pn(e){return new this(e).cbrt()}function On(e){return p(e=new this(e),e.e+1,2)}function Rn(e,n,i){return new this(e).clamp(n,i)}function An(e){if(!e||typeof e!=\"object\")throw Error(fe+\"Object expected\");var n,i,t,r=e.defaults===!0,s=[\"precision\",1,H,\"rounding\",0,8,\"toExpNeg\",-V,0,\"toExpPos\",0,V,\"maxE\",0,V,\"minE\",-V,0,\"modulo\",0,9];for(n=0;n<s.length;n+=3)if(i=s[n],r&&(this[i]=Me[i]),(t=e[i])!==void 0)if(R(t)===t&&t>=s[n+1]&&t<=s[n+2])this[i]=t;else throw Error($+i+\": \"+t);if(i=\"crypto\",r&&(this[i]=Me[i]),(t=e[i])!==void 0)if(t===!0||t===!1||t===0||t===1)if(t)if(typeof crypto<\"u\"&&crypto&&(crypto.getRandomValues||crypto.randomBytes))this[i]=!0;else throw Error(Ze);else this[i]=!1;else throw Error($+i+\": \"+t);return this}function qn(e){return new this(e).cos()}function _n(e){return new this(e).cosh()}function Ge(e){var n,i,t;function r(s){var o,u,c,f=this;if(!(f instanceof r))return new r(s);if(f.constructor=r,Fe(s)){f.s=s.s,w?!s.d||s.e>r.maxE?(f.e=NaN,f.d=null):s.e<r.minE?(f.e=0,f.d=[0]):(f.e=s.e,f.d=s.d.slice()):(f.e=s.e,f.d=s.d?s.d.slice():s.d);return}if(c=typeof s,c===\"number\"){if(s===0){f.s=1/s<0?-1:1,f.e=0,f.d=[0];return}if(s<0?(s=-s,f.s=-1):f.s=1,s===~~s&&s<1e7){for(o=0,u=s;u>=10;u/=10)o++;w?o>r.maxE?(f.e=NaN,f.d=null):o<r.minE?(f.e=0,f.d=[0]):(f.e=o,f.d=[s]):(f.e=o,f.d=[s]);return}if(s*0!==0){s||(f.s=NaN),f.e=NaN,f.d=null;return}return re(f,s.toString())}if(c===\"string\")return(u=s.charCodeAt(0))===45?(s=s.slice(1),f.s=-1):(u===43&&(s=s.slice(1)),f.s=1),Be.test(s)?re(f,s):gn(f,s);if(c===\"bigint\")return s<0?(s=-s,f.s=-1):f.s=1,re(f,s.toString());throw Error($+s)}if(r.prototype=h,r.ROUND_UP=0,r.ROUND_DOWN=1,r.ROUND_CEIL=2,r.ROUND_FLOOR=3,r.ROUND_HALF_UP=4,r.ROUND_HALF_DOWN=5,r.ROUND_HALF_EVEN=6,r.ROUND_HALF_CEIL=7,r.ROUND_HALF_FLOOR=8,r.EUCLID=9,r.config=r.set=An,r.clone=Ge,r.isDecimal=Fe,r.abs=wn,r.acos=Nn,r.acosh=vn,r.add=En,r.asin=kn,r.asinh=Sn,r.atan=Mn,r.atanh=Cn,r.atan2=bn,r.cbrt=Pn,r.ceil=On,r.clamp=Rn,r.cos=qn,r.cosh=_n,r.div=Tn,r.exp=Dn,r.floor=Fn,r.hypot=Ln,r.ln=In,r.log=Zn,r.log10=Bn,r.log2=Un,r.max=$n,r.min=Hn,r.mod=Vn,r.mul=jn,r.pow=Wn,r.random=Gn,r.round=Jn,r.sign=Xn,r.sin=Kn,r.sinh=Qn,r.sqrt=Yn,r.sub=xn,r.sum=zn,r.tan=yn,r.tanh=ei,r.trunc=ni,e===void 0&&(e={}),e&&e.defaults!==!0)for(t=[\"precision\",\"rounding\",\"toExpNeg\",\"toExpPos\",\"maxE\",\"minE\",\"modulo\",\"crypto\"],n=0;n<t.length;)e.hasOwnProperty(i=t[n++])||(e[i]=this[i]);return r.config(e),r}function Tn(e,n){return new this(e).div(n)}function Dn(e){return new this(e).exp()}function Fn(e){return p(e=new this(e),e.e+1,3)}function Ln(){var e,n,i=new this(0);for(w=!1,e=0;e<arguments.length;)if(n=new this(arguments[e++]),n.d)i.d&&(i=i.plus(n.times(n)));else{if(n.s)return w=!0,new this(1/0);i=n}return w=!0,i.sqrt()}function Fe(e){return e instanceof Y||e&&e.toStringTag===Ue||!1}function In(e){return new this(e).ln()}function Zn(e,n){return new this(e).log(n)}function Un(e){return new this(e).log(2)}function Bn(e){return new this(e).log(10)}function $n(){return Ve(this,arguments,-1)}function Hn(){return Ve(this,arguments,1)}function Vn(e,n){return new this(e).mod(n)}function jn(e,n){return new this(e).mul(n)}function Wn(e,n){return new this(e).pow(n)}function Gn(e){var n,i,t,r,s=0,o=new this(1),u=[];if(e===void 0?e=this.precision:q(e,1,H),t=Math.ceil(e/m),this.crypto)if(crypto.getRandomValues)for(n=crypto.getRandomValues(new Uint32Array(t));s<t;)r=n[s],r>=429e7?n[s]=crypto.getRandomValues(new Uint32Array(1))[0]:u[s++]=r%1e7;else if(crypto.randomBytes){for(n=crypto.randomBytes(t*=4);s<t;)r=n[s]+(n[s+1]<<8)+(n[s+2]<<16)+((n[s+3]&127)<<24),r>=214e7?crypto.randomBytes(4).copy(n,s):(u.push(r%1e7),s+=4);s=t/4}else throw Error(Ze);else for(;s<t;)u[s++]=Math.random()*1e7|0;for(t=u[--s],e%=m,t&&e&&(r=C(10,m-e),u[s]=(t/r|0)*r);u[s]===0;s--)u.pop();if(s<0)i=0,u=[0];else{for(i=-1;u[0]===0;i-=m)u.shift();for(t=1,r=u[0];r>=10;r/=10)t++;t<m&&(i-=m-t)}return o.e=i,o.d=u,o}function Jn(e){return p(e=new this(e),e.e+1,this.rounding)}function Xn(e){return e=new this(e),e.d?e.d[0]?e.s:0*e.s:e.s||NaN}function Kn(e){return new this(e).sin()}function Qn(e){return new this(e).sinh()}function Yn(e){return new this(e).sqrt()}function xn(e,n){return new this(e).sub(n)}function zn(){var e=0,n=arguments,i=new this(n[e]);for(w=!1;i.s&&++e<n.length;)i=i.plus(n[e]);return w=!0,p(i,this.precision,this.rounding)}function yn(e){return new this(e).tan()}function ei(e){return new this(e).tanh()}function ni(e){return p(e=new this(e),e.e+1,1)}h[Symbol.for(\"nodejs.util.inspect.custom\")]=h.toString;h[Symbol.toStringTag]=\"Decimal\";var Y=h.constructor=Ge(Me);se=new Y(se);oe=new Y(oe);var Je=Y;0&&(0);\n/*! Bundled license information:\n\ndecimal.js/decimal.mjs:\n  (*!\n   *  decimal.js v10.5.0\n   *  An arbitrary-precision Decimal type for JavaScript.\n   *  https://github.com/MikeMcl/decimal.js\n   *  Copyright (c) 2025 Michael Mclaughlin <<EMAIL>>\n   *  MIT Licence\n   *)\n*/\n//# sourceMappingURL=index-browser.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@prisma/client/runtime/index-browser.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Capp%5C%5Corders%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!*******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Capp%5C%5Corders%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/orders/page.tsx */ \"(app-pages-browser)/./src/app/orders/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q2dpdGh1YiU1QyU1Q3Rva2VuZGV2LW5ld3JvbyU1QyU1Q2FkbWluLXBhbmVsJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDb3JkZXJzJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSw0S0FBeUciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXGdpdGh1YlxcXFx0b2tlbmRldi1uZXdyb29cXFxcYWRtaW4tcGFuZWxcXFxcc3JjXFxcXGFwcFxcXFxvcmRlcnNcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Capp%5C%5Corders%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":
/*!*******************************************************!*\
  !*** ./node_modules/next/dist/client/app-dir/link.js ***!
  \*******************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    /**\n * A React component that extends the HTML `<a>` element to provide\n * [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation. This is the primary way to navigate between routes in Next.js.\n *\n * @remarks\n * - Prefetching is only enabled in production.\n *\n * @see https://nextjs.org/docs/app/api-reference/components/link\n */ default: function() {\n        return LinkComponent;\n    },\n    useLinkStatus: function() {\n        return useLinkStatus;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _formaturl = __webpack_require__(/*! ../../shared/lib/router/utils/format-url */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js\");\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _routerreducertypes = __webpack_require__(/*! ../components/router-reducer/router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _usemergedref = __webpack_require__(/*! ../use-merged-ref */ \"(app-pages-browser)/./node_modules/next/dist/client/use-merged-ref.js\");\nconst _utils = __webpack_require__(/*! ../../shared/lib/utils */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js\");\nconst _addbasepath = __webpack_require__(/*! ../add-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/add-base-path.js\");\nconst _warnonce = __webpack_require__(/*! ../../shared/lib/utils/warn-once */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/warn-once.js\");\nconst _links = __webpack_require__(/*! ../components/links */ \"(app-pages-browser)/./node_modules/next/dist/client/components/links.js\");\nconst _islocalurl = __webpack_require__(/*! ../../shared/lib/router/utils/is-local-url */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-local-url.js\");\nconst _approuterinstance = __webpack_require__(/*! ../components/app-router-instance */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-instance.js\");\nconst _erroronce = __webpack_require__(/*! ../../shared/lib/utils/error-once */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/error-once.js\");\nfunction isModifiedEvent(event) {\n    const eventTarget = event.currentTarget;\n    const target = eventTarget.getAttribute('target');\n    return target && target !== '_self' || event.metaKey || event.ctrlKey || event.shiftKey || event.altKey || // triggers resource download\n    event.nativeEvent && event.nativeEvent.which === 2;\n}\nfunction linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate) {\n    const { nodeName } = e.currentTarget;\n    // anchors inside an svg have a lowercase nodeName\n    const isAnchorNodeName = nodeName.toUpperCase() === 'A';\n    if (isAnchorNodeName && isModifiedEvent(e) || e.currentTarget.hasAttribute('download')) {\n        // ignore click for browser’s default behavior\n        return;\n    }\n    if (!(0, _islocalurl.isLocalURL)(href)) {\n        if (replace) {\n            // browser default behavior does not replace the history state\n            // so we need to do it manually\n            e.preventDefault();\n            location.replace(href);\n        }\n        // ignore click for browser’s default behavior\n        return;\n    }\n    e.preventDefault();\n    const navigate = ()=>{\n        if (onNavigate) {\n            let isDefaultPrevented = false;\n            onNavigate({\n                preventDefault: ()=>{\n                    isDefaultPrevented = true;\n                }\n            });\n            if (isDefaultPrevented) {\n                return;\n            }\n        }\n        (0, _approuterinstance.dispatchNavigateAction)(as || href, replace ? 'replace' : 'push', scroll != null ? scroll : true, linkInstanceRef.current);\n    };\n    _react.default.startTransition(navigate);\n}\nfunction formatStringOrUrl(urlObjOrString) {\n    if (typeof urlObjOrString === 'string') {\n        return urlObjOrString;\n    }\n    return (0, _formaturl.formatUrl)(urlObjOrString);\n}\nfunction LinkComponent(props) {\n    _s();\n    const [linkStatus, setOptimisticLinkStatus] = (0, _react.useOptimistic)(_links.IDLE_LINK_STATUS);\n    let children;\n    const linkInstanceRef = (0, _react.useRef)(null);\n    const { href: hrefProp, as: asProp, children: childrenProp, prefetch: prefetchProp = null, passHref, replace, shallow, scroll, onClick, onMouseEnter: onMouseEnterProp, onTouchStart: onTouchStartProp, legacyBehavior = false, onNavigate, ref: forwardedRef, unstable_dynamicOnHover, ...restProps } = props;\n    children = childrenProp;\n    if (legacyBehavior && (typeof children === 'string' || typeof children === 'number')) {\n        children = /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n            children: children\n        });\n    }\n    const router = _react.default.useContext(_approutercontextsharedruntime.AppRouterContext);\n    const prefetchEnabled = prefetchProp !== false;\n    /**\n   * The possible states for prefetch are:\n   * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n   * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n   * - false: we will not prefetch if in the viewport at all\n   * - 'unstable_dynamicOnHover': this starts in \"auto\" mode, but switches to \"full\" when the link is hovered\n   */ const appPrefetchKind = prefetchProp === null ? _routerreducertypes.PrefetchKind.AUTO : _routerreducertypes.PrefetchKind.FULL;\n    if (true) {\n        function createPropError(args) {\n            return Object.defineProperty(new Error(\"Failed prop type: The prop `\" + args.key + \"` expects a \" + args.expected + \" in `<Link>`, but got `\" + args.actual + \"` instead.\" + ( true ? \"\\nOpen your browser's console to view the Component stack trace.\" : 0)), \"__NEXT_ERROR_CODE\", {\n                value: \"E319\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        // TypeScript trick for type-guarding:\n        const requiredPropsGuard = {\n            href: true\n        };\n        const requiredProps = Object.keys(requiredPropsGuard);\n        requiredProps.forEach((key)=>{\n            if (key === 'href') {\n                if (props[key] == null || typeof props[key] !== 'string' && typeof props[key] !== 'object') {\n                    throw createPropError({\n                        key,\n                        expected: '`string` or `object`',\n                        actual: props[key] === null ? 'null' : typeof props[key]\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n        // TypeScript trick for type-guarding:\n        const optionalPropsGuard = {\n            as: true,\n            replace: true,\n            scroll: true,\n            shallow: true,\n            passHref: true,\n            prefetch: true,\n            unstable_dynamicOnHover: true,\n            onClick: true,\n            onMouseEnter: true,\n            onTouchStart: true,\n            legacyBehavior: true,\n            onNavigate: true\n        };\n        const optionalProps = Object.keys(optionalPropsGuard);\n        optionalProps.forEach((key)=>{\n            const valType = typeof props[key];\n            if (key === 'as') {\n                if (props[key] && valType !== 'string' && valType !== 'object') {\n                    throw createPropError({\n                        key,\n                        expected: '`string` or `object`',\n                        actual: valType\n                    });\n                }\n            } else if (key === 'onClick' || key === 'onMouseEnter' || key === 'onTouchStart' || key === 'onNavigate') {\n                if (props[key] && valType !== 'function') {\n                    throw createPropError({\n                        key,\n                        expected: '`function`',\n                        actual: valType\n                    });\n                }\n            } else if (key === 'replace' || key === 'scroll' || key === 'shallow' || key === 'passHref' || key === 'prefetch' || key === 'legacyBehavior' || key === 'unstable_dynamicOnHover') {\n                if (props[key] != null && valType !== 'boolean') {\n                    throw createPropError({\n                        key,\n                        expected: '`boolean`',\n                        actual: valType\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n    }\n    if (true) {\n        if (props.locale) {\n            (0, _warnonce.warnOnce)('The `locale` prop is not supported in `next/link` while using the `app` router. Read more about app router internalization: https://nextjs.org/docs/app/building-your-application/routing/internationalization');\n        }\n        if (!asProp) {\n            let href;\n            if (typeof hrefProp === 'string') {\n                href = hrefProp;\n            } else if (typeof hrefProp === 'object' && typeof hrefProp.pathname === 'string') {\n                href = hrefProp.pathname;\n            }\n            if (href) {\n                const hasDynamicSegment = href.split('/').some((segment)=>segment.startsWith('[') && segment.endsWith(']'));\n                if (hasDynamicSegment) {\n                    throw Object.defineProperty(new Error(\"Dynamic href `\" + href + \"` found in <Link> while using the `/app` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href\"), \"__NEXT_ERROR_CODE\", {\n                        value: \"E267\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n            }\n        }\n    }\n    const { href, as } = _react.default.useMemo({\n        \"LinkComponent.useMemo\": ()=>{\n            const resolvedHref = formatStringOrUrl(hrefProp);\n            return {\n                href: resolvedHref,\n                as: asProp ? formatStringOrUrl(asProp) : resolvedHref\n            };\n        }\n    }[\"LinkComponent.useMemo\"], [\n        hrefProp,\n        asProp\n    ]);\n    // This will return the first child, if multiple are provided it will throw an error\n    let child;\n    if (legacyBehavior) {\n        if (true) {\n            if (onClick) {\n                console.warn('\"onClick\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link');\n            }\n            if (onMouseEnterProp) {\n                console.warn('\"onMouseEnter\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link');\n            }\n            try {\n                child = _react.default.Children.only(children);\n            } catch (err) {\n                if (!children) {\n                    throw Object.defineProperty(new Error(\"No children were passed to <Link> with `href` of `\" + hrefProp + \"` but one child is required https://nextjs.org/docs/messages/link-no-children\"), \"__NEXT_ERROR_CODE\", {\n                        value: \"E320\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                throw Object.defineProperty(new Error(\"Multiple children were passed to <Link> with `href` of `\" + hrefProp + \"` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children\" + ( true ? \" \\nOpen your browser's console to view the Component stack trace.\" : 0)), \"__NEXT_ERROR_CODE\", {\n                    value: \"E266\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        } else {}\n    } else {\n        if (true) {\n            if ((children == null ? void 0 : children.type) === 'a') {\n                throw Object.defineProperty(new Error('Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E209\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        }\n    }\n    const childRef = legacyBehavior ? child && typeof child === 'object' && child.ref : forwardedRef;\n    // Use a callback ref to attach an IntersectionObserver to the anchor tag on\n    // mount. In the future we will also use this to keep track of all the\n    // currently mounted <Link> instances, e.g. so we can re-prefetch them after\n    // a revalidation or refresh.\n    const observeLinkVisibilityOnMount = _react.default.useCallback({\n        \"LinkComponent.useCallback[observeLinkVisibilityOnMount]\": (element)=>{\n            if (router !== null) {\n                linkInstanceRef.current = (0, _links.mountLinkInstance)(element, href, router, appPrefetchKind, prefetchEnabled, setOptimisticLinkStatus);\n            }\n            return ({\n                \"LinkComponent.useCallback[observeLinkVisibilityOnMount]\": ()=>{\n                    if (linkInstanceRef.current) {\n                        (0, _links.unmountLinkForCurrentNavigation)(linkInstanceRef.current);\n                        linkInstanceRef.current = null;\n                    }\n                    (0, _links.unmountPrefetchableInstance)(element);\n                }\n            })[\"LinkComponent.useCallback[observeLinkVisibilityOnMount]\"];\n        }\n    }[\"LinkComponent.useCallback[observeLinkVisibilityOnMount]\"], [\n        prefetchEnabled,\n        href,\n        router,\n        appPrefetchKind,\n        setOptimisticLinkStatus\n    ]);\n    const mergedRef = (0, _usemergedref.useMergedRef)(observeLinkVisibilityOnMount, childRef);\n    const childProps = {\n        ref: mergedRef,\n        onClick (e) {\n            if (true) {\n                if (!e) {\n                    throw Object.defineProperty(new Error('Component rendered inside next/link has to pass click event to \"onClick\" prop.'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E312\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n            }\n            if (!legacyBehavior && typeof onClick === 'function') {\n                onClick(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onClick === 'function') {\n                child.props.onClick(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (e.defaultPrevented) {\n                return;\n            }\n            linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate);\n        },\n        onMouseEnter (e) {\n            if (!legacyBehavior && typeof onMouseEnterProp === 'function') {\n                onMouseEnterProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onMouseEnter === 'function') {\n                child.props.onMouseEnter(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (!prefetchEnabled || \"development\" === 'development') {\n                return;\n            }\n            const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true;\n            (0, _links.onNavigationIntent)(e.currentTarget, upgradeToDynamicPrefetch);\n        },\n        onTouchStart:  false ? 0 : function onTouchStart(e) {\n            if (!legacyBehavior && typeof onTouchStartProp === 'function') {\n                onTouchStartProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onTouchStart === 'function') {\n                child.props.onTouchStart(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (!prefetchEnabled) {\n                return;\n            }\n            const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true;\n            (0, _links.onNavigationIntent)(e.currentTarget, upgradeToDynamicPrefetch);\n        }\n    };\n    // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n    // defined, we specify the current 'href', so that repetition is not needed by the user.\n    // If the url is absolute, we can bypass the logic to prepend the basePath.\n    if ((0, _utils.isAbsoluteUrl)(as)) {\n        childProps.href = as;\n    } else if (!legacyBehavior || passHref || child.type === 'a' && !('href' in child.props)) {\n        childProps.href = (0, _addbasepath.addBasePath)(as);\n    }\n    let link;\n    if (legacyBehavior) {\n        if (true) {\n            (0, _erroronce.errorOnce)('`legacyBehavior` is deprecated and will be removed in a future ' + 'release. A codemod is available to upgrade your components:\\n\\n' + 'npx @next/codemod@latest new-link .\\n\\n' + 'Learn more: https://nextjs.org/docs/app/building-your-application/upgrading/codemods#remove-a-tags-from-link-components');\n        }\n        link = /*#__PURE__*/ _react.default.cloneElement(child, childProps);\n    } else {\n        link = /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n            ...restProps,\n            ...childProps,\n            children: children\n        });\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(LinkStatusContext.Provider, {\n        value: linkStatus,\n        children: link\n    });\n}\n_s(LinkComponent, \"MNV6IdWv8Lu3MKc7Fm4v59uGRY0=\");\n_c = LinkComponent;\nconst LinkStatusContext = /*#__PURE__*/ (0, _react.createContext)(_links.IDLE_LINK_STATUS);\nconst useLinkStatus = ()=>{\n    return (0, _react.useContext)(LinkStatusContext);\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=link.js.map\nvar _c;\n$RefreshReg$(_c, \"LinkComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/use-merged-ref.js":
/*!*********************************************************!*\
  !*** ./node_modules/next/dist/client/use-merged-ref.js ***!
  \*********************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"useMergedRef\", ({\n    enumerable: true,\n    get: function() {\n        return useMergedRef;\n    }\n}));\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nfunction useMergedRef(refA, refB) {\n    const cleanupA = (0, _react.useRef)(null);\n    const cleanupB = (0, _react.useRef)(null);\n    // NOTE: In theory, we could skip the wrapping if only one of the refs is non-null.\n    // (this happens often if the user doesn't pass a ref to Link/Form/Image)\n    // But this can cause us to leak a cleanup-ref into user code (e.g. via `<Link legacyBehavior>`),\n    // and the user might pass that ref into ref-merging library that doesn't support cleanup refs\n    // (because it hasn't been updated for React 19)\n    // which can then cause things to blow up, because a cleanup-returning ref gets called with `null`.\n    // So in practice, it's safer to be defensive and always wrap the ref, even on React 19.\n    return (0, _react.useCallback)((current)=>{\n        if (current === null) {\n            const cleanupFnA = cleanupA.current;\n            if (cleanupFnA) {\n                cleanupA.current = null;\n                cleanupFnA();\n            }\n            const cleanupFnB = cleanupB.current;\n            if (cleanupFnB) {\n                cleanupB.current = null;\n                cleanupFnB();\n            }\n        } else {\n            if (refA) {\n                cleanupA.current = applyRef(refA, current);\n            }\n            if (refB) {\n                cleanupB.current = applyRef(refB, current);\n            }\n        }\n    }, [\n        refA,\n        refB\n    ]);\n}\nfunction applyRef(refA, current) {\n    if (typeof refA === 'function') {\n        const cleanup = refA(current);\n        if (typeof cleanup === 'function') {\n            return cleanup;\n        } else {\n            return ()=>refA(null);\n        }\n    } else {\n        refA.current = current;\n        return ()=>{\n            refA.current = null;\n        };\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-merged-ref.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/use-merged-ref.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkQ6XFxnaXRodWJcXHRva2VuZGV2LW5ld3Jvb1xcYWRtaW4tcGFuZWxcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcY29tcGlsZWRcXHJlYWN0XFxqc3gtZGV2LXJ1bnRpbWUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJykge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5wcm9kdWN0aW9uLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js":
/*!**********************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/utils/format-url.js ***!
  \**********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("// Format function modified from nodejs\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    formatUrl: function() {\n        return formatUrl;\n    },\n    formatWithValidation: function() {\n        return formatWithValidation;\n    },\n    urlObjectKeys: function() {\n        return urlObjectKeys;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _querystring = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! ./querystring */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/querystring.js\"));\nconst slashedProtocols = /https?|ftp|gopher|file/;\nfunction formatUrl(urlObj) {\n    let { auth, hostname } = urlObj;\n    let protocol = urlObj.protocol || '';\n    let pathname = urlObj.pathname || '';\n    let hash = urlObj.hash || '';\n    let query = urlObj.query || '';\n    let host = false;\n    auth = auth ? encodeURIComponent(auth).replace(/%3A/i, ':') + '@' : '';\n    if (urlObj.host) {\n        host = auth + urlObj.host;\n    } else if (hostname) {\n        host = auth + (~hostname.indexOf(':') ? \"[\" + hostname + \"]\" : hostname);\n        if (urlObj.port) {\n            host += ':' + urlObj.port;\n        }\n    }\n    if (query && typeof query === 'object') {\n        query = String(_querystring.urlQueryToSearchParams(query));\n    }\n    let search = urlObj.search || query && \"?\" + query || '';\n    if (protocol && !protocol.endsWith(':')) protocol += ':';\n    if (urlObj.slashes || (!protocol || slashedProtocols.test(protocol)) && host !== false) {\n        host = '//' + (host || '');\n        if (pathname && pathname[0] !== '/') pathname = '/' + pathname;\n    } else if (!host) {\n        host = '';\n    }\n    if (hash && hash[0] !== '#') hash = '#' + hash;\n    if (search && search[0] !== '?') search = '?' + search;\n    pathname = pathname.replace(/[?#]/g, encodeURIComponent);\n    search = search.replace('#', '%23');\n    return \"\" + protocol + host + pathname + search + hash;\n}\nconst urlObjectKeys = [\n    'auth',\n    'hash',\n    'host',\n    'hostname',\n    'href',\n    'path',\n    'pathname',\n    'port',\n    'protocol',\n    'query',\n    'search',\n    'slashes'\n];\nfunction formatWithValidation(url) {\n    if (true) {\n        if (url !== null && typeof url === 'object') {\n            Object.keys(url).forEach((key)=>{\n                if (!urlObjectKeys.includes(key)) {\n                    console.warn(\"Unknown key passed via urlObject into url.format: \" + key);\n                }\n            });\n        }\n    }\n    return formatUrl(url);\n} //# sourceMappingURL=format-url.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-local-url.js":
/*!************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/utils/is-local-url.js ***!
  \************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"isLocalURL\", ({\n    enumerable: true,\n    get: function() {\n        return isLocalURL;\n    }\n}));\nconst _utils = __webpack_require__(/*! ../../utils */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js\");\nconst _hasbasepath = __webpack_require__(/*! ../../../../client/has-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/has-base-path.js\");\nfunction isLocalURL(url) {\n    // prevent a hydration mismatch on href for url with anchor refs\n    if (!(0, _utils.isAbsoluteUrl)(url)) return true;\n    try {\n        // absolute urls can be local if they are on the same origin\n        const locationOrigin = (0, _utils.getLocationOrigin)();\n        const resolved = new URL(url, locationOrigin);\n        return resolved.origin === locationOrigin && (0, _hasbasepath.hasBasePath)(resolved.pathname);\n    } catch (_) {\n        return false;\n    }\n} //# sourceMappingURL=is-local-url.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9yb3V0ZXIvdXRpbHMvaXMtbG9jYWwtdXJsLmpzIiwibWFwcGluZ3MiOiI7Ozs7OENBTWdCQTs7O2VBQUFBOzs7bUNBTmlDO3lDQUNyQjtBQUtyQixTQUFTQSxXQUFXQyxHQUFXO0lBQ3BDLGdFQUFnRTtJQUNoRSxJQUFJLENBQUNDLENBQUFBLEdBQUFBLE9BQUFBLGFBQUFBLEVBQWNELE1BQU0sT0FBTztJQUNoQyxJQUFJO1FBQ0YsNERBQTREO1FBQzVELE1BQU1FLGlCQUFpQkMsQ0FBQUEsR0FBQUEsT0FBQUEsaUJBQUFBO1FBQ3ZCLE1BQU1DLFdBQVcsSUFBSUMsSUFBSUwsS0FBS0U7UUFDOUIsT0FBT0UsU0FBU0UsTUFBTSxLQUFLSixrQkFBa0JLLENBQUFBLEdBQUFBLGFBQUFBLFdBQUFBLEVBQVlILFNBQVNJLFFBQVE7SUFDNUUsRUFBRSxPQUFPQyxHQUFHO1FBQ1YsT0FBTztJQUNUO0FBQ0YiLCJzb3VyY2VzIjpbIkQ6XFxzcmNcXHNoYXJlZFxcbGliXFxyb3V0ZXJcXHV0aWxzXFxpcy1sb2NhbC11cmwudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaXNBYnNvbHV0ZVVybCwgZ2V0TG9jYXRpb25PcmlnaW4gfSBmcm9tICcuLi8uLi91dGlscydcbmltcG9ydCB7IGhhc0Jhc2VQYXRoIH0gZnJvbSAnLi4vLi4vLi4vLi4vY2xpZW50L2hhcy1iYXNlLXBhdGgnXG5cbi8qKlxuICogRGV0ZWN0cyB3aGV0aGVyIGEgZ2l2ZW4gdXJsIGlzIHJvdXRhYmxlIGJ5IHRoZSBOZXh0LmpzIHJvdXRlciAoYnJvd3NlciBvbmx5KS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGlzTG9jYWxVUkwodXJsOiBzdHJpbmcpOiBib29sZWFuIHtcbiAgLy8gcHJldmVudCBhIGh5ZHJhdGlvbiBtaXNtYXRjaCBvbiBocmVmIGZvciB1cmwgd2l0aCBhbmNob3IgcmVmc1xuICBpZiAoIWlzQWJzb2x1dGVVcmwodXJsKSkgcmV0dXJuIHRydWVcbiAgdHJ5IHtcbiAgICAvLyBhYnNvbHV0ZSB1cmxzIGNhbiBiZSBsb2NhbCBpZiB0aGV5IGFyZSBvbiB0aGUgc2FtZSBvcmlnaW5cbiAgICBjb25zdCBsb2NhdGlvbk9yaWdpbiA9IGdldExvY2F0aW9uT3JpZ2luKClcbiAgICBjb25zdCByZXNvbHZlZCA9IG5ldyBVUkwodXJsLCBsb2NhdGlvbk9yaWdpbilcbiAgICByZXR1cm4gcmVzb2x2ZWQub3JpZ2luID09PSBsb2NhdGlvbk9yaWdpbiAmJiBoYXNCYXNlUGF0aChyZXNvbHZlZC5wYXRobmFtZSlcbiAgfSBjYXRjaCAoXykge1xuICAgIHJldHVybiBmYWxzZVxuICB9XG59XG4iXSwibmFtZXMiOlsiaXNMb2NhbFVSTCIsInVybCIsImlzQWJzb2x1dGVVcmwiLCJsb2NhdGlvbk9yaWdpbiIsImdldExvY2F0aW9uT3JpZ2luIiwicmVzb2x2ZWQiLCJVUkwiLCJvcmlnaW4iLCJoYXNCYXNlUGF0aCIsInBhdGhuYW1lIiwiXyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-local-url.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/querystring.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/utils/querystring.js ***!
  \***********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    assign: function() {\n        return assign;\n    },\n    searchParamsToUrlQuery: function() {\n        return searchParamsToUrlQuery;\n    },\n    urlQueryToSearchParams: function() {\n        return urlQueryToSearchParams;\n    }\n});\nfunction searchParamsToUrlQuery(searchParams) {\n    const query = {};\n    for (const [key, value] of searchParams.entries()){\n        const existing = query[key];\n        if (typeof existing === 'undefined') {\n            query[key] = value;\n        } else if (Array.isArray(existing)) {\n            existing.push(value);\n        } else {\n            query[key] = [\n                existing,\n                value\n            ];\n        }\n    }\n    return query;\n}\nfunction stringifyUrlQueryParam(param) {\n    if (typeof param === 'string') {\n        return param;\n    }\n    if (typeof param === 'number' && !isNaN(param) || typeof param === 'boolean') {\n        return String(param);\n    } else {\n        return '';\n    }\n}\nfunction urlQueryToSearchParams(query) {\n    const searchParams = new URLSearchParams();\n    for (const [key, value] of Object.entries(query)){\n        if (Array.isArray(value)) {\n            for (const item of value){\n                searchParams.append(key, stringifyUrlQueryParam(item));\n            }\n        } else {\n            searchParams.set(key, stringifyUrlQueryParam(value));\n        }\n    }\n    return searchParams;\n}\nfunction assign(target) {\n    for(var _len = arguments.length, searchParamsList = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n        searchParamsList[_key - 1] = arguments[_key];\n    }\n    for (const searchParams of searchParamsList){\n        for (const key of searchParams.keys()){\n            target.delete(key);\n        }\n        for (const [key, value] of searchParams.entries()){\n            target.append(key, value);\n        }\n    }\n    return target;\n} //# sourceMappingURL=querystring.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/querystring.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js":
/*!****************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/utils.js ***!
  \****************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DecodeError: function() {\n        return DecodeError;\n    },\n    MiddlewareNotFoundError: function() {\n        return MiddlewareNotFoundError;\n    },\n    MissingStaticPage: function() {\n        return MissingStaticPage;\n    },\n    NormalizeError: function() {\n        return NormalizeError;\n    },\n    PageNotFoundError: function() {\n        return PageNotFoundError;\n    },\n    SP: function() {\n        return SP;\n    },\n    ST: function() {\n        return ST;\n    },\n    WEB_VITALS: function() {\n        return WEB_VITALS;\n    },\n    execOnce: function() {\n        return execOnce;\n    },\n    getDisplayName: function() {\n        return getDisplayName;\n    },\n    getLocationOrigin: function() {\n        return getLocationOrigin;\n    },\n    getURL: function() {\n        return getURL;\n    },\n    isAbsoluteUrl: function() {\n        return isAbsoluteUrl;\n    },\n    isResSent: function() {\n        return isResSent;\n    },\n    loadGetInitialProps: function() {\n        return loadGetInitialProps;\n    },\n    normalizeRepeatedSlashes: function() {\n        return normalizeRepeatedSlashes;\n    },\n    stringifyError: function() {\n        return stringifyError;\n    }\n});\nconst WEB_VITALS = [\n    'CLS',\n    'FCP',\n    'FID',\n    'INP',\n    'LCP',\n    'TTFB'\n];\nfunction execOnce(fn) {\n    let used = false;\n    let result;\n    return function() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        if (!used) {\n            used = true;\n            result = fn(...args);\n        }\n        return result;\n    };\n}\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/;\nconst isAbsoluteUrl = (url)=>ABSOLUTE_URL_REGEX.test(url);\nfunction getLocationOrigin() {\n    const { protocol, hostname, port } = window.location;\n    return protocol + \"//\" + hostname + (port ? ':' + port : '');\n}\nfunction getURL() {\n    const { href } = window.location;\n    const origin = getLocationOrigin();\n    return href.substring(origin.length);\n}\nfunction getDisplayName(Component) {\n    return typeof Component === 'string' ? Component : Component.displayName || Component.name || 'Unknown';\n}\nfunction isResSent(res) {\n    return res.finished || res.headersSent;\n}\nfunction normalizeRepeatedSlashes(url) {\n    const urlParts = url.split('?');\n    const urlNoQuery = urlParts[0];\n    return urlNoQuery // first we replace any non-encoded backslashes with forward\n    // then normalize repeated forward slashes\n    .replace(/\\\\/g, '/').replace(/\\/\\/+/g, '/') + (urlParts[1] ? \"?\" + urlParts.slice(1).join('?') : '');\n}\nasync function loadGetInitialProps(App, ctx) {\n    if (true) {\n        var _App_prototype;\n        if ((_App_prototype = App.prototype) == null ? void 0 : _App_prototype.getInitialProps) {\n            const message = '\"' + getDisplayName(App) + '.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.';\n            throw Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n                value: \"E394\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n    }\n    // when called from _app `ctx` is nested in `ctx`\n    const res = ctx.res || ctx.ctx && ctx.ctx.res;\n    if (!App.getInitialProps) {\n        if (ctx.ctx && ctx.Component) {\n            // @ts-ignore pageProps default\n            return {\n                pageProps: await loadGetInitialProps(ctx.Component, ctx.ctx)\n            };\n        }\n        return {};\n    }\n    const props = await App.getInitialProps(ctx);\n    if (res && isResSent(res)) {\n        return props;\n    }\n    if (!props) {\n        const message = '\"' + getDisplayName(App) + '.getInitialProps()\" should resolve to an object. But found \"' + props + '\" instead.';\n        throw Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n            value: \"E394\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    if (true) {\n        if (Object.keys(props).length === 0 && !ctx.ctx) {\n            console.warn(\"\" + getDisplayName(App) + \" returned an empty object from `getInitialProps`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps\");\n        }\n    }\n    return props;\n}\nconst SP = typeof performance !== 'undefined';\nconst ST = SP && [\n    'mark',\n    'measure',\n    'getEntriesByName'\n].every((method)=>typeof performance[method] === 'function');\nclass DecodeError extends Error {\n}\nclass NormalizeError extends Error {\n}\nclass PageNotFoundError extends Error {\n    constructor(page){\n        super();\n        this.code = 'ENOENT';\n        this.name = 'PageNotFoundError';\n        this.message = \"Cannot find module for page: \" + page;\n    }\n}\nclass MissingStaticPage extends Error {\n    constructor(page, message){\n        super();\n        this.message = \"Failed to load static file for page: \" + page + \" \" + message;\n    }\n}\nclass MiddlewareNotFoundError extends Error {\n    constructor(){\n        super();\n        this.code = 'ENOENT';\n        this.message = \"Cannot find the middleware module\";\n    }\n}\nfunction stringifyError(error) {\n    return JSON.stringify({\n        message: error.message,\n        stack: error.stack\n    });\n} //# sourceMappingURL=utils.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/error-once.js":
/*!***************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/utils/error-once.js ***!
  \***************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"errorOnce\", ({\n    enumerable: true,\n    get: function() {\n        return errorOnce;\n    }\n}));\nlet errorOnce = (_)=>{};\nif (true) {\n    const errors = new Set();\n    errorOnce = (msg)=>{\n        if (!errors.has(msg)) {\n            console.error(msg);\n        }\n        errors.add(msg);\n    };\n} //# sourceMappingURL=error-once.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi91dGlscy9lcnJvci1vbmNlLmpzIiwibWFwcGluZ3MiOiI7Ozs7NkNBV1NBOzs7ZUFBQUE7OztBQVhULElBQUlBLFlBQVksQ0FBQ0MsS0FBZTtBQUNoQyxJQUFJQyxJQUFvQixFQUFtQjtJQUN6QyxNQUFNRyxTQUFTLElBQUlDO0lBQ25CTixZQUFZLENBQUNPO1FBQ1gsSUFBSSxDQUFDRixPQUFPRyxHQUFHLENBQUNELE1BQU07WUFDcEJFLFFBQVFDLEtBQUssQ0FBQ0g7UUFDaEI7UUFDQUYsT0FBT00sR0FBRyxDQUFDSjtJQUNiO0FBQ0YiLCJzb3VyY2VzIjpbIkQ6XFxzcmNcXHNoYXJlZFxcbGliXFx1dGlsc1xcZXJyb3Itb25jZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJsZXQgZXJyb3JPbmNlID0gKF86IHN0cmluZykgPT4ge31cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIGNvbnN0IGVycm9ycyA9IG5ldyBTZXQ8c3RyaW5nPigpXG4gIGVycm9yT25jZSA9IChtc2c6IHN0cmluZykgPT4ge1xuICAgIGlmICghZXJyb3JzLmhhcyhtc2cpKSB7XG4gICAgICBjb25zb2xlLmVycm9yKG1zZylcbiAgICB9XG4gICAgZXJyb3JzLmFkZChtc2cpXG4gIH1cbn1cblxuZXhwb3J0IHsgZXJyb3JPbmNlIH1cbiJdLCJuYW1lcyI6WyJlcnJvck9uY2UiLCJfIiwicHJvY2VzcyIsImVudiIsIk5PREVfRU5WIiwiZXJyb3JzIiwiU2V0IiwibXNnIiwiaGFzIiwiY29uc29sZSIsImVycm9yIiwiYWRkIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/error-once.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/orders/page.tsx":
/*!*********************************!*\
  !*** ./src/app/orders/page.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OrdersPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @prisma/client */ \"(app-pages-browser)/./node_modules/@prisma/client/index-browser.js\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction OrdersPage() {\n    _s();\n    const [orders, setOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedStatus, setSelectedStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('ALL');\n    const [isUpdating, setIsUpdating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isMinting, setIsMinting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrdersPage.useEffect\": ()=>{\n            fetchOrders();\n        }\n    }[\"OrdersPage.useEffect\"], [\n        selectedStatus,\n        currentPage\n    ]);\n    const fetchOrders = async ()=>{\n        try {\n            var _data_pagination;\n            setIsLoading(true);\n            setError(null);\n            const params = new URLSearchParams({\n                page: currentPage.toString(),\n                limit: '10'\n            });\n            if (selectedStatus !== 'ALL') {\n                params.append('status', selectedStatus);\n            }\n            const response = await fetch(\"/api/orders?\".concat(params));\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || 'Failed to fetch orders');\n            }\n            setOrders(data.orders || []);\n            setTotalPages(((_data_pagination = data.pagination) === null || _data_pagination === void 0 ? void 0 : _data_pagination.totalPages) || 1);\n        } catch (err) {\n            console.error('Error fetching orders:', err);\n            setError(err.message || 'Failed to fetch orders');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const updateOrderStatus = async (orderId, newStatus)=>{\n        try {\n            setIsUpdating(orderId);\n            const response = await fetch(\"/api/orders/\".concat(orderId), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    status: newStatus\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || 'Failed to update order');\n            }\n            // Update the order in the local state\n            setOrders((prevOrders)=>prevOrders.map((order)=>order.id === orderId ? {\n                        ...order,\n                        status: newStatus\n                    } : order));\n            alert(\"Order status updated to \".concat(newStatus));\n        } catch (err) {\n            console.error('Error updating order:', err);\n            alert(\"Error: \".concat(err.message || 'Failed to update order'));\n        } finally{\n            setIsUpdating(null);\n        }\n    };\n    const mintTokensForOrder = async (order)=>{\n        try {\n            setIsMinting(order.id);\n            // Show confirmation dialog\n            const confirmed = confirm(\"Are you sure you want to mint \".concat(order.tokensOrdered, \" \").concat(order.token.symbol, \" tokens for \").concat(order.client.firstName, \" \").concat(order.client.lastName, \"?\\n\\n\") + \"This will:\\n\" + \"1. Mint \".concat(order.tokensOrdered, \" tokens on the blockchain\\n\") + \"2. Transfer them to the client's wallet\\n\" + \"3. Update the order status to MINTED\\n\\n\" + \"This action cannot be undone.\");\n            if (!confirmed) {\n                return;\n            }\n            console.log('Starting minting process for order:', order.id);\n            const response = await fetch(\"/api/orders/\".concat(order.id, \"/mint\"), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenAddress: order.token.address,\n                    recipientAddress: order.client.walletAddress || order.client.email,\n                    amount: order.tokensOrdered,\n                    orderId: order.id\n                })\n            });\n            const data = await response.json();\n            console.log('Mint API response:', data);\n            if (!response.ok) {\n                throw new Error(data.error || 'Failed to mint tokens');\n            }\n            // Update the order in the local state with transaction hash\n            console.log('Updating order with transaction hash:', data.transactionHash);\n            setOrders((prevOrders)=>prevOrders.map((o)=>o.id === order.id ? {\n                        ...o,\n                        status: _prisma_client__WEBPACK_IMPORTED_MODULE_3__.OrderStatus.MINTED,\n                        transactionHash: data.transactionHash,\n                        blockNumber: data.blockNumber\n                    } : o));\n            alert(\"✅ Tokens minted successfully!\\n\\n\" + \"Transaction Hash: \".concat(data.transactionHash, \"\\n\") + \"Amount: \".concat(order.tokensOrdered, \" \").concat(order.token.symbol, \"\\n\") + \"Recipient: \".concat(data.recipientAddress, \"\\n\\n\") + \"Order status updated to MINTED.\");\n        } catch (err) {\n            console.error('Error minting tokens:', err);\n            alert(\"❌ Minting failed: \".concat(err.message || 'Unknown error occurred'));\n        } finally{\n            setIsMinting(null);\n        }\n    };\n    const getStatusBadgeColor = (status)=>{\n        switch(status){\n            case _prisma_client__WEBPACK_IMPORTED_MODULE_3__.OrderStatus.PENDING_APPROVAL:\n                return 'bg-yellow-100 text-yellow-800';\n            case _prisma_client__WEBPACK_IMPORTED_MODULE_3__.OrderStatus.CONFIRMED:\n                return 'bg-blue-100 text-blue-800';\n            case _prisma_client__WEBPACK_IMPORTED_MODULE_3__.OrderStatus.MINTED:\n                return 'bg-green-100 text-green-800';\n            case _prisma_client__WEBPACK_IMPORTED_MODULE_3__.OrderStatus.CANCELLED:\n                return 'bg-red-100 text-red-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString('en-US', {\n            year: 'numeric',\n            month: 'short',\n            day: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-2\",\n                        children: \"Loading orders...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 213,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 212,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"h-5 w-5 text-red-400\",\n                                viewBox: \"0 0 20 20\",\n                                fill: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-red-800\",\n                                    children: \"Error loading orders\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-red-700 mt-1\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: fetchOrders,\n                                    className: \"mt-2 text-sm bg-red-100 hover:bg-red-200 text-red-800 px-3 py-1 rounded\",\n                                    children: \"Try Again\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 224,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 223,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: \"Orders Management\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: selectedStatus,\n                                    onChange: (e)=>setSelectedStatus(e.target.value),\n                                    className: \"border border-gray-300 rounded-md px-3 py-2 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"ALL\",\n                                            children: \"All Statuses\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: _prisma_client__WEBPACK_IMPORTED_MODULE_3__.OrderStatus.PENDING_APPROVAL,\n                                            children: \"Pending Approval\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: _prisma_client__WEBPACK_IMPORTED_MODULE_3__.OrderStatus.CONFIRMED,\n                                            children: \"Confirmed\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: _prisma_client__WEBPACK_IMPORTED_MODULE_3__.OrderStatus.MINTED,\n                                            children: \"Minted\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: _prisma_client__WEBPACK_IMPORTED_MODULE_3__.OrderStatus.CANCELLED,\n                                            children: \"Cancelled\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: fetchOrders,\n                                    className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm\",\n                                    children: \"Refresh\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 251,\n                    columnNumber: 9\n                }, this),\n                orders.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-8 text-gray-500\",\n                    children: \"No orders found.\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 276,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-x-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"min-w-full divide-y divide-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        className: \"bg-gray-50\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Token\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Investor\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Email\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Tokens Ordered\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Amount to Pay\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Payment Reference\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Creation Date\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Actions\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        className: \"bg-white divide-y divide-gray-200\",\n                                        children: orders.map((order)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"hover:bg-gray-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium text-gray-900\",\n                                                                    children: [\n                                                                        order.token.name,\n                                                                        \" (\",\n                                                                        order.token.symbol,\n                                                                        \")\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 319,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                    href: \"/tokens/\".concat(order.token.address),\n                                                                    className: \"text-sm text-blue-600 hover:text-blue-800\",\n                                                                    children: \"View Token\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 322,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm font-medium text-gray-900\",\n                                                                children: [\n                                                                    order.client.firstName,\n                                                                    \" \",\n                                                                    order.client.lastName\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 331,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            order.client.walletAddress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500 font-mono\",\n                                                                children: [\n                                                                    order.client.walletAddress.slice(0, 6),\n                                                                    \"...\",\n                                                                    order.client.walletAddress.slice(-4)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-900\",\n                                                            children: order.client.email\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 341,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(getStatusBadgeColor(order.status)),\n                                                            children: order.status\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 344,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                        children: order.tokensOrdered\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                        children: [\n                                                            \"$\",\n                                                            order.amountToPay\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-mono\",\n                                                        children: order.paymentReference\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                        children: formatDate(order.createdAt)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\",\n                                                        children: [\n                                                            order.status === _prisma_client__WEBPACK_IMPORTED_MODULE_3__.OrderStatus.PENDING_APPROVAL && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>updateOrderStatus(order.id, _prisma_client__WEBPACK_IMPORTED_MODULE_3__.OrderStatus.CONFIRMED),\n                                                                disabled: isUpdating === order.id,\n                                                                className: \"text-blue-600 hover:text-blue-900 disabled:opacity-50\",\n                                                                children: isUpdating === order.id ? 'Updating...' : 'Approve'\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 362,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            order.status === _prisma_client__WEBPACK_IMPORTED_MODULE_3__.OrderStatus.CONFIRMED && (order.client.walletAddress ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>mintTokensForOrder(order),\n                                                                disabled: isMinting === order.id || isUpdating === order.id,\n                                                                className: \"bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                                children: isMinting === order.id ? 'MINTING...' : 'MINT'\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 372,\n                                                                columnNumber: 29\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-red-600 bg-red-50 px-2 py-1 rounded\",\n                                                                children: \"No wallet address\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 380,\n                                                                columnNumber: 29\n                                                            }, this)),\n                                                            order.status === _prisma_client__WEBPACK_IMPORTED_MODULE_3__.OrderStatus.MINTED && order.transactionHash && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"https://amoy.polygonscan.com/tx/\".concat(order.transactionHash),\n                                                                target: \"_blank\",\n                                                                rel: \"noopener noreferrer\",\n                                                                className: \"bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm font-medium\",\n                                                                children: \"Tx\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 386,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            order.status !== _prisma_client__WEBPACK_IMPORTED_MODULE_3__.OrderStatus.CANCELLED && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>updateOrderStatus(order.id, _prisma_client__WEBPACK_IMPORTED_MODULE_3__.OrderStatus.CANCELLED),\n                                                                disabled: isUpdating === order.id,\n                                                                className: \"text-red-600 hover:text-red-900 disabled:opacity-50\",\n                                                                children: isUpdating === order.id ? 'Updating...' : 'Cancel'\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 396,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 360,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, order.id, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 281,\n                            columnNumber: 13\n                        }, this),\n                        totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 flex justify-between sm:hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setCurrentPage(Math.max(1, currentPage - 1)),\n                                            disabled: currentPage === 1,\n                                            className: \"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50\",\n                                            children: \"Previous\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setCurrentPage(Math.min(totalPages, currentPage + 1)),\n                                            disabled: currentPage === totalPages,\n                                            className: \"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50\",\n                                            children: \"Next\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 422,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-700\",\n                                                children: [\n                                                    \"Page \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: currentPage\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 433,\n                                                        columnNumber: 28\n                                                    }, this),\n                                                    \" of\",\n                                                    ' ',\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: totalPages\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                                className: \"relative z-0 inline-flex rounded-md shadow-sm -space-x-px\",\n                                                \"aria-label\": \"Pagination\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setCurrentPage(Math.max(1, currentPage - 1)),\n                                                        disabled: currentPage === 1,\n                                                        className: \"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50\",\n                                                        children: \"Previous\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 439,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setCurrentPage(Math.min(totalPages, currentPage + 1)),\n                                                        disabled: currentPage === totalPages,\n                                                        className: \"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50\",\n                                                        children: \"Next\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 446,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 430,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 413,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 280,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 249,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\orders\\\\page.tsx\",\n        lineNumber: 248,\n        columnNumber: 5\n    }, this);\n}\n_s(OrdersPage, \"nNKzn+8h+elCiP0tsij7Y4S7vAk=\");\n_c = OrdersPage;\nvar _c;\n$RefreshReg$(_c, \"OrdersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/orders/page.tsx\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Capp%5C%5Corders%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);