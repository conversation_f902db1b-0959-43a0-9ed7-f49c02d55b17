{"_format": "hh-sol-artifact-1", "contractName": "WhitelistV2", "sourceName": "contracts/WhitelistV2.sol", "abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"inputs": [{"internalType": "address", "name": "target", "type": "address"}], "name": "AddressEmptyCode", "type": "error"}, {"inputs": [{"internalType": "address", "name": "implementation", "type": "address"}], "name": "ERC1967InvalidImplementation", "type": "error"}, {"inputs": [], "name": "ERC1967Non<PERSON>ayable", "type": "error"}, {"inputs": [], "name": "FailedCall", "type": "error"}, {"inputs": [], "name": "InvalidInitialization", "type": "error"}, {"inputs": [], "name": "NotInitializing", "type": "error"}, {"inputs": [], "name": "UUPSUnauthorizedCallContext", "type": "error"}, {"inputs": [{"internalType": "bytes32", "name": "slot", "type": "bytes32"}], "name": "UUPSUnsupportedProxiableUUID", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}], "name": "AddressFrozen", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}], "name": "AddressUnfrozen", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "version", "type": "uint64"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}], "name": "KycApproved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}], "name": "KycRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "implementation", "type": "address"}], "name": "Upgraded", "type": "event"}, {"inputs": [], "name": "AGENT_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "UPGRADE_INTERFACE_VERSION", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_agent", "type": "address"}], "name": "addAgent", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_address", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_address", "type": "address"}], "name": "approveKyc", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "_addresses", "type": "address[]"}], "name": "batchAdd<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "_addresses", "type": "address[]"}], "name": "batchApproveKyc", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "_addresses", "type": "address[]"}], "name": "batchFreezeAddresses", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "_addresses", "type": "address[]"}], "name": "batchRemoveFrom<PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "_addresses", "type": "address[]"}], "name": "batchRevokeKyc", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "_addresses", "type": "address[]"}], "name": "batchUnfreezeAddresses", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_address", "type": "address"}], "name": "freezeAddress", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "admin", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_admin", "type": "address"}], "name": "initializeWithAgent", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_address", "type": "address"}], "name": "isFrozen", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_address", "type": "address"}], "name": "isKycApproved", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_address", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "proxiableUUID", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_agent", "type": "address"}], "name": "removeAgent", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_address", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_address", "type": "address"}], "name": "revokeKyc", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_address", "type": "address"}], "name": "unfreeze<PERSON>ddress", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "upgradeToAndCall", "outputs": [], "stateMutability": "payable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}