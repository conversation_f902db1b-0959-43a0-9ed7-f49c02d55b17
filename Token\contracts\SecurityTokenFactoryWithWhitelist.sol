// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "./SecurityTokenEnhanced.sol";
import "./Whitelist.sol";

/**
 * @title SecurityTokenFactoryWithWhitelist
 * @dev Enhanced factory with KYC and separate whitelist contracts
 * Optimized for size while maintaining all functionality
 */
contract SecurityTokenFactoryWithWhitelist is AccessControl, ReentrancyGuard {
    bytes32 public constant DEPLOYER_ROLE = keccak256("DEPLOYER_ROLE");
    
    // Deployed tokens tracking
    address[] public deployedTokens;
    mapping(string => address) public tokensBySymbol;
    mapping(address => bool) public isDeployedToken;
    mapping(address => TokenInfo) public tokenInfo;
    
    struct TokenInfo {
        address tokenAddress;
        address identityRegistry;
        string name;
        string symbol;
        uint8 decimals;
        uint256 maxSupply;
        address admin;
        uint256 deploymentTimestamp;
    }
    
    // Events
    event TokenDeployed(
        address indexed tokenAddress,
        address indexed identityRegistry,
        string name,
        string symbol,
        uint8 decimals,
        uint256 maxSupply,
        address admin
    );
    
    constructor(address admin) {
        require(admin != address(0), "Invalid admin");
        
        _grantRole(DEFAULT_ADMIN_ROLE, admin);
        _grantRole(DEPLOYER_ROLE, admin);
    }
    
    function deploySecurityToken(
        string memory name,
        string memory symbol,
        uint8 decimals,
        uint256 maxSupply,
        address admin,
        string memory tokenPrice,
        string memory bonusTiers,
        string memory tokenDetails,
        string memory tokenImageUrl
    ) external onlyRole(DEPLOYER_ROLE) nonReentrant returns (address tokenAddress) {
        // Input validation (enhanced security)
        require(bytes(name).length > 0 && bytes(name).length <= 50, "Invalid name");
        require(bytes(symbol).length > 0 && bytes(symbol).length <= 10, "Invalid symbol");
        require(admin != address(0), "Invalid admin");
        require(maxSupply > 0 && maxSupply <= 1e30, "Invalid max supply");
        require(decimals <= 18, "Invalid decimals");
        require(tokensBySymbol[symbol] == address(0), "Symbol exists");
        
        // Deploy whitelist contract first
        Whitelist whitelist = new Whitelist();
        whitelist.initializeWithAgent(admin);
        address identityRegistry = address(whitelist);
        
        // Deploy token with enhanced security and whitelist
        SecurityTokenEnhanced token = new SecurityTokenEnhanced(
            name,
            symbol,
            decimals,
            maxSupply,
            admin,
            tokenPrice,
            bonusTiers,
            tokenImageUrl,
            identityRegistry,
            address(0) // compliance placeholder
        );
        
        tokenAddress = address(token);
        
        // Store token info
        tokenInfo[tokenAddress] = TokenInfo({
            tokenAddress: tokenAddress,
            identityRegistry: identityRegistry,
            name: name,
            symbol: symbol,
            decimals: decimals,
            maxSupply: maxSupply,
            admin: admin,
            deploymentTimestamp: block.timestamp
        });
        
        // Track deployment
        deployedTokens.push(tokenAddress);
        tokensBySymbol[symbol] = tokenAddress;
        isDeployedToken[tokenAddress] = true;
        
        emit TokenDeployed(
            tokenAddress,
            identityRegistry,
            name,
            symbol,
            decimals,
            maxSupply,
            admin
        );
        
        return tokenAddress;
    }
    
    function addDeployer(address deployer) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(deployer != address(0), "Invalid deployer");
        _grantRole(DEPLOYER_ROLE, deployer);
    }
    
    function removeDeployer(address deployer) external onlyRole(DEFAULT_ADMIN_ROLE) {
        _revokeRole(DEPLOYER_ROLE, deployer);
    }
    
    function getTokenInfo(address token) external view returns (TokenInfo memory) {
        require(isDeployedToken[token], "Token not deployed by this factory");
        return tokenInfo[token];
    }
    
    function getTokenAddressBySymbol(string memory symbol) external view returns (address) {
        return tokensBySymbol[symbol];
    }
    
    function getAllDeployedTokens() external view returns (address[] memory) {
        return deployedTokens;
    }
    
    function getDeployedToken(uint256 index) external view returns (address) {
        require(index < deployedTokens.length, "Index out of bounds");
        return deployedTokens[index];
    }
    
    function getTokenCount() external view returns (uint256) {
        return deployedTokens.length;
    }
    
    function getTokensBatch(uint256 start, uint256 count) external view returns (address[] memory) {
        require(start < deployedTokens.length, "Start out of bounds");
        require(count <= 10, "Batch too large");
        
        uint256 end = start + count;
        if (end > deployedTokens.length) {
            end = deployedTokens.length;
        }
        
        address[] memory batch = new address[](end - start);
        for (uint256 i = start; i < end; i++) {
            batch[i - start] = deployedTokens[i];
        }
        
        return batch;
    }
}
