/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/contracts/upgrade/route";
exports.ids = ["app/api/contracts/upgrade/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcontracts%2Fupgrade%2Froute&page=%2Fapi%2Fcontracts%2Fupgrade%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontracts%2Fupgrade%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcontracts%2Fupgrade%2Froute&page=%2Fapi%2Fcontracts%2Fupgrade%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontracts%2Fupgrade%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_tokendev_newroo_admin_panel_src_app_api_contracts_upgrade_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/contracts/upgrade/route.ts */ \"(rsc)/./src/app/api/contracts/upgrade/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/contracts/upgrade/route\",\n        pathname: \"/api/contracts/upgrade\",\n        filename: \"route\",\n        bundlePath: \"app/api/contracts/upgrade/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api\\\\contracts\\\\upgrade\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_tokendev_newroo_admin_panel_src_app_api_contracts_upgrade_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcontracts%2Fupgrade%2Froute&page=%2Fapi%2Fcontracts%2Fupgrade%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontracts%2Fupgrade%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/contracts/upgrade/route.ts":
/*!************************************************!*\
  !*** ./src/app/api/contracts/upgrade/route.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! child_process */ \"child_process\");\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(child_process__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\n// Load private key from environment variable - in production, use a proper secrets management system\nconst PRIVATE_KEY = process.env.CONTRACT_ADMIN_PRIVATE_KEY;\nconst RPC_URLS = {\n    amoy: process.env.AMOY_RPC_URL || 'https://rpc-amoy.polygon.technology',\n    polygon: process.env.POLYGON_RPC_URL || 'https://polygon-rpc.com',\n    unknown: process.env.AMOY_RPC_URL || 'https://rpc-amoy.polygon.technology'\n};\n// Helper function to execute the command line upgrade script\nasync function executeUpgradeScript(contractAddress, contractType, network) {\n    // Calculate path to the project root (assuming admin-panel is a subdirectory)\n    const projectRoot = path__WEBPACK_IMPORTED_MODULE_2___default().resolve(process.cwd(), '..');\n    console.log(`Project root directory: ${projectRoot}`);\n    // Create a temporary batch file to execute the command\n    const tempBatchPath = path__WEBPACK_IMPORTED_MODULE_2___default().join(projectRoot, 'temp-upgrade.bat');\n    const batchContent = `@echo off\ncd Token\nset CONTRACT_ADDRESS=${contractAddress}\nset CONTRACT_TYPE=${contractType}\nnpx hardhat run scripts/04-upgrade-contracts.js --network ${network}\n`;\n    // Write the batch file\n    fs__WEBPACK_IMPORTED_MODULE_1___default().writeFileSync(tempBatchPath, batchContent);\n    console.log(`Created temporary batch file at: ${tempBatchPath}`);\n    return new Promise((resolve, reject)=>{\n        console.log(`Executing batch file from directory: ${projectRoot}`);\n        (0,child_process__WEBPACK_IMPORTED_MODULE_3__.exec)(tempBatchPath, {\n            cwd: projectRoot\n        }, (error, stdout, stderr)=>{\n            // Clean up the temporary batch file\n            try {\n                fs__WEBPACK_IMPORTED_MODULE_1___default().unlinkSync(tempBatchPath);\n                console.log('Temporary batch file deleted');\n            } catch (unlinkError) {\n                console.error('Error deleting temporary batch file:', unlinkError);\n            }\n            if (error) {\n                console.error(`Error executing upgrade script: ${error.message}`);\n                reject(`Error: ${error.message}\\n${stderr}`);\n                return;\n            }\n            resolve(stdout);\n        });\n    });\n}\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { contractAddress, contractType, network = 'amoy' } = body;\n        if (!contractAddress || !contractType) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Contract address and type are required'\n            }, {\n                status: 400\n            });\n        }\n        if (!PRIVATE_KEY) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'CONTRACT_ADMIN_PRIVATE_KEY environment variable is not set',\n                details: 'For security reasons, the API requires a secure method to sign transactions.',\n                clientSideInstructions: true,\n                message: 'The server is not configured with admin credentials. Please use the command-line upgrade script:',\n                commandExample: `# Unix/Linux/Mac:\\ncd Token\\nexport CONTRACT_ADDRESS=${contractAddress}\\nexport CONTRACT_TYPE=${contractType}\\nnpx hardhat run scripts/04-upgrade-contracts.js --network ${network === 'unknown' ? 'amoy' : network}\\n\\n# Windows Command Prompt:\\ncd Token\\nset CONTRACT_ADDRESS=${contractAddress}\\nset CONTRACT_TYPE=${contractType}\\nnpx hardhat run scripts/04-upgrade-contracts.js --network ${network === 'unknown' ? 'amoy' : network}\\n\\n# Windows PowerShell:\\ncd Token\\n$env:CONTRACT_ADDRESS=\"${contractAddress}\"\\n$env:CONTRACT_TYPE=\"${contractType}\"\\nnpx hardhat run scripts/04-upgrade-contracts.js --network ${network === 'unknown' ? 'amoy' : network}`\n            }, {\n                status: 422\n            } // 422 Unprocessable Entity is more appropriate than 500 for this case\n            );\n        }\n        // Get RPC URL for the specified network, defaulting to Amoy\n        const actualNetwork = network === 'unknown' ? 'amoy' : network;\n        try {\n            // Execute the command-line upgrade script instead of trying to do it in-process\n            console.log(`Executing upgrade script for ${contractType} at ${contractAddress} on ${actualNetwork}`);\n            const result = await executeUpgradeScript(contractAddress, contractType, actualNetwork);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: \"Contract upgraded successfully\",\n                details: result\n            });\n        } catch (scriptError) {\n            console.error('Error executing upgrade script:', scriptError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: \"Failed to upgrade contract\",\n                error: scriptError.toString()\n            }, {\n                status: 500\n            });\n        }\n    } catch (error) {\n        console.error('Error upgrading contract:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error.message || 'An unknown error occurred'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/contracts/upgrade/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcontracts%2Fupgrade%2Froute&page=%2Fapi%2Fcontracts%2Fupgrade%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontracts%2Fupgrade%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();