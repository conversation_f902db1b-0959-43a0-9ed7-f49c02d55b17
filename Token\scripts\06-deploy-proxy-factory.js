const { ethers, upgrades } = require("hardhat");
const fs = require('fs');
const path = require('path');

async function main() {
  try {
    console.log("🚀 Deploying SecurityTokenFactory via Proxy Pattern...");
    console.log("=" .repeat(70));

    // Get the deployer account
    const [deployer] = await ethers.getSigners();
    const balance = await ethers.provider.getBalance(deployer.address);
    const network = await ethers.provider.getNetwork();
    const networkName = network.name === "unknown" ? "amoy" : network.name;

    console.log("Deploying with account:", deployer.address);
    console.log("Account balance:", ethers.formatEther(balance), "ETH");
    console.log("Network:", networkName, "Chain ID:", network.chainId.toString());

    console.log("\n📋 Deployment Strategy:");
    console.log("- Using OpenZeppelin Upgradeable Proxy Pattern");
    console.log("- This bypasses contract size limits");
    console.log("- All security features included");

    console.log("\n🏗️  Deploying SecurityTokenFactory via Proxy...");

    // Deploy the factory using OpenZeppelin upgrades
    const SecurityTokenFactory = await ethers.getContractFactory("SecurityTokenFactory");
    
    console.log("Deploying implementation contract...");
    const factory = await upgrades.deployProxy(
      SecurityTokenFactory,
      [deployer.address], // admin parameter
      {
        initializer: 'initialize',
        kind: 'uups', // Use UUPS proxy pattern for gas efficiency
        gasLimit: 6000000,
      }
    );

    await factory.waitForDeployment();
    const factoryAddress = await factory.getAddress();
    
    console.log("✅ SecurityTokenFactory Proxy deployed to:", factoryAddress);

    // Get implementation address
    const implementationAddress = await upgrades.erc1967.getImplementationAddress(factoryAddress);
    console.log("✅ Implementation contract at:", implementationAddress);

    // Verify the contract is properly set up
    console.log("\n🔍 Verifying Contract Setup...");
    
    try {
      const deployerRole = await factory.DEPLOYER_ROLE();
      console.log("✅ DEPLOYER_ROLE:", deployerRole);
      
      const hasRole = await factory.hasRole(deployerRole, deployer.address);
      console.log("✅ Admin has DEPLOYER_ROLE:", hasRole);
      
      const tokenCount = await factory.getTokenCount();
      console.log("✅ Initial token count:", tokenCount.toString());
      
      // Check implementation addresses
      const tokenImpl = await factory.securityTokenImplementation();
      const whitelistImpl = await factory.whitelistImplementation();
      const kycImpl = await factory.whitelistWithKYCImplementation();
      
      console.log("✅ SecurityToken Implementation:", tokenImpl);
      console.log("✅ Whitelist Implementation:", whitelistImpl);
      console.log("✅ KYC Whitelist Implementation:", kycImpl);
      
    } catch (error) {
      console.log("❌ Contract verification failed:", error.message);
    }

    // Test deployment functionality
    console.log("\n🧪 Testing Token Deployment...");
    try {
      // Test deploying a simple token
      const testTx = await factory.deploySecurityToken(
        "Test Security Token",
        "TST",
        0, // 0 decimals
        1000000, // 1M max supply
        deployer.address,
        "1 USD",
        "No tiers",
        "Test token for proxy factory",
        ""
      );
      
      const receipt = await testTx.wait();
      console.log("✅ Test token deployment successful");
      console.log("✅ Gas used:", receipt.gasUsed.toString());
      
      // Get the deployed token address
      const newTokenCount = await factory.getTokenCount();
      console.log("✅ New token count:", newTokenCount.toString());
      
      if (newTokenCount > 0) {
        const testTokenAddress = await factory.getDeployedToken(0);
        console.log("✅ Test token deployed at:", testTokenAddress);
      }
      
    } catch (error) {
      console.log("❌ Test deployment failed:", error.message);
    }

    // Save deployment information
    const deploymentInfo = {
      network: networkName,
      chainId: network.chainId.toString(),
      factoryAddress: factoryAddress,
      implementationAddress: implementationAddress,
      adminAddress: deployer.address,
      deploymentHash: factory.deploymentTransaction().hash,
      timestamp: new Date().toISOString(),
      contractType: "SecurityTokenFactory",
      deploymentMethod: "OpenZeppelin UUPS Proxy",
      features: {
        securityAuditFixes: true,
        emergencyControls: true,
        transferFees: true,
        complianceIntegration: true,
        kycSupport: true,
        imageUrlSupport: true,
        enumerationSupport: true,
        upgradeableProxy: true
      }
    };

    // Create deployments directory if it doesn't exist
    const deploymentsDir = path.join(__dirname, "../deployments");
    if (!fs.existsSync(deploymentsDir)) {
      fs.mkdirSync(deploymentsDir, { recursive: true });
    }

    // Save deployment file
    const deploymentFile = path.join(deploymentsDir, `${networkName}-proxy.json`);
    fs.writeFileSync(deploymentFile, JSON.stringify(deploymentInfo, null, 2));

    console.log("\n💾 Deployment information saved to:", deploymentFile);

    console.log("\n🎯 ADMIN PANEL CONFIGURATION:");
    console.log("Update your admin-panel/src/config.ts:");
    console.log(`amoy: {`);
    console.log(`  factory: "${factoryAddress}",`);
    console.log(`  // Previous factory (backup): "0x69a6536629369F8948f47b897045929a57c630Fd",`);
    console.log(`}`);

    console.log("\n🔄 Next Steps:");
    console.log("   1. Update admin panel config with new factory address");
    console.log("   2. Test token creation in admin panel");
    console.log("   3. Verify all security features are working");
    console.log("   4. The factory is upgradeable for future improvements");

    console.log("\n🎉 Proxy Factory Deployment Completed Successfully!");
    console.log("✅ All security audit fixes included");
    console.log("✅ Contract size limit bypassed via proxy");
    console.log("✅ Emergency controls available");
    console.log("✅ Transfer fees supported");
    console.log("✅ Full ERC-3643 compliance");
    console.log("✅ Upgradeable for future enhancements");

  } catch (error) {
    console.error("❌ Deployment failed:", error);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
