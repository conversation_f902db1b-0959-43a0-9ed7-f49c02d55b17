"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/create-modular-token/page",{

/***/ "(app-pages-browser)/./node_modules/ethers/lib.esm/utils/fixednumber.js":
/*!**********************************************************!*\
  !*** ./node_modules/ethers/lib.esm/utils/fixednumber.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FixedNumber: () => (/* binding */ FixedNumber)\n/* harmony export */ });\n/* harmony import */ var _data_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./data.js */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/data.js\");\n/* harmony import */ var _errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./errors.js */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/errors.js\");\n/* harmony import */ var _maths_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./maths.js */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/maths.js\");\n/* harmony import */ var _properties_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./properties.js */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/properties.js\");\n/**\n *  The **FixedNumber** class permits using values with decimal places,\n *  using fixed-pont math.\n *\n *  Fixed-point math is still based on integers under-the-hood, but uses an\n *  internal offset to store fractional components below, and each operation\n *  corrects for this after each operation.\n *\n *  @_section: api/utils/fixed-point-math:Fixed-Point Maths  [about-fixed-point-math]\n */\n\n\n\n\nconst BN_N1 = BigInt(-1);\nconst BN_0 = BigInt(0);\nconst BN_1 = BigInt(1);\nconst BN_5 = BigInt(5);\nconst _guard = {};\n// Constant to pull zeros from for multipliers\nlet Zeros = \"0000\";\nwhile (Zeros.length < 80) {\n    Zeros += Zeros;\n}\n// Returns a string \"1\" followed by decimal \"0\"s\nfunction getTens(decimals) {\n    let result = Zeros;\n    while (result.length < decimals) {\n        result += result;\n    }\n    return BigInt(\"1\" + result.substring(0, decimals));\n}\nfunction checkValue(val, format, safeOp) {\n    const width = BigInt(format.width);\n    if (format.signed) {\n        const limit = (BN_1 << (width - BN_1));\n        (0,_errors_js__WEBPACK_IMPORTED_MODULE_0__.assert)(safeOp == null || (val >= -limit && val < limit), \"overflow\", \"NUMERIC_FAULT\", {\n            operation: safeOp, fault: \"overflow\", value: val\n        });\n        if (val > BN_0) {\n            val = (0,_maths_js__WEBPACK_IMPORTED_MODULE_1__.fromTwos)((0,_maths_js__WEBPACK_IMPORTED_MODULE_1__.mask)(val, width), width);\n        }\n        else {\n            val = -(0,_maths_js__WEBPACK_IMPORTED_MODULE_1__.fromTwos)((0,_maths_js__WEBPACK_IMPORTED_MODULE_1__.mask)(-val, width), width);\n        }\n    }\n    else {\n        const limit = (BN_1 << width);\n        (0,_errors_js__WEBPACK_IMPORTED_MODULE_0__.assert)(safeOp == null || (val >= 0 && val < limit), \"overflow\", \"NUMERIC_FAULT\", {\n            operation: safeOp, fault: \"overflow\", value: val\n        });\n        val = (((val % limit) + limit) % limit) & (limit - BN_1);\n    }\n    return val;\n}\nfunction getFormat(value) {\n    if (typeof (value) === \"number\") {\n        value = `fixed128x${value}`;\n    }\n    let signed = true;\n    let width = 128;\n    let decimals = 18;\n    if (typeof (value) === \"string\") {\n        // Parse the format string\n        if (value === \"fixed\") {\n            // defaults...\n        }\n        else if (value === \"ufixed\") {\n            signed = false;\n        }\n        else {\n            const match = value.match(/^(u?)fixed([0-9]+)x([0-9]+)$/);\n            (0,_errors_js__WEBPACK_IMPORTED_MODULE_0__.assertArgument)(match, \"invalid fixed format\", \"format\", value);\n            signed = (match[1] !== \"u\");\n            width = parseInt(match[2]);\n            decimals = parseInt(match[3]);\n        }\n    }\n    else if (value) {\n        // Extract the values from the object\n        const v = value;\n        const check = (key, type, defaultValue) => {\n            if (v[key] == null) {\n                return defaultValue;\n            }\n            (0,_errors_js__WEBPACK_IMPORTED_MODULE_0__.assertArgument)(typeof (v[key]) === type, \"invalid fixed format (\" + key + \" not \" + type + \")\", \"format.\" + key, v[key]);\n            return v[key];\n        };\n        signed = check(\"signed\", \"boolean\", signed);\n        width = check(\"width\", \"number\", width);\n        decimals = check(\"decimals\", \"number\", decimals);\n    }\n    (0,_errors_js__WEBPACK_IMPORTED_MODULE_0__.assertArgument)((width % 8) === 0, \"invalid FixedNumber width (not byte aligned)\", \"format.width\", width);\n    (0,_errors_js__WEBPACK_IMPORTED_MODULE_0__.assertArgument)(decimals <= 80, \"invalid FixedNumber decimals (too large)\", \"format.decimals\", decimals);\n    const name = (signed ? \"\" : \"u\") + \"fixed\" + String(width) + \"x\" + String(decimals);\n    return { signed, width, decimals, name };\n}\nfunction toString(val, decimals) {\n    let negative = \"\";\n    if (val < BN_0) {\n        negative = \"-\";\n        val *= BN_N1;\n    }\n    let str = val.toString();\n    // No decimal point for whole values\n    if (decimals === 0) {\n        return (negative + str);\n    }\n    // Pad out to the whole component (including a whole digit)\n    while (str.length <= decimals) {\n        str = Zeros + str;\n    }\n    // Insert the decimal point\n    const index = str.length - decimals;\n    str = str.substring(0, index) + \".\" + str.substring(index);\n    // Trim the whole component (leaving at least one 0)\n    while (str[0] === \"0\" && str[1] !== \".\") {\n        str = str.substring(1);\n    }\n    // Trim the decimal component (leaving at least one 0)\n    while (str[str.length - 1] === \"0\" && str[str.length - 2] !== \".\") {\n        str = str.substring(0, str.length - 1);\n    }\n    return (negative + str);\n}\n/**\n *  A FixedNumber represents a value over its [[FixedFormat]]\n *  arithmetic field.\n *\n *  A FixedNumber can be used to perform math, losslessly, on\n *  values which have decmial places.\n *\n *  A FixedNumber has a fixed bit-width to store values in, and stores all\n *  values internally by multiplying the value by 10 raised to the power of\n *  %%decimals%%.\n *\n *  If operations are performed that cause a value to grow too high (close to\n *  positive infinity) or too low (close to negative infinity), the value\n *  is said to //overflow//.\n *\n *  For example, an 8-bit signed value, with 0 decimals may only be within\n *  the range ``-128`` to ``127``; so ``-128 - 1`` will overflow and become\n *  ``127``. Likewise, ``127 + 1`` will overflow and become ``-127``.\n *\n *  Many operation have a normal and //unsafe// variant. The normal variant\n *  will throw a [[NumericFaultError]] on any overflow, while the //unsafe//\n *  variant will silently allow overflow, corrupting its value value.\n *\n *  If operations are performed that cause a value to become too small\n *  (close to zero), the value loses precison and is said to //underflow//.\n *\n *  For example, a value with 1 decimal place may store a number as small\n *  as ``0.1``, but the value of ``0.1 / 2`` is ``0.05``, which cannot fit\n *  into 1 decimal place, so underflow occurs which means precision is lost\n *  and the value becomes ``0``.\n *\n *  Some operations have a normal and //signalling// variant. The normal\n *  variant will silently ignore underflow, while the //signalling// variant\n *  will thow a [[NumericFaultError]] on underflow.\n */\nclass FixedNumber {\n    /**\n     *  The specific fixed-point arithmetic field for this value.\n     */\n    format;\n    #format;\n    // The actual value (accounting for decimals)\n    #val;\n    // A base-10 value to multiple values by to maintain the magnitude\n    #tens;\n    /**\n     *  This is a property so console.log shows a human-meaningful value.\n     *\n     *  @private\n     */\n    _value;\n    // Use this when changing this file to get some typing info,\n    // but then switch to any to mask the internal type\n    //constructor(guard: any, value: bigint, format: _FixedFormat) {\n    /**\n     *  @private\n     */\n    constructor(guard, value, format) {\n        (0,_errors_js__WEBPACK_IMPORTED_MODULE_0__.assertPrivate)(guard, _guard, \"FixedNumber\");\n        this.#val = value;\n        this.#format = format;\n        const _value = toString(value, format.decimals);\n        (0,_properties_js__WEBPACK_IMPORTED_MODULE_2__.defineProperties)(this, { format: format.name, _value });\n        this.#tens = getTens(format.decimals);\n    }\n    /**\n     *  If true, negative values are permitted, otherwise only\n     *  positive values and zero are allowed.\n     */\n    get signed() { return this.#format.signed; }\n    /**\n     *  The number of bits available to store the value.\n     */\n    get width() { return this.#format.width; }\n    /**\n     *  The number of decimal places in the fixed-point arithment field.\n     */\n    get decimals() { return this.#format.decimals; }\n    /**\n     *  The value as an integer, based on the smallest unit the\n     *  [[decimals]] allow.\n     */\n    get value() { return this.#val; }\n    #checkFormat(other) {\n        (0,_errors_js__WEBPACK_IMPORTED_MODULE_0__.assertArgument)(this.format === other.format, \"incompatible format; use fixedNumber.toFormat\", \"other\", other);\n    }\n    #checkValue(val, safeOp) {\n        /*\n                const width = BigInt(this.width);\n                if (this.signed) {\n                    const limit = (BN_1 << (width - BN_1));\n                    assert(safeOp == null || (val >= -limit  && val < limit), \"overflow\", \"NUMERIC_FAULT\", {\n                        operation: <string>safeOp, fault: \"overflow\", value: val\n                    });\n        \n                    if (val > BN_0) {\n                        val = fromTwos(mask(val, width), width);\n                    } else {\n                        val = -fromTwos(mask(-val, width), width);\n                    }\n        \n                } else {\n                    const masked = mask(val, width);\n                    assert(safeOp == null || (val >= 0 && val === masked), \"overflow\", \"NUMERIC_FAULT\", {\n                        operation: <string>safeOp, fault: \"overflow\", value: val\n                    });\n                    val = masked;\n                }\n        */\n        val = checkValue(val, this.#format, safeOp);\n        return new FixedNumber(_guard, val, this.#format);\n    }\n    #add(o, safeOp) {\n        this.#checkFormat(o);\n        return this.#checkValue(this.#val + o.#val, safeOp);\n    }\n    /**\n     *  Returns a new [[FixedNumber]] with the result of %%this%% added\n     *  to %%other%%, ignoring overflow.\n     */\n    addUnsafe(other) { return this.#add(other); }\n    /**\n     *  Returns a new [[FixedNumber]] with the result of %%this%% added\n     *  to %%other%%. A [[NumericFaultError]] is thrown if overflow\n     *  occurs.\n     */\n    add(other) { return this.#add(other, \"add\"); }\n    #sub(o, safeOp) {\n        this.#checkFormat(o);\n        return this.#checkValue(this.#val - o.#val, safeOp);\n    }\n    /**\n     *  Returns a new [[FixedNumber]] with the result of %%other%% subtracted\n     *  from %%this%%, ignoring overflow.\n     */\n    subUnsafe(other) { return this.#sub(other); }\n    /**\n     *  Returns a new [[FixedNumber]] with the result of %%other%% subtracted\n     *  from %%this%%. A [[NumericFaultError]] is thrown if overflow\n     *  occurs.\n     */\n    sub(other) { return this.#sub(other, \"sub\"); }\n    #mul(o, safeOp) {\n        this.#checkFormat(o);\n        return this.#checkValue((this.#val * o.#val) / this.#tens, safeOp);\n    }\n    /**\n     *  Returns a new [[FixedNumber]] with the result of %%this%% multiplied\n     *  by %%other%%, ignoring overflow and underflow (precision loss).\n     */\n    mulUnsafe(other) { return this.#mul(other); }\n    /**\n     *  Returns a new [[FixedNumber]] with the result of %%this%% multiplied\n     *  by %%other%%. A [[NumericFaultError]] is thrown if overflow\n     *  occurs.\n     */\n    mul(other) { return this.#mul(other, \"mul\"); }\n    /**\n     *  Returns a new [[FixedNumber]] with the result of %%this%% multiplied\n     *  by %%other%%. A [[NumericFaultError]] is thrown if overflow\n     *  occurs or if underflow (precision loss) occurs.\n     */\n    mulSignal(other) {\n        this.#checkFormat(other);\n        const value = this.#val * other.#val;\n        (0,_errors_js__WEBPACK_IMPORTED_MODULE_0__.assert)((value % this.#tens) === BN_0, \"precision lost during signalling mul\", \"NUMERIC_FAULT\", {\n            operation: \"mulSignal\", fault: \"underflow\", value: this\n        });\n        return this.#checkValue(value / this.#tens, \"mulSignal\");\n    }\n    #div(o, safeOp) {\n        (0,_errors_js__WEBPACK_IMPORTED_MODULE_0__.assert)(o.#val !== BN_0, \"division by zero\", \"NUMERIC_FAULT\", {\n            operation: \"div\", fault: \"divide-by-zero\", value: this\n        });\n        this.#checkFormat(o);\n        return this.#checkValue((this.#val * this.#tens) / o.#val, safeOp);\n    }\n    /**\n     *  Returns a new [[FixedNumber]] with the result of %%this%% divided\n     *  by %%other%%, ignoring underflow (precision loss). A\n     *  [[NumericFaultError]] is thrown if overflow occurs.\n     */\n    divUnsafe(other) { return this.#div(other); }\n    /**\n     *  Returns a new [[FixedNumber]] with the result of %%this%% divided\n     *  by %%other%%, ignoring underflow (precision loss). A\n     *  [[NumericFaultError]] is thrown if overflow occurs.\n     */\n    div(other) { return this.#div(other, \"div\"); }\n    /**\n     *  Returns a new [[FixedNumber]] with the result of %%this%% divided\n     *  by %%other%%. A [[NumericFaultError]] is thrown if underflow\n     *  (precision loss) occurs.\n     */\n    divSignal(other) {\n        (0,_errors_js__WEBPACK_IMPORTED_MODULE_0__.assert)(other.#val !== BN_0, \"division by zero\", \"NUMERIC_FAULT\", {\n            operation: \"div\", fault: \"divide-by-zero\", value: this\n        });\n        this.#checkFormat(other);\n        const value = (this.#val * this.#tens);\n        (0,_errors_js__WEBPACK_IMPORTED_MODULE_0__.assert)((value % other.#val) === BN_0, \"precision lost during signalling div\", \"NUMERIC_FAULT\", {\n            operation: \"divSignal\", fault: \"underflow\", value: this\n        });\n        return this.#checkValue(value / other.#val, \"divSignal\");\n    }\n    /**\n     *  Returns a comparison result between %%this%% and %%other%%.\n     *\n     *  This is suitable for use in sorting, where ``-1`` implies %%this%%\n     *  is smaller, ``1`` implies %%this%% is larger and ``0`` implies\n     *  both are equal.\n     */\n    cmp(other) {\n        let a = this.value, b = other.value;\n        // Coerce a and b to the same magnitude\n        const delta = this.decimals - other.decimals;\n        if (delta > 0) {\n            b *= getTens(delta);\n        }\n        else if (delta < 0) {\n            a *= getTens(-delta);\n        }\n        // Comnpare\n        if (a < b) {\n            return -1;\n        }\n        if (a > b) {\n            return 1;\n        }\n        return 0;\n    }\n    /**\n     *  Returns true if %%other%% is equal to %%this%%.\n     */\n    eq(other) { return this.cmp(other) === 0; }\n    /**\n     *  Returns true if %%other%% is less than to %%this%%.\n     */\n    lt(other) { return this.cmp(other) < 0; }\n    /**\n     *  Returns true if %%other%% is less than or equal to %%this%%.\n     */\n    lte(other) { return this.cmp(other) <= 0; }\n    /**\n     *  Returns true if %%other%% is greater than to %%this%%.\n     */\n    gt(other) { return this.cmp(other) > 0; }\n    /**\n     *  Returns true if %%other%% is greater than or equal to %%this%%.\n     */\n    gte(other) { return this.cmp(other) >= 0; }\n    /**\n     *  Returns a new [[FixedNumber]] which is the largest **integer**\n     *  that is less than or equal to %%this%%.\n     *\n     *  The decimal component of the result will always be ``0``.\n     */\n    floor() {\n        let val = this.#val;\n        if (this.#val < BN_0) {\n            val -= this.#tens - BN_1;\n        }\n        val = (this.#val / this.#tens) * this.#tens;\n        return this.#checkValue(val, \"floor\");\n    }\n    /**\n     *  Returns a new [[FixedNumber]] which is the smallest **integer**\n     *  that is greater than or equal to %%this%%.\n     *\n     *  The decimal component of the result will always be ``0``.\n     */\n    ceiling() {\n        let val = this.#val;\n        if (this.#val > BN_0) {\n            val += this.#tens - BN_1;\n        }\n        val = (this.#val / this.#tens) * this.#tens;\n        return this.#checkValue(val, \"ceiling\");\n    }\n    /**\n     *  Returns a new [[FixedNumber]] with the decimal component\n     *  rounded up on ties at %%decimals%% places.\n     */\n    round(decimals) {\n        if (decimals == null) {\n            decimals = 0;\n        }\n        // Not enough precision to not already be rounded\n        if (decimals >= this.decimals) {\n            return this;\n        }\n        const delta = this.decimals - decimals;\n        const bump = BN_5 * getTens(delta - 1);\n        let value = this.value + bump;\n        const tens = getTens(delta);\n        value = (value / tens) * tens;\n        checkValue(value, this.#format, \"round\");\n        return new FixedNumber(_guard, value, this.#format);\n    }\n    /**\n     *  Returns true if %%this%% is equal to ``0``.\n     */\n    isZero() { return (this.#val === BN_0); }\n    /**\n     *  Returns true if %%this%% is less than ``0``.\n     */\n    isNegative() { return (this.#val < BN_0); }\n    /**\n     *  Returns the string representation of %%this%%.\n     */\n    toString() { return this._value; }\n    /**\n     *  Returns a float approximation.\n     *\n     *  Due to IEEE 754 precission (or lack thereof), this function\n     *  can only return an approximation and most values will contain\n     *  rounding errors.\n     */\n    toUnsafeFloat() { return parseFloat(this.toString()); }\n    /**\n     *  Return a new [[FixedNumber]] with the same value but has had\n     *  its field set to %%format%%.\n     *\n     *  This will throw if the value cannot fit into %%format%%.\n     */\n    toFormat(format) {\n        return FixedNumber.fromString(this.toString(), format);\n    }\n    /**\n     *  Creates a new [[FixedNumber]] for %%value%% divided by\n     *  %%decimal%% places with %%format%%.\n     *\n     *  This will throw a [[NumericFaultError]] if %%value%% (once adjusted\n     *  for %%decimals%%) cannot fit in %%format%%, either due to overflow\n     *  or underflow (precision loss).\n     */\n    static fromValue(_value, _decimals, _format) {\n        const decimals = (_decimals == null) ? 0 : (0,_maths_js__WEBPACK_IMPORTED_MODULE_1__.getNumber)(_decimals);\n        const format = getFormat(_format);\n        let value = (0,_maths_js__WEBPACK_IMPORTED_MODULE_1__.getBigInt)(_value, \"value\");\n        const delta = decimals - format.decimals;\n        if (delta > 0) {\n            const tens = getTens(delta);\n            (0,_errors_js__WEBPACK_IMPORTED_MODULE_0__.assert)((value % tens) === BN_0, \"value loses precision for format\", \"NUMERIC_FAULT\", {\n                operation: \"fromValue\", fault: \"underflow\", value: _value\n            });\n            value /= tens;\n        }\n        else if (delta < 0) {\n            value *= getTens(-delta);\n        }\n        checkValue(value, format, \"fromValue\");\n        return new FixedNumber(_guard, value, format);\n    }\n    /**\n     *  Creates a new [[FixedNumber]] for %%value%% with %%format%%.\n     *\n     *  This will throw a [[NumericFaultError]] if %%value%% cannot fit\n     *  in %%format%%, either due to overflow or underflow (precision loss).\n     */\n    static fromString(_value, _format) {\n        const match = _value.match(/^(-?)([0-9]*)\\.?([0-9]*)$/);\n        (0,_errors_js__WEBPACK_IMPORTED_MODULE_0__.assertArgument)(match && (match[2].length + match[3].length) > 0, \"invalid FixedNumber string value\", \"value\", _value);\n        const format = getFormat(_format);\n        let whole = (match[2] || \"0\"), decimal = (match[3] || \"\");\n        // Pad out the decimals\n        while (decimal.length < format.decimals) {\n            decimal += Zeros;\n        }\n        // Check precision is safe\n        (0,_errors_js__WEBPACK_IMPORTED_MODULE_0__.assert)(decimal.substring(format.decimals).match(/^0*$/), \"too many decimals for format\", \"NUMERIC_FAULT\", {\n            operation: \"fromString\", fault: \"underflow\", value: _value\n        });\n        // Remove extra padding\n        decimal = decimal.substring(0, format.decimals);\n        const value = BigInt(match[1] + whole + decimal);\n        checkValue(value, format, \"fromString\");\n        return new FixedNumber(_guard, value, format);\n    }\n    /**\n     *  Creates a new [[FixedNumber]] with the big-endian representation\n     *  %%value%% with %%format%%.\n     *\n     *  This will throw a [[NumericFaultError]] if %%value%% cannot fit\n     *  in %%format%% due to overflow.\n     */\n    static fromBytes(_value, _format) {\n        let value = (0,_maths_js__WEBPACK_IMPORTED_MODULE_1__.toBigInt)((0,_data_js__WEBPACK_IMPORTED_MODULE_3__.getBytes)(_value, \"value\"));\n        const format = getFormat(_format);\n        if (format.signed) {\n            value = (0,_maths_js__WEBPACK_IMPORTED_MODULE_1__.fromTwos)(value, format.width);\n        }\n        checkValue(value, format, \"fromBytes\");\n        return new FixedNumber(_guard, value, format);\n    }\n}\n//const f1 = FixedNumber.fromString(\"12.56\", \"fixed16x2\");\n//const f2 = FixedNumber.fromString(\"0.3\", \"fixed16x2\");\n//console.log(f1.divSignal(f2));\n//const BUMP = FixedNumber.from(\"0.5\");\n//# sourceMappingURL=fixednumber.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/ethers/lib.esm/utils/fixednumber.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js":
/*!****************************************************!*\
  !*** ./node_modules/ethers/lib.esm/utils/units.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatEther: () => (/* binding */ formatEther),\n/* harmony export */   formatUnits: () => (/* binding */ formatUnits),\n/* harmony export */   parseEther: () => (/* binding */ parseEther),\n/* harmony export */   parseUnits: () => (/* binding */ parseUnits)\n/* harmony export */ });\n/* harmony import */ var _errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./errors.js */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/errors.js\");\n/* harmony import */ var _fixednumber_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./fixednumber.js */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/fixednumber.js\");\n/* harmony import */ var _maths_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./maths.js */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/maths.js\");\n/**\n *  Most interactions with Ethereum requires integer values, which use\n *  the smallest magnitude unit.\n *\n *  For example, imagine dealing with dollars and cents. Since dollars\n *  are divisible, non-integer values are possible, such as ``$10.77``.\n *  By using the smallest indivisible unit (i.e. cents), the value can\n *  be kept as the integer ``1077``.\n *\n *  When receiving decimal input from the user (as a decimal string),\n *  the value should be converted to an integer and when showing a user\n *  a value, the integer value should be converted to a decimal string.\n *\n *  This creates a clear distinction, between values to be used by code\n *  (integers) and values used for display logic to users (decimals).\n *\n *  The native unit in Ethereum, //ether// is divisible to 18 decimal places,\n *  where each individual unit is called a //wei//.\n *\n *  @_subsection api/utils:Unit Conversion  [about-units]\n */\n\n\n\nconst names = [\n    \"wei\",\n    \"kwei\",\n    \"mwei\",\n    \"gwei\",\n    \"szabo\",\n    \"finney\",\n    \"ether\",\n];\n/**\n *  Converts %%value%% into a //decimal string//, assuming %%unit%% decimal\n *  places. The %%unit%% may be the number of decimal places or the name of\n *  a unit (e.g. ``\"gwei\"`` for 9 decimal places).\n *\n */\nfunction formatUnits(value, unit) {\n    let decimals = 18;\n    if (typeof (unit) === \"string\") {\n        const index = names.indexOf(unit);\n        (0,_errors_js__WEBPACK_IMPORTED_MODULE_0__.assertArgument)(index >= 0, \"invalid unit\", \"unit\", unit);\n        decimals = 3 * index;\n    }\n    else if (unit != null) {\n        decimals = (0,_maths_js__WEBPACK_IMPORTED_MODULE_1__.getNumber)(unit, \"unit\");\n    }\n    return _fixednumber_js__WEBPACK_IMPORTED_MODULE_2__.FixedNumber.fromValue(value, decimals, { decimals, width: 512 }).toString();\n}\n/**\n *  Converts the //decimal string// %%value%% to a BigInt, assuming\n *  %%unit%% decimal places. The %%unit%% may the number of decimal places\n *  or the name of a unit (e.g. ``\"gwei\"`` for 9 decimal places).\n */\nfunction parseUnits(value, unit) {\n    (0,_errors_js__WEBPACK_IMPORTED_MODULE_0__.assertArgument)(typeof (value) === \"string\", \"value must be a string\", \"value\", value);\n    let decimals = 18;\n    if (typeof (unit) === \"string\") {\n        const index = names.indexOf(unit);\n        (0,_errors_js__WEBPACK_IMPORTED_MODULE_0__.assertArgument)(index >= 0, \"invalid unit\", \"unit\", unit);\n        decimals = 3 * index;\n    }\n    else if (unit != null) {\n        decimals = (0,_maths_js__WEBPACK_IMPORTED_MODULE_1__.getNumber)(unit, \"unit\");\n    }\n    return _fixednumber_js__WEBPACK_IMPORTED_MODULE_2__.FixedNumber.fromString(value, { decimals, width: 512 }).value;\n}\n/**\n *  Converts %%value%% into a //decimal string// using 18 decimal places.\n */\nfunction formatEther(wei) {\n    return formatUnits(wei, 18);\n}\n/**\n *  Converts the //decimal string// %%ether%% to a BigInt, using 18\n *  decimal places.\n */\nfunction parseEther(ether) {\n    return parseUnits(ether, 18);\n}\n//# sourceMappingURL=units.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/create-modular-token/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/create-modular-token/page.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CreateModularTokenPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// Contract addresses from environment\nconst SECURITY_TOKEN_CORE_ADDRESS = \"******************************************\";\nconst UPGRADE_MANAGER_ADDRESS = \"******************************************\";\nconst MODULAR_TOKEN_FACTORY_ADDRESS = \"******************************************\";\nfunction CreateModularTokenPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    // State Management\n    const [provider, setProvider] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [signer, setSigner] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deployedToken, setDeployedToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        symbol: '',\n        decimals: 0,\n        maxSupply: '10000000',\n        adminAddress: '',\n        tokenPrice: '1.00 USD',\n        bonusTiers: 'Early Bird: 10%, Standard: 5%, Late: 0%',\n        tokenDetails: 'Security token with advanced compliance features',\n        tokenImageUrl: ''\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CreateModularTokenPage.useEffect\": ()=>{\n            initializeProvider();\n        }\n    }[\"CreateModularTokenPage.useEffect\"], []);\n    const initializeProvider = async ()=>{\n        try {\n            if ( true && window.ethereum) {\n                const provider = new ethers__WEBPACK_IMPORTED_MODULE_4__.BrowserProvider(window.ethereum);\n                await provider.send('eth_requestAccounts', []);\n                const signer = await provider.getSigner();\n                const address = await signer.getAddress();\n                setProvider(provider);\n                setSigner(signer);\n                setFormData((prev)=>({\n                        ...prev,\n                        adminAddress: address\n                    }));\n            } else {\n                setError('MetaMask not found. Please install MetaMask to create tokens.');\n            }\n        } catch (error) {\n            console.error('Error initializing provider:', error);\n            setError('Failed to connect to wallet');\n        }\n    };\n    const handleInputChange = (e)=>{\n        const { name, value, type } = e.target;\n        let processedValue = value;\n        if (name === 'decimals') {\n            processedValue = parseInt(value, 10);\n        }\n        setFormData({\n            ...formData,\n            [name]: processedValue\n        });\n    };\n    const deployModularToken = async ()=>{\n        if (!signer || !MODULAR_TOKEN_FACTORY_ADDRESS) {\n            setError('Please connect your wallet first');\n            return;\n        }\n        setIsSubmitting(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            // Import the ModularTokenFactory ABI\n            const ModularTokenFactoryABI = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_contracts_ModularTokenFactory_json\").then(__webpack_require__.t.bind(__webpack_require__, /*! ../../contracts/ModularTokenFactory.json */ \"(app-pages-browser)/./src/contracts/ModularTokenFactory.json\", 19));\n            // Create factory contract instance\n            const factory = new ethers__WEBPACK_IMPORTED_MODULE_5__.Contract(MODULAR_TOKEN_FACTORY_ADDRESS, ModularTokenFactoryABI.default, signer);\n            // Check if user has DEPLOYER_ROLE\n            const DEPLOYER_ROLE = await factory.DEPLOYER_ROLE();\n            const userAddress = await signer.getAddress();\n            const hasDeployerRole = await factory.hasRole(DEPLOYER_ROLE, userAddress);\n            if (!hasDeployerRole) {\n                setError('You do not have permission to deploy tokens. Please contact an administrator to grant you the DEPLOYER_ROLE.');\n                return;\n            }\n            // Convert max supply to proper units\n            const maxSupplyWei = ethers__WEBPACK_IMPORTED_MODULE_6__.parseUnits(formData.maxSupply, formData.decimals);\n            // Deploy the token through the factory\n            console.log('Deploying token with params:', {\n                name: formData.name,\n                symbol: formData.symbol,\n                decimals: formData.decimals,\n                maxSupply: formData.maxSupply,\n                admin: formData.adminAddress,\n                tokenPrice: formData.tokenPrice,\n                bonusTiers: formData.bonusTiers,\n                tokenDetails: formData.tokenDetails,\n                tokenImageUrl: formData.tokenImageUrl\n            });\n            const tx = await factory.deployToken(formData.name, formData.symbol, formData.decimals, maxSupplyWei, formData.adminAddress, formData.tokenPrice, formData.bonusTiers, formData.tokenDetails, formData.tokenImageUrl);\n            console.log('Transaction sent:', tx.hash);\n            setSuccess('Transaction sent! Waiting for confirmation...');\n            const receipt = await tx.wait();\n            console.log('Transaction confirmed:', receipt);\n            // Get the deployed token address from the event\n            const event = receipt.logs.find((log)=>{\n                try {\n                    const parsed = factory.interface.parseLog(log);\n                    return parsed.name === 'TokenDeployed';\n                } catch (e) {\n                    return false;\n                }\n            });\n            if (event) {\n                const parsedEvent = factory.interface.parseLog(event);\n                const tokenAddress = parsedEvent.args.tokenAddress;\n                setDeployedToken({\n                    address: tokenAddress,\n                    name: formData.name,\n                    symbol: formData.symbol,\n                    transactionHash: tx.hash\n                });\n                setSuccess(\"Modular token deployed successfully! Address: \".concat(tokenAddress));\n            } else {\n                setError('Token deployed but could not find deployment event. Please check the transaction.');\n            }\n        } catch (error) {\n            console.error('Error deploying modular token:', error);\n            // Parse common error messages\n            let errorMessage = error.message;\n            if (error.message.includes('AccessControlUnauthorizedAccount')) {\n                errorMessage = 'You do not have permission to deploy tokens. Please contact an administrator.';\n            } else if (error.message.includes('execution reverted')) {\n                errorMessage = 'Transaction failed. Please check your inputs and try again.';\n            }\n            setError(\"Failed to deploy modular token: \".concat(errorMessage));\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // Validation\n        if (!formData.name || !formData.symbol || !formData.adminAddress) {\n            setError('Please fill in all required fields');\n            return;\n        }\n        if (formData.decimals < 0 || formData.decimals > 18) {\n            setError('Decimals must be between 0 and 18');\n            return;\n        }\n        await deployModularToken();\n    };\n    if (!provider || !signer) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"text-blue-600 hover:text-blue-800 mr-4\",\n                            children: \"← Back to Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold\",\n                            children: \"Create Modular Security Token\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                    lineNumber: 222,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold mb-2\",\n                            children: \"Connect Wallet\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"Please connect your MetaMask wallet to create modular tokens.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: initializeProvider,\n                            className: \"bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\",\n                            children: \"Connect Wallet\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n            lineNumber: 221,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/\",\n                        className: \"text-blue-600 hover:text-blue-800 mr-4\",\n                        children: \"← Back to Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold\",\n                        children: \"Create Modular Security Token\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 251,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-4 px-3 py-1 text-xs font-medium rounded-full bg-purple-100 text-purple-800\",\n                        children: \"Amoy Testnet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 252,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                lineNumber: 247,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 p-4 bg-purple-50 border border-purple-200 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-purple-600 text-xl\",\n                                children: \"\\uD83D\\uDE80\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-purple-800\",\n                                    children: \"Next-Generation Modular Architecture\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-1 text-sm text-purple-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Create tokens using our advanced modular architecture with:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"mt-1 list-disc list-inside space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Upgradeable Contracts:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Future-proof with secure upgrade mechanisms\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Modular Design:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                            lineNumber: 271,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Add new features without redeployment\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Enhanced Security:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                            lineNumber: 272,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Timelock protection and emergency controls\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Gas Optimization:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Efficient proxy pattern implementation\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                lineNumber: 258,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-sm font-medium text-blue-800 mb-2\",\n                        children: \"Modular Architecture Contracts\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-blue-700 space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"SecurityTokenCore:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 font-mono text-xs\",\n                                        children: SECURITY_TOKEN_CORE_ADDRESS\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"UpgradeManager:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 font-mono text-xs\",\n                                        children: UPGRADE_MANAGER_ADDRESS\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                lineNumber: 281,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-red-600 mr-2\",\n                            children: \"⚠️\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 300,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                    lineNumber: 298,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                lineNumber: 297,\n                columnNumber: 9\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-green-600 mr-2\",\n                            children: \"✅\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 309,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: success\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                    lineNumber: 308,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                lineNumber: 307,\n                columnNumber: 9\n            }, this),\n            deployedToken && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 bg-green-50 border border-green-200 rounded-lg p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-green-800 mb-4\",\n                        children: \"\\uD83C\\uDF89 Modular Token Created Successfully!\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 318,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Token Name:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: deployedToken.name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Symbol:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: deployedToken.symbol\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Contract Address:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-mono text-xs\",\n                                        children: deployedToken.address\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>window.open(\"https://amoy.polygonscan.com/address/\".concat(deployedToken.address), '_blank'),\n                                className: \"bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded text-sm\",\n                                children: \"View on PolygonScan\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/modular-tokens\",\n                                className: \"bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded text-sm\",\n                                children: \"Manage Token\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 333,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                lineNumber: 317,\n                columnNumber: 9\n            }, this),\n            !deployedToken && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold mb-4\",\n                        children: \"Token Configuration\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"name\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Token Name *\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                id: \"name\",\n                                                name: \"name\",\n                                                value: formData.name,\n                                                onChange: handleInputChange,\n                                                placeholder: \"e.g., Augment Security Token\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"symbol\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Token Symbol *\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                id: \"symbol\",\n                                                name: \"symbol\",\n                                                value: formData.symbol,\n                                                onChange: handleInputChange,\n                                                placeholder: \"e.g., AST\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"decimals\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Decimals *\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                id: \"decimals\",\n                                                name: \"decimals\",\n                                                value: formData.decimals,\n                                                onChange: handleInputChange,\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                required: true,\n                                                children: [\n                                                    ...Array(19)\n                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: i,\n                                                        children: [\n                                                            i,\n                                                            \" decimals\"\n                                                        ]\n                                                    }, i, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"maxSupply\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Maximum Supply *\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                id: \"maxSupply\",\n                                                name: \"maxSupply\",\n                                                value: formData.maxSupply,\n                                                onChange: handleInputChange,\n                                                placeholder: \"10000000\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"adminAddress\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Admin Address *\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        id: \"adminAddress\",\n                                        name: \"adminAddress\",\n                                        value: formData.adminAddress,\n                                        onChange: handleInputChange,\n                                        placeholder: \"0x...\",\n                                        className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 428,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"tokenPrice\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Token Price\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                id: \"tokenPrice\",\n                                                name: \"tokenPrice\",\n                                                value: formData.tokenPrice,\n                                                onChange: handleInputChange,\n                                                placeholder: \"1.00 USD\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 445,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"tokenImageUrl\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Token Image URL\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"url\",\n                                                id: \"tokenImageUrl\",\n                                                name: \"tokenImageUrl\",\n                                                value: formData.tokenImageUrl,\n                                                onChange: handleInputChange,\n                                                placeholder: \"https://...\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 456,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 440,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"bonusTiers\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Bonus Tiers\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        id: \"bonusTiers\",\n                                        name: \"bonusTiers\",\n                                        value: formData.bonusTiers,\n                                        onChange: handleInputChange,\n                                        rows: 2,\n                                        placeholder: \"Early Bird: 10%, Standard: 5%, Late: 0%\",\n                                        className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 472,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"tokenDetails\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Token Details\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 488,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        id: \"tokenDetails\",\n                                        name: \"tokenDetails\",\n                                        value: formData.tokenDetails,\n                                        onChange: handleInputChange,\n                                        rows: 3,\n                                        placeholder: \"Describe your security token...\",\n                                        className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 487,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: isSubmitting,\n                                    className: \"w-full bg-purple-600 hover:bg-purple-700 text-white font-bold py-3 px-4 rounded disabled:opacity-50\",\n                                    children: isSubmitting ? '🔄 Creating Modular Token...' : '🚀 Create Modular Token'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                    lineNumber: 503,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 502,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 355,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                lineNumber: 352,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-yellow-600 text-xl\",\n                                children: \"⚠️\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 519,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 518,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-yellow-800\",\n                                    children: \"Development Notice\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                    lineNumber: 522,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-1 text-sm text-yellow-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"This is a demonstration of the modular token creation interface. In a production environment, this would deploy new proxy instances of the modular architecture contracts.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                            lineNumber: 526,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1\",\n                                            children: \"Currently, it references the existing deployed contracts for demonstration purposes.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                            lineNumber: 527,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                    lineNumber: 525,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 521,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                    lineNumber: 517,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                lineNumber: 516,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n        lineNumber: 246,\n        columnNumber: 5\n    }, this);\n}\n_s(CreateModularTokenPage, \"nvH3z261AgqD36O3Af5uhAJl7Os=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = CreateModularTokenPage;\nvar _c;\n$RefreshReg$(_c, \"CreateModularTokenPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/create-modular-token/page.tsx\n"));

/***/ })

});