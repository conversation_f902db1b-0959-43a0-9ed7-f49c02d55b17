const { ethers } = require("hardhat");

async function main() {
  const tokenAddress = "******************************************";
  
  console.log("🔍 CHECKING PAUSE STATUS");
  console.log("=" .repeat(50));
  console.log("Token Address:", tokenAddress);
  
  const [deployer] = await ethers.getSigners();
  const SecurityTokenMinimal = await ethers.getContractFactory("SecurityTokenMinimal");
  const token = SecurityTokenMinimal.attach(tokenAddress);
  
  try {
    // Check pause status using isPaused()
    const isPaused = await token.isPaused();
    console.log("✅ isPaused():", isPaused);
    
    // Try paused() function (should fail on minimal token)
    try {
      const paused = await token.paused();
      console.log("✅ paused():", paused);
    } catch (err) {
      console.log("❌ paused() function not available:", err.message.split('(')[0]);
    }
    
    // Check other token details
    const name = await token.name();
    const symbol = await token.symbol();
    const version = await token.version();
    
    console.log("\n📋 TOKEN DETAILS:");
    console.log("Name:", name);
    console.log("Symbol:", symbol);
    console.log("Version:", version);
    console.log("Is Paused:", isPaused);
    
    // If paused, try to unpause
    if (isPaused) {
      console.log("\n🔧 TOKEN IS PAUSED - ATTEMPTING TO UNPAUSE");
      
      try {
        const tx = await token.unpause();
        console.log("✅ Unpause transaction submitted:", tx.hash);
        
        await tx.wait();
        console.log("✅ Transaction confirmed");
        
        // Check status again
        const newStatus = await token.isPaused();
        console.log("✅ New pause status:", newStatus);
        
      } catch (err) {
        console.log("❌ Failed to unpause:", err.message);
      }
    } else {
      console.log("\n✅ TOKEN IS ALREADY ACTIVE (NOT PAUSED)");
    }
    
  } catch (error) {
    console.error("❌ Error checking pause status:", error.message);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
