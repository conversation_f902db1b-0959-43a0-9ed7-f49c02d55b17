import { ethers } from 'ethers';
import { NextRequest, NextResponse } from 'next/server';
import SecurityTokenABI from '../../../../contracts/SecurityToken.json';
import WhitelistABI from '../../../../contracts/Whitelist.json';
import { networkConfig } from '../../../../config';

// Load private key from environment variable - in production, use a proper secrets management system
const PRIVATE_KEY = process.env.CONTRACT_ADMIN_PRIVATE_KEY;
const RPC_URLS = {
  amoy: process.env.AMOY_RPC_URL || 'https://rpc-amoy.polygon.technology/',
  polygon: process.env.POLYGON_RPC_URL || 'https://polygon-rpc.com',
  unknown: process.env.AMOY_RPC_URL || 'https://rpc-amoy.polygon.technology/', // Default to Amoy for unknown networks
};

// Debug log for wallet derivation
if (PRIVATE_KEY) {
  try {
    const wallet = new ethers.Wallet(PRIVATE_KEY);
    console.log('Admin wallet derived from private key:', wallet.address);
    // Check if this matches the expected address
    const expectedAddress = '******************************************';
    if (wallet.address.toLowerCase() !== expectedAddress.toLowerCase()) {
      console.warn('WARNING: Derived wallet address does not match expected address!');
      console.warn(`Expected: ${expectedAddress}, Derived: ${wallet.address}`);
    } else {
      console.log('✅ Wallet address verification passed');
    }
  } catch (error) {
    console.error('Error deriving wallet from private key:', error.message);
  }
} else {
  console.warn('CONTRACT_ADMIN_PRIVATE_KEY environment variable not set');
}

// Network chain IDs
const CHAIN_IDS = {
  amoy: 80002,
  polygon: 137,
  unknown: 80002, // Default to Amoy for unknown networks
};

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      tokenAddress,
      action,
      address,
      addresses,
      network = 'amoy'
    } = body;

    if (!tokenAddress || !action) {
      return NextResponse.json(
        { error: 'Token address and action are required' },
        { status: 400 }
      );
    }

    if (!PRIVATE_KEY) {
      return NextResponse.json(
        {
          error: 'CONTRACT_ADMIN_PRIVATE_KEY environment variable not set',
          details: 'For security reasons, the API requires a secure method to sign transactions.',
          clientSideInstructions: true,
          message: 'The server is not configured with admin credentials. You can still use the token management script:',
          commandExample: `# Unix/Linux/Mac:\nexport TOKEN_ADDRESS=${tokenAddress}\nexport ACTION=${action}\nexport ADDRESS=${address || ''}\nexport ADDRESSES=${addresses ? addresses.join(',') : ''}\nnpx hardhat run scripts/05-manage-token.js --network ${network === 'unknown' ? 'amoy' : network}\n\n# Windows Command Prompt:\nset TOKEN_ADDRESS=${tokenAddress}\nset ACTION=${action}\nset ADDRESS=${address || ''}\nset ADDRESSES=${addresses ? addresses.join(',') : ''}\nnpx hardhat run scripts/05-manage-token.js --network ${network === 'unknown' ? 'amoy' : network}\n\n# Windows PowerShell:\n$env:TOKEN_ADDRESS="${tokenAddress}"\n$env:ACTION="${action}"\n$env:ADDRESS="${address || ''}"\n$env:ADDRESSES="${addresses ? addresses.join(',') : ''}"\nnpx hardhat run scripts/05-manage-token.js --network ${network === 'unknown' ? 'amoy' : network}`
        },
        { status: 422 }  // 422 Unprocessable Entity
      );
    }

    // Get RPC URL for the specified network, defaulting to Amoy
    const actualNetwork = network === 'unknown' ? 'amoy' : network;
    const rpcUrl = RPC_URLS[actualNetwork as keyof typeof RPC_URLS] || RPC_URLS.amoy;
    const chainId = CHAIN_IDS[actualNetwork as keyof typeof CHAIN_IDS] || CHAIN_IDS.amoy;

    console.log(`Using network: ${actualNetwork}, RPC URL: ${rpcUrl}, Chain ID: ${chainId}`);

    // Validate inputs based on action
    if (['addToWhitelist', 'removeFromWhitelist', 'freezeAddress', 'unfreezeAddress'].includes(action) && !address) {
      return NextResponse.json(
        { error: 'Address is required for this action' },
        { status: 400 }
      );
    }

    if (action === 'batchAddToWhitelist' && (!addresses || !Array.isArray(addresses) || addresses.length === 0)) {
      return NextResponse.json(
        { error: 'Addresses array is required for batch operations' },
        { status: 400 }
      );
    }

    // Connect to the network with explicit chainId
    const provider = new ethers.JsonRpcProvider(rpcUrl, {
      chainId,
      name: actualNetwork
    });

    // Ensure the network is connected and recognized
    const network_details = await provider.getNetwork();
    console.log(`Connected to network: ${network_details.name} (Chain ID: ${network_details.chainId})`);

    // Create the wallet with the correctly configured provider
    const wallet = new ethers.Wallet(PRIVATE_KEY, provider);
    console.log(`Wallet address: ${wallet.address}, Wallet balance: ${await provider.getBalance(wallet.address)}`);

    // Double-check the wallet address
    if (wallet.address.toLowerCase() !== '******************************************'.toLowerCase()) {
      console.error('❌ WARNING: Wallet address mismatch when connecting to provider!');
      console.error('This could indicate an issue with the environment variable or private key format');
    }

    // Step 1: Connect to the token contract
    const tokenContract = new ethers.Contract(
      tokenAddress,
      SecurityTokenABI.abi,
      wallet
    );

    // Step 2: Check if this is an enhanced factory token with built-in whitelist
    console.log("Checking token type...");

    // Try to get identityRegistry address
    let whitelistAddress;
    let whitelistContract;

    try {
      whitelistAddress = await tokenContract.identityRegistry();
      console.log(`Identity registry address: ${whitelistAddress}`);

      if (whitelistAddress && whitelistAddress !== ethers.ZeroAddress) {
        // This token has a separate identity registry contract
        whitelistContract = new ethers.Contract(
          whitelistAddress,
          WhitelistABI.abi,
          wallet
        );
      } else {
        // This is an enhanced token with built-in whitelist functionality
        console.log("Using token contract for whitelist operations (enhanced token)");
        whitelistContract = tokenContract; // Use the token contract itself
      }
    } catch (error) {
      // Fallback: use the token contract itself for whitelist operations
      console.log("Fallback: using token contract for whitelist operations");
      whitelistContract = tokenContract;
    }

    // Execute the requested action
    let tx;
    let result;

    try {
      switch (action) {
        case 'addToWhitelist':
          console.log(`Adding ${address} to whitelist...`);
          try {
            if (whitelistContract === tokenContract) {
              // Enhanced token with built-in whitelist
              tx = await whitelistContract.updateWhitelist(address, true);
            } else {
              // Separate whitelist contract
              tx = await whitelistContract.addToWhitelist(address);
            }
          } catch (error) {
            // Fallback method
            tx = await whitelistContract.addToWhitelist(address);
          }
          break;

        case 'batchAddToWhitelist':
          console.log(`Batch adding ${addresses.length} addresses to whitelist...`);
          tx = await whitelistContract.batchAddToWhitelist(addresses);
          break;

        case 'removeFromWhitelist':
          tx = await whitelistContract.removeFromWhitelist(address);
          break;

        case 'freezeAddress':
          tx = await whitelistContract.freezeAddress(address);
          break;

        case 'unfreezeAddress':
          tx = await whitelistContract.unfreezeAddress(address);
          break;

        case 'isWhitelisted':
          // This is a view function, no transaction needed
          try {
            if (whitelistContract === tokenContract) {
              // Enhanced token with built-in whitelist
              result = await whitelistContract.isWhitelisted(address);
            } else {
              // Separate whitelist contract
              result = await whitelistContract.isWhitelisted(address);
            }
          } catch (error) {
            // Fallback method
            result = await whitelistContract.isWhitelisted(address);
          }
          return NextResponse.json({
            success: true,
            isWhitelisted: result
          });

        default:
          return NextResponse.json(
            { error: 'Invalid action. Supported actions: addToWhitelist, batchAddToWhitelist, removeFromWhitelist, freezeAddress, unfreezeAddress, isWhitelisted' },
            { status: 400 }
          );
      }

      console.log(`Transaction hash: ${tx.hash}`);

      // Wait for the transaction to be mined
      const receipt = await tx.wait();

      return NextResponse.json({
        success: true,
        action,
        txHash: tx.hash,
        blockNumber: receipt.blockNumber
      });
    } catch (txError: any) {
      console.error('Transaction error:', txError);

      // Check if the issue might be a permissions problem
      try {
        const hasAgentRole = await whitelistContract.hasRole(
          ethers.keccak256(ethers.toUtf8Bytes("AGENT_ROLE")),
          wallet.address
        );

        if (!hasAgentRole) {
          return NextResponse.json({
            success: false,
            error: "The connected wallet doesn't have the AGENT_ROLE on the whitelist contract",
            details: `Please grant AGENT_ROLE to ${wallet.address} on the whitelist contract (${whitelistAddress})`
          }, { status: 403 });
        }
      } catch (roleCheckError) {
        console.error('Role check error:', roleCheckError);
      }

      throw txError; // Re-throw to be caught by the outer catch
    }

  } catch (error: any) {
    console.error('Error managing whitelist:', error);
    return NextResponse.json(
      { error: error.message || 'An unknown error occurred' },
      { status: 500 }
    );
  }
}