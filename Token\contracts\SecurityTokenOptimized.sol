// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";

contract SecurityTokenOptimized is Initializable, ERC20Upgradeable, AccessControlUpgradeable, ReentrancyGuardUpgradeable {
    bytes32 public constant AGENT_ROLE = keccak256("AGENT_ROLE");
    bytes32 public constant TRANSFER_MANAGER_ROLE = keccak256("TRANSFER_MANAGER_ROLE");
    
    uint8 private _decimals;
    uint256 public maxSupply;
    string public tokenPrice;
    string public bonusTiers;
    string public tokenImageUrl;
    address public identityRegistry;
    address public compliance;
    
    address[] public agents;
    mapping(address => bool) public isAgent;
    mapping(address => uint256) private _agreementAcceptances;
    mapping(address => uint256) public frozenBalances;
    mapping(address => bool) public isWhitelisted;
    mapping(uint256 => bool) public usedClaimIds;
    mapping(address => uint256[]) public accountClaims;
    
    bool private _emergencyPaused;
    mapping(bytes4 => bool) private _functionPaused;
    uint256 public totalFrozenSupply;
    uint256 public nextClaimId;
    
    // Transfer fee system
    bool public transferFeesEnabled;
    uint256 public transferFeePercentage;
    address public feeCollector;
    mapping(address => bool) public feeExemptAddresses;
    mapping(address => uint256) public customFeePercentages;
    uint256 public totalFeesCollected;
    
    // Security features
    uint256 public securityLevel;
    mapping(address => uint256) public lastActionTimestamp;
    mapping(address => uint256) public actionCount;
    
    event AgentAdded(address indexed agent);
    event AgentRemoved(address indexed agent);
    event AgreementAccepted(address indexed account, uint256 timestamp);
    event TokensFrozen(address indexed account, uint256 amount);
    event TokensUnfrozen(address indexed account, uint256 amount);
    event WhitelistUpdated(address indexed account, bool whitelisted);
    event ClaimGenerated(uint256 indexed claimId, address indexed account);
    event EmergencyPaused(address indexed admin);
    event EmergencyUnpaused(address indexed admin);
    event TransferFeeCollected(address indexed from, address indexed to, uint256 amount, uint256 fee);
    
    modifier onlyAgent() { require(hasRole(AGENT_ROLE, _msgSender()), "Not agent"); _; }
    modifier onlyTransferManager() { require(hasRole(TRANSFER_MANAGER_ROLE, _msgSender()), "Not transfer manager"); _; }
    modifier whenNotPaused() { require(!_emergencyPaused && !_functionPaused[msg.sig], "Paused"); _; }
    modifier onlyWhitelisted(address account) { require(isWhitelisted[account], "Not whitelisted"); _; }
    
    function initialize(
        string memory name, string memory symbol, uint8 decimals_, uint256 maxSupply_,
        address identityRegistry_, address compliance_, address admin,
        string memory tokenPrice_, string memory bonusTiers_, string memory tokenImageUrl_
    ) public initializer {
        __ERC20_init(name, symbol);
        __AccessControl_init();
        __ReentrancyGuard_init();
        
        require(admin != address(0) && maxSupply_ > 0 && decimals_ <= 18, "Invalid params");
        
        _decimals = decimals_;
        maxSupply = maxSupply_;
        tokenPrice = tokenPrice_;
        bonusTiers = bonusTiers_;
        tokenImageUrl = tokenImageUrl_;
        identityRegistry = identityRegistry_;
        compliance = compliance_;
        nextClaimId = 1000;
        securityLevel = 1;
        
        _grantRole(DEFAULT_ADMIN_ROLE, admin);
        _grantRole(AGENT_ROLE, admin);
        _grantRole(TRANSFER_MANAGER_ROLE, admin);
        
        agents.push(admin);
        isAgent[admin] = true;
        isWhitelisted[admin] = true;
        
        emit AgentAdded(admin);
        emit WhitelistUpdated(admin, true);
    }
    
    function decimals() public view override returns (uint8) { return _decimals; }
    
    function availableBalanceOf(address account) public view returns (uint256) {
        uint256 total = balanceOf(account);
        uint256 frozen = frozenBalances[account];
        return total > frozen ? total - frozen : 0;
    }
    
    function mint(address to, uint256 amount) external onlyAgent nonReentrant whenNotPaused onlyWhitelisted(to) {
        require(to != address(0) && amount > 0, "Invalid params");
        require(totalSupply() + amount <= maxSupply, "Exceeds max supply");
        _validateOperation(msg.sig, _msgSender(), amount);
        _mint(to, amount);
    }
    
    function batchMint(address[] memory recipients, uint256[] memory amounts) external onlyAgent nonReentrant whenNotPaused {
        require(recipients.length == amounts.length && recipients.length <= 100, "Invalid batch");
        uint256 totalAmount = 0;
        for (uint256 i = 0; i < amounts.length; i++) {
            require(recipients[i] != address(0) && amounts[i] > 0 && isWhitelisted[recipients[i]], "Invalid recipient");
            totalAmount += amounts[i];
        }
        require(totalSupply() + totalAmount <= maxSupply, "Exceeds max supply");
        for (uint256 i = 0; i < recipients.length; i++) {
            _mint(recipients[i], amounts[i]);
        }
    }
    
    function transfer(address to, uint256 amount) public override whenNotPaused onlyWhitelisted(to) returns (bool) {
        require(availableBalanceOf(_msgSender()) >= amount, "Insufficient available balance");
        return _transferWithFees(_msgSender(), to, amount);
    }
    
    function transferFrom(address from, address to, uint256 amount) public override whenNotPaused onlyWhitelisted(to) returns (bool) {
        require(availableBalanceOf(from) >= amount, "Insufficient available balance");
        _spendAllowance(from, _msgSender(), amount);
        return _transferWithFees(from, to, amount);
    }
    
    function forcedTransfer(address from, address to, uint256 amount) external onlyTransferManager nonReentrant whenNotPaused returns (bool) {
        require(from != address(0) && to != address(0) && amount > 0, "Invalid params");
        require(availableBalanceOf(from) >= amount, "Insufficient available balance");
        _transfer(from, to, amount);
        return true;
    }
    
    function _transferWithFees(address from, address to, uint256 amount) internal returns (bool) {
        if (transferFeesEnabled && !feeExemptAddresses[from] && feeCollector != address(0)) {
            uint256 feePercentage = customFeePercentages[from] > 0 ? customFeePercentages[from] : transferFeePercentage;
            uint256 fee = (amount * feePercentage) / 10000;
            if (fee > 0) {
                _transfer(from, feeCollector, fee);
                totalFeesCollected += fee;
                emit TransferFeeCollected(from, to, amount, fee);
                amount -= fee;
            }
        }
        _transfer(from, to, amount);
        return true;
    }
    
    function freezeTokens(address account, uint256 amount) external onlyAgent whenNotPaused {
        require(account != address(0) && amount > 0, "Invalid params");
        require(balanceOf(account) >= frozenBalances[account] + amount, "Insufficient balance");
        frozenBalances[account] += amount;
        totalFrozenSupply += amount;
        emit TokensFrozen(account, amount);
    }
    
    function unfreezeTokens(address account, uint256 amount) external onlyAgent whenNotPaused {
        require(account != address(0) && amount > 0, "Invalid params");
        require(frozenBalances[account] >= amount, "Insufficient frozen balance");
        frozenBalances[account] -= amount;
        totalFrozenSupply -= amount;
        emit TokensUnfrozen(account, amount);
    }
    
    function updateWhitelist(address account, bool whitelisted) external onlyAgent {
        require(account != address(0), "Invalid account");
        isWhitelisted[account] = whitelisted;
        emit WhitelistUpdated(account, whitelisted);
    }
    
    function batchUpdateWhitelist(address[] memory accounts, bool[] memory statuses) external onlyAgent {
        require(accounts.length == statuses.length && accounts.length <= 100, "Invalid batch");
        for (uint256 i = 0; i < accounts.length; i++) {
            require(accounts[i] != address(0), "Invalid account");
            isWhitelisted[accounts[i]] = statuses[i];
            emit WhitelistUpdated(accounts[i], statuses[i]);
        }
    }
    
    function generateClaimId(address account) external onlyAgent returns (uint256 claimId) {
        uint256 entropy = uint256(keccak256(abi.encodePacked(block.timestamp, block.prevrandao, account, nextClaimId, totalSupply(), msg.sender)));
        claimId = (entropy % 1000000) + nextClaimId;
        while (usedClaimIds[claimId]) claimId++;
        usedClaimIds[claimId] = true;
        accountClaims[account].push(claimId);
        nextClaimId = claimId + 1;
        emit ClaimGenerated(claimId, account);
        return claimId;
    }
    
    function addAgent(address agent) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(agent != address(0) && !isAgent[agent] && agents.length < 50, "Invalid agent");
        _grantRole(AGENT_ROLE, agent);
        agents.push(agent);
        isAgent[agent] = true;
        emit AgentAdded(agent);
    }
    
    function removeAgent(address agent) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(isAgent[agent] && agents.length > 1, "Cannot remove agent");
        _revokeRole(AGENT_ROLE, agent);
        isAgent[agent] = false;
        for (uint256 i = 0; i < agents.length; i++) {
            if (agents[i] == agent) {
                agents[i] = agents[agents.length - 1];
                agents.pop();
                break;
            }
        }
        emit AgentRemoved(agent);
    }
    
    function acceptAgreement() external {
        require(_agreementAcceptances[_msgSender()] == 0, "Already accepted");
        _agreementAcceptances[_msgSender()] = block.timestamp;
        emit AgreementAccepted(_msgSender(), block.timestamp);
    }
    
    function emergencyPause() external onlyRole(DEFAULT_ADMIN_ROLE) {
        _emergencyPaused = true;
        emit EmergencyPaused(_msgSender());
    }
    
    function emergencyUnpause() external onlyRole(DEFAULT_ADMIN_ROLE) {
        _emergencyPaused = false;
        emit EmergencyUnpaused(_msgSender());
    }
    
    function pauseFunction(bytes4 functionSelector) external onlyRole(DEFAULT_ADMIN_ROLE) {
        _functionPaused[functionSelector] = true;
    }
    
    function unpauseFunction(bytes4 functionSelector) external onlyRole(DEFAULT_ADMIN_ROLE) {
        _functionPaused[functionSelector] = false;
    }
    
    function enableTransferFees(uint256 feePercentage, address collector) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(feePercentage <= 1000 && collector != address(0), "Invalid fee params");
        transferFeesEnabled = true;
        transferFeePercentage = feePercentage;
        feeCollector = collector;
    }
    
    function disableTransferFees() external onlyRole(DEFAULT_ADMIN_ROLE) {
        transferFeesEnabled = false;
        transferFeePercentage = 0;
        feeCollector = address(0);
    }
    
    function setFeeExemption(address account, bool exempt) external onlyRole(DEFAULT_ADMIN_ROLE) {
        feeExemptAddresses[account] = exempt;
    }
    
    function setCustomFee(address account, uint256 feePercentage) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(feePercentage <= 1000, "Fee too high");
        customFeePercentages[account] = feePercentage;
    }
    
    function setSecurityLevel(uint256 level) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(level >= 1 && level <= 5, "Invalid level");
        securityLevel = level;
    }
    
    function _validateOperation(bytes4 functionSelector, address caller, uint256 amount) internal {
        if (securityLevel >= 2) {
            require(block.timestamp >= lastActionTimestamp[caller] + (securityLevel * 60), "Rate limited");
        }
        if (securityLevel >= 3) {
            require(actionCount[caller] < (100 / securityLevel), "Action limit exceeded");
        }
        if (securityLevel >= 4 && amount > 0) {
            require(amount <= (1000000 / securityLevel), "Amount too high");
        }
        lastActionTimestamp[caller] = block.timestamp;
        actionCount[caller]++;
        if (block.timestamp % 86400 == 0) actionCount[caller] = 0;
    }
    
    // View functions
    function getAllAgents() external view returns (address[] memory) { return agents; }
    function getAgentCount() external view returns (uint256) { return agents.length; }
    function getAgentAt(uint256 index) external view returns (address) { return agents[index]; }
    function hasAcceptedAgreement(address account) external view returns (bool) { return _agreementAcceptances[account] > 0; }
    function getAgreementAcceptanceTimestamp(address account) external view returns (uint256) { return _agreementAcceptances[account]; }
    function getAccountClaims(address account) external view returns (uint256[] memory) { return accountClaims[account]; }
    function isEmergencyPaused() external view returns (bool) { return _emergencyPaused; }
    function isFunctionPaused(bytes4 functionSelector) external view returns (bool) { return _functionPaused[functionSelector]; }
    function version() external pure returns (string memory) { return "4.0.0-optimized"; }
    
    uint256[50] private __gap;
}
