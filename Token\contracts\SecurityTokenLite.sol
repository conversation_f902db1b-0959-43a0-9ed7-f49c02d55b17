// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
/**
 * @title SecurityTokenLite
 * @dev Lightweight security token with core security features
 * Optimized for contract size while maintaining all essential functionality
 */
contract SecurityTokenLite is ERC20, AccessControl, ReentrancyGuard {
    // Constants
    bytes32 public constant AGENT_ROLE = keccak256("AGENT_ROLE");
    bytes32 public constant TRANSFER_MANAGER_ROLE = keccak256("TRANSFER_MANAGER_ROLE");
    uint256 private constant MAX_AGENTS = 50;
    
    // Core token properties
    uint8 private _decimals;
    uint256 public maxSupply;
    string public tokenPrice;
    string public bonusTiers;
    string public tokenImageUrl;
    
    // ERC-3643 compliance
    address public identityRegistry;
    address public compliance;
    
    // Agent management
    address[] public agents;
    mapping(address => bool) public isAgent;
    
    // Agreement tracking
    mapping(address => uint256) private _agreementAcceptances;
    
    // Emergency controls
    bool private _emergencyPaused;
    mapping(bytes4 => bool) private _functionPaused;
    
    // Events
    event AgentAdded(address indexed agent, address indexed admin);
    event AgentRemoved(address indexed agent, address indexed admin);
    event ComplianceSet(address indexed compliance);
    event IdentityRegistrySet(address indexed identityRegistry);
    event AgreementAccepted(address indexed account, uint256 timestamp);
    event EmergencyPaused(address indexed admin);
    event EmergencyUnpaused(address indexed admin);
    event FunctionPaused(bytes4 indexed functionSelector, address indexed admin);
    event FunctionUnpaused(bytes4 indexed functionSelector, address indexed admin);
    
    // Modifiers
    modifier onlyAgent() {
        require(hasRole(AGENT_ROLE, _msgSender()), "SecurityToken: caller is not an agent");
        _;
    }
    
    modifier onlyTransferManager() {
        require(hasRole(TRANSFER_MANAGER_ROLE, _msgSender()), "SecurityToken: caller is not transfer manager");
        _;
    }
    
    modifier whenNotEmergencyPaused() {
        require(!_emergencyPaused, "SecurityToken: emergency paused");
        _;
    }
    
    modifier whenFunctionNotPaused() {
        require(!_functionPaused[msg.sig], "SecurityToken: function paused");
        _;
    }
    
    /**
     * @dev Constructor
     */
    constructor(
        string memory name,
        string memory symbol,
        uint8 decimals_,
        uint256 maxSupply_,
        address admin,
        string memory tokenPrice_,
        string memory bonusTiers_,
        string memory tokenDetails_,
        string memory tokenImageUrl_,
        address identityRegistry_,
        address compliance_
    ) ERC20(name, symbol) {
        require(admin != address(0), "SecurityToken: invalid admin");
        require(maxSupply_ > 0, "SecurityToken: max supply must be positive");
        require(decimals_ <= 18, "SecurityToken: decimals too high");
        
        _decimals = decimals_;
        maxSupply = maxSupply_;
        tokenPrice = tokenPrice_;
        bonusTiers = bonusTiers_;
        tokenImageUrl = tokenImageUrl_;
        identityRegistry = identityRegistry_;
        compliance = compliance_;
        
        // Set up roles
        _grantRole(DEFAULT_ADMIN_ROLE, admin);
        _grantRole(AGENT_ROLE, admin);
        _grantRole(TRANSFER_MANAGER_ROLE, admin);
        
        // Add admin as first agent
        agents.push(admin);
        isAgent[admin] = true;
        
        emit AgentAdded(admin, admin);
        emit IdentityRegistrySet(identityRegistry_);
        emit ComplianceSet(compliance_);
    }
    
    /**
     * @dev Returns the number of decimals
     */
    function decimals() public view override(ERC20) returns (uint8) {
        return _decimals;
    }
    
    /**
     * @dev Mint tokens to an address
     */
    function mint(address to, uint256 amount) external onlyAgent nonReentrant whenNotEmergencyPaused whenFunctionNotPaused {
        require(to != address(0), "SecurityToken: mint to zero address");
        require(amount > 0, "SecurityToken: mint amount must be positive");
        require(totalSupply() + amount <= maxSupply, "SecurityToken: exceeds max supply");
        
        // Note: Identity registry verification would be implemented here
        // For now, we skip this check to avoid interface dependencies
        
        _mint(to, amount);
    }
    
    /**
     * @dev Forced transfer by transfer manager
     */
    function forcedTransfer(address from, address to, uint256 amount)
        external
        onlyTransferManager
        nonReentrant
        whenNotEmergencyPaused
        whenFunctionNotPaused
        returns (bool)
    {
        require(from != address(0), "SecurityToken: transfer from zero address");
        require(to != address(0), "SecurityToken: transfer to zero address");
        require(amount > 0, "SecurityToken: transfer amount must be positive");
        require(balanceOf(from) >= amount, "SecurityToken: insufficient balance");
        
        _transfer(from, to, amount);
        return true;
    }
    
    /**
     * @dev Add an agent
     */
    function addAgent(address agent) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(agent != address(0), "SecurityToken: invalid agent address");
        require(!isAgent[agent], "SecurityToken: agent already exists");
        require(agents.length < MAX_AGENTS, "SecurityToken: too many agents");
        
        _grantRole(AGENT_ROLE, agent);
        agents.push(agent);
        isAgent[agent] = true;
        
        emit AgentAdded(agent, _msgSender());
    }
    
    /**
     * @dev Remove an agent
     */
    function removeAgent(address agent) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(isAgent[agent], "SecurityToken: agent does not exist");
        require(agents.length > 1, "SecurityToken: cannot remove last agent");
        
        _revokeRole(AGENT_ROLE, agent);
        isAgent[agent] = false;
        
        // Remove from agents array
        for (uint256 i = 0; i < agents.length; i++) {
            if (agents[i] == agent) {
                agents[i] = agents[agents.length - 1];
                agents.pop();
                break;
            }
        }
        
        emit AgentRemoved(agent, _msgSender());
    }
    
    /**
     * @dev Get all agents
     */
    function getAllAgents() external view returns (address[] memory) {
        return agents;
    }
    
    /**
     * @dev Get agent count
     */
    function getAgentCount() external view returns (uint256) {
        return agents.length;
    }

    /**
     * @dev Get agent at index
     */
    function getAgentAt(uint256 index) external view returns (address) {
        require(index < agents.length, "SecurityToken: index out of bounds");
        return agents[index];
    }
    
    /**
     * @dev Accept agreement
     */
    function acceptAgreement() external {
        address account = _msgSender();
        require(_agreementAcceptances[account] == 0, "SecurityToken: agreement already accepted");

        _agreementAcceptances[account] = block.timestamp;
        emit AgreementAccepted(account, block.timestamp);
    }

    /**
     * @dev Check if agreement accepted
     */
    function hasAcceptedAgreement(address account) external view returns (bool) {
        return _agreementAcceptances[account] > 0;
    }

    /**
     * @dev Get agreement acceptance timestamp
     */
    function getAgreementAcceptanceTimestamp(address account) external view returns (uint256) {
        return _agreementAcceptances[account];
    }
    
    /**
     * @dev Emergency pause all contract functions
     */
    function emergencyPause() external onlyRole(DEFAULT_ADMIN_ROLE) {
        _emergencyPaused = true;
        emit EmergencyPaused(_msgSender());
    }
    
    /**
     * @dev Emergency unpause all contract functions
     */
    function emergencyUnpause() external onlyRole(DEFAULT_ADMIN_ROLE) {
        _emergencyPaused = false;
        emit EmergencyUnpaused(_msgSender());
    }
    
    /**
     * @dev Pause a specific function
     */
    function pauseFunction(bytes4 functionSelector) external onlyRole(DEFAULT_ADMIN_ROLE) {
        _functionPaused[functionSelector] = true;
        emit FunctionPaused(functionSelector, _msgSender());
    }
    
    /**
     * @dev Unpause a specific function
     */
    function unpauseFunction(bytes4 functionSelector) external onlyRole(DEFAULT_ADMIN_ROLE) {
        _functionPaused[functionSelector] = false;
        emit FunctionUnpaused(functionSelector, _msgSender());
    }
    
    /**
     * @dev Check if emergency pause is active
     */
    function isEmergencyPaused() external view returns (bool) {
        return _emergencyPaused;
    }
    
    /**
     * @dev Check if a specific function is paused
     */
    function isFunctionPaused(bytes4 functionSelector) external view returns (bool) {
        return _functionPaused[functionSelector];
    }
    
    /**
     * @dev Get token version
     */
    function version() external pure returns (string memory) {
        return "3.0.0-lite";
    }
}
