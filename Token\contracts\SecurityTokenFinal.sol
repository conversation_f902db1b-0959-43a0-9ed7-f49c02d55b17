// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";

/**
 * @title SecurityTokenFinal
 * @dev Complete security token with ALL audit fixes EXCEPT transfer fees
 * Optimized for size while maintaining maximum security and functionality
 */
contract SecurityTokenFinal is 
    Initializable, 
    ERC20Upgradeable, 
    AccessControlUpgradeable, 
    ReentrancyGuardUpgradeable
{
    // Constants
    bytes32 public constant AGENT_ROLE = keccak256("AGENT_ROLE");
    bytes32 public constant TRANSFER_MANAGER_ROLE = keccak256("TRANSFER_MANAGER_ROLE");
    
    // Core token properties
    uint8 private _decimals;
    uint256 public maxSupply;
    string public tokenPrice;
    string public bonusTiers;
    string public tokenImageUrl;
    
    // ERC-3643 compliance
    address public identityRegistry;
    address public compliance;
    
    // Agent management
    address[] public agents;
    mapping(address => bool) public isAgent;
    
    // Agreement tracking
    mapping(address => uint256) private _agreementAcceptances;
    
    // Advanced features
    mapping(address => uint256) public frozenBalances;
    mapping(address => bool) public isWhitelisted;
    uint256 public totalFrozenSupply;
    
    // Claim management
    mapping(uint256 => bool) public usedClaimIds;
    uint256 public nextClaimId;
    mapping(address => uint256[]) public accountClaims;
    
    // Emergency controls
    bool private _emergencyPaused;
    mapping(bytes4 => bool) private _functionPaused;
    
    // Security features
    uint256 public securityLevel;
    mapping(address => uint256) public lastActionTimestamp;
    mapping(address => uint256) public actionCount;
    
    // Events
    event AgentAdded(address indexed agent, address indexed admin);
    event AgentRemoved(address indexed agent, address indexed admin);
    event ComplianceSet(address indexed compliance);
    event IdentityRegistrySet(address indexed identityRegistry);
    event AgreementAccepted(address indexed account, uint256 timestamp);
    event TokensFrozen(address indexed account, uint256 amount);
    event TokensUnfrozen(address indexed account, uint256 amount);
    event WhitelistUpdated(address indexed account, bool whitelisted);
    event ClaimGenerated(uint256 indexed claimId, address indexed account);
    event EmergencyPaused(address indexed admin);
    event EmergencyUnpaused(address indexed admin);
    event FunctionPaused(bytes4 indexed functionSelector, address indexed admin);
    event FunctionUnpaused(bytes4 indexed functionSelector, address indexed admin);
    event SecurityLevelChanged(uint256 oldLevel, uint256 newLevel);
    
    // Modifiers
    modifier onlyAgent() {
        require(hasRole(AGENT_ROLE, _msgSender()), "SecurityToken: caller is not an agent");
        _;
    }
    
    modifier onlyTransferManager() {
        require(hasRole(TRANSFER_MANAGER_ROLE, _msgSender()), "SecurityToken: caller is not transfer manager");
        _;
    }
    
    modifier whenOperationAllowed() {
        require(!_emergencyPaused, "SecurityToken: emergency paused");
        require(!_functionPaused[msg.sig], "SecurityToken: function paused");
        _;
    }
    
    modifier onlyWhitelisted(address account) {
        require(isWhitelisted[account], "SecurityToken: account not whitelisted");
        _;
    }
    
    /**
     * @dev Initialize the token
     */
    function initialize(
        string memory name,
        string memory symbol,
        uint8 decimals_,
        uint256 maxSupply_,
        address identityRegistry_,
        address compliance_,
        address admin,
        string memory tokenPrice_,
        string memory bonusTiers_,
        string memory tokenDetails_,
        string memory tokenImageUrl_
    ) public initializer {
        __ERC20_init(name, symbol);
        __AccessControl_init();
        __ReentrancyGuard_init();
        
        require(admin != address(0), "SecurityToken: invalid admin");
        require(maxSupply_ > 0, "SecurityToken: max supply must be positive");
        require(decimals_ <= 18, "SecurityToken: decimals too high");
        
        _decimals = decimals_;
        maxSupply = maxSupply_;
        tokenPrice = tokenPrice_;
        bonusTiers = bonusTiers_;
        tokenImageUrl = tokenImageUrl_;
        identityRegistry = identityRegistry_;
        compliance = compliance_;
        nextClaimId = 1000; // Start claim IDs from 1000
        securityLevel = 1; // Default security level
        
        // Set up roles
        _grantRole(DEFAULT_ADMIN_ROLE, admin);
        _grantRole(AGENT_ROLE, admin);
        _grantRole(TRANSFER_MANAGER_ROLE, admin);
        
        // Add admin as first agent and whitelist
        agents.push(admin);
        isAgent[admin] = true;
        isWhitelisted[admin] = true;
        
        emit AgentAdded(admin, admin);
        emit IdentityRegistrySet(identityRegistry_);
        emit ComplianceSet(compliance_);
        emit WhitelistUpdated(admin, true);
    }
    
    /**
     * @dev Returns the number of decimals
     */
    function decimals() public view override returns (uint8) {
        return _decimals;
    }
    
    /**
     * @dev Get available balance (total - frozen)
     */
    function availableBalanceOf(address account) public view returns (uint256) {
        uint256 totalBalance = balanceOf(account);
        uint256 frozen = frozenBalances[account];
        return totalBalance > frozen ? totalBalance - frozen : 0;
    }
    
    /**
     * @dev Mint tokens to an address
     */
    function mint(address to, uint256 amount) external onlyAgent nonReentrant whenOperationAllowed onlyWhitelisted(to) {
        require(to != address(0), "SecurityToken: mint to zero address");
        require(amount > 0, "SecurityToken: mint amount must be positive");
        require(totalSupply() + amount <= maxSupply, "SecurityToken: exceeds max supply");
        
        _validateOperation(msg.sig, _msgSender(), amount);
        _mint(to, amount);
    }
    
    /**
     * @dev Batch mint tokens
     */
    function batchMint(address[] memory recipients, uint256[] memory amounts) 
        external 
        onlyAgent 
        nonReentrant 
        whenOperationAllowed 
    {
        require(recipients.length == amounts.length, "SecurityToken: array length mismatch");
        require(recipients.length <= 100, "SecurityToken: batch too large");
        
        uint256 totalAmount = 0;
        for (uint256 i = 0; i < amounts.length; i++) {
            totalAmount += amounts[i];
        }
        require(totalSupply() + totalAmount <= maxSupply, "SecurityToken: exceeds max supply");
        
        for (uint256 i = 0; i < recipients.length; i++) {
            require(recipients[i] != address(0), "SecurityToken: mint to zero address");
            require(amounts[i] > 0, "SecurityToken: mint amount must be positive");
            require(isWhitelisted[recipients[i]], "SecurityToken: recipient not whitelisted");
            
            _mint(recipients[i], amounts[i]);
        }
    }
    
    /**
     * @dev Transfer with compliance checks
     */
    function transfer(address to, uint256 amount) public override whenOperationAllowed onlyWhitelisted(to) returns (bool) {
        require(availableBalanceOf(_msgSender()) >= amount, "SecurityToken: insufficient available balance");
        return super.transfer(to, amount);
    }
    
    /**
     * @dev TransferFrom with compliance checks
     */
    function transferFrom(address from, address to, uint256 amount) 
        public 
        override 
        whenOperationAllowed 
        onlyWhitelisted(to) 
        returns (bool) 
    {
        require(availableBalanceOf(from) >= amount, "SecurityToken: insufficient available balance");
        return super.transferFrom(from, to, amount);
    }
    
    /**
     * @dev Forced transfer by transfer manager
     */
    function forcedTransfer(address from, address to, uint256 amount)
        external
        onlyTransferManager
        nonReentrant
        whenOperationAllowed
        returns (bool)
    {
        require(from != address(0), "SecurityToken: transfer from zero address");
        require(to != address(0), "SecurityToken: transfer to zero address");
        require(amount > 0, "SecurityToken: transfer amount must be positive");
        require(availableBalanceOf(from) >= amount, "SecurityToken: insufficient available balance");
        
        _transfer(from, to, amount);
        return true;
    }
    
    /**
     * @dev Freeze tokens for an account
     */
    function freezeTokens(address account, uint256 amount) external onlyAgent whenOperationAllowed {
        require(account != address(0), "SecurityToken: invalid account");
        require(amount > 0, "SecurityToken: amount must be positive");
        require(balanceOf(account) >= frozenBalances[account] + amount, "SecurityToken: insufficient balance to freeze");
        
        frozenBalances[account] += amount;
        totalFrozenSupply += amount;
        
        emit TokensFrozen(account, amount);
    }
    
    /**
     * @dev Unfreeze tokens for an account
     */
    function unfreezeTokens(address account, uint256 amount) external onlyAgent whenOperationAllowed {
        require(account != address(0), "SecurityToken: invalid account");
        require(amount > 0, "SecurityToken: amount must be positive");
        require(frozenBalances[account] >= amount, "SecurityToken: insufficient frozen balance");
        
        frozenBalances[account] -= amount;
        totalFrozenSupply -= amount;
        
        emit TokensUnfrozen(account, amount);
    }
    
    /**
     * @dev Update whitelist status
     */
    function updateWhitelist(address account, bool whitelisted) external onlyAgent {
        require(account != address(0), "SecurityToken: invalid account");
        isWhitelisted[account] = whitelisted;
        emit WhitelistUpdated(account, whitelisted);
    }
    
    /**
     * @dev Batch update whitelist
     */
    function batchUpdateWhitelist(address[] memory accounts, bool[] memory statuses) external onlyAgent {
        require(accounts.length == statuses.length, "SecurityToken: array length mismatch");
        require(accounts.length <= 100, "SecurityToken: batch too large");
        
        for (uint256 i = 0; i < accounts.length; i++) {
            require(accounts[i] != address(0), "SecurityToken: invalid account");
            isWhitelisted[accounts[i]] = statuses[i];
            emit WhitelistUpdated(accounts[i], statuses[i]);
        }
    }
    
    /**
     * @dev Generate claim ID with enhanced entropy
     */
    function generateClaimId(address account) external onlyAgent returns (uint256 claimId) {
        // Enhanced entropy from multiple sources
        uint256 entropy = uint256(keccak256(abi.encodePacked(
            block.timestamp,
            block.prevrandao,
            account,
            nextClaimId,
            totalSupply(),
            msg.sender
        )));
        
        claimId = (entropy % 1000000) + nextClaimId;
        
        // Ensure uniqueness
        while (usedClaimIds[claimId]) {
            claimId++;
        }
        
        usedClaimIds[claimId] = true;
        accountClaims[account].push(claimId);
        nextClaimId = claimId + 1;
        
        emit ClaimGenerated(claimId, account);
        return claimId;
    }
    
    /**
     * @dev Add an agent
     */
    function addAgent(address agent) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(agent != address(0), "SecurityToken: invalid agent address");
        require(!isAgent[agent], "SecurityToken: agent already exists");
        require(agents.length < 50, "SecurityToken: too many agents");
        
        _grantRole(AGENT_ROLE, agent);
        agents.push(agent);
        isAgent[agent] = true;
        
        emit AgentAdded(agent, _msgSender());
    }
    
    /**
     * @dev Remove an agent
     */
    function removeAgent(address agent) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(isAgent[agent], "SecurityToken: agent does not exist");
        require(agents.length > 1, "SecurityToken: cannot remove last agent");
        
        _revokeRole(AGENT_ROLE, agent);
        isAgent[agent] = false;
        
        // Remove from agents array
        for (uint256 i = 0; i < agents.length; i++) {
            if (agents[i] == agent) {
                agents[i] = agents[agents.length - 1];
                agents.pop();
                break;
            }
        }
        
        emit AgentRemoved(agent, _msgSender());
    }
    
    /**
     * @dev Accept agreement
     */
    function acceptAgreement() external {
        address account = _msgSender();
        require(_agreementAcceptances[account] == 0, "SecurityToken: agreement already accepted");
        
        _agreementAcceptances[account] = block.timestamp;
        emit AgreementAccepted(account, block.timestamp);
    }
    
    /**
     * @dev Emergency pause all contract functions
     */
    function emergencyPause() external onlyRole(DEFAULT_ADMIN_ROLE) {
        _emergencyPaused = true;
        emit EmergencyPaused(_msgSender());
    }
    
    /**
     * @dev Emergency unpause all contract functions
     */
    function emergencyUnpause() external onlyRole(DEFAULT_ADMIN_ROLE) {
        _emergencyPaused = false;
        emit EmergencyUnpaused(_msgSender());
    }
    
    /**
     * @dev Pause a specific function
     */
    function pauseFunction(bytes4 functionSelector) external onlyRole(DEFAULT_ADMIN_ROLE) {
        _functionPaused[functionSelector] = true;
        emit FunctionPaused(functionSelector, _msgSender());
    }
    
    /**
     * @dev Unpause a specific function
     */
    function unpauseFunction(bytes4 functionSelector) external onlyRole(DEFAULT_ADMIN_ROLE) {
        _functionPaused[functionSelector] = false;
        emit FunctionUnpaused(functionSelector, _msgSender());
    }
    
    /**
     * @dev Set security level (1-5, higher = more restrictive)
     */
    function setSecurityLevel(uint256 _level) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(_level >= 1 && _level <= 5, "SecurityToken: invalid security level");
        uint256 oldLevel = securityLevel;
        securityLevel = _level;
        emit SecurityLevelChanged(oldLevel, _level);
    }
    
    /**
     * @dev Validate operation with enhanced security checks
     */
    function _validateOperation(bytes4 functionSelector, address caller, uint256 amount) internal {
        // Rate limiting based on security level
        if (securityLevel >= 2) {
            require(
                block.timestamp >= lastActionTimestamp[caller] + (securityLevel * 60),
                "SecurityToken: rate limited"
            );
        }
        
        // Action count limiting
        if (securityLevel >= 3) {
            require(actionCount[caller] < (100 / securityLevel), "SecurityToken: action limit exceeded");
        }
        
        // Amount validation based on security level
        if (securityLevel >= 4 && amount > 0) {
            require(amount <= (1000000 / securityLevel), "SecurityToken: amount too high for security level");
        }
        
        // Update tracking
        lastActionTimestamp[caller] = block.timestamp;
        actionCount[caller]++;
        
        // Reset daily action count (simplified)
        if (block.timestamp % 86400 == 0) {
            actionCount[caller] = 0;
        }
    }
    
    // View functions
    function getAllAgents() external view returns (address[] memory) {
        return agents;
    }
    
    function getAgentCount() external view returns (uint256) {
        return agents.length;
    }
    
    function getAgentAt(uint256 index) external view returns (address) {
        require(index < agents.length, "SecurityToken: index out of bounds");
        return agents[index];
    }
    
    function hasAcceptedAgreement(address account) external view returns (bool) {
        return _agreementAcceptances[account] > 0;
    }
    
    function getAgreementAcceptanceTimestamp(address account) external view returns (uint256) {
        return _agreementAcceptances[account];
    }
    
    function getAccountClaims(address account) external view returns (uint256[] memory) {
        return accountClaims[account];
    }
    
    function isEmergencyPaused() external view returns (bool) {
        return _emergencyPaused;
    }
    
    function isFunctionPaused(bytes4 functionSelector) external view returns (bool) {
        return _functionPaused[functionSelector];
    }
    
    function version() external pure returns (string memory) {
        return "4.0.0-final";
    }
    
    /**
     * @dev Required gap for upgradeable contracts
     */
    uint256[50] private __gap;
}
