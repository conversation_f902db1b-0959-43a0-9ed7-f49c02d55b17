// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";

/**
 * @title ClaimRegistry
 * @dev Registry for storing and managing identity claims for ERC-3643 tokens
 * This works alongside existing whitelist contracts to provide claim-based verification
 */
contract ClaimRegistry is Initializable, AccessControlUpgradeable, UUPSUpgradeable {
    bytes32 public constant CLAIM_ISSUER_ROLE = keccak256("CLAIM_ISSUER_ROLE");
    bytes32 public constant CLAIM_VERIFIER_ROLE = keccak256("CLAIM_VERIFIER_ROLE");

    // Custom claim management
    uint256 private _nextClaimTypeId;

    // Nonce for claim ID generation to prevent collisions
    mapping(address => uint256) private _claimNonces;

    struct ClaimType {
        uint256 id;
        string name;
        string description;
        address creator;
        uint256 createdAt;
        bool active;
    }

    // Mapping: claimTypeId => ClaimType
    mapping(uint256 => ClaimType) public claimTypes;

    // Mapping: creator => claimTypeIds[]
    mapping(address => uint256[]) public creatorClaimTypes;

    struct Claim {
        uint256 claimType;
        address issuer;
        bytes signature;
        bytes data;
        string uri;
        uint256 issuedAt;
        uint256 expiresAt;
        bool revoked;
    }

    // Mapping: subject => claimType => claimId => Claim
    mapping(address => mapping(uint256 => mapping(bytes32 => Claim))) private claims;
    
    // Mapping: subject => claimType => claimIds[]
    mapping(address => mapping(uint256 => bytes32[])) private claimIds;
    
    // Mapping: subject => claimType => bool (for quick lookup)
    mapping(address => mapping(uint256 => bool)) private hasClaim;

    event ClaimAdded(
        address indexed subject,
        uint256 indexed claimType,
        bytes32 indexed claimId,
        address issuer
    );

    event ClaimRevoked(
        address indexed subject,
        uint256 indexed claimType,
        bytes32 indexed claimId,
        address issuer
    );

    event ClaimTypeCreated(
        uint256 indexed claimTypeId,
        string name,
        string description,
        address indexed creator
    );

    event ClaimTypeUpdated(
        uint256 indexed claimTypeId,
        string name,
        string description,
        bool active
    );

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers();
    }

    function initialize(address admin) public initializer {
        __AccessControl_init();
        __UUPSUpgradeable_init();

        _grantRole(DEFAULT_ADMIN_ROLE, admin);
        _grantRole(CLAIM_ISSUER_ROLE, admin);
        _grantRole(CLAIM_VERIFIER_ROLE, admin);

        // Initialize claim type counter
        _nextClaimTypeId = 1;

        // Create default claim types for backward compatibility
        _createClaimType("KYC Verification", "Basic identity verification through KYC process");
        _createClaimType("Accredited Investor", "Qualified as an accredited investor");
        _createClaimType("Jurisdiction Compliance", "Meets specific jurisdiction requirements");
        _createClaimType("General Qualification", "General investment qualification");
    }

    /**
     * @dev Create a new claim type
     */
    function createClaimType(
        string calldata name,
        string calldata description
    ) external onlyRole(CLAIM_ISSUER_ROLE) returns (uint256) {
        return _createClaimType(name, description);
    }

    /**
     * @dev Internal function to create claim type
     */
    function _createClaimType(
        string memory name,
        string memory description
    ) internal returns (uint256) {
        uint256 claimTypeId = _nextClaimTypeId++;

        claimTypes[claimTypeId] = ClaimType({
            id: claimTypeId,
            name: name,
            description: description,
            creator: msg.sender,
            createdAt: block.timestamp,
            active: true
        });

        creatorClaimTypes[msg.sender].push(claimTypeId);

        emit ClaimTypeCreated(claimTypeId, name, description, msg.sender);
        return claimTypeId;
    }

    /**
     * @dev Update claim type details
     */
    function updateClaimType(
        uint256 claimTypeId,
        string calldata name,
        string calldata description,
        bool active
    ) external {
        ClaimType storage claimType = claimTypes[claimTypeId];
        require(claimType.id != 0, "ClaimRegistry: claim type does not exist");
        require(
            claimType.creator == msg.sender || hasRole(DEFAULT_ADMIN_ROLE, msg.sender),
            "ClaimRegistry: not authorized to update"
        );

        claimType.name = name;
        claimType.description = description;
        claimType.active = active;

        emit ClaimTypeUpdated(claimTypeId, name, description, active);
    }

    /**
     * @dev Issue a claim for a subject
     */
    function issueClaim(
        address subject,
        uint256 claimType,
        bytes calldata signature,
        bytes calldata data,
        string calldata uri,
        uint256 expiresAt
    ) external onlyRole(CLAIM_ISSUER_ROLE) returns (bytes32) {
        require(claimTypes[claimType].id != 0, "ClaimRegistry: invalid claim type");
        require(claimTypes[claimType].active, "ClaimRegistry: claim type is inactive");
        require(subject != address(0), "ClaimRegistry: cannot issue claim for zero address");

        // Increment nonce for better claim ID generation
        uint256 nonce = _claimNonces[subject]++;

        // Generate more secure claim ID using multiple entropy sources
        bytes32 claimId = keccak256(abi.encodePacked(
            subject,
            claimType,
            block.timestamp,
            block.difficulty,
            msg.sender,
            nonce,
            blockhash(block.number - 1)
        ));

        // Ensure claim ID doesn't already exist (extremely unlikely but good practice)
        require(claims[subject][claimType][claimId].issuer == address(0), "ClaimRegistry: claim ID collision");

        claims[subject][claimType][claimId] = Claim({
            claimType: claimType,
            issuer: msg.sender,
            signature: signature,
            data: data,
            uri: uri,
            issuedAt: block.timestamp,
            expiresAt: expiresAt,
            revoked: false
        });

        claimIds[subject][claimType].push(claimId);
        hasClaim[subject][claimType] = true;

        emit ClaimAdded(subject, claimType, claimId, msg.sender);
        return claimId;
    }

    /**
     * @dev Revoke a claim
     */
    function revokeClaim(
        address subject,
        uint256 claimType,
        bytes32 claimId
    ) external {
        Claim storage claim = claims[subject][claimType][claimId];
        require(claim.issuer != address(0), "ClaimRegistry: claim does not exist");
        require(
            claim.issuer == msg.sender || hasRole(DEFAULT_ADMIN_ROLE, msg.sender),
            "ClaimRegistry: not authorized to revoke"
        );

        claim.revoked = true;
        emit ClaimRevoked(subject, claimType, claimId, claim.issuer);
    }

    /**
     * @dev Check if subject has a valid claim of specific type
     */
    function hasValidClaim(address subject, uint256 claimType) external view returns (bool) {
        if (!hasClaim[subject][claimType]) return false;

        bytes32[] memory ids = claimIds[subject][claimType];
        for (uint256 i = 0; i < ids.length; i++) {
            Claim memory claim = claims[subject][claimType][ids[i]];
            if (!claim.revoked && (claim.expiresAt == 0 || claim.expiresAt > block.timestamp)) {
                return true;
            }
        }
        return false;
    }

    /**
     * @dev Get claim details
     */
    function getClaim(
        address subject,
        uint256 claimType,
        bytes32 claimId
    ) external view returns (Claim memory) {
        return claims[subject][claimType][claimId];
    }

    /**
     * @dev Get all claim IDs for a subject and claim type
     */
    function getClaimIds(address subject, uint256 claimType) external view returns (bytes32[] memory) {
        return claimIds[subject][claimType];
    }

    /**
     * @dev Get all claim types created by an address
     */
    function getClaimTypesByCreator(address creator) external view returns (uint256[] memory) {
        return creatorClaimTypes[creator];
    }

    /**
     * @dev Get all active claim types (paginated)
     */
    function getActiveClaimTypes(uint256 offset, uint256 limit) external view returns (ClaimType[] memory) {
        require(limit > 0 && limit <= 100, "ClaimRegistry: invalid limit");

        // Count active claim types
        uint256 activeCount = 0;
        for (uint256 i = 1; i < _nextClaimTypeId; i++) {
            if (claimTypes[i].active) {
                activeCount++;
            }
        }

        if (offset >= activeCount) {
            return new ClaimType[](0);
        }

        uint256 resultLength = activeCount - offset;
        if (resultLength > limit) {
            resultLength = limit;
        }

        ClaimType[] memory result = new ClaimType[](resultLength);
        uint256 resultIndex = 0;
        uint256 currentOffset = 0;

        for (uint256 i = 1; i < _nextClaimTypeId && resultIndex < resultLength; i++) {
            if (claimTypes[i].active) {
                if (currentOffset >= offset) {
                    result[resultIndex] = claimTypes[i];
                    resultIndex++;
                }
                currentOffset++;
            }
        }

        return result;
    }

    /**
     * @dev Get total number of claim types
     */
    function getTotalClaimTypes() external view returns (uint256) {
        return _nextClaimTypeId - 1;
    }

    /**
     * @dev Check if claim type exists and is active
     */
    function isValidClaimType(uint256 claimTypeId) external view returns (bool) {
        return claimTypes[claimTypeId].id != 0 && claimTypes[claimTypeId].active;
    }

    function _authorizeUpgrade(address newImplementation) internal override onlyRole(DEFAULT_ADMIN_ROLE) {}
}
