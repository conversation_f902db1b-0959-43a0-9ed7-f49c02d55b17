// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol";
import "./SecurityTokenComplete.sol";
import "./modules/SecurityModuleComplete.sol";
import "./modules/TransferModuleComplete.sol";
import "./IdentityRegistry.sol";
import "./Compliance.sol";
import "./ClaimRegistry.sol";

/**
 * @title SecurityTokenFactoryComplete
 * @dev Complete factory with ALL features and security audit fixes
 * Uses proxy pattern to deploy modular tokens with every feature
 */
contract SecurityTokenFactoryComplete is AccessControl, ReentrancyGuard {
    // Constants
    bytes32 public constant DEPLOYER_ROLE = keccak256("DEPLOYER_ROLE");
    uint256 private constant MAX_BATCH_SIZE = 10;
    
    // Implementation contracts
    address public securityTokenImplementation;
    address public securityModuleImplementation;
    address public transferModuleImplementation;
    address public identityRegistryImplementation;
    address public complianceImplementation;
    address public claimRegistryImplementation;
    
    // Deployed tokens tracking
    address[] public deployedTokens;
    mapping(string => address) public tokensBySymbol;
    mapping(address => bool) public isDeployedToken;
    mapping(address => TokenInfo) public tokenInfo;
    
    // Token information structure
    struct TokenInfo {
        address tokenAddress;
        address identityRegistry;
        address compliance;
        address claimRegistry;
        address securityModule;
        address transferModule;
        string name;
        string symbol;
        uint8 decimals;
        uint256 maxSupply;
        address admin;
        bool hasKYC;
        bool hasTransferFees;
        uint256 deploymentTimestamp;
    }
    
    // Events
    event TokenDeployed(
        address indexed tokenAddress,
        address indexed identityRegistryAddress,
        address indexed complianceAddress,
        address claimRegistryAddress,
        address securityModuleAddress,
        address transferModuleAddress,
        string name,
        string symbol,
        uint8 decimals,
        uint256 maxSupply,
        address admin,
        bool hasKYC,
        bool hasTransferFees,
        string tokenImageUrl
    );
    
    event ImplementationsUpdated(
        address securityToken,
        address securityModule,
        address transferModule,
        address identityRegistry,
        address compliance,
        address claimRegistry
    );
    
    /**
     * @dev Constructor
     */
    constructor(address admin) {
        require(admin != address(0), "SecurityTokenFactory: invalid admin address");
        
        // Deploy implementation contracts
        _deployImplementations(admin);
        
        // Set up roles
        _grantRole(DEFAULT_ADMIN_ROLE, admin);
        _grantRole(DEPLOYER_ROLE, admin);
    }
    
    /**
     * @dev Deploy implementation contracts
     */
    function _deployImplementations(address admin) internal {
        // Deploy token implementation
        securityTokenImplementation = address(new SecurityTokenComplete());
        
        // Deploy module implementations
        securityModuleImplementation = address(new SecurityModuleComplete());
        transferModuleImplementation = address(new TransferModuleComplete());
        
        // Deploy registry implementations
        identityRegistryImplementation = address(new IdentityRegistry());
        complianceImplementation = address(new Compliance());
        claimRegistryImplementation = address(new ClaimRegistry());
        
        emit ImplementationsUpdated(
            securityTokenImplementation,
            securityModuleImplementation,
            transferModuleImplementation,
            identityRegistryImplementation,
            complianceImplementation,
            claimRegistryImplementation
        );
    }
    
    /**
     * @dev Deploy a complete security token with ALL features
     */
    function deployCompleteSecurityToken(
        string memory name,
        string memory symbol,
        uint8 decimals,
        uint256 maxSupply,
        address admin,
        string memory tokenPrice,
        string memory bonusTiers,
        string memory tokenDetails,
        string memory tokenImageUrl,
        bool withKYC,
        bool withTransferFees
    ) external onlyRole(DEPLOYER_ROLE) nonReentrant returns (
        address tokenAddress,
        address identityRegistryAddress,
        address complianceAddress,
        address claimRegistryAddress,
        address securityModuleAddress,
        address transferModuleAddress
    ) {
        // Input validation
        require(bytes(name).length > 0 && bytes(name).length <= 50, "SecurityTokenFactory: invalid name");
        require(bytes(symbol).length > 0 && bytes(symbol).length <= 10, "SecurityTokenFactory: invalid symbol");
        require(admin != address(0), "SecurityTokenFactory: invalid admin address");
        require(maxSupply > 0 && maxSupply <= 1e30, "SecurityTokenFactory: invalid max supply");
        require(decimals <= 18, "SecurityTokenFactory: invalid decimals");
        require(tokensBySymbol[symbol] == address(0), "SecurityTokenFactory: symbol already exists");
        
        // Deploy claim registry
        bytes memory claimRegistryData = abi.encodeWithSelector(
            ClaimRegistry(address(0)).initialize.selector,
            admin
        );
        ERC1967Proxy claimRegistryProxy = new ERC1967Proxy(claimRegistryImplementation, claimRegistryData);
        claimRegistryAddress = address(claimRegistryProxy);
        
        // Deploy identity registry
        bytes memory identityRegistryData = abi.encodeWithSelector(
            IdentityRegistry(address(0)).initialize.selector,
            admin,
            claimRegistryAddress
        );
        ERC1967Proxy identityRegistryProxy = new ERC1967Proxy(identityRegistryImplementation, identityRegistryData);
        identityRegistryAddress = address(identityRegistryProxy);
        
        // Deploy compliance contract
        bytes memory complianceData = abi.encodeWithSelector(
            Compliance(address(0)).initialize.selector,
            admin,
            identityRegistryAddress
        );
        ERC1967Proxy complianceProxy = new ERC1967Proxy(complianceImplementation, complianceData);
        complianceAddress = address(complianceProxy);
        
        // Deploy token
        bytes memory tokenData = abi.encodeWithSelector(
            SecurityTokenComplete(address(0)).initialize.selector,
            name,
            symbol,
            decimals,
            maxSupply,
            identityRegistryAddress,
            complianceAddress,
            admin,
            tokenPrice,
            bonusTiers,
            tokenDetails,
            tokenImageUrl
        );
        ERC1967Proxy tokenProxy = new ERC1967Proxy(securityTokenImplementation, tokenData);
        tokenAddress = address(tokenProxy);
        
        // Deploy security module
        bytes memory securityModuleData = abi.encodeWithSelector(
            SecurityModuleComplete(address(0)).initialize.selector,
            tokenAddress,
            admin
        );
        ERC1967Proxy securityModuleProxy = new ERC1967Proxy(securityModuleImplementation, securityModuleData);
        securityModuleAddress = address(securityModuleProxy);
        
        // Deploy transfer module
        bytes memory transferModuleData = abi.encodeWithSelector(
            TransferModuleComplete(address(0)).initialize.selector,
            tokenAddress,
            admin
        );
        ERC1967Proxy transferModuleProxy = new ERC1967Proxy(transferModuleImplementation, transferModuleData);
        transferModuleAddress = address(transferModuleProxy);
        
        // Set modules on token
        SecurityTokenComplete(tokenAddress).setModules(securityModuleAddress, transferModuleAddress);
        
        // Enable transfer fees if requested
        if (withTransferFees) {
            TransferModuleComplete(transferModuleAddress).enableTransferFees(100, admin); // 1% default fee
        }
        
        // Store token information
        tokenInfo[tokenAddress] = TokenInfo({
            tokenAddress: tokenAddress,
            identityRegistry: identityRegistryAddress,
            compliance: complianceAddress,
            claimRegistry: claimRegistryAddress,
            securityModule: securityModuleAddress,
            transferModule: transferModuleAddress,
            name: name,
            symbol: symbol,
            decimals: decimals,
            maxSupply: maxSupply,
            admin: admin,
            hasKYC: withKYC,
            hasTransferFees: withTransferFees,
            deploymentTimestamp: block.timestamp
        });
        
        // Track deployment
        deployedTokens.push(tokenAddress);
        tokensBySymbol[symbol] = tokenAddress;
        isDeployedToken[tokenAddress] = true;
        
        emit TokenDeployed(
            tokenAddress,
            identityRegistryAddress,
            complianceAddress,
            claimRegistryAddress,
            securityModuleAddress,
            transferModuleAddress,
            name,
            symbol,
            decimals,
            maxSupply,
            admin,
            withKYC,
            withTransferFees,
            tokenImageUrl
        );
        
        return (
            tokenAddress,
            identityRegistryAddress,
            complianceAddress,
            claimRegistryAddress,
            securityModuleAddress,
            transferModuleAddress
        );
    }
    
    /**
     * @dev Deploy security token (backward compatibility)
     */
    function deploySecurityToken(
        string memory name,
        string memory symbol,
        uint8 decimals,
        uint256 maxSupply,
        address admin,
        string memory tokenPrice,
        string memory bonusTiers,
        string memory tokenDetails,
        string memory tokenImageUrl
    ) external onlyRole(DEPLOYER_ROLE) nonReentrant returns (address tokenAddress) {
        (tokenAddress, , , , ,) = this.deployCompleteSecurityToken(
            name, symbol, decimals, maxSupply, admin,
            tokenPrice, bonusTiers, tokenDetails, tokenImageUrl,
            false, false // No KYC, no transfer fees by default
        );
        return tokenAddress;
    }
    
    /**
     * @dev Get complete token information
     */
    function getTokenInfo(address token) external view returns (TokenInfo memory) {
        require(isDeployedToken[token], "SecurityTokenFactory: token not deployed by this factory");
        return tokenInfo[token];
    }
    
    /**
     * @dev Add a new deployer
     */
    function addDeployer(address deployer) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(deployer != address(0), "SecurityTokenFactory: invalid deployer address");
        _grantRole(DEPLOYER_ROLE, deployer);
    }
    
    /**
     * @dev Remove a deployer
     */
    function removeDeployer(address deployer) external onlyRole(DEFAULT_ADMIN_ROLE) {
        _revokeRole(DEPLOYER_ROLE, deployer);
    }
    
    /**
     * @dev Get token address by symbol
     */
    function getTokenAddressBySymbol(string memory symbol) external view returns (address) {
        return tokensBySymbol[symbol];
    }
    
    /**
     * @dev Get all deployed tokens
     */
    function getAllDeployedTokens() external view returns (address[] memory) {
        return deployedTokens;
    }
    
    /**
     * @dev Get deployed token by index
     */
    function getDeployedToken(uint256 index) external view returns (address) {
        require(index < deployedTokens.length, "SecurityTokenFactory: index out of bounds");
        return deployedTokens[index];
    }
    
    /**
     * @dev Get token count
     */
    function getTokenCount() external view returns (uint256) {
        return deployedTokens.length;
    }
    
    /**
     * @dev Batch get tokens
     */
    function getTokensBatch(uint256 start, uint256 count) external view returns (address[] memory) {
        require(start < deployedTokens.length, "SecurityTokenFactory: start index out of bounds");
        require(count <= MAX_BATCH_SIZE, "SecurityTokenFactory: batch size too large");
        
        uint256 end = start + count;
        if (end > deployedTokens.length) {
            end = deployedTokens.length;
        }
        
        address[] memory batch = new address[](end - start);
        for (uint256 i = start; i < end; i++) {
            batch[i - start] = deployedTokens[i];
        }
        
        return batch;
    }
}
