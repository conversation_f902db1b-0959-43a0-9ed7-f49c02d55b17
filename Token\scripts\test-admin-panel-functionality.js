const { ethers } = require("hardhat");

async function main() {
  const tokenAddress = "******************************************";
  const adminPanelUrl = "http://localhost:6677";
  
  console.log("🔍 TESTING ADMIN PANEL FUNCTIONALITY");
  console.log("=" .repeat(80));
  console.log("Token Address:", tokenAddress);
  console.log("Admin Panel URL:", adminPanelUrl);
  
  // Test 1: Check if admin panel can fetch token details
  console.log("\n📋 TEST 1: TOKEN DETAILS API");
  console.log("-" .repeat(50));
  
  try {
    const response = await fetch(`${adminPanelUrl}/api/contracts/token-details?tokenAddress=${tokenAddress}&network=amoy`);
    const data = await response.json();
    
    if (response.ok && data.success) {
      console.log("✅ Token details API working");
      console.log(`   Name: ${data.name}`);
      console.log(`   Symbol: ${data.symbol}`);
      console.log(`   Version: ${data.version}`);
      console.log(`   Has KYC: ${data.hasKYC}`);
      console.log(`   Whitelist Address: ${data.whitelistAddress}`);
    } else {
      console.log("❌ Token details API failed:", data.error || response.statusText);
    }
  } catch (error) {
    console.log("❌ Token details API error:", error.message);
  }
  
  // Test 2: Check whitelist listing functionality
  console.log("\n📋 TEST 2: WHITELIST LISTING API");
  console.log("-" .repeat(50));
  
  try {
    const response = await fetch(`${adminPanelUrl}/api/contracts/token/whitelist-list?tokenAddress=${tokenAddress}&network=amoy`);
    const data = await response.json();
    
    if (response.ok && data.success) {
      console.log("✅ Whitelist listing API working");
      console.log(`   Total whitelisted addresses: ${data.totalAddresses}`);
      console.log(`   Addresses found: ${data.whitelistedAddresses.length}`);
      
      data.whitelistedAddresses.forEach((addr, index) => {
        console.log(`   ${index + 1}. ${addr.address}`);
        console.log(`      Whitelisted: ${addr.isWhitelisted}`);
        console.log(`      KYC Approved: ${addr.isKycApproved}`);
        console.log(`      Verified: ${addr.isVerified}`);
        console.log(`      Balance: ${addr.balance}`);
      });
    } else {
      console.log("❌ Whitelist listing API failed:", data.error || response.statusText);
    }
  } catch (error) {
    console.log("❌ Whitelist listing API error:", error.message);
  }
  
  // Test 3: Test whitelist management operations
  console.log("\n📋 TEST 3: WHITELIST MANAGEMENT OPERATIONS");
  console.log("-" .repeat(50));
  
  const testAddress = "******************************************";
  
  // Test adding to whitelist
  try {
    const addResponse = await fetch(`${adminPanelUrl}/api/contracts/whitelist/add`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        tokenAddress: tokenAddress,
        address: testAddress,
        network: 'amoy'
      })
    });
    
    const addData = await addResponse.json();
    
    if (addResponse.ok && addData.success) {
      console.log("✅ Add to whitelist API working");
      console.log(`   Transaction hash: ${addData.transactionHash}`);
    } else {
      console.log("❌ Add to whitelist API failed:", addData.error || addResponse.statusText);
    }
  } catch (error) {
    console.log("❌ Add to whitelist API error:", error.message);
  }
  
  // Test 4: Test KYC management operations
  console.log("\n📋 TEST 4: KYC MANAGEMENT OPERATIONS");
  console.log("-" .repeat(50));
  
  try {
    const kycResponse = await fetch(`${adminPanelUrl}/api/contracts/kyc/approve`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        tokenAddress: tokenAddress,
        address: testAddress,
        network: 'amoy'
      })
    });
    
    const kycData = await kycResponse.json();
    
    if (kycResponse.ok && kycData.success) {
      console.log("✅ KYC approval API working");
      console.log(`   Transaction hash: ${kycData.transactionHash}`);
    } else {
      console.log("❌ KYC approval API failed:", kycData.error || kycResponse.statusText);
    }
  } catch (error) {
    console.log("❌ KYC approval API error:", error.message);
  }
  
  // Test 5: Test token minting
  console.log("\n📋 TEST 5: TOKEN MINTING OPERATIONS");
  console.log("-" .repeat(50));
  
  try {
    const mintResponse = await fetch(`${adminPanelUrl}/api/contracts/mint`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        tokenAddress: tokenAddress,
        to: testAddress,
        amount: "1",
        network: 'amoy'
      })
    });
    
    const mintData = await mintResponse.json();
    
    if (mintResponse.ok && mintData.success) {
      console.log("✅ Token minting API working");
      console.log(`   Transaction hash: ${mintData.transactionHash}`);
    } else {
      console.log("❌ Token minting API failed:", mintData.error || mintResponse.statusText);
    }
  } catch (error) {
    console.log("❌ Token minting API error:", error.message);
  }
  
  // Test 6: Test agent management
  console.log("\n📋 TEST 6: AGENT MANAGEMENT OPERATIONS");
  console.log("-" .repeat(50));
  
  try {
    const agentResponse = await fetch(`${adminPanelUrl}/api/contracts/agents/add`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        tokenAddress: tokenAddress,
        agentAddress: testAddress,
        network: 'amoy'
      })
    });
    
    const agentData = await agentResponse.json();
    
    if (agentResponse.ok && agentData.success) {
      console.log("✅ Add agent API working");
      console.log(`   Transaction hash: ${agentData.transactionHash}`);
    } else {
      console.log("❌ Add agent API failed:", agentData.error || agentResponse.statusText);
    }
  } catch (error) {
    console.log("❌ Add agent API error:", error.message);
  }
  
  // Test 7: Test emergency controls
  console.log("\n📋 TEST 7: EMERGENCY CONTROL OPERATIONS");
  console.log("-" .repeat(50));
  
  try {
    // Test pause
    const pauseResponse = await fetch(`${adminPanelUrl}/api/contracts/pause`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        tokenAddress: tokenAddress,
        network: 'amoy'
      })
    });
    
    const pauseData = await pauseResponse.json();
    
    if (pauseResponse.ok && pauseData.success) {
      console.log("✅ Pause token API working");
      console.log(`   Transaction hash: ${pauseData.transactionHash}`);
      
      // Test unpause
      const unpauseResponse = await fetch(`${adminPanelUrl}/api/contracts/unpause`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tokenAddress: tokenAddress,
          network: 'amoy'
        })
      });
      
      const unpauseData = await unpauseResponse.json();
      
      if (unpauseResponse.ok && unpauseData.success) {
        console.log("✅ Unpause token API working");
        console.log(`   Transaction hash: ${unpauseData.transactionHash}`);
      } else {
        console.log("❌ Unpause token API failed:", unpauseData.error || unpauseResponse.statusText);
      }
    } else {
      console.log("❌ Pause token API failed:", pauseData.error || pauseResponse.statusText);
    }
  } catch (error) {
    console.log("❌ Emergency controls API error:", error.message);
  }
  
  // Test 8: Verify final state
  console.log("\n📋 TEST 8: VERIFY FINAL STATE");
  console.log("-" .repeat(50));
  
  try {
    // Check if test address is now whitelisted and KYC approved
    const [deployer] = await ethers.getSigners();
    const SecurityTokenMinimal = await ethers.getContractFactory("SecurityTokenMinimal");
    const token = SecurityTokenMinimal.attach(tokenAddress);
    
    const isWhitelisted = await token.isWhitelisted(testAddress);
    const isKycApproved = await token.isKycApproved(testAddress);
    const isVerified = await token.isVerified(testAddress);
    const balance = await token.balanceOf(testAddress);
    const agentCount = await token.getAgentCount();
    const isPaused = await token.isPaused();
    
    console.log("Final verification from blockchain:");
    console.log(`   Test address whitelisted: ${isWhitelisted}`);
    console.log(`   Test address KYC approved: ${isKycApproved}`);
    console.log(`   Test address verified: ${isVerified}`);
    console.log(`   Test address balance: ${balance}`);
    console.log(`   Total agents: ${agentCount}`);
    console.log(`   Token paused: ${isPaused}`);
    
  } catch (error) {
    console.log("❌ Final verification error:", error.message);
  }
  
  console.log("\n🎯 ADMIN PANEL FUNCTIONALITY SUMMARY");
  console.log("=" .repeat(80));
  console.log("This test verifies what operations can actually be performed");
  console.log("through the admin panel APIs against the deployed token contract.");
  console.log("");
  console.log("All successful operations above confirm that the admin panel");
  console.log("can fully manage the token through its web interface.");
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
