const { ethers } = require("hardhat");

async function main() {
  const enhancedTokenAddress = "******************************************"; // Augment_019
  
  console.log("🔍 CHECKING ENHANCED TOKEN ACTUAL INTERFACE");
  console.log("=" .repeat(80));
  console.log("Enhanced Token Address:", enhancedTokenAddress);
  
  const provider = new ethers.JsonRpcProvider("https://rpc-amoy.polygon.technology/");
  
  // Try to get basic info first
  const basicABI = [
    "function name() view returns (string)",
    "function symbol() view returns (string)",
    "function version() view returns (string)",
    "function identityRegistry() view returns (address)"
  ];
  
  const token = new ethers.Contract(enhancedTokenAddress, basicABI, provider);
  
  try {
    const name = await token.name();
    const symbol = await token.symbol();
    const version = await token.version();
    const identityRegistry = await token.identityRegistry();
    
    console.log("Basic Token Info:");
    console.log(`  Name: ${name}`);
    console.log(`  Symbol: ${symbol}`);
    console.log(`  Version: ${version}`);
    console.log(`  Identity Registry: ${identityRegistry}`);
    
    // Check if it has an external whitelist contract
    if (identityRegistry && identityRegistry !== "******************************************") {
      console.log("\n🔍 CHECKING EXTERNAL WHITELIST CONTRACT");
      console.log("Identity Registry Address:", identityRegistry);
      
      const whitelistABI = [
        "function isWhitelisted(address) view returns (bool)",
        "function isKycApproved(address) view returns (bool)",
        "function isVerified(address) view returns (bool)",
        "function addToWhitelist(address)",
        "function removeFromWhitelist(address)",
        "function approveKyc(address)",
        "function revokeKyc(address)"
      ];
      
      const whitelist = new ethers.Contract(identityRegistry, whitelistABI, provider);
      
      try {
        const testAddress = "******************************************";
        const isWhitelisted = await whitelist.isWhitelisted(testAddress);
        const isKycApproved = await whitelist.isKycApproved(testAddress);
        const isVerified = await whitelist.isVerified(testAddress);
        
        console.log("External Whitelist Status:");
        console.log(`  Test address whitelisted: ${isWhitelisted}`);
        console.log(`  Test address KYC approved: ${isKycApproved}`);
        console.log(`  Test address verified: ${isVerified}`);
        
        console.log("\n✅ Enhanced token uses EXTERNAL whitelist contract");
        console.log("✅ Whitelist functions are in separate contract");
        
      } catch (error) {
        console.log("❌ Error checking external whitelist:", error.message);
      }
    } else {
      console.log("\n⚠️  No external identity registry found");
    }
    
    // Now check what functions the token itself has
    console.log("\n🔍 CHECKING TOKEN CONTRACT FUNCTIONS");
    
    const tokenFunctionABI = [
      "function TRANSFER_MANAGER_ROLE() view returns (bytes32)",
      "function forcedTransfer(address,address,uint256) returns (bool)",
      "function freezeTokens(address,uint256)",
      "function unfreezeTokens(address,uint256)",
      "function availableBalanceOf(address) view returns (uint256)",
      "function frozenBalances(address) view returns (uint256)",
      "function acceptAgreement()",
      "function pauseFunction(bytes4)",
      "function setSecurityLevel(uint256)",
      "function isPaused() view returns (bool)",
      "function getAllAgents() view returns (address[])",
      "function mint(address,uint256)"
    ];
    
    const tokenWithFunctions = new ethers.Contract(enhancedTokenAddress, tokenFunctionABI, provider);
    
    const functionsToCheck = [
      "TRANSFER_MANAGER_ROLE", "availableBalanceOf", "frozenBalances", 
      "isPaused", "getAllAgents"
    ];
    
    console.log("Token Contract Functions:");
    for (const funcName of functionsToCheck) {
      try {
        if (funcName === "TRANSFER_MANAGER_ROLE") {
          const result = await tokenWithFunctions[funcName]();
          console.log(`  ✅ ${funcName}: ${result}`);
        } else if (funcName === "availableBalanceOf" || funcName === "frozenBalances") {
          const result = await tokenWithFunctions[funcName]("******************************************");
          console.log(`  ✅ ${funcName}: ${result}`);
        } else if (funcName === "isPaused") {
          const result = await tokenWithFunctions[funcName]();
          console.log(`  ✅ ${funcName}: ${result}`);
        } else if (funcName === "getAllAgents") {
          const result = await tokenWithFunctions[funcName]();
          console.log(`  ✅ ${funcName}: [${result.join(', ')}]`);
        }
      } catch (error) {
        console.log(`  ❌ ${funcName}: Not available`);
      }
    }
    
  } catch (error) {
    console.log("❌ Error checking enhanced token:", error.message);
  }
  
  console.log("\n🎯 ENHANCED TOKEN ARCHITECTURE SUMMARY");
  console.log("=" .repeat(80));
  console.log("The enhanced token uses a TWO-CONTRACT architecture:");
  console.log("1. Token Contract - Handles transfers, minting, freezing");
  console.log("2. Whitelist Contract - Handles whitelist and KYC management");
  console.log("");
  console.log("The minimal token uses a ONE-CONTRACT architecture:");
  console.log("1. Token Contract - Handles everything (transfers, minting, whitelist, KYC)");
  console.log("");
  console.log("This explains why whitelist functions appear 'missing' from enhanced token -");
  console.log("they're in a separate contract!");
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
