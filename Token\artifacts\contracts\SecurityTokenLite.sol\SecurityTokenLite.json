{"_format": "hh-sol-artifact-1", "contractName": "SecurityTokenLite", "sourceName": "contracts/SecurityTokenLite.sol", "abi": [{"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint8", "name": "decimals_", "type": "uint8"}, {"internalType": "uint256", "name": "maxSupply_", "type": "uint256"}, {"internalType": "address", "name": "admin", "type": "address"}, {"internalType": "string", "name": "tokenPrice_", "type": "string"}, {"internalType": "string", "name": "bonusTiers_", "type": "string"}, {"internalType": "string", "name": "tokenDetails_", "type": "string"}, {"internalType": "string", "name": "tokenImageUrl_", "type": "string"}, {"internalType": "address", "name": "identityRegistry_", "type": "address"}, {"internalType": "address", "name": "compliance_", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientAllowance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "name": "ERC20InvalidApprover", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "name": "ERC20InvalidReceiver", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "ERC20InvalidSender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "name": "ERC20InvalidSpender", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "agent", "type": "address"}, {"indexed": true, "internalType": "address", "name": "admin", "type": "address"}], "name": "AgentAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "agent", "type": "address"}, {"indexed": true, "internalType": "address", "name": "admin", "type": "address"}], "name": "AgentRemoved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "AgreementAccepted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "compliance", "type": "address"}], "name": "ComplianceSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "admin", "type": "address"}], "name": "EmergencyPaused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "admin", "type": "address"}], "name": "EmergencyUnpaused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes4", "name": "functionSelector", "type": "bytes4"}, {"indexed": true, "internalType": "address", "name": "admin", "type": "address"}], "name": "FunctionPaused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes4", "name": "functionSelector", "type": "bytes4"}, {"indexed": true, "internalType": "address", "name": "admin", "type": "address"}], "name": "FunctionUnpaused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "identityRegistry", "type": "address"}], "name": "IdentityRegistrySet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [], "name": "AGENT_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "TRANSFER_MANAGER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "acceptAgreement", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "agent", "type": "address"}], "name": "addAgent", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "agents", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "bonusTiers", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "compliance", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "emergencyPause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "emergencyUnpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "forcedTransfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "index", "type": "uint256"}], "name": "getAgentAt", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getAgentCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "getAgreementAcceptanceTimestamp", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getAllAgents", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "hasAcceptedAgreement", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "identityRegistry", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "isAgent", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "isEmergencyPaused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "functionSelector", "type": "bytes4"}], "name": "isFunctionPaused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "maxSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "mint", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "functionSelector", "type": "bytes4"}], "name": "pauseFunction", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "agent", "type": "address"}], "name": "removeAgent", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "tokenImageUrl", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "tokenPrice", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "functionSelector", "type": "bytes4"}], "name": "unpauseFunction", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "version", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "pure", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}