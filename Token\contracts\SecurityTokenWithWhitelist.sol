// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";

/**
 * @title SecurityTokenWithWhitelist
 * @dev Minimal security token with essential KYC and whitelist functionality
 * Optimized for size while maintaining all required features
 */
contract SecurityTokenWithWhitelist is ERC20, AccessControl, ReentrancyGuard {
    bytes32 public constant AGENT_ROLE = keccak256("AGENT_ROLE");
    
    uint8 private _decimals;
    uint256 public maxSupply;
    string public tokenPrice;
    string public bonusTiers;
    string public tokenImageUrl;
    address public identityRegistry;
    
    // Whitelist and KYC mappings
    mapping(address => bool) private _whitelisted;
    mapping(address => bool) private _kycApproved;
    mapping(address => bool) private _frozen;
    
    address[] public agents;
    mapping(address => bool) public isAgent;
    mapping(address => uint256) private _agreements;
    
    bool private _paused;
    
    event AgentAdded(address indexed agent);
    event AgentRemoved(address indexed agent);
    event AgreementAccepted(address indexed account, uint256 timestamp);
    event AddressWhitelisted(address indexed account);
    event AddressRemovedFromWhitelist(address indexed account);
    event KycApproved(address indexed account);
    event KycRevoked(address indexed account);
    event AddressFrozen(address indexed account);
    event AddressUnfrozen(address indexed account);
    event Paused(address indexed admin);
    event Unpaused(address indexed admin);
    
    modifier onlyAgent() { 
        require(hasRole(AGENT_ROLE, _msgSender()), "Not agent"); 
        _; 
    }
    
    modifier notPaused() { 
        require(!_paused, "Paused"); 
        _; 
    }
    
    modifier onlyWhitelisted(address account) {
        require(_whitelisted[account], "Not whitelisted");
        require(_kycApproved[account], "KYC not approved");
        require(!_frozen[account], "Account frozen");
        _;
    }
    
    constructor(
        string memory name,
        string memory symbol,
        uint8 decimals_,
        uint256 maxSupply_,
        address admin,
        string memory tokenPrice_,
        string memory bonusTiers_,
        string memory tokenImageUrl_,
        address identityRegistry_,
        address // compliance placeholder
    ) ERC20(name, symbol) {
        require(admin != address(0) && maxSupply_ > 0 && decimals_ <= 18, "Invalid params");
        
        _decimals = decimals_;
        maxSupply = maxSupply_;
        tokenPrice = tokenPrice_;
        bonusTiers = bonusTiers_;
        tokenImageUrl = tokenImageUrl_;
        identityRegistry = identityRegistry_;
        
        _grantRole(DEFAULT_ADMIN_ROLE, admin);
        _grantRole(AGENT_ROLE, admin);
        
        agents.push(admin);
        isAgent[admin] = true;
        
        // Auto-whitelist and approve KYC for admin
        _whitelisted[admin] = true;
        _kycApproved[admin] = true;
        
        emit AgentAdded(admin);
        emit AddressWhitelisted(admin);
        emit KycApproved(admin);
    }
    
    function decimals() public view override returns (uint8) { 
        return _decimals; 
    }
    
    function version() external pure returns (string memory) { 
        return "4.0.0-with-whitelist"; 
    }
    
    // Whitelist functions
    function isWhitelisted(address account) external view returns (bool) {
        return _whitelisted[account];
    }
    
    function isKycApproved(address account) external view returns (bool) {
        return _kycApproved[account];
    }
    
    function isFrozen(address account) external view returns (bool) {
        return _frozen[account];
    }
    
    function isVerified(address account) external view returns (bool) {
        return _whitelisted[account] && _kycApproved[account] && !_frozen[account];
    }
    
    function updateWhitelist(address account, bool status) external onlyAgent {
        require(account != address(0), "Invalid address");
        _whitelisted[account] = status;
        if (status) {
            emit AddressWhitelisted(account);
        } else {
            emit AddressRemovedFromWhitelist(account);
        }
    }
    
    function batchUpdateWhitelist(address[] calldata accounts, bool[] calldata statuses) external onlyAgent {
        require(accounts.length == statuses.length, "Array length mismatch");
        require(accounts.length <= 50, "Batch too large");
        
        for (uint256 i = 0; i < accounts.length; i++) {
            require(accounts[i] != address(0), "Invalid address");
            _whitelisted[accounts[i]] = statuses[i];
            if (statuses[i]) {
                emit AddressWhitelisted(accounts[i]);
            } else {
                emit AddressRemovedFromWhitelist(accounts[i]);
            }
        }
    }
    
    function approveKyc(address account) external onlyAgent {
        require(account != address(0), "Invalid address");
        _kycApproved[account] = true;
        emit KycApproved(account);
    }
    
    function revokeKyc(address account) external onlyAgent {
        require(account != address(0), "Invalid address");
        _kycApproved[account] = false;
        emit KycRevoked(account);
    }
    
    function freezeAddress(address account) external onlyAgent {
        require(account != address(0), "Invalid address");
        _frozen[account] = true;
        emit AddressFrozen(account);
    }
    
    function unfreezeAddress(address account) external onlyAgent {
        require(account != address(0), "Invalid address");
        _frozen[account] = false;
        emit AddressUnfrozen(account);
    }
    
    // Legacy whitelist functions for compatibility
    function addToWhitelist(address account) external onlyAgent {
        require(account != address(0), "Invalid address");
        _whitelisted[account] = true;
        emit AddressWhitelisted(account);
    }

    function removeFromWhitelist(address account) external onlyAgent {
        require(account != address(0), "Invalid address");
        _whitelisted[account] = false;
        emit AddressRemovedFromWhitelist(account);
    }
    
    function batchAddToWhitelist(address[] calldata accounts) external onlyAgent {
        require(accounts.length <= 50, "Batch too large");

        for (uint256 i = 0; i < accounts.length; i++) {
            require(accounts[i] != address(0), "Invalid address");
            _whitelisted[accounts[i]] = true;
            emit AddressWhitelisted(accounts[i]);
        }
    }
    
    // Token functions with KYC checks
    function mint(address to, uint256 amount) external onlyAgent nonReentrant notPaused onlyWhitelisted(to) {
        require(amount > 0, "Invalid amount");
        require(totalSupply() + amount <= maxSupply, "Exceeds max supply");
        _mint(to, amount);
    }
    
    function transfer(address to, uint256 amount) public override notPaused onlyWhitelisted(to) returns (bool) {
        require(_whitelisted[_msgSender()], "Sender not whitelisted");
        require(_kycApproved[_msgSender()], "Sender KYC not approved");
        require(!_frozen[_msgSender()], "Sender frozen");
        return super.transfer(to, amount);
    }
    
    function transferFrom(address from, address to, uint256 amount) public override notPaused onlyWhitelisted(from) onlyWhitelisted(to) returns (bool) {
        return super.transferFrom(from, to, amount);
    }
    
    // Agent management
    function addAgent(address agent) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(agent != address(0) && !isAgent[agent] && agents.length < 20, "Invalid agent");
        _grantRole(AGENT_ROLE, agent);
        agents.push(agent);
        isAgent[agent] = true;
        emit AgentAdded(agent);
    }
    
    function removeAgent(address agent) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(isAgent[agent] && agents.length > 1, "Cannot remove");
        _revokeRole(AGENT_ROLE, agent);
        isAgent[agent] = false;
        for (uint256 i = 0; i < agents.length; i++) {
            if (agents[i] == agent) {
                agents[i] = agents[agents.length - 1];
                agents.pop();
                break;
            }
        }
        emit AgentRemoved(agent);
    }
    
    // Agreement tracking
    function acceptAgreement() external {
        require(_agreements[_msgSender()] == 0, "Already accepted");
        _agreements[_msgSender()] = block.timestamp;
        emit AgreementAccepted(_msgSender(), block.timestamp);
    }
    
    // Emergency controls
    function pause() external onlyRole(DEFAULT_ADMIN_ROLE) {
        _paused = true;
        emit Paused(_msgSender());
    }
    
    function unpause() external onlyRole(DEFAULT_ADMIN_ROLE) {
        _paused = false;
        emit Unpaused(_msgSender());
    }
    
    // View functions
    function getAllAgents() external view returns (address[] memory) { 
        return agents; 
    }
    
    function getAgentCount() external view returns (uint256) { 
        return agents.length; 
    }
    
    function hasAcceptedAgreement(address account) external view returns (bool) { 
        return _agreements[account] > 0; 
    }
    
    function getAgreementTimestamp(address account) external view returns (uint256) { 
        return _agreements[account]; 
    }
    
    function isPaused() external view returns (bool) { 
        return _paused; 
    }
}
