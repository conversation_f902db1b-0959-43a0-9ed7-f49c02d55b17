const { ethers } = require("hardhat");

async function main() {
  const tokenAddress = process.env.TOKEN_ADDRESS || "******************************************";
  const testAddress = process.env.TEST_ADDRESS || "******************************************";
  
  console.log("🔍 Debugging Token Operations");
  console.log("=" .repeat(60));
  console.log("Token Address:", tokenAddress);
  console.log("Test Address:", testAddress);
  
  const [deployer] = await ethers.getSigners();
  console.log("Using account:", deployer.address);
  
  // Connect to the token
  const SecurityTokenMinimal = await ethers.getContractFactory("SecurityTokenMinimal");
  const token = SecurityTokenMinimal.attach(tokenAddress);
  
  try {
    console.log("\n📋 Current Token State:");
    const name = await token.name();
    const symbol = await token.symbol();
    const version = await token.version();
    const isPaused = await token.isPaused();
    
    console.log("Name:", name);
    console.log("Symbol:", symbol);
    console.log("Version:", version);
    console.log("Is Paused:", isPaused);
    
    console.log("\n🔑 Current User Roles:");
    const DEFAULT_ADMIN_ROLE = await token.DEFAULT_ADMIN_ROLE();
    const AGENT_ROLE = await token.AGENT_ROLE();
    
    const hasAdminRole = await token.hasRole(DEFAULT_ADMIN_ROLE, deployer.address);
    const hasAgentRole = await token.hasRole(AGENT_ROLE, deployer.address);
    
    console.log("Has DEFAULT_ADMIN_ROLE:", hasAdminRole);
    console.log("Has AGENT_ROLE:", hasAgentRole);
    
    console.log("\n👥 Current Agents:");
    const agentCount = await token.getAgentCount();
    console.log("Agent count:", agentCount.toString());
    
    const allAgents = await token.getAllAgents();
    for (let i = 0; i < allAgents.length; i++) {
      console.log(`  ${i + 1}. ${allAgents[i]}`);
    }
    
    console.log("\n✅ Current Whitelist Status:");
    const isWhitelisted = await token.isWhitelisted(deployer.address);
    const isKycApproved = await token.isKycApproved(deployer.address);
    const isVerified = await token.isVerified(deployer.address);
    
    console.log(`${deployer.address} is whitelisted:`, isWhitelisted);
    console.log(`${deployer.address} is KYC approved:`, isKycApproved);
    console.log(`${deployer.address} is verified:`, isVerified);
    
    // Test whitelist status for test address
    const testIsWhitelisted = await token.isWhitelisted(testAddress);
    const testIsKycApproved = await token.isKycApproved(testAddress);
    const testIsVerified = await token.isVerified(testAddress);
    
    console.log(`${testAddress} is whitelisted:`, testIsWhitelisted);
    console.log(`${testAddress} is KYC approved:`, testIsKycApproved);
    console.log(`${testAddress} is verified:`, testIsVerified);
    
    console.log("\n🧪 Testing Operations (DRY RUN):");
    
    // Test 1: Try to estimate gas for updateWhitelist
    console.log("\n1. Testing updateWhitelist gas estimation...");
    try {
      const gasEstimate = await token.updateWhitelist.estimateGas(testAddress, true);
      console.log("✅ updateWhitelist gas estimate:", gasEstimate.toString());
    } catch (error) {
      console.log("❌ updateWhitelist gas estimation failed:", error.message);
      if (error.message.includes("Not agent")) {
        console.log("💡 Issue: User doesn't have AGENT_ROLE");
      }
    }
    
    // Test 2: Try to estimate gas for addToWhitelist
    console.log("\n2. Testing addToWhitelist gas estimation...");
    try {
      const gasEstimate = await token.addToWhitelist.estimateGas(testAddress);
      console.log("✅ addToWhitelist gas estimate:", gasEstimate.toString());
    } catch (error) {
      console.log("❌ addToWhitelist gas estimation failed:", error.message);
    }
    
    // Test 3: Try to estimate gas for approveKyc
    console.log("\n3. Testing approveKyc gas estimation...");
    try {
      const gasEstimate = await token.approveKyc.estimateGas(testAddress);
      console.log("✅ approveKyc gas estimate:", gasEstimate.toString());
    } catch (error) {
      console.log("❌ approveKyc gas estimation failed:", error.message);
    }
    
    // Test 4: Try to estimate gas for addAgent
    console.log("\n4. Testing addAgent gas estimation...");
    try {
      const gasEstimate = await token.addAgent.estimateGas("0xabcdefabcdefabcdefabcdefabcdefabcdefabcd");
      console.log("✅ addAgent gas estimate:", gasEstimate.toString());
    } catch (error) {
      console.log("❌ addAgent gas estimation failed:", error.message);
      if (error.message.includes("AccessControl")) {
        console.log("💡 Issue: User doesn't have DEFAULT_ADMIN_ROLE");
      }
    }
    
    console.log("\n🔧 ACTUAL OPERATION TEST:");
    console.log("Attempting to whitelist test address...");
    
    try {
      // Try the actual operation with low gas to see the exact error
      const tx = await token.updateWhitelist(testAddress, true, {
        gasLimit: 100000,
        gasPrice: ethers.parseUnits("50", "gwei")
      });
      
      console.log("✅ Transaction submitted:", tx.hash);
      const receipt = await tx.wait();
      console.log("✅ Transaction confirmed, gas used:", receipt.gasUsed.toString());
      
      // Check if it worked
      const newStatus = await token.isWhitelisted(testAddress);
      console.log("✅ Test address now whitelisted:", newStatus);
      
    } catch (error) {
      console.log("❌ Actual operation failed:", error.message);
      
      // Analyze the error
      if (error.message.includes("Not agent")) {
        console.log("\n💡 DIAGNOSIS: AGENT_ROLE ISSUE");
        console.log("The user doesn't have AGENT_ROLE on the token contract.");
        console.log("Even though the admin panel shows you have admin role,");
        console.log("the whitelist functions require AGENT_ROLE specifically.");
      } else if (error.message.includes("execution reverted")) {
        console.log("\n💡 DIAGNOSIS: CONTRACT REVERT");
        console.log("The transaction is reverting due to a require() statement.");
        console.log("This could be due to:");
        console.log("- Role permission issue");
        console.log("- Invalid parameters");
        console.log("- Contract state issue");
      }
    }
    
  } catch (error) {
    console.error("❌ Error during debugging:", error.message);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
