'use client';

import { useState, useEffect } from 'react';
import { ethers } from 'ethers';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import { getNetworkConfig } from '../../../config';
import SecurityTokenABI from '../../../contracts/SecurityToken.json';
import SecurityTokenMinimalABI from '../../../contracts/SecurityTokenMinimal.json';
import WhitelistABI from '../../../contracts/Whitelist.json';
import * as WhitelistUtils from '../../../utils/whitelist';
import ClientManagement from './components/ClientManagement';
import OrdersManagement from './components/OrdersManagement';
import ClaimsManagement from './components/ClaimsManagement';

interface TokenDetails {
  name: string;
  symbol: string;
  totalSupply: string;
  maxSupply: string;
  tokenPrice: string;
  bonusTiers: string;
  paused: boolean;
  whitelistAddress: string;
  totalSupplyRaw?: bigint;
  decimals?: number;
  network?: string;
  tokenDetails?: string;
  tokenVersion?: string;
  adminAddress?: string;
  hasKYC?: boolean;
  tokenCategory?: string;
}

export default function TokenDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const tokenAddress = params.address as string;

  const [tokenDetails, setTokenDetails] = useState<TokenDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isPaused, setIsPaused] = useState(false);
  const [walletConnected, setWalletConnected] = useState(false);
  const [network, setNetwork] = useState<string>('');
  const [mintingStatus, setMintingStatus] = useState<'idle' | 'pending' | 'success' | 'error'>('idle');
  const [mintTxHash, setMintTxHash] = useState<string | null>(null);
  const [agentRole, setAgentRole] = useState<boolean>(false);
  const [whitelistAddress, setWhitelistAddress] = useState<string>('');
  const [whitelistStatus, setWhitelistStatus] = useState<'idle' | 'pending' | 'success' | 'error'>('idle');
  const [whitelistTxHash, setWhitelistTxHash] = useState<string | null>(null);

  // Add state for agent management
  const [agents, setAgents] = useState<string[]>([]);
  const [isLoadingAgents, setIsLoadingAgents] = useState<boolean>(false);
  const [newAgentAddress, setNewAgentAddress] = useState<string>('');
  const [addAgentStatus, setAddAgentStatus] = useState<'idle' | 'pending' | 'success' | 'error'>('idle');
  const [addAgentTxHash, setAddAgentTxHash] = useState<string | null>(null);
  const [removeAgentStatus, setRemoveAgentStatus] = useState<'idle' | 'pending' | 'success' | 'error'>('idle');
  const [removeAgentTxHash, setRemoveAgentTxHash] = useState<string | null>(null);

  const [upgradeStatus, setUpgradeStatus] = useState<'idle' | 'pending' | 'success' | 'error'>('idle');
  const [upgradeTxHash, setUpgradeTxHash] = useState<string | null>(null);
  const [hasAdminRole, setHasAdminRole] = useState<boolean>(false);
  const [hasTransferManagerRole, setHasTransferManagerRole] = useState<boolean>(false);
  const [batchWhitelistStatus, setBatchWhitelistStatus] = useState<'idle' | 'pending' | 'success' | 'error'>('idle');
  const [batchWhitelistTxHash, setBatchWhitelistTxHash] = useState<string | null>(null);

  const [whitelistManagementTab, setWhitelistManagementTab] = useState<'token' | 'direct' | 'list' | 'kyc' | 'clients' | 'orders' | 'claims'>('token');
  const [directWhitelistStatus, setDirectWhitelistStatus] = useState<'idle' | 'pending' | 'success' | 'error'>('idle');
  const [directWhitelistTxHash, setDirectWhitelistTxHash] = useState<string | null>(null);
  const [directBatchWhitelistStatus, setDirectBatchWhitelistStatus] = useState<'idle' | 'pending' | 'success' | 'error'>('idle');
  const [directBatchWhitelistTxHash, setDirectBatchWhitelistTxHash] = useState<string | null>(null);
  const [addressStatus, setAddressStatus] = useState<{address: string, isWhitelisted?: boolean, isFrozen?: boolean} | null>(null);
  const [checkingAddressStatus, setCheckingAddressStatus] = useState<boolean>(false);

  // Add state for whitelisted addresses list
  const [whitelistedAddresses, setWhitelistedAddresses] = useState<Array<{
    address: string,
    frozen: boolean,
    balance: string,
    frozenTokens: string
  }>>([]);
  const [isLoadingWhitelistedAddresses, setIsLoadingWhitelistedAddresses] = useState<boolean>(false);
  const [forceTransferStatus, setForceTransferStatus] = useState<'idle' | 'pending' | 'success' | 'error'>('idle');
  const [forceTransferTxHash, setForceTransferTxHash] = useState<string | null>(null);
  const [vaultAddress, setVaultAddress] = useState<string>('');
  const [unfreezeStatus, setUnfreezeStatus] = useState<'idle' | 'pending' | 'success' | 'error'>('idle');
  const [unfreezeTxHash, setUnfreezeTxHash] = useState<string | null>(null);

  // Add state for token metadata update
  const [tokenPriceUpdateStatus, setTokenPriceUpdateStatus] = useState<'idle' | 'pending' | 'success' | 'error'>('idle');
  const [tokenPriceUpdateTxHash, setTokenPriceUpdateTxHash] = useState<string | null>(null);
  const [editTokenPrice, setEditTokenPrice] = useState<string>('');
  const [editCurrency, setEditCurrency] = useState<string>('USD');
  const [editBonusTiers, setEditBonusTiers] = useState<string>('');
  const [showTokenMetadataForm, setShowTokenMetadataForm] = useState<boolean>(false);

  // Currency options
  const currencyOptions = [
    { value: 'USD', label: 'USD - US Dollar' },
    { value: 'EUR', label: 'EUR - Euro' },
    { value: 'GBP', label: 'GBP - British Pound' },
    { value: 'JPY', label: 'JPY - Japanese Yen' },
    { value: 'CAD', label: 'CAD - Canadian Dollar' },
    { value: 'AUD', label: 'AUD - Australian Dollar' },
    { value: 'CHF', label: 'CHF - Swiss Franc' },
    { value: 'CNY', label: 'CNY - Chinese Yuan' },
    { value: 'BTC', label: 'BTC - Bitcoin' },
    { value: 'ETH', label: 'ETH - Ethereum' },
    { value: 'USDC', label: 'USDC - USD Coin' },
    { value: 'USDT', label: 'USDT - Tether' }
  ];

  // Add state for KYC management
  const [kycStatus, setKycStatus] = useState<'idle' | 'pending' | 'success' | 'error'>('idle');
  const [kycTxHash, setKycTxHash] = useState<string | null>(null);
  const [batchKycStatus, setBatchKycStatus] = useState<'idle' | 'pending' | 'success' | 'error'>('idle');
  const [batchKycTxHash, setBatchKycTxHash] = useState<string | null>(null);
  const [addressKycStatus, setAddressKycStatus] = useState<{address: string, isKycApproved?: boolean} | null>(null);
  const [checkingKycStatus, setCheckingKycStatus] = useState<boolean>(false);

  // Function to get the correct ABI based on token type
  const getTokenABI = async (provider: any) => {
    try {
      // Try to detect if it's a minimal token by checking for built-in whitelist functions
      const testContract = new ethers.Contract(tokenAddress, SecurityTokenMinimalABI.abi, provider);
      await testContract.isWhitelisted(ethers.ZeroAddress);
      console.log("✅ Detected minimal token - using SecurityTokenMinimalABI");
      return SecurityTokenMinimalABI.abi;
    } catch (err) {
      console.log("✅ Detected enhanced token - using SecurityTokenABI");
      return SecurityTokenABI.abi;
    }
  };

  // Function to sync whitelist status to database
  const syncWhitelistToDatabase = async (walletAddress: string, isWhitelisted: boolean) => {
    try {
      console.log(`Syncing whitelist status to database: ${walletAddress} -> ${isWhitelisted}`);

      // Find client by wallet address
      const clientResponse = await fetch(`/api/clients?search=${encodeURIComponent(walletAddress)}&limit=1`);
      if (!clientResponse.ok) {
        console.warn(`Could not find client with wallet ${walletAddress}`);
        return;
      }

      const clientData = await clientResponse.json();
      const client = clientData.clients?.[0];

      if (!client) {
        console.warn(`No client found with wallet address ${walletAddress}`);
        return;
      }

      // Update or create token approval record
      const approvalResponse = await fetch(`/api/tokens/${tokenAddress}/clients/${client.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          whitelistApproved: isWhitelisted,
          approvalStatus: isWhitelisted ? 'APPROVED' : 'PENDING',
          approvedBy: isWhitelisted ? 'admin@blockchain' : null,
          approvedAt: isWhitelisted ? new Date().toISOString() : null,
          notes: isWhitelisted ? 'Whitelisted via blockchain transaction' : 'Removed from whitelist via blockchain'
        })
      });

      if (approvalResponse.ok) {
        console.log(`✅ Successfully synced whitelist status for ${walletAddress}`);
      } else {
        console.warn(`❌ Failed to sync whitelist status for ${walletAddress}`);
      }
    } catch (error) {
      console.error('Error syncing whitelist to database:', error);
    }
  };

  useEffect(() => {
    checkWalletConnection();
  }, []);

  useEffect(() => {
    if (tokenAddress) {
      fetchTokenDetails();
    }
  }, [tokenAddress, walletConnected]);

  // Modify the useEffect that calls fetchAgents to ensure it has proper timing
  useEffect(() => {
    if (tokenAddress && walletConnected && hasAdminRole) {
      fetchAgents();
    }
  }, [tokenAddress, walletConnected, hasAdminRole]);

  // Add an additional useEffect to ensure agents are loaded whenever the component is mounted
  useEffect(() => {
    // This will run when the component mounts (page load/refresh)
    const loadAgentsOnInit = async () => {
      if (tokenAddress) {
        try {
          // Always attempt to check wallet connection first
          await checkWalletConnection();

          // If wallet is connected but we don't have token details yet, fetch them
          if (walletConnected && !tokenDetails) {
            await fetchTokenDetails();
          }

          // If we have admin role after fetching token details, get agents
          if (walletConnected && hasAdminRole) {
            fetchAgents();
          }
        } catch (err) {
          console.error("Error loading agents on initialization:", err);
        }
      }
    };

    loadAgentsOnInit();

    // Re-fetch agents periodically to ensure list stays current
    const agentRefreshInterval = setInterval(() => {
      if (tokenAddress && walletConnected && hasAdminRole) {
        fetchAgents();
      }
    }, 30000); // Refresh every 30 seconds

    // Clean up interval on component unmount
    return () => clearInterval(agentRefreshInterval);
  }, []);

  // Add useEffect to fetch whitelisted addresses when tab changes
  useEffect(() => {
    if (whitelistManagementTab === 'list' && tokenDetails?.whitelistAddress && tokenDetails.whitelistAddress !== ethers.ZeroAddress) {
      fetchWhitelistedAddresses();
    }
  }, [whitelistManagementTab, tokenDetails?.whitelistAddress]);

  const checkWalletConnection = async () => {
    try {
      if (window.ethereum) {
        const provider = new ethers.BrowserProvider(window.ethereum);
        const accounts = await provider.listAccounts();

        if (accounts.length > 0) {
          setWalletConnected(true);

          // Detect network
          const chainId = (await provider.getNetwork()).chainId;
          if (chainId.toString() === '80002') {
            setNetwork('amoy');
          } else if (chainId.toString() === '137') {
            setNetwork('polygon');
          } else {
            setNetwork('unknown');
          }
        } else {
          setWalletConnected(false);
        }
      }
    } catch (error) {
      console.error("Error checking wallet connection:", error);
      setWalletConnected(false);
    }
  };

  const connectWallet = async () => {
    try {
      if (window.ethereum) {
        const provider = new ethers.BrowserProvider(window.ethereum);
        await provider.send("eth_requestAccounts", []);
        await checkWalletConnection();
      } else {
        setError("Please install MetaMask to use this feature!");
      }
    } catch (error) {
      console.error("Error connecting wallet:", error);
    }
  };

  const fetchTokenDetails = async () => {
    setIsLoading(true);
    setError(null);

    try {
      if (!ethers.isAddress(tokenAddress)) {
        throw new Error("Invalid token address");
      }

      // If wallet is not connected, try to get a provider anyway to read info
      let provider;
      let signer = null;

      if (window.ethereum) {
        provider = new ethers.BrowserProvider(window.ethereum);
        if (walletConnected) {
          try {
            signer = await provider.getSigner();
          } catch (err) {
            console.warn("Could not get signer:", err);
          }
        }
      } else {
        // Fall back to a public provider - for read-only operations
        const networkConfig = getNetworkConfig('amoy'); // Default to amoy
        provider = new ethers.JsonRpcProvider(networkConfig.rpcUrl);
      }

      if (!provider) {
        throw new Error("Could not create provider. Please connect your wallet.");
      }

      // Get the correct ABI for this token type
      const tokenABI = await getTokenABI(provider);

      // Connect to the token contract
      const tokenContract = new ethers.Contract(
        tokenAddress,
        tokenABI,
        provider
      );

      // Read token details
      const name = await tokenContract.name();
      const symbol = await tokenContract.symbol();
      const decimalsRaw = await tokenContract.decimals();
      const decimals = Number(decimalsRaw);

      console.log("=== TOKEN CONTRACT DETAILS ===");
      console.log(`Token Name: ${name}`);
      console.log(`Token Symbol: ${symbol}`);
      console.log(`Decimals (raw): ${decimalsRaw}`);
      console.log(`Decimals (number): ${decimals}`);
      console.log("=== END TOKEN CONTRACT DETAILS ===");
      const totalSupplyRaw = await tokenContract.totalSupply();
      const totalSupply = decimals === 0
        ? totalSupplyRaw.toString()
        : ethers.formatUnits(totalSupplyRaw, decimals);
      const maxSupplyRaw = await tokenContract.maxSupply();
      const maxSupply = decimals === 0
        ? maxSupplyRaw.toString()
        : ethers.formatUnits(maxSupplyRaw, decimals);

      // Read other token details
      let tokenPrice = "";
      let bonusTiers = "";
      let paused = false;
      let whitelistAddress = "";
      let tokenDetailsStr = "";
      let tokenVersion = "";
      let adminAddress = "";

      try {
        tokenPrice = await tokenContract.tokenPrice();
      } catch (err) {
        console.warn("Could not read tokenPrice:", err);
        tokenPrice = "Unknown";
      }

      try {
        bonusTiers = await tokenContract.bonusTiers();
      } catch (err) {
        console.warn("Could not read bonusTiers:", err);
        bonusTiers = "Unknown";
      }

      try {
        tokenDetailsStr = await tokenContract.tokenDetails();
      } catch (err) {
        console.warn("Could not read tokenDetails:", err);
        tokenDetailsStr = "Unknown";
      }

      try {
        tokenVersion = await tokenContract.version();
      } catch (err) {
        console.warn("Could not read version:", err);
        tokenVersion = "Unknown";
      }

      try {
        // Try isPaused() first (minimal token), then paused() (enhanced token)
        try {
          paused = await tokenContract.isPaused();
          console.log("✅ Successfully read pause status using isPaused():", paused);
        } catch (err: any) {
          console.log("❌ isPaused() failed, trying paused():", err.message);
          try {
            paused = await tokenContract.paused();
            console.log("✅ Successfully read pause status using paused():", paused);
          } catch (err2: any) {
            console.log("❌ paused() also failed:", err2.message);
            throw err2;
          }
        }
      } catch (err) {
        console.warn("Could not read paused status:", err);
        paused = false; // Default to not paused if we can't read it
      }

      try {
        whitelistAddress = await tokenContract.identityRegistry();
        setWhitelistAddress(whitelistAddress);
      } catch (err) {
        console.warn("Could not read identityRegistry:", err);
        whitelistAddress = ethers.ZeroAddress;
        setWhitelistAddress(ethers.ZeroAddress);
      }

      // Get admin address using agent functions
      try {
        // Try to get the first agent (which is usually the admin)
        const agentCount = await tokenContract.getAgentCount();
        if (agentCount > 0) {
          adminAddress = await tokenContract.getAgentAt(0);
        } else {
          adminAddress = "No agents found";
        }
      } catch (err) {
        console.warn("Could not read agent address:", err);
        // Fallback: try to get all agents
        try {
          const allAgents = await tokenContract.getAllAgents();
          if (allAgents && allAgents.length > 0) {
            adminAddress = allAgents[0];
          } else {
            adminAddress = "No agents found";
          }
        } catch (err2) {
          console.warn("Could not read all agents:", err2);
          adminAddress = "Unknown";
        }
      }

      // Check for AGENT_ROLE, ADMIN_ROLE, and TRANSFER_MANAGER_ROLE if we have a signer
      if (signer) {
        try {
          const AGENT_ROLE = await tokenContract.AGENT_ROLE();
          const DEFAULT_ADMIN_ROLE = await tokenContract.DEFAULT_ADMIN_ROLE();
          const userAddress = await signer.getAddress();
          const hasAgentRole = await tokenContract.hasRole(AGENT_ROLE, userAddress);
          const hasAdminRole = await tokenContract.hasRole(DEFAULT_ADMIN_ROLE, userAddress);

          // Check for TRANSFER_MANAGER_ROLE only if it exists
          let hasTransferManagerRole = false;
          try {
            const TRANSFER_MANAGER_ROLE = await tokenContract.TRANSFER_MANAGER_ROLE();
            hasTransferManagerRole = await tokenContract.hasRole(TRANSFER_MANAGER_ROLE, userAddress);
          } catch (err) {
            console.log("TRANSFER_MANAGER_ROLE not available on this token (minimal token)");
            hasTransferManagerRole = false;
          }

          setAgentRole(hasAgentRole);
          setHasAdminRole(hasAdminRole);
          setHasTransferManagerRole(hasTransferManagerRole);

          console.log(`User roles - AGENT: ${hasAgentRole}, ADMIN: ${hasAdminRole}, TRANSFER_MANAGER: ${hasTransferManagerRole}`);

          if (!hasAgentRole) {
            console.warn("Connected wallet does not have AGENT_ROLE");
          }
          if (!hasAdminRole) {
            console.warn("Connected wallet does not have DEFAULT_ADMIN_ROLE");
          }
          if (!hasTransferManagerRole) {
            console.warn("Connected wallet does not have TRANSFER_MANAGER_ROLE (or role doesn't exist)");
          }
        } catch (err) {
          console.warn("Could not check roles:", err);
        }
      }

      // Check if this is an enhanced token with built-in whitelist functionality
      let hasBuiltInWhitelist = false;
      let isEnhancedToken = false;

      try {
        // Check if this is an enhanced token by checking version
        const version = await tokenContract.version();
        console.log("🔍 Token version detected:", version);
        if (version && (version.includes("enhanced") || version.includes("with-whitelist") || version.includes("minimal-whitelist"))) {
          isEnhancedToken = true;
          console.log("✅ Detected enhanced token version:", version);
        }
      } catch (err: any) {
        console.log("❌ No version function or not enhanced token:", err.message);
      }

      try {
        // Try to call isWhitelisted function to detect built-in whitelist
        await tokenContract.isWhitelisted(ethers.ZeroAddress);
        hasBuiltInWhitelist = true;
        console.log("✅ Detected enhanced token with built-in whitelist functionality");
      } catch (err) {
        console.log("❌ No built-in whitelist functionality detected");

        // For enhanced tokens without built-in whitelist, we still want to show KYC functionality
        if (isEnhancedToken) {
          console.log("✅ Enhanced token detected - enabling KYC functionality even without built-in whitelist");
        }
      }

      // Extract custom token category from token details or name/symbol
      let tokenCategory = "Security Token"; // Default for enhanced tokens
      if (tokenDetailsStr && tokenDetailsStr !== "Unknown") {
        const details = tokenDetailsStr.toLowerCase();

        // Check for custom token types from the dropdown
        if (details.includes('equity')) tokenCategory = "Equity";
        else if (details.includes('bond')) tokenCategory = "Bond";
        else if (details.includes('debenture')) tokenCategory = "Debenture";
        else if (details.includes('warrant')) tokenCategory = "Warrant";
        else if (details.includes('real estate') || details.includes('realestate')) tokenCategory = "Real Estate";
        else if (details.includes('carbon')) tokenCategory = "Carbon Credit";
        else if (details.includes('commodity')) tokenCategory = "Commodity";
      } else if (isEnhancedToken) {
        // For enhanced tokens without specific details, use a default category
        tokenCategory = "Security Token";
      } else {
        tokenCategory = "Not specified";
      }

      // Create token details object
      console.log("🔍 Token detection results:");
      console.log("  - isEnhancedToken:", isEnhancedToken);
      console.log("  - hasBuiltInWhitelist:", hasBuiltInWhitelist);
      console.log("  - whitelistAddress:", whitelistAddress);
      console.log("  - tokenVersion:", tokenVersion);

      // Enhanced logic for KYC detection
      const hasExternalWhitelist = whitelistAddress && whitelistAddress !== ethers.ZeroAddress;
      const isEnhancedTokenWithKYC = isEnhancedToken ||
                                    (tokenVersion && (tokenVersion.includes("enhanced") ||
                                                     tokenVersion.includes("with-whitelist") ||
                                                     tokenVersion.includes("minimal-whitelist")));
      const finalHasKYC = hasExternalWhitelist || hasBuiltInWhitelist || isEnhancedTokenWithKYC;

      console.log("  - hasExternalWhitelist:", hasExternalWhitelist);
      console.log("  - isEnhancedTokenWithKYC:", isEnhancedTokenWithKYC);
      console.log("  - Final hasKYC:", finalHasKYC);

      const details: TokenDetails = {
        name,
        symbol,
        totalSupply,
        maxSupply,
        tokenPrice,
        bonusTiers,
        paused,
        whitelistAddress: hasBuiltInWhitelist ? tokenAddress : (isEnhancedToken ? tokenAddress : whitelistAddress), // Use token address for enhanced tokens
        totalSupplyRaw,
        decimals,
        network: network || 'unknown',
        tokenDetails: tokenDetailsStr,
        tokenVersion,
        adminAddress,
        hasKYC: Boolean(finalHasKYC), // Enhanced tokens have KYC
        tokenCategory
      };

      console.log("🎯 Final token details object:", details);
      console.log("🎯 Setting isPaused state to:", paused);
      setTokenDetails(details);
      setIsPaused(paused);
      setIsLoading(false);
    } catch (err: any) {
      console.error('Error fetching token details:', err);
      setError(err.message || 'Error fetching token details. Please try again.');
      setIsLoading(false);

      // Try to use a mock entry as fallback
      if (tokenAddress && !tokenDetails) {
        console.log("Using mock data as fallback");
        setTokenDetails({
          name: 'Unknown Token',
          symbol: 'UNKNOWN',
          totalSupply: '0',
          maxSupply: '0',
          tokenPrice: 'Unknown',
          bonusTiers: 'Unknown',
          paused: false,
          whitelistAddress: ethers.ZeroAddress,
          network: network || 'unknown',
          tokenDetails: 'Unknown',
          tokenVersion: 'Unknown',
          adminAddress: 'Unknown',
          hasKYC: false,
          tokenCategory: 'Not specified'
        });
        setIsLoading(false);
      }
    }
  };

    const handlePauseToken = async () => {
    if (!walletConnected) {
      alert("Please connect your wallet first");
      return;
    }

    try {
      if (!window.ethereum) {
        throw new Error('Please install MetaMask to use this feature!');
      }

      const provider = new ethers.BrowserProvider(window.ethereum);
      const signer = await provider.getSigner();

      // Connect to the token contract with signer
      const tokenContract = new ethers.Contract(
        tokenAddress,
        SecurityTokenABI.abi,
        signer
      );

      // Check if user has DEFAULT_ADMIN_ROLE
      const DEFAULT_ADMIN_ROLE = await tokenContract.DEFAULT_ADMIN_ROLE();
      const userAddress = await signer.getAddress();
      const hasRole = await tokenContract.hasRole(DEFAULT_ADMIN_ROLE, userAddress);

      if (!hasRole) {
        throw new Error("You don't have permission to pause/unpause this token");
      }

      // Optimized gas settings - 5M gas limit, 50 gwei gas price
      const gasLimit = BigInt(5000000);
      const gasPrice = ethers.parseUnits("50", "gwei");

      console.log("Using optimized gas settings for pause/unpause:");
      console.log("Gas limit:", gasLimit.toString());
      console.log("Gas price:", ethers.formatUnits(gasPrice, "gwei"), "gwei");

      // Call pause or unpause function with optimized gas settings
      const tx = isPaused ?
        await tokenContract.unpause({ gasLimit, gasPrice }) :
        await tokenContract.pause({ gasLimit, gasPrice });

      alert(`Transaction submitted. Please wait for confirmation...`);

      // Wait for the transaction to be mined
      await tx.wait();

      // Update the state
      setIsPaused(!isPaused);

      // Show success message
      alert(`Token successfully ${isPaused ? 'unpaused' : 'paused'}.`);

      // Refresh token details
      fetchTokenDetails();
    } catch (err: any) {
      console.error('Error pausing/unpausing token:', err);
      alert(`Error: ${err.message || 'Error pausing/unpausing token. Please try again.'}`);
    }
  };

  const handleMintTokens = async () => {
    if (!walletConnected) {
      alert("Please connect your wallet first");
      return;
    }

    const amount = prompt('Enter amount to mint:');
    if (!amount) return;

    const recipient = prompt('Enter recipient address:');
    if (!recipient || !ethers.isAddress(recipient)) {
      alert("Invalid recipient address");
      return;
    }

    try {
      setMintingStatus('pending');

      if (!window.ethereum) {
        throw new Error('Please install MetaMask to use this feature!');
      }

      const provider = new ethers.BrowserProvider(window.ethereum);
      const signer = await provider.getSigner();

      // Connect to the token contract with signer
      const tokenContract = new ethers.Contract(
        tokenAddress,
        SecurityTokenABI.abi,
        signer
      );

      // Check if user has AGENT_ROLE
      if (!agentRole) {
        const AGENT_ROLE = await tokenContract.AGENT_ROLE();
        const userAddress = await signer.getAddress();
        const hasRole = await tokenContract.hasRole(AGENT_ROLE, userAddress);

        if (!hasRole) {
          throw new Error("You don't have permission to mint tokens. You need the AGENT_ROLE.");
        }
      }

      // Read decimals directly from contract to ensure accuracy
      const decimalsRaw = await tokenContract.decimals();
      const decimals = Number(decimalsRaw);
      console.log(`Token decimals (from contract): ${decimals}`);
      console.log(`Amount to mint: ${amount}`);

      let amountWei: bigint;
      try {
        if (decimals === 0) {
          // For tokens with 0 decimals, use the amount as-is
          amountWei = BigInt(amount);
          console.log(`Amount (0 decimals): ${amountWei.toString()}`);
        } else {
          // For tokens with decimals, convert using parseUnits
          amountWei = ethers.parseUnits(amount, decimals);
          console.log(`Amount (${decimals} decimals): ${amountWei.toString()}`);
        }
      } catch (err: any) {
        throw new Error(`Invalid amount format: ${err.message}`);
      }

      // Check if amount would exceed max supply
      const currentSupply = tokenDetails?.totalSupplyRaw || BigInt(0);
      const maxSupply = BigInt(tokenDetails?.maxSupply || 0);
      if (decimals === 0) {
        // For 0 decimals, maxSupply is already in the correct format
        if (currentSupply + amountWei > maxSupply) {
          throw new Error(`Minting ${amount} tokens would exceed max supply of ${maxSupply.toString()}`);
        }
      } else {
        // For tokens with decimals, we need to compare properly
        const maxSupplyWei = maxSupply * BigInt(10 ** decimals);
        if (currentSupply + amountWei > maxSupplyWei) {
          throw new Error(`Minting ${amount} tokens would exceed max supply of ${ethers.formatUnits(maxSupplyWei, decimals)}`);
        }
      }

      console.log(`Final amount to mint: ${amountWei.toString()}`);
      console.log(`Current supply: ${currentSupply.toString()}`);
      console.log(`Max supply: ${maxSupply.toString()}`);

      // Comprehensive pre-mint checks
      console.log("=== PRE-MINT DIAGNOSTIC CHECKS ===");

      // Check if token is paused
      try {
        let isPausedCheck = false;
        try {
          isPausedCheck = await tokenContract.isPaused();
        } catch (err) {
          isPausedCheck = await tokenContract.paused();
        }
        console.log(`Token paused: ${isPausedCheck}`);
        if (isPausedCheck) {
          throw new Error("Token is currently paused. Cannot mint tokens.");
        }
      } catch (err: any) {
        console.warn("Could not check pause status:", err);
      }

      // Check user's AGENT_ROLE
      try {
        const AGENT_ROLE = await tokenContract.AGENT_ROLE();
        const userAddress = await signer.getAddress();
        const hasAgentRole = await tokenContract.hasRole(AGENT_ROLE, userAddress);
        console.log(`User has AGENT_ROLE: ${hasAgentRole}`);
        if (!hasAgentRole) {
          throw new Error("You don't have AGENT_ROLE. Cannot mint tokens.");
        }
      } catch (err: any) {
        console.warn("Could not check AGENT_ROLE:", err);
      }

      // Check recipient whitelist and frozen status
      try {
        const whitelistAddress = await tokenContract.identityRegistry();
        console.log(`Whitelist contract: ${whitelistAddress}`);

        if (whitelistAddress && whitelistAddress !== ethers.ZeroAddress) {
          const whitelist = new ethers.Contract(
            whitelistAddress,
            WhitelistABI.abi,
            provider
          );

          const isWhitelisted = await whitelist.isWhitelisted(recipient);
          console.log(`Recipient whitelisted: ${isWhitelisted}`);
          if (!isWhitelisted) {
            throw new Error("Recipient is not whitelisted. Please add them to the whitelist first.");
          }

          const isFrozen = await whitelist.isFrozen(recipient);
          console.log(`Recipient frozen: ${isFrozen}`);
          if (isFrozen) {
            throw new Error("Recipient is frozen and cannot receive tokens.");
          }
        }
      } catch (err: any) {
        if (err.message.includes("not whitelisted") || err.message.includes("frozen")) {
          throw err;
        }
        console.warn("Could not check whitelist status:", err);
      }

      // Final validation
      if (amountWei <= 0) {
        throw new Error("Amount must be greater than 0");
      }

      console.log("=== ALL PRE-MINT CHECKS PASSED ===");

      // Use optimized gas parameters that work reliably for Amoy testnet
      try {
        // Optimized gas settings - 5M gas limit, 50 gwei gas price
        const gasLimit = BigInt(5000000);
        const gasPrice = ethers.parseUnits("50", "gwei");

        console.log("Using optimized gas settings for Amoy testnet:");
        console.log("Gas limit:", gasLimit.toString());
        console.log("Gas price:", ethers.formatUnits(gasPrice, "gwei"), "gwei");

        // Call the mint function with our optimized gas parameters
        const tx = await tokenContract.mint(recipient, amountWei, {
          gasLimit: gasLimit,
          gasPrice: gasPrice
        });

        setMintTxHash(tx.hash);

        // Wait for the transaction to be mined
        await tx.wait();

        setMintingStatus('success');

        // Refresh token details
        fetchTokenDetails();

        alert(`Successfully minted ${amount} tokens to ${recipient}.`);
      } catch (err: any) {
        console.error("Error during minting:", err);
        const errorMessage = err.message || "Unknown error";

        throw new Error(`Failed to mint tokens: ${errorMessage}`);
      }
    } catch (err: any) {
      console.error('Error minting tokens:', err);
      setMintingStatus('error');

      let errorMsg = err.message || 'Error minting tokens. Please try again.';

      alert(`Error: ${errorMsg}`);
    } finally {
      if (mintingStatus === 'pending') {
        setMintingStatus('idle');
      }
    }
  };

  const handleWhitelistAddress = async () => {
    if (!walletConnected) {
      alert("Please connect your wallet first");
      return;
    }

    const address = prompt('Enter address to whitelist:');
    if (!address || !ethers.isAddress(address)) {
      alert("Invalid address");
      return;
    }

    try {
      setWhitelistStatus('pending');

      // Use client-side transaction instead of API
      if (!window.ethereum) {
        throw new Error('Please install MetaMask to use this feature!');
      }

      const provider = new ethers.BrowserProvider(window.ethereum);
      const signer = await provider.getSigner();

      // Get the correct ABI for this token type
      const tokenABI = await getTokenABI(provider);

      // Connect to the token contract to get whitelist address
      const tokenContract = new ethers.Contract(
        tokenAddress,
        tokenABI,
        signer
      );

      // Check if this token has built-in whitelist functionality (minimal token)
      let whitelistContract;
      let hasBuiltInWhitelist = false;

      try {
        // Test if the token contract has built-in whitelist functions
        await tokenContract.isWhitelisted(ethers.ZeroAddress);
        console.log("✅ Token has built-in whitelist functionality");
        whitelistContract = tokenContract; // Use token contract itself
        hasBuiltInWhitelist = true;
      } catch (err) {
        console.log("❌ No built-in whitelist, checking for external whitelist contract");

        // Check for external whitelist contract
        const whitelistAddress = await tokenContract.identityRegistry();
        if (whitelistAddress && whitelistAddress !== ethers.ZeroAddress) {
          console.log("✅ Found external whitelist contract:", whitelistAddress);
          whitelistContract = new ethers.Contract(
            whitelistAddress,
            WhitelistABI.abi,
            signer
          );
        } else {
          throw new Error("No whitelist functionality found for this token");
        }
      }

      // Check if user has AGENT_ROLE
      const AGENT_ROLE = ethers.keccak256(ethers.toUtf8Bytes("AGENT_ROLE"));
      const userAddress = await signer.getAddress();
      const hasAgentRole = await whitelistContract.hasRole(AGENT_ROLE, userAddress);

      if (!hasAgentRole) {
        throw new Error("You don't have permission to whitelist addresses. You need the AGENT_ROLE on the whitelist contract.");
      }

      // Add to whitelist with optimized gas settings
      console.log(`Adding ${address} to whitelist...`);

      // Use optimized gas settings for Amoy testnet
      const gasLimit = BigInt(200000); // 200k gas limit
      const gasPrice = ethers.parseUnits("50", "gwei"); // 50 gwei gas price

      console.log("Using optimized gas settings:");
      console.log("Gas limit:", gasLimit.toString());
      console.log("Gas price:", ethers.formatUnits(gasPrice, "gwei"), "gwei");

      // Add to whitelist - use appropriate method based on contract type
      let tx;
      if (hasBuiltInWhitelist) {
        // Token has built-in whitelist functions (minimal token)
        console.log("🔧 Using built-in addToWhitelist function (legacy compatibility)");
        // Use the legacy compatibility function which is simpler
        tx = await whitelistContract.addToWhitelist(address, {
          gasLimit,
          gasPrice
        });
      } else {
        // External whitelist contract
        console.log("🔧 Using external whitelist addToWhitelist function");
        tx = await whitelistContract.addToWhitelist(address, {
          gasLimit,
          gasPrice
        });
      }

      setWhitelistTxHash(tx.hash);
      alert(`Transaction submitted. Hash: ${tx.hash}`);

      // Wait for confirmation
      await tx.wait();

      setWhitelistStatus('success');
      alert(`Successfully whitelisted address: ${address}`);

      // Update database to sync whitelist status
      await syncWhitelistToDatabase(address, true);

      // Refresh token details
      fetchTokenDetails();
    } catch (err: any) {
      console.error('Error whitelisting address:', err);
      setWhitelistStatus('error');
      alert(`Error: ${err.message || 'Error whitelisting address. Please try again.'}`);
    } finally {
      if (whitelistStatus === 'pending') {
        setWhitelistStatus('idle');
      }
    }
  };

  const handleBatchWhitelist = async () => {
    if (!walletConnected) {
      alert("Please connect your wallet first");
      return;
    }

    const addressesText = prompt('Enter addresses to whitelist (separated by commas):');
    if (!addressesText) return;

    // Parse and validate addresses
    const addressesList = addressesText
      .split(',')
      .map(addr => addr.trim())
      .filter(addr => addr.length > 0);

    // Validate each address
    const invalidAddresses = addressesList.filter(addr => !ethers.isAddress(addr));
    if (invalidAddresses.length > 0) {
      alert(`Invalid addresses found:\n${invalidAddresses.join('\n')}\n\nPlease fix and try again.`);
      return;
    }

    try {
      setBatchWhitelistStatus('pending');

      // Use client-side transaction instead of API
      if (!window.ethereum) {
        throw new Error('Please install MetaMask to use this feature!');
      }

      const provider = new ethers.BrowserProvider(window.ethereum);
      const signer = await provider.getSigner();

      // Connect to the token contract to get whitelist address
      const tokenContract = new ethers.Contract(
        tokenAddress,
        SecurityTokenABI.abi,
        signer
      );

      // Get the whitelist contract address
      const whitelistAddress = await tokenContract.identityRegistry();
      if (!whitelistAddress || whitelistAddress === ethers.ZeroAddress) {
        throw new Error("No whitelist contract found for this token");
      }

      // Connect to the whitelist contract
      const whitelistContract = new ethers.Contract(
        whitelistAddress,
        WhitelistABI.abi,
        signer
      );

      // Check if user has AGENT_ROLE
      const AGENT_ROLE = ethers.keccak256(ethers.toUtf8Bytes("AGENT_ROLE"));
      const userAddress = await signer.getAddress();
      const hasAgentRole = await whitelistContract.hasRole(AGENT_ROLE, userAddress);

      if (!hasAgentRole) {
        throw new Error("You don't have permission to whitelist addresses. You need the AGENT_ROLE on the whitelist contract.");
      }

      // Batch add to whitelist with optimized gas settings
      console.log(`Batch adding ${addressesList.length} addresses to whitelist...`);

      // Use optimized gas settings for Amoy testnet (higher limit for batch operations)
      const gasLimit = BigInt(500000 + (addressesList.length * 50000)); // Base 500k + 50k per address
      const gasPrice = ethers.parseUnits("50", "gwei"); // 50 gwei gas price

      console.log("Using optimized gas settings for batch operation:");
      console.log("Gas limit:", gasLimit.toString());
      console.log("Gas price:", ethers.formatUnits(gasPrice, "gwei"), "gwei");

      // Batch add to whitelist - use appropriate method based on contract type
      let tx;
      if (whitelistContract === tokenContract) {
        // Enhanced token with built-in whitelist
        const statuses = new Array(addressesList.length).fill(true);
        tx = await whitelistContract.batchUpdateWhitelist(addressesList, statuses, {
          gasLimit,
          gasPrice
        });
      } else {
        // Separate whitelist contract
        tx = await whitelistContract.batchAddToWhitelist(addressesList, {
          gasLimit,
          gasPrice
        });
      }

      setBatchWhitelistTxHash(tx.hash);
      alert(`Transaction submitted. Hash: ${tx.hash}`);

      // Wait for confirmation
      await tx.wait();

      setBatchWhitelistStatus('success');
      alert(`Successfully whitelisted ${addressesList.length} addresses.`);

      // Update database to sync whitelist status for all addresses
      await Promise.all(addressesList.map(addr => syncWhitelistToDatabase(addr, true)));

      // Refresh token details
      fetchTokenDetails();
    } catch (err: any) {
      console.error('Error batch whitelisting addresses:', err);
      setBatchWhitelistStatus('error');
      alert(`Error: ${err.message || 'Error whitelisting addresses. Please try again.'}`);
    } finally {
      if (batchWhitelistStatus === 'pending') {
        setBatchWhitelistStatus('idle');
      }
    }
  };

  const handleUpgradeContract = async () => {
    if (!walletConnected) {
      alert("Please connect your wallet first");
      return;
    }

    const confirmUpgrade = confirm('Are you sure you want to upgrade this token contract? This will add whitelist management functions directly to the token contract.');
    if (!confirmUpgrade) return;

    try {
      setUpgradeStatus('pending');

      if (!window.ethereum) {
        throw new Error('Please install MetaMask to use this feature!');
      }

      const provider = new ethers.BrowserProvider(window.ethereum);
      const signer = await provider.getSigner();

      // Check if user has DEFAULT_ADMIN_ROLE
      if (!hasAdminRole) {
        throw new Error("You don't have permission to upgrade this contract. You need the DEFAULT_ADMIN_ROLE.");
      }

      try {
        // Call our API endpoint for contract upgrades
        const response = await fetch('/api/contracts/upgrade', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            contractAddress: tokenAddress,
            contractType: 'token',
            network: (tokenDetails?.network || 'amoy')
          }),
        });

        const data = await response.json();

        if (!response.ok) {
          // Handle detailed error response from API
          if (data.clientSideInstructions) {
            setUpgradeStatus('error');
            // Format a nicer error message with the command example
            const message = `${data.message}\n\n${data.commandExample}`;
            alert(message);
            throw new Error(data.error);
          } else {
            throw new Error(data.error || 'Failed to upgrade contract');
          }
        }

        if (!data.success) {
          // If the API indicates it can't perform the upgrade itself
          setUpgradeStatus('error');
          alert(`${data.message}\n\n${data.details || ''}`);
          throw new Error(data.message);
        }

        setUpgradeStatus('success');
        alert('Contract successfully upgraded! The whitelist functions are now available.');

        // Refresh token details
        fetchTokenDetails();
      } catch (err: any) {
        console.error("Error upgrading contract:", err);
        const errorMessage = err.message || "Unknown error";
        throw new Error(`Failed to upgrade contract: ${errorMessage}`);
      }
    } catch (err: any) {
      console.error('Error upgrading contract:', err);
      setUpgradeStatus('error');
      alert(`Error: ${err.message || 'Error upgrading contract. Please try again.'}`);
    } finally {
      if (upgradeStatus === 'pending') {
        setUpgradeStatus('idle');
      }
    }
  };

  const getBlockExplorerUrl = (network: string | undefined, address: string) => {
    if (!address) return '#';

    // Convert network to lowercase for case-insensitive comparison
    const networkLower = network?.toLowerCase() || 'unknown';

    switch (networkLower) {
      case 'amoy':
        return `https://www.oklink.com/amoy/address/${address}`;
      case 'polygon':
        return `https://polygonscan.com/address/${address}`;
      case 'mumbai':
        return `https://mumbai.polygonscan.com/address/${address}`;
      case 'ethereum':
      case 'mainnet':
        return `https://etherscan.io/address/${address}`;
      case 'goerli':
        return `https://goerli.etherscan.io/address/${address}`;
      case 'sepolia':
        return `https://sepolia.etherscan.io/address/${address}`;
      case 'bsc':
      case 'binance':
        return `https://bscscan.com/address/${address}`;
      case 'arbitrum':
        return `https://arbiscan.io/address/${address}`;
      case 'optimism':
        return `https://optimistic.etherscan.io/address/${address}`;
      case 'base':
        return `https://basescan.org/address/${address}`;
      case 'avalanche':
        return `https://snowtrace.io/address/${address}`;
      default:
        console.warn(`Unknown network: ${network}, falling back to Etherscan`);
        return `https://etherscan.io/address/${address}`;
    }
  };

  const getTransactionExplorerUrl = (network: string | undefined, txHash: string | null) => {
    if (!txHash) return '#';

    // Convert network to lowercase for case-insensitive comparison
    const networkLower = network?.toLowerCase() || 'unknown';

    switch (networkLower) {
      case 'amoy':
        return `https://www.oklink.com/amoy/tx/${txHash}`;
      case 'polygon':
        return `https://polygonscan.com/tx/${txHash}`;
      case 'mumbai':
        return `https://mumbai.polygonscan.com/tx/${txHash}`;
      case 'ethereum':
      case 'mainnet':
        return `https://etherscan.io/tx/${txHash}`;
      case 'goerli':
        return `https://goerli.etherscan.io/tx/${txHash}`;
      case 'sepolia':
        return `https://sepolia.etherscan.io/tx/${txHash}`;
      case 'bsc':
      case 'binance':
        return `https://bscscan.com/tx/${txHash}`;
      case 'arbitrum':
        return `https://arbiscan.io/tx/${txHash}`;
      case 'optimism':
        return `https://optimistic.etherscan.io/tx/${txHash}`;
      case 'base':
        return `https://basescan.org/tx/${txHash}`;
      case 'avalanche':
        return `https://snowtrace.io/tx/${txHash}`;
      default:
        console.warn(`Unknown network: ${network}, falling back to Etherscan`);
        return `https://etherscan.io/tx/${txHash}`;
    }
  };

  // New function for handling direct whitelist operations
  const handleDirectWhitelistAddress = async () => {
    if (!walletConnected) {
      alert("Please connect your wallet first");
      return;
    }

    if (!whitelistAddress) {
      alert("Whitelist address not available. Please check the token configuration.");
      return;
    }

    const address = prompt('Enter address to whitelist:');
    if (!address || !ethers.isAddress(address)) {
      alert("Invalid address");
      return;
    }

    try {
      setDirectWhitelistStatus('pending');

      // Call our whitelist utility function
      const result = await WhitelistUtils.addToWhitelist(
        whitelistAddress,
        address,
        tokenDetails?.network || 'amoy'
      );

      setDirectWhitelistTxHash(result.txHash);
      setDirectWhitelistStatus('success');

      // Alert success
      alert(`Successfully whitelisted address: ${address}`);

      // Update database to sync whitelist status
      await syncWhitelistToDatabase(address, true);

    } catch (err: any) {
      console.error('Error directly whitelisting address:', err);
      setDirectWhitelistStatus('error');
      alert(`Error: ${err.message || 'Error whitelisting address. Please try again.'}`);
    } finally {
      if (directWhitelistStatus === 'pending') {
        setDirectWhitelistStatus('idle');
      }
    }
  };

  // Function for batch whitelisting directly through the whitelist contract
  const handleDirectBatchWhitelist = async () => {
    if (!walletConnected) {
      alert("Please connect your wallet first");
      return;
    }

    if (!whitelistAddress) {
      alert("Whitelist address not available. Please check the token configuration.");
      return;
    }

    const addressesText = prompt('Enter addresses to whitelist (separated by commas):');
    if (!addressesText) return;

    // Parse and validate addresses
    const addressesList = addressesText
      .split(',')
      .map(addr => addr.trim())
      .filter(addr => addr.length > 0);

    // Validate each address
    const invalidAddresses = addressesList.filter(addr => !ethers.isAddress(addr));
    if (invalidAddresses.length > 0) {
      alert(`Invalid addresses found:\n${invalidAddresses.join('\n')}\n\nPlease fix and try again.`);
      return;
    }

    try {
      setDirectBatchWhitelistStatus('pending');

      // Call our whitelist utility function
      const result = await WhitelistUtils.batchAddToWhitelist(
        whitelistAddress,
        addressesList,
        tokenDetails?.network || 'amoy'
      );

      setDirectBatchWhitelistTxHash(result.txHash);
      setDirectBatchWhitelistStatus('success');

      // Alert success
      alert(`Successfully whitelisted ${addressesList.length} addresses.`);

      // Update database to sync whitelist status for all addresses
      await Promise.all(addressesList.map(addr => syncWhitelistToDatabase(addr, true)));

    } catch (err: any) {
      console.error('Error directly batch whitelisting addresses:', err);
      setDirectBatchWhitelistStatus('error');
      alert(`Error: ${err.message || 'Error whitelisting addresses. Please try again.'}`);
    } finally {
      if (directBatchWhitelistStatus === 'pending') {
        setDirectBatchWhitelistStatus('idle');
      }
    }
  };

  // Function to check address status (whitelist and frozen)
  const handleCheckAddressStatus = async () => {
    const address = prompt('Enter address to check:');
    if (!address || !ethers.isAddress(address)) {
      alert("Invalid address");
      return;
    }

    if (!whitelistAddress) {
      alert("Whitelist address not available. Please check the token configuration.");
      return;
    }

    try {
      setCheckingAddressStatus(true);

      // Call our whitelist utility functions
      const isWhitelisted = await WhitelistUtils.isWhitelisted(
        whitelistAddress,
        address,
        tokenDetails?.network || 'amoy'
      );

      const isFrozen = await WhitelistUtils.isFrozen(
        whitelistAddress,
        address,
        tokenDetails?.network || 'amoy'
      );

      setAddressStatus({
        address,
        isWhitelisted,
        isFrozen
      });

    } catch (err: any) {
      console.error('Error checking address status:', err);
      alert(`Error: ${err.message || 'Error checking address status. Please try again.'}`);
    } finally {
      setCheckingAddressStatus(false);
    }
  };

  // Function to freeze an address
  const handleFreezeAddress = async (address: string) => {
    if (!walletConnected) {
      alert("Please connect your wallet first");
      return;
    }

    if (!whitelistAddress) {
      alert("Whitelist address not available. Please check the token configuration.");
      return;
    }

    if (!agentRole) {
      alert("You need AGENT_ROLE to freeze this address");
      return;
    }

    if (!confirm(`Are you sure you want to freeze this address?`)) {
      return;
    }

    try {
      if (!window.ethereum) {
        throw new Error('Please install MetaMask to use this feature!');
      }

      const provider = new ethers.BrowserProvider(window.ethereum);
      const signer = await provider.getSigner();

      // Connect to the whitelist contract
      const whitelistContract = new ethers.Contract(
        whitelistAddress,
        WhitelistABI.abi,
        signer
      );

      // Check if user has AGENT_ROLE
      const AGENT_ROLE = await whitelistContract.AGENT_ROLE();
      const userAddress = await signer.getAddress();
      const hasAgentRole = await whitelistContract.hasRole(AGENT_ROLE, userAddress);

      if (!hasAgentRole) {
        throw new Error("You don't have AGENT_ROLE on the whitelist contract. Cannot freeze addresses.");
      }

      // Use optimized gas settings
      const gasLimit = BigInt(200000); // 200k gas limit
      const gasPrice = ethers.parseUnits("50", "gwei"); // 50 gwei gas price

      console.log("Freezing address with optimized gas settings:");
      console.log("Gas limit:", gasLimit.toString());
      console.log("Gas price:", ethers.formatUnits(gasPrice, "gwei"), "gwei");

      const tx = await whitelistContract.freezeAddress(address, {
        gasLimit,
        gasPrice
      });

      alert(`Transaction submitted. Hash: ${tx.hash}`);

      // Wait for the transaction to be mined
      await tx.wait();

      alert(`Successfully froze address: ${address}`);
      fetchTokenDetails();
      fetchWhitelistedAddresses(); // Refresh the whitelist
    } catch (err: any) {
      console.error('Error freezing address:', err);
      alert(`Error: ${err.message || 'Error freezing address. Please try again.'}`);
    }
  };

  // Function to unfreeze an address
  const handleUnfreezeAddress = async (address: string) => {
    if (!walletConnected) {
      alert("Please connect your wallet first");
      return;
    }

    if (!whitelistAddress) {
      alert("Whitelist address not available. Please check the token configuration.");
      return;
    }

    if (!agentRole) {
      alert("You need AGENT_ROLE to unfreeze this address");
      return;
    }

    if (!confirm(`Are you sure you want to unfreeze this address?`)) {
      return;
    }

    try {
      if (!window.ethereum) {
        throw new Error('Please install MetaMask to use this feature!');
      }

      const provider = new ethers.BrowserProvider(window.ethereum);
      const signer = await provider.getSigner();

      // Connect to the whitelist contract
      const whitelistContract = new ethers.Contract(
        whitelistAddress,
        WhitelistABI.abi,
        signer
      );

      // Check if user has AGENT_ROLE
      const AGENT_ROLE = await whitelistContract.AGENT_ROLE();
      const userAddress = await signer.getAddress();
      const hasAgentRole = await whitelistContract.hasRole(AGENT_ROLE, userAddress);

      if (!hasAgentRole) {
        throw new Error("You don't have AGENT_ROLE on the whitelist contract. Cannot unfreeze addresses.");
      }

      // Use optimized gas settings
      const gasLimit = BigInt(200000); // 200k gas limit
      const gasPrice = ethers.parseUnits("50", "gwei"); // 50 gwei gas price

      console.log("Unfreezing address with optimized gas settings:");
      console.log("Gas limit:", gasLimit.toString());
      console.log("Gas price:", ethers.formatUnits(gasPrice, "gwei"), "gwei");

      const tx = await whitelistContract.unfreezeAddress(address, {
        gasLimit,
        gasPrice
      });

      alert(`Transaction submitted. Hash: ${tx.hash}`);

      // Wait for the transaction to be mined
      await tx.wait();

      alert(`Successfully unfroze address: ${address}`);
      fetchTokenDetails();
      fetchWhitelistedAddresses(); // Refresh the whitelist
    } catch (err: any) {
      console.error('Error unfreezing address:', err);
      alert(`Error: ${err.message || 'Error unfreezing address. Please try again.'}`);
    }
  };

  // Add function to fetch whitelisted addresses directly from blockchain
  const fetchWhitelistedAddresses = async () => {
    setIsLoadingWhitelistedAddresses(true);

    try {
      if (!window.ethereum) {
        throw new Error('MetaMask not found');
      }

      const provider = new ethers.BrowserProvider(window.ethereum);

      // Get the correct ABI for this token type
      const tokenABI = await getTokenABI(provider);

      // Connect to the token contract
      const tokenContract = new ethers.Contract(tokenAddress, tokenABI, provider);

      console.log("🔍 Fetching whitelisted addresses directly from blockchain...");

      // Get whitelist events to find all addresses that were ever whitelisted
      const whitelistedAddresses: Array<{
        address: string,
        isWhitelisted: boolean,
        isKycApproved: boolean,
        isVerified: boolean,
        balance: string,
        frozen: boolean,
        frozenTokens: string
      }> = [];

      // List of known addresses to check (you can expand this list)
      const knownAddresses = [
        "******************************************", // Your main address
        "******************************************", // Address you tried to whitelist
        "******************************************", // Test address
        "******************************************", // Another test address
      ];

      // Try to get events first
      try {
        console.log("🔍 Checking for AddressWhitelisted events...");
        const filter = tokenContract.filters.AddressWhitelisted();
        const events = await tokenContract.queryFilter(filter, 0, 'latest');

        console.log(`Found ${events.length} AddressWhitelisted events`);

        // Add addresses from events to known addresses
        for (const event of events) {
          if ('args' in event && event.args && event.args.length > 0) {
            const address = event.args[0] as string;
            if (!knownAddresses.includes(address)) {
              knownAddresses.push(address);
            }
          }
        }
      } catch (eventError) {
        console.log("Could not fetch events, using known addresses only:", eventError);
      }

      console.log(`Checking ${knownAddresses.length} addresses for whitelist status...`);

      // Check each known address
      for (const address of knownAddresses) {
        try {
          const isWhitelisted = await tokenContract.isWhitelisted(address);

          // Only include if actually whitelisted
          if (isWhitelisted) {
            const isKycApproved = await tokenContract.isKycApproved(address);
            const isVerified = await tokenContract.isVerified(address);
            const balance = await tokenContract.balanceOf(address);

            // For minimal tokens, we don't have frozen token functionality
            // So we'll set these to default values
            const frozen = false;
            const frozenTokens = "0";

            whitelistedAddresses.push({
              address,
              isWhitelisted,
              isKycApproved,
              isVerified,
              balance: balance.toString(),
              frozen,
              frozenTokens
            });

            console.log(`✅ Found whitelisted address: ${address}`);
          }
        } catch (error) {
          console.log(`❌ Error checking address ${address}:`, error);
        }
      }

      // Remove duplicates (in case the same address appears multiple times)
      const uniqueAddresses = whitelistedAddresses.filter((addr, index, self) =>
        index === self.findIndex(a => a.address === addr.address)
      );

      console.log(`✅ Found ${uniqueAddresses.length} unique whitelisted addresses`);
      setWhitelistedAddresses(uniqueAddresses);

      // Set vault address to the first admin/agent address
      try {
        const allAgents = await tokenContract.getAllAgents();
        if (allAgents && allAgents.length > 0) {
          setVaultAddress(allAgents[0]);
        }
      } catch (err) {
        console.log("Could not get vault address from agents");
      }

    } catch (error) {
      console.error('Error fetching whitelisted addresses:', error);
      console.log(`Could not fetch whitelisted addresses: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setWhitelistedAddresses([]);
    } finally {
      setIsLoadingWhitelistedAddresses(false);
    }
  };

  // Add function for force transferring tokens
  const handleForceTransfer = async (fromAddress: string) => {
    if (!walletConnected) {
      alert("Please connect your wallet first");
      return;
    }

    if (!hasAdminRole && !hasTransferManagerRole) {
      alert("You need DEFAULT_ADMIN_ROLE or TRANSFER_MANAGER_ROLE to force transfer tokens");
      return;
    }

    const toAddress = prompt('Enter recipient address:');
    if (!toAddress || !ethers.isAddress(toAddress)) {
      alert("Invalid recipient address");
      return;
    }

    const amount = prompt('Enter amount to transfer:');
    if (!amount || isNaN(Number(amount)) || Number(amount) <= 0) {
      alert("Invalid amount");
      return;
    }

    if (!confirm(`Are you sure you want to force transfer ${amount} tokens from ${fromAddress} to ${toAddress}?`)) {
      return;
    }

    setForceTransferStatus('pending');

    try {
      if (!window.ethereum) {
        throw new Error('Please install MetaMask to use this feature!');
      }

      const provider = new ethers.BrowserProvider(window.ethereum);
      const signer = await provider.getSigner();

      // Connect to the token contract
      const tokenContract = new ethers.Contract(
        tokenAddress,
        SecurityTokenABI.abi,
        signer
      );

      // Check if user has TRANSFER_MANAGER_ROLE or DEFAULT_ADMIN_ROLE (required for forcedTransfer)
      const DEFAULT_ADMIN_ROLE = await tokenContract.DEFAULT_ADMIN_ROLE();
      const userAddress = await signer.getAddress();
      const hasAdminRole = await tokenContract.hasRole(DEFAULT_ADMIN_ROLE, userAddress);

      // Check for TRANSFER_MANAGER_ROLE only if it exists
      let hasTransferManagerRole = false;
      try {
        const TRANSFER_MANAGER_ROLE = await tokenContract.TRANSFER_MANAGER_ROLE();
        hasTransferManagerRole = await tokenContract.hasRole(TRANSFER_MANAGER_ROLE, userAddress);
      } catch (err) {
        console.log("TRANSFER_MANAGER_ROLE not available on this token");
        hasTransferManagerRole = false;
      }

      if (!hasTransferManagerRole && !hasAdminRole) {
        throw new Error("You don't have TRANSFER_MANAGER_ROLE or DEFAULT_ADMIN_ROLE. Cannot force transfer tokens.");
      }

      console.log(`Force transfer permissions - TRANSFER_MANAGER: ${hasTransferManagerRole}, ADMIN: ${hasAdminRole}`);

      // Read decimals directly from contract to ensure accuracy
      const decimalsRaw = await tokenContract.decimals();
      const decimals = Number(decimalsRaw);
      console.log(`Token decimals (from contract): ${decimals}`);
      console.log(`Amount to force transfer: ${amount}`);

      // Convert amount based on decimals
      let parsedAmount: bigint;
      if (decimals === 0) {
        parsedAmount = BigInt(amount);
        console.log(`Amount (0 decimals): ${parsedAmount.toString()}`);
      } else {
        parsedAmount = ethers.parseUnits(amount, decimals);
        console.log(`Amount (${decimals} decimals): ${parsedAmount.toString()}`);
      }

      console.log(`Final amount to force transfer: ${parsedAmount.toString()}`);

      console.log(`Force transferring ${amount} tokens from ${fromAddress} to ${toAddress}...`);

      // Use optimized gas settings
      const gasLimit = BigInt(500000); // 500k gas limit for forcedTransfer
      const gasPrice = ethers.parseUnits("50", "gwei"); // 50 gwei gas price

      console.log("Using optimized gas settings for force transfer:");
      console.log("Gas limit:", gasLimit.toString());
      console.log("Gas price:", ethers.formatUnits(gasPrice, "gwei"), "gwei");

      // Using the forcedTransfer function
      const tx = await tokenContract.forcedTransfer(fromAddress, toAddress, parsedAmount, {
        gasLimit,
        gasPrice
      });

      alert(`Transaction submitted. Hash: ${tx.hash}`);

      // Wait for the transaction to be mined
      await tx.wait();

      setForceTransferTxHash(tx.hash);
      setForceTransferStatus('success');
      alert(`Successfully transferred ${amount} tokens from ${fromAddress} to ${toAddress}`);

      // Refresh the list to show updated balances
      fetchWhitelistedAddresses();
    } catch (err: any) {
      console.error('Error forcing token transfer:', err);
      setForceTransferStatus('error');
      alert(`Error: ${err.message || 'Error forcing token transfer. Please try again.'}`);
    } finally {
      if (forceTransferStatus === 'pending') {
        setForceTransferStatus('idle');
      }
    }
  };

  // Add function for partial token freezing
  const handlePartialFreeze = async (fromAddress: string) => {
    if (!walletConnected) {
      alert("Please connect your wallet first");
      return;
    }

    if (!hasAdminRole && !hasTransferManagerRole) {
      alert("You need DEFAULT_ADMIN_ROLE or TRANSFER_MANAGER_ROLE to partially freeze tokens");
      return;
    }

    const amount = prompt('Enter amount of tokens to freeze:');
    if (!amount || isNaN(Number(amount)) || Number(amount) <= 0) {
      alert("Invalid amount");
      return;
    }

    if (!confirm(`Are you sure you want to freeze ${amount} tokens from ${fromAddress}?`)) {
      return;
    }

    try {
      if (!window.ethereum) {
        throw new Error('Please install MetaMask to use this feature!');
      }

      const provider = new ethers.BrowserProvider(window.ethereum);
      const signer = await provider.getSigner();

      // Connect to the token contract
      const tokenContract = new ethers.Contract(
        tokenAddress,
        SecurityTokenABI.abi,
        signer
      );

      // Check if user has TRANSFER_MANAGER_ROLE or DEFAULT_ADMIN_ROLE (required for freeze)
      const DEFAULT_ADMIN_ROLE = await tokenContract.DEFAULT_ADMIN_ROLE();
      const userAddress = await signer.getAddress();
      const hasAdminRole = await tokenContract.hasRole(DEFAULT_ADMIN_ROLE, userAddress);

      // Check for TRANSFER_MANAGER_ROLE only if it exists
      let hasTransferManagerRole = false;
      try {
        const TRANSFER_MANAGER_ROLE = await tokenContract.TRANSFER_MANAGER_ROLE();
        hasTransferManagerRole = await tokenContract.hasRole(TRANSFER_MANAGER_ROLE, userAddress);
      } catch (err) {
        console.log("TRANSFER_MANAGER_ROLE not available on this token");
        hasTransferManagerRole = false;
      }

      if (!hasTransferManagerRole && !hasAdminRole) {
        throw new Error("You don't have TRANSFER_MANAGER_ROLE or DEFAULT_ADMIN_ROLE. Cannot freeze tokens.");
      }

      console.log(`Partial freeze permissions - TRANSFER_MANAGER: ${hasTransferManagerRole}, ADMIN: ${hasAdminRole}`);

      // Read decimals directly from contract to ensure accuracy
      const decimalsRaw = await tokenContract.decimals();
      const decimals = Number(decimalsRaw);
      console.log(`Token decimals (from contract): ${decimals}`);
      console.log(`Amount to freeze: ${amount}`);

      // Convert amount based on decimals
      let parsedAmount: bigint;
      if (decimals === 0) {
        parsedAmount = BigInt(amount);
        console.log(`Amount (0 decimals): ${parsedAmount.toString()}`);
      } else {
        parsedAmount = ethers.parseUnits(amount, decimals);
        console.log(`Amount (${decimals} decimals): ${parsedAmount.toString()}`);
      }

      console.log(`Final amount to freeze: ${parsedAmount.toString()}`);

      // For partial freeze, we need a vault address to transfer tokens to
      // This is a simplified approach - in production you might want a dedicated vault contract
      const vaultAddress = userAddress; // Use admin address as vault for now

      // Check conditions before attempting transfer
      console.log("Checking transfer conditions...");

      // Check balances
      const fromBalance = await tokenContract.balanceOf(fromAddress);
      const vaultBalance = await tokenContract.balanceOf(vaultAddress);
      console.log(`From address balance: ${fromBalance.toString()}`);
      console.log(`Vault address balance: ${vaultBalance.toString()}`);

      if (fromBalance < parsedAmount) {
        throw new Error(`Insufficient balance. Address has ${fromBalance.toString()} tokens but trying to freeze ${parsedAmount.toString()} tokens.`);
      }

      // Check if contract is paused
      try {
        let isPausedCheck = false;
        try {
          isPausedCheck = await tokenContract.isPaused();
        } catch (err) {
          isPausedCheck = await tokenContract.paused();
        }
        console.log(`Contract paused: ${isPausedCheck}`);
        if (isPausedCheck) {
          throw new Error("Token contract is paused. Cannot perform transfers.");
        }
      } catch (err) {
        console.log("Could not check paused state (method might not exist)");
      }

      // Check whitelist status if whitelist contract exists
      if (whitelistAddress) {
        try {
          const whitelistContract = new ethers.Contract(whitelistAddress, WhitelistABI.abi, signer);
          const fromWhitelisted = await whitelistContract.isWhitelisted(fromAddress);
          const vaultWhitelisted = await whitelistContract.isWhitelisted(vaultAddress);
          console.log(`From address whitelisted: ${fromWhitelisted}`);
          console.log(`Vault address whitelisted: ${vaultWhitelisted}`);

          if (!fromWhitelisted) {
            console.warn(`Warning: From address ${fromAddress} is not whitelisted`);
          }
          if (!vaultWhitelisted) {
            console.warn(`Warning: Vault address ${vaultAddress} is not whitelisted`);
            console.log("Auto-whitelisting vault address...");

            // Auto-whitelist the vault address
            try {
              const whitelistTx = await whitelistContract.addToWhitelist(vaultAddress, {
                gasLimit: BigInt(200000),
                gasPrice: ethers.parseUnits("50", "gwei")
              });

              console.log(`Whitelisting vault transaction: ${whitelistTx.hash}`);
              await whitelistTx.wait();
              console.log("Vault address successfully whitelisted!");
            } catch (whitelistError: any) {
              console.error("Failed to whitelist vault address:", whitelistError);
              throw new Error(`Vault address ${vaultAddress} is not whitelisted and auto-whitelisting failed: ${whitelistError.message}`);
            }
          }
        } catch (err) {
          console.log("Could not check whitelist status:", err);
        }
      }

      console.log(`Freezing ${amount} tokens from ${fromAddress} to vault ${vaultAddress}...`);

      // Use optimized gas settings
      const gasLimit = BigInt(500000); // 500k gas limit for adminTransfer
      const gasPrice = ethers.parseUnits("50", "gwei"); // 50 gwei gas price

      console.log("Using optimized gas settings for partial freeze:");
      console.log("Gas limit:", gasLimit.toString());
      console.log("Gas price:", ethers.formatUnits(gasPrice, "gwei"), "gwei");

      // Using the forcedTransfer function to move tokens to the vault
      const tx = await tokenContract.forcedTransfer(fromAddress, vaultAddress, parsedAmount, {
        gasLimit,
        gasPrice
      });

      alert(`Transaction submitted. Hash: ${tx.hash}`);

      // Wait for the transaction to be mined
      await tx.wait();

      alert(`Successfully froze ${amount} tokens from ${fromAddress}`);
      fetchWhitelistedAddresses(); // Refresh the list
    } catch (err: any) {
      console.error('Error partially freezing tokens:', err);
      alert(`Error: ${err.message || 'Error freezing tokens. Please try again.'}`);
    }
  };

  // Add function to update token metadata
  const handleUpdateTokenMetadata = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!walletConnected) {
      alert("Please connect your wallet first");
      return;
    }

    if (!hasAdminRole) {
      alert("You need DEFAULT_ADMIN_ROLE to update token metadata");
      return;
    }

    if (!editTokenPrice && !editBonusTiers) {
      alert("Please enter at least one of token price or bonus tiers");
      return;
    }

    if (!confirm("Are you sure you want to update the token metadata?")) {
      return;
    }

    setTokenPriceUpdateStatus('pending');

    try {
      const response = await fetch('/api/contracts/token/update-metadata', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tokenAddress,
          tokenPrice: editTokenPrice ? `${editTokenPrice} ${editCurrency}` : undefined,
          bonusTiers: editBonusTiers || undefined,
          network: tokenDetails?.network || 'amoy'
        }),
      });

      const data = await response.json();

      if (data.success) {
        setTokenPriceUpdateTxHash(data.txHash);
        setTokenPriceUpdateStatus('success');
        alert("Token metadata updated successfully");
        fetchTokenDetails(); // Refresh the token details
        setShowTokenMetadataForm(false); // Hide the form
      } else {
        throw new Error(data.error || 'Unknown error');
      }
    } catch (err: any) {
      console.error('Error updating token metadata:', err);
      setTokenPriceUpdateStatus('error');
      alert(`Error: ${err.message || 'Error updating token metadata. Please try again.'}`);
    } finally {
      if (tokenPriceUpdateStatus === 'pending') {
        setTokenPriceUpdateStatus('idle');
      }
    }
  };

  // Add function to handle unfreezing tokens
  const handleUnfreezeTokens = async (toAddress: string) => {
    if (!walletConnected) {
      alert("Please connect your wallet first");
      return;
    }

    if (!hasAdminRole && !hasTransferManagerRole) {
      alert("You need DEFAULT_ADMIN_ROLE or TRANSFER_MANAGER_ROLE to unfreeze tokens");
      return;
    }

    if (!tokenDetails) {
      alert("Token details not available");
      return;
    }

    // Find the address in the whitelisted addresses list
    const addressInfo = whitelistedAddresses.find(a => a.address === toAddress);
    if (!addressInfo) {
      alert("Address not found in whitelist");
      return;
    }

    const frozenAmount = parseFloat(addressInfo.frozenTokens);
    if (frozenAmount <= 0) {
      alert("No frozen tokens to unfreeze");
      return;
    }

    const amount = prompt(`Enter amount to unfreeze (max ${frozenAmount} ${tokenDetails?.symbol || 'tokens'}):`, frozenAmount.toString());
    if (!amount || isNaN(Number(amount)) || Number(amount) <= 0 || Number(amount) > frozenAmount) {
      alert("Invalid amount");
      return;
    }

    if (!confirm(`Are you sure you want to unfreeze ${amount} ${tokenDetails?.symbol || 'tokens'} for ${toAddress}?`)) {
      return;
    }

    setUnfreezeStatus('pending');

    try {
      if (!window.ethereum) {
        throw new Error('Please install MetaMask to use this feature!');
      }

      const provider = new ethers.BrowserProvider(window.ethereum);
      const signer = await provider.getSigner();

      // Connect to the token contract
      const tokenContract = new ethers.Contract(
        tokenAddress,
        SecurityTokenABI.abi,
        signer
      );

      // Check if user has TRANSFER_MANAGER_ROLE or DEFAULT_ADMIN_ROLE (required for unfreeze)
      const DEFAULT_ADMIN_ROLE = await tokenContract.DEFAULT_ADMIN_ROLE();
      const userAddress = await signer.getAddress();
      const hasAdminRole = await tokenContract.hasRole(DEFAULT_ADMIN_ROLE, userAddress);

      // Check for TRANSFER_MANAGER_ROLE only if it exists
      let hasTransferManagerRole = false;
      try {
        const TRANSFER_MANAGER_ROLE = await tokenContract.TRANSFER_MANAGER_ROLE();
        hasTransferManagerRole = await tokenContract.hasRole(TRANSFER_MANAGER_ROLE, userAddress);
      } catch (err) {
        console.log("TRANSFER_MANAGER_ROLE not available on this token");
        hasTransferManagerRole = false;
      }

      if (!hasTransferManagerRole && !hasAdminRole) {
        throw new Error("You don't have TRANSFER_MANAGER_ROLE or DEFAULT_ADMIN_ROLE. Cannot unfreeze tokens.");
      }

      console.log(`Partial unfreeze permissions - TRANSFER_MANAGER: ${hasTransferManagerRole}, ADMIN: ${hasAdminRole}`);

      // Read decimals directly from contract to ensure accuracy
      const decimalsRaw = await tokenContract.decimals();
      const decimals = Number(decimalsRaw);
      console.log(`Token decimals (from contract): ${decimals}`);
      console.log(`Amount to unfreeze: ${amount}`);

      // Convert amount based on decimals
      let parsedAmount: bigint;
      if (decimals === 0) {
        parsedAmount = BigInt(amount);
        console.log(`Amount (0 decimals): ${parsedAmount.toString()}`);
      } else {
        parsedAmount = ethers.parseUnits(amount, decimals);
        console.log(`Amount (${decimals} decimals): ${parsedAmount.toString()}`);
      }

      console.log(`Final amount to unfreeze: ${parsedAmount.toString()}`);

      // For partial unfreeze, we transfer tokens from the vault (admin address) back to the user
      const vaultAddress = userAddress; // Use admin address as vault for now

      console.log(`Unfreezing ${amount} tokens from vault ${vaultAddress} to ${toAddress}...`);

      // Use optimized gas settings
      const gasLimit = BigInt(500000); // 500k gas limit for adminTransfer
      const gasPrice = ethers.parseUnits("50", "gwei"); // 50 gwei gas price

      console.log("Using optimized gas settings for partial unfreeze:");
      console.log("Gas limit:", gasLimit.toString());
      console.log("Gas price:", ethers.formatUnits(gasPrice, "gwei"), "gwei");

      // Using the forcedTransfer function to move tokens from the vault to the user
      const tx = await tokenContract.forcedTransfer(vaultAddress, toAddress, parsedAmount, {
        gasLimit,
        gasPrice
      });

      alert(`Transaction submitted. Hash: ${tx.hash}`);

      // Wait for the transaction to be mined
      await tx.wait();

      setUnfreezeStatus('success');
      alert(`Successfully unfroze ${amount} ${tokenDetails?.symbol || 'tokens'} for ${toAddress}`);

      // Refresh the list to show updated balances
      fetchWhitelistedAddresses();
    } catch (err: any) {
      console.error('Error unfreezing tokens:', err);
      setUnfreezeStatus('error');
      alert(`Error: ${err.message || 'Error unfreezing tokens. Please try again.'}`);
    } finally {
      if (unfreezeStatus === 'pending') {
        setUnfreezeStatus('idle');
      }
    }
  };

  // Add function to check KYC status
  const handleCheckKycStatus = async () => {
    if (!addressStatus || !addressStatus.address || !whitelistAddress || whitelistAddress === ethers.ZeroAddress) {
      return;
    }

    setCheckingKycStatus(true);
    try {
      const isKycApproved = await WhitelistUtils.isKycApproved(
        whitelistAddress,
        addressStatus.address,
        network
      );

      setAddressKycStatus({
        address: addressStatus.address,
        isKycApproved
      });
    } catch (error) {
      console.error("Error checking KYC status:", error);
    } finally {
      setCheckingKycStatus(false);
    }
  };

  // Add function to approve KYC for an address
  const handleApproveKyc = async (address: string) => {
    if (!whitelistAddress || whitelistAddress === ethers.ZeroAddress) {
      return;
    }

    setKycStatus('pending');
    try {
      const result = await WhitelistUtils.approveKyc(
        whitelistAddress,
        address,
        network
      );

      setKycStatus('success');
      setKycTxHash(result.txHash || null);

      // Refresh the KYC status after approval
      setTimeout(() => {
        if (addressStatus && addressStatus.address) {
          handleCheckKycStatus();
        }
      }, 2000);
    } catch (error) {
      console.error("Error approving KYC:", error);
      setKycStatus('error');
    }
  };

  // Add function to revoke KYC for an address
  const handleRevokeKyc = async (address: string) => {
    if (!whitelistAddress || whitelistAddress === ethers.ZeroAddress) {
      return;
    }

    setKycStatus('pending');
    try {
      const result = await WhitelistUtils.revokeKyc(
        whitelistAddress,
        address,
        network
      );

      setKycStatus('success');
      setKycTxHash(result.txHash || null);

      // Refresh the KYC status after revocation
      setTimeout(() => {
        if (addressStatus && addressStatus.address) {
          handleCheckKycStatus();
        }
      }, 2000);
    } catch (error) {
      console.error("Error revoking KYC:", error);
      setKycStatus('error');
    }
  };

  // Add function to batch approve KYC
  const handleBatchApproveKyc = async () => {
    const addressTextArea = document.getElementById('batch-kyc-addresses') as HTMLTextAreaElement;
    if (!addressTextArea || !addressTextArea.value.trim() || !whitelistAddress || whitelistAddress === ethers.ZeroAddress) {
      return;
    }

    const addresses = addressTextArea.value
      .split('\n')
      .map(addr => addr.trim())
      .filter(addr => addr && ethers.isAddress(addr));

    if (addresses.length === 0) {
      alert('No valid addresses found');
      return;
    }

    setBatchKycStatus('pending');
    try {
      const result = await WhitelistUtils.batchApproveKyc(
        whitelistAddress,
        addresses,
        network
      );

      setBatchKycStatus('success');
      setBatchKycTxHash(result.txHash || null);

      // Clear the textarea after successful batch approval
      addressTextArea.value = '';
    } catch (error) {
      console.error("Error batch approving KYC:", error);
      setBatchKycStatus('error');
    }
  };

  // Add function to batch revoke KYC
  const handleBatchRevokeKyc = async () => {
    const addressTextArea = document.getElementById('batch-kyc-addresses') as HTMLTextAreaElement;
    if (!addressTextArea || !addressTextArea.value.trim() || !whitelistAddress || whitelistAddress === ethers.ZeroAddress) {
      return;
    }

    const addresses = addressTextArea.value
      .split('\n')
      .map(addr => addr.trim())
      .filter(addr => addr && ethers.isAddress(addr));

    if (addresses.length === 0) {
      alert('No valid addresses found');
      return;
    }

    setBatchKycStatus('pending');
    try {
      const result = await WhitelistUtils.batchRevokeKyc(
        whitelistAddress,
        addresses,
        network
      );

      setBatchKycStatus('success');
      setBatchKycTxHash(result.txHash || null);

      // Clear the textarea after successful batch revocation
      addressTextArea.value = '';
    } catch (error) {
      console.error("Error batch revoking KYC:", error);
      setBatchKycStatus('error');
    }
  };

  // Modify fetchAgents to be more direct and thorough
  const fetchAgents = async () => {
    if (!tokenAddress) return;
    console.log("Fetching agents for token:", tokenAddress);

    setIsLoadingAgents(true);
    try {
      // Create a provider - try to use window.ethereum, otherwise fallback to a read-only provider
      let provider;
      if (window.ethereum) {
        provider = new ethers.BrowserProvider(window.ethereum);
      } else {
        // Fall back to a public provider for read-only operations
        const networkConfig = getNetworkConfig(tokenDetails?.network || 'amoy');
        provider = new ethers.JsonRpcProvider(networkConfig.rpcUrl);
      }

      if (!provider) {
        throw new Error("Could not create provider");
      }

      // Connect to the token contract
      const tokenContract = new ethers.Contract(
        tokenAddress,
        SecurityTokenABI.abi,
        provider
      );

      // Get the AGENT_ROLE constant
      const AGENT_ROLE = await tokenContract.AGENT_ROLE();
      console.log("AGENT_ROLE:", AGENT_ROLE);

      // Get the current account if possible
      let currentAccount = '';
      try {
        if (window.ethereum && window.ethereum.selectedAddress) {
          currentAccount = window.ethereum.selectedAddress;
        } else if (window.ethereum) {
          const signer = await provider.getSigner();
          currentAccount = await signer.getAddress();
        }
      } catch (err) {
        console.warn("Could not get current address:", err);
      }

      // Initialize a set to track agent addresses (prevents duplicates)
      const agentAddressSet = new Set<string>();

      // Function to check if an address has the AGENT_ROLE and add it to our set if it does
      const checkAndAddAgentRole = async (address: string) => {
        if (!address || !ethers.isAddress(address)) return false;

        try {
          const normalizedAddress = address.toLowerCase();

          // Skip if we've already checked this address
          if (agentAddressSet.has(normalizedAddress)) return true;

          // Check if address has AGENT_ROLE
          const hasRole = await tokenContract.hasRole(AGENT_ROLE, address);
          if (hasRole) {
            console.log(`Found agent: ${address}`);
            agentAddressSet.add(normalizedAddress);
            return true;
          }
        } catch (err) {
          console.warn(`Error checking agent role for ${address}:`, err);
        }
        return false;
      };

      // APPROACH 1: Try direct role member functions if available
      try {
        console.log("Trying getRoleMemberCount method...");
        const roleMemberCount = await tokenContract.getRoleMemberCount(AGENT_ROLE);
        console.log(`Role member count: ${roleMemberCount}`);

        for (let i = 0; i < roleMemberCount; i++) {
          try {
            const memberAddress = await tokenContract.getRoleMember(AGENT_ROLE, i);
            await checkAndAddAgentRole(memberAddress);
          } catch (err) {
            console.warn(`Error getting role member at index ${i}:`, err);
          }
        }
      } catch (err) {
        console.warn("Contract doesn't have getRoleMemberCount/getRoleMember functions:", err);
      }

      // APPROACH 2: Check current user and owner addresses
      if (currentAccount) {
        await checkAndAddAgentRole(currentAccount);
      }

      // Check owner address if contract has owner() method
      try {
        const owner = await tokenContract.owner();
        if (owner) {
          await checkAndAddAgentRole(owner);
        }
      } catch (err) {
        console.warn("Contract doesn't have owner() function");
      }

      // APPROACH 3: Brute force check any known/configured admin addresses
      // Add any addresses that might have admin roles in this specific project
      const knownAdminAddresses: string[] = [
        // Add specific addresses from the project if you know them
        "******************************************", // Add this as an example - replace with actual addresses
        "******************************************", // Another example address
        "******************************************", // Lowercase version to test duplicate handling
      ];

      for (const addr of knownAdminAddresses) {
        await checkAndAddAgentRole(addr);
      }

      // APPROACH 4: Check events without pagination first (for small contracts)
      try {
        console.log("Checking RoleGranted events");
        const roleGrantedFilter = tokenContract.filters.RoleGranted(AGENT_ROLE, null, null);
        const roleRevokedFilter = tokenContract.filters.RoleRevoked(AGENT_ROLE, null, null);

        // Start without block range limitations first (may fail for large contracts)
        try {
          const grantEvents = await tokenContract.queryFilter(roleGrantedFilter);
          console.log(`Found ${grantEvents.length} grant events (no block range)`);

          // Add all grant event addresses
          for (const event of grantEvents) {
            const typedEvent = event as unknown as { args: { account: string } };
            if (typedEvent.args && typedEvent.args.account) {
              await checkAndAddAgentRole(typedEvent.args.account);
            }
          }

          // Remove any revoked addresses
          const revokeEvents = await tokenContract.queryFilter(roleRevokedFilter);
          console.log(`Found ${revokeEvents.length} revoke events (no block range)`);

          for (const event of revokeEvents) {
            const typedEvent = event as unknown as { args: { account: string } };
            if (typedEvent.args && typedEvent.args.account) {
              // Don't remove immediately - check if they got the role again after it was revoked
              const address = typedEvent.args.account.toLowerCase();
              if (agentAddressSet.has(address)) {
                // Double-check current status
                const stillHasRole = await tokenContract.hasRole(AGENT_ROLE, typedEvent.args.account);
                if (!stillHasRole) {
                  console.log(`Removing revoked agent: ${typedEvent.args.account}`);
                  agentAddressSet.delete(address);
                } else {
                  console.log(`Agent ${typedEvent.args.account} had role revoked but was granted again later`);
                }
              }
            }
          }
        } catch (err) {
          console.warn("Error querying events without block range:", err);

          // APPROACH 5: Fall back to paginated event queries if "no range" query fails
          console.log("Falling back to paginated event queries");
          const latestBlock = await provider.getBlockNumber();
          const fromBlock = Math.max(0, latestBlock - 1000000); // ~1 million blocks
          const batchSize = 50000; // Smaller batch size to avoid errors

          // Process in smaller batches
          for (let startBlock = fromBlock; startBlock <= latestBlock; startBlock += batchSize) {
            const endBlock = Math.min(startBlock + batchSize - 1, latestBlock);
            console.log(`Querying events from blocks ${startBlock} to ${endBlock}`);

            try {
              // Check both granted and revoked events in same range
              const grantEvents = await tokenContract.queryFilter(roleGrantedFilter, startBlock, endBlock);
              console.log(`Found ${grantEvents.length} grant events in blocks ${startBlock}-${endBlock}`);

              for (const event of grantEvents) {
                const typedEvent = event as unknown as { args: { account: string } };
                if (typedEvent.args && typedEvent.args.account) {
                  await checkAndAddAgentRole(typedEvent.args.account);
                }
              }
            } catch (err) {
              console.warn(`Error querying grant events for blocks ${startBlock}-${endBlock}:`, err);
            }
          }
        }
      } catch (err) {
        console.warn("Error in event-based approach:", err);
      }

      // APPROACH 6: Manually check imported addresses if we still need more
      // If in the future you know specific addresses that should be agents, add them here
      if (agentAddressSet.size === 0 && window.ethereum) {
        try {
          // Import any addresses from localStorage if they exist
          const savedAgents = localStorage.getItem(`token-${tokenAddress}-agents`);
          if (savedAgents) {
            const savedAddresses = JSON.parse(savedAgents);
            console.log("Found saved agent addresses:", savedAddresses);

            for (const addr of savedAddresses) {
              if (ethers.isAddress(addr)) {
                await checkAndAddAgentRole(addr);
              }
            }
          }
        } catch (err) {
          console.warn("Error checking saved agents:", err);
        }
      }

      // Convert set to array for state
      const agentList = Array.from(agentAddressSet);
      console.log(`Final agent list contains ${agentList.length} agents:`, agentList);

      // Store the list for future reference
      try {
        if (window.localStorage && agentList.length > 0) {
          localStorage.setItem(`token-${tokenAddress}-agents`, JSON.stringify(agentList));
        }
      } catch (err) {
        console.warn("Could not save agent list to localStorage:", err);
      }

      // Set the agents list
      setAgents(agentList);
    } catch (err: any) {
      console.error('Error fetching agents:', err);
      setAgents([]);
    } finally {
      setIsLoadingAgents(false);
    }
  };

  // Add function to add a new agent
  const handleAddAgent = async () => {
    if (!walletConnected || !hasAdminRole) {
      alert("Please connect your wallet with admin role first");
      return;
    }

    if (!newAgentAddress || !ethers.isAddress(newAgentAddress)) {
      alert("Please enter a valid Ethereum address");
      return;
    }

    try {
      setAddAgentStatus('pending');

      if (!window.ethereum) {
        throw new Error('Please install MetaMask to use this feature!');
      }

      const provider = new ethers.BrowserProvider(window.ethereum);
      const signer = await provider.getSigner();

      // Connect to the token contract with signer
      const tokenContract = new ethers.Contract(
        tokenAddress,
        SecurityTokenABI.abi,
        signer
      );

      // Get the AGENT_ROLE constant
      const AGENT_ROLE = await tokenContract.AGENT_ROLE();

      // Check if already an agent using hasRole instead of isAgent
      const isAlreadyAgent = await tokenContract.hasRole(AGENT_ROLE, newAgentAddress);
      if (isAlreadyAgent) {
        throw new Error("This address is already an agent");
      }

      // Optimized gas settings
      const gasLimit = BigInt(5000000);
      const gasPrice = ethers.parseUnits("50", "gwei");

      // Use grantRole instead of addAgent
      const tx = await tokenContract.grantRole(AGENT_ROLE, newAgentAddress, { gasLimit, gasPrice });
      setAddAgentTxHash(tx.hash);

      // Wait for the transaction to be mined
      await tx.wait();

      setAddAgentStatus('success');

      // Clear input and refresh agents list
      setNewAgentAddress('');
      fetchAgents();

      alert(`Agent ${newAgentAddress} added successfully!`);
    } catch (err: any) {
      console.error('Error adding agent:', err);
      setAddAgentStatus('error');
      alert(`Error: ${err.message || 'Error adding agent. Please try again.'}`);
    } finally {
      if (addAgentStatus === 'pending') {
        setAddAgentStatus('idle');
      }
    }
  };

  // Properly fix the handleRemoveAgent function to reference the correct variables
  const handleRemoveAgent = async (agentAddress: string) => {
    if (!walletConnected || !hasAdminRole) {
      alert("Please connect your wallet with admin role first");
      return;
    }

    try {
      // Check if trying to remove yourself
      if (!window.ethereum) {
        throw new Error('Please install MetaMask to use this feature!');
      }

      const provider = new ethers.BrowserProvider(window.ethereum);
      const signer = await provider.getSigner();
      const currentAddress = await signer.getAddress();

      if (agentAddress.toLowerCase() === currentAddress.toLowerCase()) {
        alert("You cannot remove yourself as an agent");
        return;
      }

      if (!confirm(`Are you sure you want to remove ${agentAddress} as an agent?`)) {
        return;
      }

      setRemoveAgentStatus('pending');

      // Connect to the token contract with signer
      const tokenContract = new ethers.Contract(
        tokenAddress,
        SecurityTokenABI.abi,
        signer
      );

      // Get the AGENT_ROLE constant
      const AGENT_ROLE = await tokenContract.AGENT_ROLE();

      // Optimized gas settings
      const gasLimit = BigInt(5000000);
      const gasPrice = ethers.parseUnits("50", "gwei");

      // Use revokeRole instead of removeAgent
      const tx = await tokenContract.revokeRole(AGENT_ROLE, agentAddress, { gasLimit, gasPrice });
      setRemoveAgentTxHash(tx.hash);

      // Wait for the transaction to be mined
      await tx.wait();

      setRemoveAgentStatus('success');

      // Refresh agents list
      fetchAgents();

      alert(`Agent ${agentAddress} removed successfully!`);
    } catch (err: any) {
      console.error('Error removing agent:', err);
      setRemoveAgentStatus('error');
      alert(`Error: ${err.message || 'Error removing agent. Please try again.'}`);
    } finally {
      if (removeAgentStatus === 'pending') {
        setRemoveAgentStatus('idle');
      }
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center my-12">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 my-6">
        <p>{error}</p>
        <div className="mt-4 flex space-x-4">
          <button
            onClick={fetchTokenDetails}
            className="text-blue-600 hover:text-blue-800"
          >
            Retry
          </button>
          <button
            onClick={() => router.push('/')}
            className="text-blue-600 hover:text-blue-800"
          >
            Return to Dashboard
          </button>
        </div>
      </div>
    );
  }

  if (!tokenDetails) {
    return (
      <div className="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 my-6">
        <p>Token details not found.</p>
        <button
          onClick={() => router.push('/')}
          className="mt-4 text-blue-600 hover:text-blue-800"
        >
          Return to Dashboard
        </button>
      </div>
    );
  }

  const networkName = tokenDetails.network ?
    tokenDetails.network.charAt(0).toUpperCase() + tokenDetails.network.slice(1) :
    'Unknown';

  return (
    <div>
      <div className="mb-6 flex items-center flex-wrap">
        <Link href="/" className="text-blue-600 hover:text-blue-800 mr-4">
          &larr; Back to Dashboard
        </Link>
        <h1 className="text-3xl font-bold mr-2">{tokenDetails.name} ({tokenDetails.symbol})</h1>

        <div className="ml-auto flex items-center">
          {!walletConnected && (
            <button
              onClick={connectWallet}
              className="bg-purple-600 hover:bg-purple-700 text-white py-1 px-3 rounded text-sm mr-2"
            >
              Connect Wallet
            </button>
          )}

          <span className="px-3 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
            {networkName} Network
          </span>
        </div>
      </div>

      <div className="bg-white shadow-md rounded-lg p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Token Details</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p className="text-sm text-gray-500">Contract Address</p>
            <div className="flex items-center">
              <p className="font-mono">{tokenAddress}</p>
              <a
                href={getBlockExplorerUrl(tokenDetails.network, tokenAddress)}
                target="_blank"
                rel="noopener noreferrer"
                className="ml-2 text-xs text-blue-600 hover:text-blue-800"
              >
                View
              </a>
            </div>
          </div>

          <div>
            <p className="text-sm text-gray-500">Whitelist Address</p>
            <div className="flex items-center">
              <p className="font-mono truncate">{tokenDetails.whitelistAddress}</p>
              {tokenDetails.whitelistAddress && tokenDetails.whitelistAddress !== ethers.ZeroAddress && (
                <a
                  href={getBlockExplorerUrl(tokenDetails.network, tokenDetails.whitelistAddress)}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="ml-2 text-xs text-blue-600 hover:text-blue-800"
                >
                  View
                </a>
              )}
            </div>
          </div>

          <div>
            <p className="text-sm text-gray-500">Total Supply</p>
            <p>{tokenDetails.totalSupply} {tokenDetails.symbol}</p>
          </div>

          <div>
            <p className="text-sm text-gray-500">Maximum Supply</p>
            <p>{tokenDetails.maxSupply} {tokenDetails.symbol}</p>
          </div>

          <div>
            <p className="text-sm text-gray-500">Decimals</p>
            <p>{tokenDetails.decimals !== undefined ? tokenDetails.decimals : 'Unknown'}</p>
          </div>

          <div>
            <p className="text-sm text-gray-500">Token Type</p>
            <p>Security Token</p>
          </div>

          <div>
            <p className="text-sm text-gray-500">Token Category</p>
            <p>
              {tokenDetails.tokenCategory && tokenDetails.tokenCategory !== 'Not specified' ? (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {tokenDetails.tokenCategory}
                </span>
              ) : (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                  Not specified
                </span>
              )}
            </p>
          </div>

          <div>
            <p className="text-sm text-gray-500">Owner/Admin</p>
            <div className="flex items-center">
              <p className="font-mono text-sm truncate">{tokenDetails.adminAddress || 'Unknown'}</p>
              {tokenDetails.adminAddress && tokenDetails.adminAddress !== 'Unknown' && tokenDetails.adminAddress !== 'No admin found' && (
                <a
                  href={getBlockExplorerUrl(tokenDetails.network, tokenDetails.adminAddress)}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="ml-2 text-xs text-blue-600 hover:text-blue-800"
                >
                  View
                </a>
              )}
            </div>
          </div>

          <div>
            <p className="text-sm text-gray-500">KYC/Whitelist</p>
            <p>
              {tokenDetails.hasKYC ? (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Enabled
                </span>
              ) : (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                  Disabled
                </span>
              )}
            </p>
          </div>

          <div>
            <p className="text-sm text-gray-500">Token Price</p>
            <div className="flex items-center">
              <p>{tokenDetails.tokenPrice}</p>
              {hasAdminRole && (
                <button
                  onClick={() => {
                    if (!showTokenMetadataForm) {
                      // Parse current token price to extract price and currency
                      const currentPrice = tokenDetails.tokenPrice || '';
                      const priceMatch = currentPrice.match(/^([\d.]+)\s*(.*)$/);
                      if (priceMatch) {
                        setEditTokenPrice(priceMatch[1]);
                        const currency = priceMatch[2].trim().toUpperCase();
                        if (currencyOptions.find(opt => opt.value === currency)) {
                          setEditCurrency(currency);
                        }
                      }
                    }
                    setShowTokenMetadataForm(!showTokenMetadataForm);
                  }}
                  className="ml-2 text-xs text-blue-600 hover:text-blue-800"
                >
                  {showTokenMetadataForm ? 'Cancel' : 'Edit'}
                </button>
              )}
            </div>
          </div>

          <div>
            <p className="text-sm text-gray-500">Contract Version</p>
            <p>{tokenDetails.tokenVersion || 'Unknown'}</p>
          </div>

          <div>
            <p className="text-sm text-gray-500">Status</p>
            <p>
              {isPaused ? (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                  Paused
                </span>
              ) : (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Active
                </span>
              )}
            </p>
          </div>
        </div>

        <div className="mt-6">
          <p className="text-sm text-gray-500">Bonus Tiers</p>
          <p className="whitespace-pre-line">{tokenDetails.bonusTiers}</p>
        </div>

        {tokenDetails.tokenDetails && tokenDetails.tokenDetails !== 'Unknown' && (
          <div className="mt-6">
            <p className="text-sm text-gray-500">Token Details</p>
            <p className="text-sm bg-gray-50 p-2 rounded">{tokenDetails.tokenDetails}</p>
          </div>
        )}

        {/* Token Metadata Update Form */}
        {showTokenMetadataForm && (
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <h3 className="text-lg font-medium mb-4">Update Token Metadata</h3>
            <form onSubmit={handleUpdateTokenMetadata}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2" htmlFor="tokenPrice">
                    Token Price
                  </label>
                  <input
                    type="text"
                    id="tokenPrice"
                    value={editTokenPrice}
                    onChange={(e) => setEditTokenPrice(e.target.value)}
                    placeholder="e.g., 10"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                  <p className="mt-1 text-sm text-gray-500">Enter price amount only</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2" htmlFor="currency">
                    Currency
                  </label>
                  <select
                    id="currency"
                    value={editCurrency}
                    onChange={(e) => setEditCurrency(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
                    {currencyOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                  <p className="mt-1 text-sm text-gray-500">Select pricing currency</p>
                </div>
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2" htmlFor="bonusTiers">
                  Bonus Tiers
                </label>
                <textarea
                  id="bonusTiers"
                  value={editBonusTiers}
                  onChange={(e) => setEditBonusTiers(e.target.value)}
                  placeholder={tokenDetails.bonusTiers}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
                <p className="mt-1 text-sm text-gray-500">Leave empty to keep current value</p>
              </div>

              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={() => setShowTokenMetadataForm(false)}
                  className="mr-3 px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={tokenPriceUpdateStatus === 'pending' || (!editTokenPrice && !editBonusTiers)}
                  className={`px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
                    tokenPriceUpdateStatus === 'pending' || (!editTokenPrice && !editBonusTiers) ? 'opacity-50 cursor-not-allowed' : ''
                  }`}
                >
                  {tokenPriceUpdateStatus === 'pending' ? 'Updating...' : 'Update Metadata'}
                </button>
              </div>
            </form>

            {tokenPriceUpdateStatus === 'success' && tokenPriceUpdateTxHash && (
              <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
                <p className="text-sm text-green-800">Metadata updated successfully!</p>
                <a
                  href={getTransactionExplorerUrl(tokenDetails.network, tokenPriceUpdateTxHash)}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-xs text-blue-600 hover:underline"
                >
                  View transaction
                </a>
              </div>
            )}
          </div>
        )}
      </div>

      <div className="bg-white shadow-md rounded-lg p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Actions</h2>

        {network === 'amoy' && walletConnected && (
          <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4 rounded-md">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-yellow-700">
                  <strong>Amoy Testnet Notice:</strong> You are on the Amoy testnet which may have gas calculation issues. If minting fails, try using the command line script instead.
                </p>
              </div>
            </div>
          </div>
        )}

        {!walletConnected ? (
          <div className="bg-yellow-50 p-4 rounded mb-4">
            <p>Connect your wallet to perform token actions</p>
            <button
              onClick={connectWallet}
              className="mt-2 bg-blue-600 hover:bg-blue-700 text-white py-1 px-3 rounded text-sm"
            >
              Connect Wallet
            </button>
          </div>
        ) : !agentRole ? (
          <div className="bg-yellow-50 p-4 rounded mb-4">
            <p>Your wallet does not have the required roles to perform token actions</p>
          </div>
        ) : null}

        <div className="flex flex-wrap gap-4">
          <button
            onClick={async () => {
              console.log("🔄 Manual refresh clicked");
              setIsLoading(true);
              await fetchTokenDetails();
              console.log("🔄 Manual refresh completed, isPaused state:", isPaused);
            }}
            className="px-4 py-2 rounded text-white bg-blue-600 hover:bg-blue-700"
          >
            🔄 Refresh Status
          </button>

          <button
            onClick={async () => {
              console.log("🔍 Direct pause status check");
              try {
                if (!window.ethereum) {
                  alert('MetaMask not found');
                  return;
                }

                const provider = new ethers.BrowserProvider(window.ethereum);
                const tokenContract = new ethers.Contract(
                  tokenAddress,
                  SecurityTokenABI.abi,
                  provider
                );

                let pauseStatus = false;
                try {
                  pauseStatus = await tokenContract.isPaused();
                  console.log("✅ Direct isPaused() result:", pauseStatus);
                } catch (err: any) {
                  console.log("❌ isPaused() failed:", err.message);
                  try {
                    pauseStatus = await tokenContract.paused();
                    console.log("✅ Direct paused() result:", pauseStatus);
                  } catch (err2: any) {
                    console.log("❌ paused() also failed:", err2.message);
                  }
                }

                console.log("🎯 Setting isPaused state directly to:", pauseStatus);
                setIsPaused(pauseStatus);
                alert(`Direct check: Token is ${pauseStatus ? 'PAUSED' : 'ACTIVE'}`);

              } catch (error: any) {
                console.error("❌ Direct check failed:", error);
                alert(`Error: ${error.message}`);
              }
            }}
            className="px-4 py-2 rounded text-white bg-purple-600 hover:bg-purple-700"
          >
            🔍 Direct Check
          </button>

          <button
            onClick={async () => {
              console.log("🔍 Testing whitelist functionality");
              const testAddr = prompt('Enter address to test whitelist:');
              if (!testAddr || !ethers.isAddress(testAddr)) {
                alert('Invalid address');
                return;
              }

              try {
                if (!window.ethereum) {
                  alert('MetaMask not found');
                  return;
                }

                const provider = new ethers.BrowserProvider(window.ethereum);
                const signer = await provider.getSigner();

                // Get the correct ABI for this token type
                const tokenABI = await getTokenABI(provider);

                const tokenContract = new ethers.Contract(
                  tokenAddress,
                  tokenABI,
                  signer
                );

                // Test whitelist functions
                console.log("🔍 Testing whitelist functions...");

                // Check current status
                const isWhitelisted = await tokenContract.isWhitelisted(testAddr);
                const isKycApproved = await tokenContract.isKycApproved(testAddr);
                const isVerified = await tokenContract.isVerified(testAddr);

                console.log(`Current status for ${testAddr}:`);
                console.log(`  Whitelisted: ${isWhitelisted}`);
                console.log(`  KYC: ${isKycApproved}`);
                console.log(`  Verified: ${isVerified}`);

                if (!isWhitelisted) {
                  console.log("🔧 Adding to whitelist...");
                  const tx = await tokenContract.addToWhitelist(testAddr);
                  console.log("✅ Transaction submitted:", tx.hash);
                  await tx.wait();
                  console.log("✅ Transaction confirmed");

                  const newStatus = await tokenContract.isWhitelisted(testAddr);
                  console.log("✅ New whitelist status:", newStatus);
                  alert(`Successfully whitelisted ${testAddr}`);
                } else {
                  alert(`${testAddr} is already whitelisted`);
                }

              } catch (error: any) {
                console.error("❌ Whitelist test failed:", error);
                alert(`Whitelist test failed: ${error.message}`);
              }
            }}
            className="px-4 py-2 rounded text-white bg-orange-600 hover:bg-orange-700"
          >
            🧪 Test Whitelist
          </button>

          <button
            onClick={handlePauseToken}
            disabled={!walletConnected}
            className={`px-4 py-2 rounded text-white ${
              isPaused
                ? 'bg-green-600 hover:bg-green-700'
                : 'bg-red-600 hover:bg-red-700'
            } ${!walletConnected ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            {isPaused ? 'Unpause Token' : 'Pause Token'}
          </button>

          <button
            onClick={handleMintTokens}
            disabled={!walletConnected || mintingStatus === 'pending'}
            className={`bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded flex items-center ${
              !walletConnected || mintingStatus === 'pending' ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            {mintingStatus === 'pending' ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Minting...
              </>
            ) : 'Mint Tokens'}
          </button>

          <button
            onClick={handleWhitelistAddress}
            disabled={!walletConnected || whitelistStatus === 'pending' || !whitelistAddress || whitelistAddress === ethers.ZeroAddress}
            className={`bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded flex items-center ${
              !walletConnected || whitelistStatus === 'pending' || !whitelistAddress || whitelistAddress === ethers.ZeroAddress ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            {whitelistStatus === 'pending' ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Whitelisting...
              </>
            ) : 'Whitelist Address'}
          </button>

          <button
            onClick={handleBatchWhitelist}
            disabled={!walletConnected || batchWhitelistStatus === 'pending' || !whitelistAddress || whitelistAddress === ethers.ZeroAddress}
            className={`bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded flex items-center ${
              !walletConnected || batchWhitelistStatus === 'pending' || !whitelistAddress || whitelistAddress === ethers.ZeroAddress ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            {batchWhitelistStatus === 'pending' ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Batch Whitelisting...
              </>
            ) : 'Batch Whitelist'}
          </button>

          <a
            href={tokenDetails.whitelistAddress && tokenDetails.whitelistAddress !== ethers.ZeroAddress ?
              getBlockExplorerUrl(tokenDetails.network, tokenDetails.whitelistAddress) : '#'}
            target="_blank"
            rel="noopener noreferrer"
            className={`bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded inline-flex items-center ${
              !tokenDetails.whitelistAddress || tokenDetails.whitelistAddress === ethers.ZeroAddress ?
                'opacity-50 cursor-not-allowed' : ''
            }`}
            onClick={(e) => {
              if (!tokenDetails.whitelistAddress || tokenDetails.whitelistAddress === ethers.ZeroAddress) {
                e.preventDefault();
                alert('No whitelist address available');
              }
            }}
          >
            View Whitelist
          </a>

          <Link
            href={`/transfer-controls?token=${tokenAddress}`}
            className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded inline-flex items-center"
          >
            🔒 Transfer Controls
          </Link>

          {hasAdminRole && (
            <button
              onClick={handleUpgradeContract}
              disabled={!walletConnected || upgradeStatus === 'pending' || !hasAdminRole}
              className={`bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded flex items-center ${
                !walletConnected || upgradeStatus === 'pending' || !hasAdminRole ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              {upgradeStatus === 'pending' ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Upgrading...
                </>
              ) : 'Upgrade Contract'}
            </button>
          )}
        </div>

        {mintTxHash && mintingStatus !== 'idle' && (
          <div className={`mt-4 p-3 rounded ${
            mintingStatus === 'pending' ? 'bg-yellow-50' :
            mintingStatus === 'success' ? 'bg-green-50' :
            mintingStatus === 'error' ? 'bg-red-50' : ''
          }`}>
            <p className="text-sm">
              {mintingStatus === 'pending' && 'Transaction in progress...'}
              {mintingStatus === 'success' && 'Transaction completed successfully!'}
              {mintingStatus === 'error' && 'Transaction failed. See console for details.'}
            </p>
            <a
              href={getTransactionExplorerUrl(tokenDetails.network, mintTxHash)}
              target="_blank"
              rel="noopener noreferrer"
              className="text-xs text-blue-600 hover:text-blue-800"
            >
              View transaction on explorer
            </a>
          </div>
        )}

        {whitelistTxHash && whitelistStatus !== 'idle' && (
          <div className={`mt-4 p-3 rounded ${
            whitelistStatus === 'pending' ? 'bg-yellow-50' :
            whitelistStatus === 'success' ? 'bg-green-50' :
            whitelistStatus === 'error' ? 'bg-red-50' : ''
          }`}>
            <p className="text-sm">
              {whitelistStatus === 'pending' && 'Whitelist transaction in progress...'}
              {whitelistStatus === 'success' && 'Address successfully whitelisted!'}
              {whitelistStatus === 'error' && 'Whitelist transaction failed. See console for details.'}
            </p>
            <a
              href={getTransactionExplorerUrl(tokenDetails.network, whitelistTxHash)}
              target="_blank"
              rel="noopener noreferrer"
              className="text-xs text-blue-600 hover:text-blue-800"
            >
              View transaction on explorer
            </a>
          </div>
        )}

        {batchWhitelistTxHash && batchWhitelistStatus !== 'idle' && (
          <div className={`mt-4 p-3 rounded ${
            batchWhitelistStatus === 'pending' ? 'bg-yellow-50' :
            batchWhitelistStatus === 'success' ? 'bg-green-50' :
            batchWhitelistStatus === 'error' ? 'bg-red-50' : ''
          }`}>
            <p className="text-sm">
              {batchWhitelistStatus === 'pending' && 'Batch whitelist transaction in progress...'}
              {batchWhitelistStatus === 'success' && 'Multiple addresses successfully whitelisted!'}
              {batchWhitelistStatus === 'error' && 'Batch whitelist transaction failed. See console for details.'}
            </p>
            <a
              href={getTransactionExplorerUrl(tokenDetails.network, batchWhitelistTxHash)}
              target="_blank"
              rel="noopener noreferrer"
              className="text-xs text-blue-600 hover:text-blue-800"
            >
              View transaction on explorer
            </a>
          </div>
        )}

        {upgradeStatus === 'error' && (
          <div className="mt-4 p-3 rounded bg-yellow-50">
            <p className="text-sm font-medium">Contract Upgrade Required</p>
            <p className="text-sm mt-1">
              The whitelist functions need to be added to the token contract. Please follow these steps:
            </p>
            <pre className="mt-2 bg-gray-800 text-gray-200 p-3 rounded text-xs overflow-x-auto">
              # Run in your terminal
              export CONTRACT_ADDRESS={tokenAddress}
              export CONTRACT_TYPE=token
              npx hardhat run scripts/04-upgrade-contracts.js --network &lt;your-network&gt;
            </pre>
            <p className="text-sm mt-2">After running this script, refresh the page and try again.</p>
          </div>
        )}
      </div>

      <div className="bg-gray-100 p-4 rounded-lg">
        <h2 className="text-lg font-medium mb-2">Token Transfer Activity</h2>
        <p className="text-gray-600">
          To view token transfer activity, visit the token address on the blockchain explorer.
        </p>
        <a
          href={getBlockExplorerUrl(tokenDetails.network, tokenAddress)}
          target="_blank"
          rel="noopener noreferrer"
          className="mt-2 inline-block text-blue-600 hover:text-blue-800"
        >
          View on {tokenDetails.network === 'amoy' ? 'OKLink Explorer' :
                  tokenDetails.network === 'polygon' ? 'PolygonScan' :
                  'Blockchain Explorer'}
        </a>
      </div>

      <div className="mt-8 bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-xl font-semibold mb-4">Whitelist Management</h2>

        {tokenDetails?.hasKYC ? (
          <div>
            <div className="mb-4 border-b">
              <ul className="flex flex-wrap -mb-px text-sm font-medium text-center" role="tablist">
                <li className="mr-2" role="presentation">
                  <button
                    className={`inline-block p-4 border-b-2 rounded-t-lg ${whitelistManagementTab === 'token' ? 'border-blue-600 text-blue-600' : 'border-transparent hover:text-gray-600 hover:border-gray-300'}`}
                    onClick={() => setWhitelistManagementTab('token')}
                  >
                    Token Whitelist
                  </button>
                </li>
                <li className="mr-2" role="presentation">
                  <button
                    className={`inline-block p-4 border-b-2 rounded-t-lg ${whitelistManagementTab === 'direct' ? 'border-blue-600 text-blue-600' : 'border-transparent hover:text-gray-600 hover:border-gray-300'}`}
                    onClick={() => setWhitelistManagementTab('direct')}
                  >
                    Direct Whitelist
                  </button>
                </li>
                <li className="mr-2" role="presentation">
                  <button
                    className={`inline-block p-4 border-b-2 rounded-t-lg ${whitelistManagementTab === 'list' ? 'border-blue-600 text-blue-600' : 'border-transparent hover:text-gray-600 hover:border-gray-300'}`}
                    onClick={() => setWhitelistManagementTab('list')}
                  >
                    View Whitelist
                  </button>
                </li>
                <li className="mr-2" role="presentation">
                  <button
                    className={`inline-block p-4 border-b-2 rounded-t-lg ${whitelistManagementTab === 'kyc' ? 'border-blue-600 text-blue-600' : 'border-transparent hover:text-gray-600 hover:border-gray-300'}`}
                    onClick={() => setWhitelistManagementTab('kyc')}
                  >
                    KYC Management
                  </button>
                </li>
                <li className="mr-2" role="presentation">
                  <button
                    className={`inline-block p-4 border-b-2 rounded-t-lg ${whitelistManagementTab === 'clients' ? 'border-blue-600 text-blue-600' : 'border-transparent hover:text-gray-600 hover:border-gray-300'}`}
                    onClick={() => setWhitelistManagementTab('clients')}
                  >
                    Client Management
                  </button>
                </li>
                <li className="mr-2" role="presentation">
                  <button
                    className={`inline-block p-4 border-b-2 rounded-t-lg ${whitelistManagementTab === 'orders' ? 'border-blue-600 text-blue-600' : 'border-transparent hover:text-gray-600 hover:border-gray-300'}`}
                    onClick={() => setWhitelistManagementTab('orders')}
                  >
                    Orders
                  </button>
                </li>
                <li className="mr-2" role="presentation">
                  <button
                    className={`inline-block p-4 border-b-2 rounded-t-lg ${whitelistManagementTab === 'claims' ? 'border-blue-600 text-blue-600' : 'border-transparent hover:text-gray-600 hover:border-gray-300'}`}
                    onClick={() => setWhitelistManagementTab('claims')}
                  >
                    Blockchain Claims
                  </button>
                </li>
              </ul>
            </div>

            {/* Add KYC Management Tab */}
            {whitelistManagementTab === 'kyc' && (
              <div className="mt-4">
                <h3 className="text-xl font-semibold mb-4">KYC Management</h3>

                <div className="mb-8 bg-gray-50 p-4 rounded-lg">
                  <h4 className="text-lg font-medium mb-2">Check KYC Status</h4>
                  <div className="mb-4 flex items-end space-x-2">
                    <div className="flex-grow">
                      <label htmlFor="check-kyc-address" className="block text-sm font-medium text-gray-700 mb-1">
                        Address to Check
                      </label>
                      <input
                        type="text"
                        id="check-kyc-address"
                        placeholder="0x..."
                        value={addressStatus?.address || ''}
                        onChange={(e) => setAddressStatus({...addressStatus || {}, address: e.target.value})}
                        className="w-full p-2 border border-gray-300 rounded-md"
                      />
                    </div>
                    <button
                      onClick={handleCheckKycStatus}
                      disabled={!addressStatus?.address || checkingKycStatus}
                      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400"
                    >
                      {checkingKycStatus ? 'Checking...' : 'Check Status'}
                    </button>
                  </div>

                  {addressKycStatus && addressKycStatus.address && (
                    <div className="p-3 border rounded-md bg-white">
                      <p>
                        <span className="font-medium">Address:</span> {addressKycStatus.address}
                      </p>
                      <p>
                        <span className="font-medium">KYC Status:</span>{' '}
                        {addressKycStatus.isKycApproved !== undefined ? (
                          <span className={addressKycStatus.isKycApproved ? 'text-green-600' : 'text-red-600'}>
                            {addressKycStatus.isKycApproved ? 'Approved' : 'Not Approved'}
                          </span>
                        ) : (
                          'Unknown'
                        )}
                      </p>

                      <div className="mt-3 flex space-x-2">
                        {addressKycStatus.isKycApproved === false && (
                          <button
                            onClick={() => handleApproveKyc(addressKycStatus.address)}
                            disabled={kycStatus === 'pending'}
                            className="px-3 py-1 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 disabled:bg-gray-400"
                          >
                            {kycStatus === 'pending' ? 'Approving...' : 'Approve KYC'}
                          </button>
                        )}

                        {addressKycStatus.isKycApproved === true && (
                          <button
                            onClick={() => handleRevokeKyc(addressKycStatus.address)}
                            disabled={kycStatus === 'pending'}
                            className="px-3 py-1 bg-red-600 text-white text-sm rounded-md hover:bg-red-700 disabled:bg-gray-400"
                          >
                            {kycStatus === 'pending' ? 'Revoking...' : 'Revoke KYC'}
                          </button>
                        )}
                      </div>

                      {kycStatus === 'success' && kycTxHash && (
                        <p className="mt-2 text-sm text-green-600">
                          Transaction successful!{' '}
                          <a
                            href={getTransactionExplorerUrl(network, kycTxHash)}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="underline"
                          >
                            View on explorer
                          </a>
                        </p>
                      )}

                      {kycStatus === 'error' && (
                        <p className="mt-2 text-sm text-red-600">
                          Error processing KYC transaction. Check console for details.
                        </p>
                      )}
                    </div>
                  )}
                </div>

                <div className="mb-4 bg-gray-50 p-4 rounded-lg">
                  <h4 className="text-lg font-medium mb-2">Batch KYC Management</h4>
                  <div className="mb-4">
                    <label htmlFor="batch-kyc-addresses" className="block text-sm font-medium text-gray-700 mb-1">
                      Addresses (one per line)
                    </label>
                    <textarea
                      id="batch-kyc-addresses"
                      rows={5}
                      placeholder="0x...\n0x...\n0x..."
                      className="w-full p-2 border border-gray-300 rounded-md"
                    ></textarea>
                  </div>

                  <div className="flex space-x-2">
                    <button
                      onClick={handleBatchApproveKyc}
                      disabled={batchKycStatus === 'pending'}
                      className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-400"
                    >
                      {batchKycStatus === 'pending' ? 'Processing...' : 'Batch Approve KYC'}
                    </button>

                    <button
                      onClick={handleBatchRevokeKyc}
                      disabled={batchKycStatus === 'pending'}
                      className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:bg-gray-400"
                    >
                      {batchKycStatus === 'pending' ? 'Processing...' : 'Batch Revoke KYC'}
                    </button>
                  </div>

                  {batchKycStatus === 'success' && batchKycTxHash && (
                    <p className="mt-2 text-sm text-green-600">
                      Batch KYC transaction successful!{' '}
                      <a
                        href={getTransactionExplorerUrl(network, batchKycTxHash)}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="underline"
                      >
                        View on explorer
                      </a>
                    </p>
                  )}

                  {batchKycStatus === 'error' && (
                    <p className="mt-2 text-sm text-red-600">
                      Error processing batch KYC transaction. Check console for details.
                    </p>
                  )}
                </div>
              </div>
            )}

            {/* Existing whitelist management tabs */}
            {whitelistManagementTab === 'token' ? (
              // Token-based whitelist management (existing functionality)
              <div>
                <p className="mb-4">Whitelist addresses through the token contract.</p>
                <div className="flex space-x-2">
                  <button
                    className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
                    onClick={handleWhitelistAddress}
                    disabled={!walletConnected || !agentRole || whitelistStatus === 'pending'}
                  >
                    {whitelistStatus === 'pending' ? 'Whitelisting...' : 'Whitelist Address'}
                  </button>

                  <button
                    className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
                    onClick={handleBatchWhitelist}
                    disabled={!walletConnected || !agentRole || batchWhitelistStatus === 'pending'}
                  >
                    {batchWhitelistStatus === 'pending' ? 'Processing...' : 'Batch Whitelist'}
                  </button>
                </div>

                {whitelistStatus === 'success' && whitelistTxHash && (
                  <div className="mt-4 p-2 bg-green-100 border border-green-300 rounded">
                    <p>Address whitelisted successfully!</p>
                    <p>
                      <a
                        href={getTransactionExplorerUrl(tokenDetails?.network, whitelistTxHash)}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-500 hover:underline"
                      >
                        View transaction →
                      </a>
                    </p>
                  </div>
                )}

                {batchWhitelistStatus === 'success' && batchWhitelistTxHash && (
                  <div className="mt-4 p-2 bg-green-100 border border-green-300 rounded">
                    <p>Addresses whitelisted in batch successfully!</p>
                    <p>
                      <a
                        href={getTransactionExplorerUrl(tokenDetails?.network, batchWhitelistTxHash)}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-500 hover:underline"
                      >
                        View transaction →
                      </a>
                    </p>
                  </div>
                )}
              </div>
            ) : whitelistManagementTab === 'direct' ? (
              // Direct whitelist management (existing functionality)
              <div>
                <p className="mb-4">Directly manage whitelist through the whitelist contract.</p>
                <div className="flex space-x-2">
                  <button
                    className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
                    onClick={handleDirectWhitelistAddress}
                    disabled={!walletConnected || directWhitelistStatus === 'pending'}
                  >
                    {directWhitelistStatus === 'pending' ? 'Whitelisting...' : 'Whitelist Address'}
                  </button>

                  <button
                    className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
                    onClick={handleDirectBatchWhitelist}
                    disabled={!walletConnected || directBatchWhitelistStatus === 'pending'}
                  >
                    {directBatchWhitelistStatus === 'pending' ? 'Processing...' : 'Batch Whitelist'}
                  </button>
                </div>

                {directWhitelistStatus === 'success' && directWhitelistTxHash && (
                  <div className="mt-4 p-2 bg-green-100 border border-green-300 rounded">
                    <p>Address whitelisted successfully!</p>
                    <p>
                      <a
                        href={getTransactionExplorerUrl(tokenDetails?.network, directWhitelistTxHash)}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-500 hover:underline"
                      >
                        View transaction →
                      </a>
                    </p>
                  </div>
                )}
              </div>
            ) : whitelistManagementTab === 'clients' ? (
              // Client Management tab
              <div>
                <ClientManagement tokenAddress={tokenAddress} />
              </div>
            ) : whitelistManagementTab === 'orders' ? (
              // Orders Management tab
              <div>
                <OrdersManagement tokenAddress={tokenAddress} />
              </div>
            ) : whitelistManagementTab === 'claims' ? (
              // Blockchain Claims Management tab
              <div>
                <ClaimsManagement tokenAddress={tokenAddress} />
              </div>
            ) : (
              // New tab for listing whitelisted addresses
              <div>
                <p className="mb-4">View and manage all whitelisted addresses.</p>

                <div className="flex justify-between mb-4">
                  <button
                    className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
                    onClick={fetchWhitelistedAddresses}
                    disabled={isLoadingWhitelistedAddresses}
                  >
                    {isLoadingWhitelistedAddresses ? 'Loading...' : 'Refresh List'}
                  </button>
                </div>

                {/* Add summary statistics section */}
                {whitelistedAddresses.length > 0 && (
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                    <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
                      <p className="text-sm text-gray-500 mb-1">Total Whitelisted Addresses</p>
                      <p className="text-xl font-semibold">{whitelistedAddresses.length}</p>
                    </div>
                    <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
                      <p className="text-sm text-gray-500 mb-1">Active Balances</p>
                      <p className="text-xl font-semibold text-green-600">
                        {whitelistedAddresses
                          .filter(a => !a.frozen)
                          .reduce((sum, a) => sum + parseFloat(a.balance), 0)
                          .toLocaleString(undefined, { minimumFractionDigits: 0, maximumFractionDigits: 4 })} {tokenDetails.symbol}
                      </p>
                    </div>
                    <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
                      <p className="text-sm text-gray-500 mb-1">Frozen Balances</p>
                      <p className="text-xl font-semibold text-red-600">
                        {whitelistedAddresses
                          .filter(a => a.frozen)
                          .reduce((sum, a) => sum + parseFloat(a.balance), 0)
                          .toLocaleString(undefined, { minimumFractionDigits: 0, maximumFractionDigits: 4 })} {tokenDetails.symbol}
                      </p>
                    </div>
                    <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
                      <p className="text-sm text-gray-500 mb-1">Partially Frozen Tokens</p>
                      <p className="text-xl font-semibold text-orange-600">
                        {whitelistedAddresses
                          .reduce((sum, a) => sum + parseFloat(a.frozenTokens), 0)
                          .toLocaleString(undefined, { minimumFractionDigits: 0, maximumFractionDigits: 4 })} {tokenDetails.symbol}
                      </p>
                    </div>
                  </div>
                )}

                {isLoadingWhitelistedAddresses ? (
                  <div className="text-center py-8">
                    <p className="text-lg">Loading whitelisted addresses...</p>
                  </div>
                ) : whitelistedAddresses.length > 0 ? (
                  <div className="overflow-x-auto">
                    <table className="min-w-full bg-white border border-gray-200">
                      <thead className="bg-gray-100">
                        <tr>
                          <th className="py-2 px-4 border-b text-left">Address</th>
                          <th className="py-2 px-4 border-b text-center">Status</th>
                          <th className="py-2 px-4 border-b text-center">Active Balance</th>
                          <th className="py-2 px-4 border-b text-center">Frozen Tokens</th>
                          <th className="py-2 px-4 border-b text-center">Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {whitelistedAddresses.map((item) => (
                          <tr key={item.address} className="hover:bg-gray-50">
                            <td className="py-2 px-4 border-b">
                              <a
                                href={getBlockExplorerUrl(tokenDetails?.network, item.address)}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-500 hover:underline font-mono text-sm"
                              >
                                {item.address}
                              </a>
                            </td>
                            <td className="py-2 px-4 border-b text-center">
                              {item.frozen ? (
                                <span className="inline-flex items-center px-2 py-1 bg-red-100 text-red-800 rounded text-sm">
                                  Frozen
                                </span>
                              ) : (
                                <span className="inline-flex items-center px-2 py-1 bg-green-100 text-green-800 rounded text-sm">
                                  Active
                                </span>
                              )}
                            </td>
                            <td className="py-2 px-4 border-b text-right font-mono">
                              <span className={item.frozen ? "text-red-600" : "text-black"}>
                                {parseFloat(item.balance).toLocaleString(undefined, { minimumFractionDigits: 0, maximumFractionDigits: 4 })} {tokenDetails.symbol}
                              </span>
                            </td>
                            <td className="py-2 px-4 border-b text-right font-mono">
                              <span className="text-orange-600">
                                {parseFloat(item.frozenTokens).toLocaleString(undefined, { minimumFractionDigits: 0, maximumFractionDigits: 4 })} {tokenDetails.symbol}
                              </span>
                            </td>
                            <td className="py-2 px-4 border-b text-center">
                              <div className="flex justify-center space-x-2">
                                {!item.frozen ? (
                                  <>
                                    <button
                                      className="px-3 py-1 bg-red-500 text-white text-sm rounded hover:bg-red-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
                                      onClick={() => handleFreezeAddress(item.address)}
                                      disabled={!walletConnected || !agentRole}
                                      title="Block this address completely"
                                    >
                                      Block
                                    </button>
                                    <button
                                      className="px-3 py-1 bg-orange-500 text-white text-sm rounded hover:bg-orange-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
                                      onClick={() => handlePartialFreeze(item.address)}
                                      disabled={!walletConnected || (!hasAdminRole && !hasTransferManagerRole) || parseFloat(item.balance) === 0}
                                      title={parseFloat(item.balance) === 0 ? "No tokens to freeze" : "Freeze specific amount of tokens"}
                                    >
                                      Freeze
                                    </button>
                                  </>
                                ) : (
                                  <button
                                    className="px-3 py-1 bg-green-500 text-white text-sm rounded hover:bg-green-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
                                    onClick={() => handleUnfreezeAddress(item.address)}
                                    disabled={!walletConnected || !agentRole}
                                    title="Unblock this address"
                                  >
                                    Unblock
                                  </button>
                                )}
                                {parseFloat(item.frozenTokens) > 0 && (
                                  <button
                                    className="px-3 py-1 bg-green-500 text-white text-sm rounded hover:bg-green-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
                                    onClick={() => handleUnfreezeTokens(item.address)}
                                    disabled={!walletConnected || (!hasAdminRole && !hasTransferManagerRole) || unfreezeStatus === 'pending'}
                                    title="Unfreeze tokens for this address"
                                  >
                                    Unfreeze
                                  </button>
                                )}
                                <button
                                  className="px-3 py-1 bg-purple-500 text-white text-sm rounded hover:bg-purple-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
                                  onClick={() => handleForceTransfer(item.address)}
                                  disabled={!walletConnected || (!hasAdminRole && !hasTransferManagerRole) || forceTransferStatus === 'pending' || parseFloat(item.balance) === 0}
                                  title={parseFloat(item.balance) === 0 ? "No tokens to transfer" : "Force transfer tokens from this address"}
                                >
                                  Transfer
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <div className="text-center py-8 bg-gray-50 rounded">
                    <p className="text-lg text-gray-600">No whitelisted addresses found.</p>
                  </div>
                )}

                {forceTransferStatus === 'success' && forceTransferTxHash && (
                  <div className="mt-4 p-2 bg-green-100 border border-green-300 rounded">
                    <p>Force transfer completed successfully!</p>
                    <p>
                      <a
                        href={getTransactionExplorerUrl(tokenDetails?.network, forceTransferTxHash)}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-500 hover:underline"
                      >
                        View transaction →
                      </a>
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-600">No whitelist contract found for this token.</p>
          </div>
        )}
      </div>

      {/* Add Agent Management Section */}
      <div className="bg-white shadow-md rounded-lg p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Agent Management</h2>
        <p className="text-gray-600 mb-4">
          Agents are authorized addresses that can mint tokens and manage the whitelist.
        </p>

        {!walletConnected ? (
          <div className="bg-yellow-50 p-4 rounded mb-4">
            <p>Connect your wallet to manage agents</p>
            <button
              onClick={connectWallet}
              className="mt-2 bg-blue-600 hover:bg-blue-700 text-white py-1 px-3 rounded text-sm"
            >
              Connect Wallet
            </button>
          </div>
        ) : !hasAdminRole ? (
          <div className="bg-yellow-50 p-4 rounded mb-4">
            <p>You need admin role to manage agents</p>
          </div>
        ) : (
          <>
            {/* Form to add a new agent */}
            <div className="mb-6 bg-gray-50 p-4 rounded-lg">
              <h3 className="text-lg font-medium mb-2">Add New Agent</h3>
              <div className="flex items-center">
                <input
                  type="text"
                  value={newAgentAddress}
                  onChange={(e) => setNewAgentAddress(e.target.value)}
                  placeholder="Enter Ethereum address (0x...)"
                  className="flex-1 p-2 border border-gray-300 rounded-l focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <button
                  onClick={handleAddAgent}
                  disabled={addAgentStatus === 'pending' || !ethers.isAddress(newAgentAddress)}
                  className={`bg-green-600 hover:bg-green-700 text-white p-2 rounded-r ${
                    addAgentStatus === 'pending' || !ethers.isAddress(newAgentAddress) ? 'opacity-50 cursor-not-allowed' : ''
                  }`}
                >
                  {addAgentStatus === 'pending' ? 'Adding...' : 'Add Agent'}
                </button>
              </div>

              {addAgentStatus === 'success' && addAgentTxHash && (
                <div className="mt-2 p-2 bg-green-100 text-green-800 rounded text-sm">
                  Agent added successfully!
                  <a
                    href={getTransactionExplorerUrl(tokenDetails?.network, addAgentTxHash)}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="ml-2 text-blue-600 hover:underline"
                  >
                    View transaction
                  </a>
                </div>
              )}

              {addAgentStatus === 'error' && (
                <div className="mt-2 p-2 bg-red-100 text-red-800 rounded text-sm">
                  Error adding agent. Please check console for details.
                </div>
              )}
            </div>

            {/* Agent List */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-lg font-medium mb-2">Current Agents</h3>

              {isLoadingAgents ? (
                <div className="text-center py-4">
                  <p>Loading agents...</p>
                </div>
              ) : agents.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full bg-white border border-gray-200">
                    <thead className="bg-gray-100">
                      <tr>
                        <th className="py-2 px-4 border-b text-left">Agent Address</th>
                        <th className="py-2 px-4 border-b text-right">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {agents.map((agent) => (
                        <tr key={agent} className="hover:bg-gray-50">
                          <td className="py-2 px-4 border-b">
                            <a
                              href={getBlockExplorerUrl(tokenDetails?.network, agent)}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-blue-500 hover:underline font-mono text-sm"
                            >
                              {agent}
                              {agent.toLowerCase() === (window.ethereum && window.ethereum.selectedAddress ? window.ethereum.selectedAddress.toLowerCase() : '') && (
                                <span className="ml-2 text-xs text-gray-500">(You)</span>
                              )}
                            </a>
                          </td>
                          <td className="py-2 px-4 border-b text-right">
                            {agent.toLowerCase() !== (window.ethereum && window.ethereum.selectedAddress ? window.ethereum.selectedAddress.toLowerCase() : '') && (
                              <button
                                onClick={() => handleRemoveAgent(agent)}
                                disabled={removeAgentStatus === 'pending'}
                                className={`bg-red-600 hover:bg-red-700 text-white py-1 px-2 rounded text-sm ${
                                  removeAgentStatus === 'pending' ? 'opacity-50 cursor-not-allowed' : ''
                                }`}
                              >
                                {removeAgentStatus === 'pending' ? 'Removing...' : 'Remove'}
                              </button>
                            )}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-4 border rounded bg-white">
                  <p className="text-gray-500">No agents found</p>
                </div>
              )}

              <button
                onClick={fetchAgents}
                disabled={isLoadingAgents}
                className="mt-4 bg-blue-600 hover:bg-blue-700 text-white py-1 px-3 rounded text-sm"
              >
                {isLoadingAgents ? 'Refreshing...' : 'Refresh Agents'}
              </button>

              {removeAgentStatus === 'success' && removeAgentTxHash && (
                <div className="mt-4 p-2 bg-green-100 text-green-800 rounded text-sm">
                  Agent removed successfully!
                  <a
                    href={getTransactionExplorerUrl(tokenDetails?.network, removeAgentTxHash)}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="ml-2 text-blue-600 hover:underline"
                  >
                    View transaction
                  </a>
                </div>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
}