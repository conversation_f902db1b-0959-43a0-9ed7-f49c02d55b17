const { ethers } = require("hardhat");

async function main() {
  const tokenAddress = process.env.TOKEN_ADDRESS || "******************************************";
  const testAddress = "******************************************"; // Test address to whitelist
  
  console.log("🧪 Testing Admin Functions");
  console.log("=" .repeat(50));
  console.log("Token Address:", tokenAddress);
  console.log("Test Address:", testAddress);
  
  const [deployer] = await ethers.getSigners();
  console.log("Using account:", deployer.address);
  
  // Connect to the token
  const SecurityTokenMinimal = await ethers.getContractFactory("SecurityTokenMinimal");
  const token = SecurityTokenMinimal.attach(tokenAddress);
  
  try {
    console.log("\n🔍 Pre-test Status:");
    const isWhitelistedBefore = await token.isWhitelisted(testAddress);
    console.log(`${testAddress} whitelisted before:`, isWhitelistedBefore);
    
    console.log("\n🔧 Testing updateWhitelist function...");
    try {
      const tx = await token.updateWhitelist(testAddress, true, {
        gasLimit: 100000,
        gasPrice: ethers.parseUnits("50", "gwei")
      });
      console.log("Transaction hash:", tx.hash);
      await tx.wait();
      console.log("✅ updateWhitelist function works!");
      
      const isWhitelistedAfter = await token.isWhitelisted(testAddress);
      console.log(`${testAddress} whitelisted after:`, isWhitelistedAfter);
      
    } catch (error) {
      console.log("❌ updateWhitelist failed:", error.message);
      if (error.message.includes("Not agent")) {
        console.log("💡 This suggests a role permission issue");
      }
    }
    
    console.log("\n🔧 Testing addToWhitelist function (legacy)...");
    try {
      const tx2 = await token.addToWhitelist("******************************************", {
        gasLimit: 100000,
        gasPrice: ethers.parseUnits("50", "gwei")
      });
      console.log("Transaction hash:", tx2.hash);
      await tx2.wait();
      console.log("✅ addToWhitelist function works!");
      
    } catch (error) {
      console.log("❌ addToWhitelist failed:", error.message);
    }
    
    console.log("\n🔧 Testing addAgent function...");
    try {
      const tx3 = await token.addAgent("******************************************", {
        gasLimit: 150000,
        gasPrice: ethers.parseUnits("50", "gwei")
      });
      console.log("Transaction hash:", tx3.hash);
      await tx3.wait();
      console.log("✅ addAgent function works!");
      
      const agentCount = await token.getAgentCount();
      console.log("New agent count:", agentCount.toString());
      
    } catch (error) {
      console.log("❌ addAgent failed:", error.message);
    }
    
    console.log("\n✅ Admin function tests completed!");
    
  } catch (error) {
    console.error("❌ Error during testing:", error.message);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
