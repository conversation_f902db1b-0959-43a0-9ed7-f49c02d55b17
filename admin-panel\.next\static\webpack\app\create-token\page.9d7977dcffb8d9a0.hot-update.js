"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/create-token/page",{

/***/ "(app-pages-browser)/./src/app/create-token/hooks/useTokenDeployment.ts":
/*!**********************************************************!*\
  !*** ./src/app/create-token/hooks/useTokenDeployment.ts ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTokenDeployment: () => (/* binding */ useTokenDeployment)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/constants/addresses.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/address/checks.js\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../config */ \"(app-pages-browser)/./src/config.ts\");\n/* harmony import */ var _contracts_SecurityTokenFactory_json__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../contracts/SecurityTokenFactory.json */ \"(app-pages-browser)/./src/contracts/SecurityTokenFactory.json\");\n/* harmony import */ var _contracts_SecurityToken_json__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../contracts/SecurityToken.json */ \"(app-pages-browser)/./src/contracts/SecurityToken.json\");\n/* harmony import */ var _useERC3643Integration__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./useERC3643Integration */ \"(app-pages-browser)/./src/app/create-token/hooks/useERC3643Integration.ts\");\n\n\n\n\n\n\n/**\r\n * Custom hook for token deployment logic\r\n *\r\n * Encapsulates all the token deployment functionality including state management,\r\n * transaction handling, and error handling\r\n */ function useTokenDeployment(network, factoryAddress, hasDeployerRole, kycSupported) {\n    // State management\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [deployedToken, setDeployedToken] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [transactionHash, setTransactionHash] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [deploymentStep, setDeploymentStep] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('idle');\n    // ERC-3643 integration\n    const { setupERC3643Compliance, isERC3643Available } = (0,_useERC3643Integration__WEBPACK_IMPORTED_MODULE_4__.useERC3643Integration)();\n    /**\r\n   * Save token data to database\r\n   */ const saveTokenToDatabase = async (deployedToken, formData, transactionHash, blockNumber, network)=>{\n        // Fetch totalSupply from the blockchain\n        let totalSupply = '0';\n        try {\n            // Create a new provider instance for blockchain calls\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_5__.BrowserProvider(window.ethereum);\n            const token = new ethers__WEBPACK_IMPORTED_MODULE_6__.Contract(deployedToken.address, _contracts_SecurityToken_json__WEBPACK_IMPORTED_MODULE_3__.abi, provider);\n            const totalSupplyRaw = await token.totalSupply();\n            totalSupply = deployedToken.decimals === 0 ? totalSupplyRaw.toString() : ethers__WEBPACK_IMPORTED_MODULE_7__.formatUnits(totalSupplyRaw, deployedToken.decimals);\n        } catch (error) {\n            console.warn('Could not fetch totalSupply from blockchain, using default 0:', error);\n        }\n        const tokenData = {\n            address: deployedToken.address,\n            transactionHash: transactionHash,\n            blockNumber: blockNumber,\n            network: network,\n            name: deployedToken.name,\n            symbol: deployedToken.symbol,\n            decimals: deployedToken.decimals,\n            maxSupply: deployedToken.maxSupply,\n            totalSupply: totalSupply,\n            tokenType: formData.tokenType,\n            tokenPrice: deployedToken.tokenPrice,\n            currency: deployedToken.currency,\n            bonusTiers: deployedToken.bonusTiers,\n            tokenImageUrl: deployedToken.tokenImageUrl,\n            whitelistAddress: deployedToken.whitelistAddress,\n            adminAddress: deployedToken.admin,\n            hasKYC: deployedToken.hasKYC,\n            selectedClaims: formData.selectedClaims,\n            isActive: true,\n            deployedBy: deployedToken.admin,\n            deploymentNotes: \"\".concat(formData.tokenType, \" token deployed via admin panel\")\n        };\n        const response = await fetch('/api/tokens', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify(tokenData)\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(\"Database save failed: \".concat(errorData.error || 'Unknown error'));\n        }\n        return await response.json();\n    };\n    /**\r\n   * Deploy a new token with the provided form data\r\n   */ const deployToken = async (formData)=>{\n        setIsSubmitting(true);\n        setError(null);\n        setSuccess(null);\n        setDeployedToken(null);\n        setTransactionHash(null);\n        setDeploymentStep('preparing');\n        try {\n            // Validate form data\n            validateFormData(formData);\n            // Get network configuration\n            const networkConfig = (0,_config__WEBPACK_IMPORTED_MODULE_1__.getNetworkConfig)(network);\n            if (!factoryAddress) {\n                throw new Error(\"No factory address configured for network: \".concat(network));\n            }\n            if (!window.ethereum) {\n                throw new Error('Please install MetaMask to use this feature!');\n            }\n            setDeploymentStep('connecting');\n            // Get provider and signer\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_5__.BrowserProvider(window.ethereum);\n            const signer = await provider.getSigner();\n            // Verify network connection\n            await verifyNetworkConnection(provider, network);\n            // Connect to the factory contract\n            const factory = new ethers__WEBPACK_IMPORTED_MODULE_6__.Contract(factoryAddress, _contracts_SecurityTokenFactory_json__WEBPACK_IMPORTED_MODULE_2__.abi, signer);\n            console.log(\"Connected to factory at:\", factoryAddress);\n            // Verify deployer role\n            await verifyDeployerRole(factory, signer, hasDeployerRole);\n            // Verify KYC support\n            await verifyKYCSupport(factory, formData.enableKYC);\n            // Check if token symbol already exists\n            await checkTokenSymbolAvailability(factory, formData.symbol);\n            // Convert maxSupply to the appropriate unit based on decimals\n            const maxSupplyWei = formData.decimals === 0 ? BigInt(formData.maxSupply) : ethers__WEBPACK_IMPORTED_MODULE_7__.parseUnits(formData.maxSupply, formData.decimals);\n            setDeploymentStep('deploying');\n            console.log(\"Deploying token with params:\", {\n                name: formData.name,\n                symbol: formData.symbol,\n                decimals: formData.decimals,\n                maxSupply: formData.maxSupply,\n                admin: formData.ownerAddress,\n                tokenPrice: formData.tokenPrice,\n                bonusTiers: formData.bonusTiers,\n                enableKYC: formData.enableKYC\n            });\n            // Create the transaction\n            const tx = await createDeployTransaction(factory, formData, maxSupplyWei, network, kycSupported);\n            setTransactionHash(tx.hash);\n            console.log(\"Transaction hash:\", tx.hash);\n            setDeploymentStep('confirming');\n            // Wait for the transaction to be mined\n            const receipt = await tx.wait();\n            console.log(\"Transaction mined in block:\", receipt.blockNumber);\n            setDeploymentStep('fetching');\n            // Get the token address\n            const tokenAddress = await factory.getTokenAddressBySymbol(formData.symbol);\n            if (tokenAddress && tokenAddress !== ethers__WEBPACK_IMPORTED_MODULE_8__.ZeroAddress) {\n                // Create token deployment result\n                const deploymentResult = await getDeploymentResult(tokenAddress, provider, formData);\n                setDeployedToken(deploymentResult);\n                // Save token to database\n                try {\n                    await saveTokenToDatabase(deploymentResult, formData, tx.hash, receipt.blockNumber.toString(), network);\n                    console.log(\"Token successfully saved to database\");\n                } catch (dbError) {\n                    console.warn(\"Failed to save token to database:\", dbError);\n                // Don't fail the deployment if database save fails\n                }\n                // Setup ERC-3643 compliance if available\n                if (isERC3643Available()) {\n                    setDeploymentStep('setting_up_compliance');\n                    console.log(\"🏛️ Setting up ERC-3643 compliance...\");\n                    try {\n                        const complianceResult = await setupERC3643Compliance(tokenAddress, formData.ownerAddress, signer, {\n                            name: formData.name,\n                            symbol: formData.symbol,\n                            tokenType: formData.tokenType,\n                            country: formData.issuerCountry || 'US',\n                            selectedClaims: formData.selectedClaims\n                        });\n                        if (complianceResult.errors.length > 0) {\n                            console.warn(\"⚠️ Some ERC-3643 setup steps failed:\", complianceResult.errors);\n                        // Don't fail deployment, just warn\n                        } else {\n                            console.log(\"✅ ERC-3643 compliance setup completed successfully\");\n                        }\n                    } catch (complianceError) {\n                        console.warn(\"⚠️ ERC-3643 compliance setup failed:\", complianceError);\n                    // Don't fail deployment, just warn\n                    }\n                } else {\n                    console.log(\"ℹ️ ERC-3643 contracts not available, skipping compliance setup\");\n                }\n                setDeploymentStep('completed');\n                setSuccess('Token \"'.concat(formData.name, '\" (').concat(formData.symbol, \") successfully deployed!\"));\n            } else {\n                throw new Error(\"Token deployment failed: Could not retrieve token address\");\n            }\n        } catch (err) {\n            handleDeploymentError(err, network);\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    /**\r\n   * Validate form data before deployment\r\n   */ const validateFormData = (formData)=>{\n        if (!formData.name || !formData.symbol || !formData.maxSupply || !formData.ownerAddress) {\n            throw new Error('Please fill in all required fields');\n        }\n        if (!ethers__WEBPACK_IMPORTED_MODULE_9__.isAddress(formData.ownerAddress)) {\n            throw new Error('Invalid owner address');\n        }\n        if (formData.decimals < 0 || formData.decimals > 18) {\n            throw new Error('Decimals must be between 0 and 18');\n        }\n    };\n    /**\r\n   * Verify that the wallet is connected to the correct network\r\n   */ const verifyNetworkConnection = async (provider, network)=>{\n        const chainId = (await provider.getNetwork()).chainId;\n        if (network === 'amoy' && chainId.toString() !== '80002') {\n            throw new Error('Please connect your wallet to the Amoy network (Chain ID: 80002)');\n        } else if (network === 'polygon' && chainId.toString() !== '137') {\n            throw new Error('Please connect your wallet to the Polygon network (Chain ID: 137)');\n        }\n    };\n    /**\r\n   * Verify that the connected wallet has DEPLOYER_ROLE\r\n   */ const verifyDeployerRole = async (factory, signer, hasRole)=>{\n        if (!hasRole) {\n            const DEPLOYER_ROLE = await factory.DEPLOYER_ROLE();\n            const hasDeployerRole = await factory.hasRole(DEPLOYER_ROLE, await signer.getAddress());\n            if (!hasDeployerRole) {\n                throw new Error(\"Your wallet does not have the DEPLOYER_ROLE required to create tokens\");\n            }\n        }\n    };\n    /**\r\n   * Check if token symbol is available\r\n   */ const checkTokenSymbolAvailability = async (factory, symbol)=>{\n        try {\n            const existingTokenAddress = await factory.getTokenAddressBySymbol(symbol);\n            if (existingTokenAddress !== ethers__WEBPACK_IMPORTED_MODULE_8__.ZeroAddress) {\n                throw new Error('Token with symbol \"'.concat(symbol, '\" already exists at address ').concat(existingTokenAddress, \". Please choose a different symbol.\"));\n            }\n        } catch (err) {\n            if (err.message.includes(\"already exists\")) {\n                throw err; // Re-throw our custom error\n            }\n            // If it's a different error, log it but don't fail the deployment\n            console.warn(\"Could not check token symbol availability:\", err.message);\n        }\n    };\n    /**\r\n   * Verify KYC support in the factory contract\r\n   */ const verifyKYCSupport = async (factory, enableKYC)=>{\n        try {\n            // Check if whitelistWithKYCImplementation exists\n            const kycImplementation = await factory.whitelistWithKYCImplementation();\n            // Check function exists by examining the ABI\n            const hasKYCFunction = factory.interface.fragments.some((fragment)=>fragment.type === \"function\" && 'name' in fragment && fragment.name === \"deploySecurityTokenWithOptions\");\n            if (!hasKYCFunction) {\n                throw new Error(\"The deployed factory contract doesn't support the KYC functionality. Please deploy an updated factory contract.\");\n            }\n            if (kycImplementation === ethers__WEBPACK_IMPORTED_MODULE_8__.ZeroAddress) {\n                throw new Error(\"KYC implementation address is not set in the factory contract.\");\n            }\n        } catch (err) {\n            if (err.message.includes(\"whitelistWithKYCImplementation\")) {\n                throw new Error(\"The deployed factory contract doesn't support the KYC functionality. Please deploy an updated factory contract.\");\n            }\n            throw err;\n        }\n    };\n    /**\r\n   * Create the deployment transaction with the appropriate gas settings\r\n   */ const createDeployTransaction = async (factory, formData, maxSupplyWei, network, supportsKYC)=>{\n        // Determine if KYC is supported\n        let canUseKYC = supportsKYC;\n        try {\n            await factory.whitelistWithKYCImplementation();\n            const hasKYCFunction = factory.interface.fragments.some((fragment)=>fragment.type === \"function\" && 'name' in fragment && fragment.name === \"deploySecurityTokenWithOptions\");\n            canUseKYC = hasKYCFunction;\n        } catch (err) {\n            console.log(\"KYC functionality not supported in this factory contract, using standard deployment\");\n            canUseKYC = false;\n            // If KYC was enabled but not supported, warn the user\n            if (formData.enableKYC) {\n                console.warn(\"KYC requested but not supported by the contract. Proceeding with standard token deployment.\");\n            }\n        }\n        // Optimized gas settings for Amoy testnet\n        if (network === 'amoy') {\n            // Optimized gas settings for Amoy testnet - 5M gas limit, 50 gwei gas price\n            const gasLimit = BigInt(5000000);\n            const gasPrice = ethers__WEBPACK_IMPORTED_MODULE_7__.parseUnits(\"50\", \"gwei\");\n            console.log(\"Using optimized gas settings for Amoy testnet:\");\n            console.log(\"Gas limit:\", gasLimit.toString());\n            console.log(\"Gas price:\", ethers__WEBPACK_IMPORTED_MODULE_7__.formatUnits(gasPrice, \"gwei\"), \"gwei\");\n            // Call the appropriate function based on KYC support\n            if (canUseKYC) {\n                console.log(\"Calling deploySecurityTokenWithOptions with KYC:\", formData.enableKYC);\n                return await factory.deploySecurityTokenWithOptions(formData.name, formData.symbol, formData.decimals, maxSupplyWei, formData.ownerAddress, \"\".concat(formData.tokenPrice, \" \").concat(formData.currency), formData.bonusTiers, \"\".concat(formData.tokenType, \" token deployed via admin panel\"), formData.tokenImageUrl || \"\", formData.enableKYC, {\n                    gasLimit,\n                    gasPrice\n                });\n            } else {\n                console.log(\"Calling deploySecurityToken (no KYC support)\");\n                return await factory.deploySecurityToken(formData.name, formData.symbol, formData.decimals, maxSupplyWei, formData.ownerAddress, \"\".concat(formData.tokenPrice, \" \").concat(formData.currency), formData.bonusTiers, \"\".concat(formData.tokenType, \" token deployed via admin panel\"), formData.tokenImageUrl || \"\", {\n                    gasLimit,\n                    gasPrice\n                });\n            }\n        } else {\n            // For other networks, try to estimate gas\n            let gasLimit;\n            try {\n                // Estimate gas based on which function we can call\n                let gasEstimate;\n                if (canUseKYC) {\n                    gasEstimate = await factory.deploySecurityTokenWithOptions.estimateGas(formData.name, formData.symbol, formData.decimals, maxSupplyWei, formData.ownerAddress, \"\".concat(formData.tokenPrice, \" \").concat(formData.currency), formData.bonusTiers, \"\".concat(formData.tokenType, \" token deployed via admin panel\"), formData.tokenImageUrl || \"\", formData.enableKYC);\n                } else {\n                    gasEstimate = await factory.deploySecurityToken.estimateGas(formData.name, formData.symbol, formData.decimals, maxSupplyWei, formData.ownerAddress, \"\".concat(formData.tokenPrice, \" \").concat(formData.currency), formData.bonusTiers, \"\".concat(formData.tokenType, \" token deployed via admin panel\"), formData.tokenImageUrl || \"\");\n                }\n                console.log(\"Gas estimate:\", gasEstimate.toString());\n                // Add 50% to the gas estimate to be safer\n                gasLimit = gasEstimate * BigInt(150) / BigInt(100);\n                console.log(\"Using calculated gas limit:\", gasLimit.toString());\n            } catch (estimateErr) {\n                console.error(\"Gas estimation failed, using fixed limit:\", estimateErr);\n                // Fallback to fixed gas limit if estimation fails\n                gasLimit = BigInt(2000000); // Use 2M for non-Amoy networks\n                console.log(\"Using fallback gas limit:\", gasLimit.toString());\n            }\n            // Call without specific gas price for networks that calculate it properly\n            if (canUseKYC) {\n                return await factory.deploySecurityTokenWithOptions(formData.name, formData.symbol, formData.decimals, maxSupplyWei, formData.ownerAddress, \"\".concat(formData.tokenPrice, \" \").concat(formData.currency), formData.bonusTiers, \"\".concat(formData.tokenType, \" token deployed via admin panel\"), formData.tokenImageUrl || \"\", formData.enableKYC, {\n                    gasLimit\n                });\n            } else {\n                return await factory.deploySecurityToken(formData.name, formData.symbol, formData.decimals, maxSupplyWei, formData.ownerAddress, \"\".concat(formData.tokenPrice, \" \").concat(formData.currency), formData.bonusTiers, \"\".concat(formData.tokenType, \" token deployed via admin panel\"), formData.tokenImageUrl || \"\", {\n                    gasLimit\n                });\n            }\n        }\n    };\n    /**\r\n   * Get deployment result after successful transaction\r\n   */ const getDeploymentResult = async (tokenAddress, provider, formData)=>{\n        console.log(\"Token successfully deployed at:\", tokenAddress);\n        // Connect to the token contract\n        const token = new ethers__WEBPACK_IMPORTED_MODULE_6__.Contract(tokenAddress, _contracts_SecurityToken_json__WEBPACK_IMPORTED_MODULE_3__.abi, provider);\n        // Get identity registry address (whitelist contract)\n        const whitelistAddress = await token.identityRegistry();\n        // Get token decimals from the contract\n        const tokenDecimals = await token.decimals();\n        const decimalsNumber = Number(tokenDecimals);\n        // Format maxSupply based on decimals\n        const maxSupplyRaw = await token.maxSupply();\n        const maxSupplyFormatted = decimalsNumber === 0 ? maxSupplyRaw.toString() : ethers__WEBPACK_IMPORTED_MODULE_7__.formatUnits(maxSupplyRaw, tokenDecimals);\n        // Try to get the image URL from the contract if supported\n        let tokenImageUrl = formData.tokenImageUrl;\n        try {\n            // Check if the token contract supports tokenImageUrl function\n            if (token.interface.fragments.some((fragment)=>fragment.type === \"function\" && 'name' in fragment && fragment.name === \"tokenImageUrl\")) {\n                tokenImageUrl = await token.tokenImageUrl();\n            }\n        } catch (error) {\n            console.log(\"Token contract doesn't support image URL, using form data\");\n        }\n        // Create deployed token object\n        return {\n            address: tokenAddress,\n            name: await token.name(),\n            symbol: await token.symbol(),\n            decimals: decimalsNumber,\n            maxSupply: maxSupplyFormatted,\n            whitelistAddress: whitelistAddress,\n            admin: formData.ownerAddress,\n            tokenPrice: \"\".concat(formData.tokenPrice, \" \").concat(formData.currency),\n            currency: formData.currency,\n            bonusTiers: formData.bonusTiers,\n            hasKYC: formData.enableKYC,\n            tokenImageUrl: tokenImageUrl\n        };\n    };\n    /**\r\n   * Handle deployment errors with detailed messages\r\n   */ const handleDeploymentError = (err, network)=>{\n        console.error('Error creating token:', err);\n        // Extract more details from the error for debugging\n        const errorDetails = typeof err === 'object' ? JSON.stringify({\n            code: err.code,\n            message: err.message,\n            data: err.data,\n            info: err.info\n        }, null, 2) : String(err);\n        // Special handling for specific contract errors\n        if (err.message.includes(\"transaction execution reverted\")) {\n            // This is likely a contract validation error\n            setError(\"Transaction failed: The contract rejected the transaction. This could be due to:\\n\\n• Token symbol already exists - try a different symbol\\n• Invalid parameters (empty name/symbol, zero max supply, etc.)\\n• Access control issues\\n\\nPlease check your inputs and try again with a unique token symbol.\\n\\nTechnical details: \".concat(err.message));\n        } else if (err.message.includes(\"gas required exceeds allowance\") || err.message.includes(\"intrinsic gas too low\") || err.message.includes(\"Internal JSON-RPC error\")) {\n            // For Amoy testnet specifically, provide CLI alternative\n            if (network === 'amoy') {\n                // Create a CLI command template - actual values will be filled in by the UI\n                const cliCommand = '# For Windows PowerShell:\\ncd Token\\n$env:NETWORK=\"amoy\"\\n$env:TOKEN_NAME=\"YourTokenName\"\\n$env:TOKEN_SYMBOL=\"YTS\"\\n$env:TOKEN_DECIMALS=\"0\"\\n$env:MAX_SUPPLY=\"1000000\"\\n$env:ADMIN_ADDRESS=\"0xYourAddress\"\\n$env:TOKEN_PRICE=\"10 USD\"\\n$env:BONUS_TIERS=\"Tier 1: 5%, Tier 2: 10%\"\\nnpx hardhat run scripts/02-deploy-token.js --network amoy';\n                setError(\"Gas estimation failed on Amoy testnet. This is a common issue with this network.\\n\\nYou can try using this command line script instead:\\n\\n\".concat(cliCommand));\n            } else {\n                setError(\"Transaction failed due to gas calculation issues: \".concat(err.message, \"\\n\\nDetails: \").concat(errorDetails));\n            }\n        } else if (err.message.includes(\"Internal JSON-RPC error\") || err.message.includes(\"could not coalesce error\")) {\n            setError(\"Transaction failed. This is likely due to gas calculation issues on the Amoy testnet. Try using the command line script instead.\");\n        } else {\n            setError(\"Transaction failed: \".concat(err.message, \"\\n\\nDetails: \").concat(errorDetails));\n        }\n        setDeploymentStep('failed');\n    };\n    return {\n        isSubmitting,\n        error,\n        success,\n        deployedToken,\n        transactionHash,\n        deploymentStep,\n        deployToken\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/create-token/hooks/useTokenDeployment.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/create-token/page.tsx":
/*!***************************************!*\
  !*** ./src/app/create-token/page.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CreateTokenPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../config */ \"(app-pages-browser)/./src/config.ts\");\n/* harmony import */ var _contracts_SecurityTokenFactory_json__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../contracts/SecurityTokenFactory.json */ \"(app-pages-browser)/./src/contracts/SecurityTokenFactory.json\");\n/* harmony import */ var _components__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components */ \"(app-pages-browser)/./src/app/create-token/components/index.ts\");\n/* harmony import */ var _hooks__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./hooks */ \"(app-pages-browser)/./src/app/create-token/hooks/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n/**\r\n * CreateTokenPage Component\r\n *\r\n * Main component for the token creation page. Manages the overall flow of token\r\n * creation including form handling, deployment process, and result display.\r\n */ function CreateTokenPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams)();\n    const defaultNetwork = searchParams.get('network') || 'amoy';\n    // State Management\n    const [network, setNetwork] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultNetwork);\n    const [factoryAddress, setFactoryAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [hasDeployerRole, setHasDeployerRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [kycSupported, setKYCSupported] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        symbol: '',\n        decimals: 18,\n        maxSupply: '1000000',\n        ownerAddress: '',\n        tokenPrice: '10',\n        currency: 'USD',\n        tokenType: 'equity',\n        bonusTiers: 'Tier 1: 5%, Tier 2: 10%, Tier 3: 15%',\n        enableKYC: false,\n        tokenImageUrl: '',\n        selectedClaims: [\n            '10101010000001',\n            '10101010000004'\n        ],\n        issuerCountry: 'US' // Default country\n    });\n    // Use the custom hook for token deployment logic\n    const { isSubmitting, error, success, deployedToken, transactionHash, deploymentStep, deployToken } = (0,_hooks__WEBPACK_IMPORTED_MODULE_7__.useTokenDeployment)(network, factoryAddress, hasDeployerRole, kycSupported);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CreateTokenPage.useEffect\": ()=>{\n            // Get factory address for current network\n            const addresses = (0,_config__WEBPACK_IMPORTED_MODULE_4__.getContractAddresses)(network);\n            setFactoryAddress(addresses.factory || '');\n            // Auto-fill owner address from connected wallet and check permissions\n            if (window.ethereum) {\n                initWallet();\n            }\n        }\n    }[\"CreateTokenPage.useEffect\"], [\n        network,\n        factoryAddress\n    ]);\n    /**\r\n   * Initialize wallet and check permissions\r\n   */ const initWallet = async ()=>{\n        try {\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_8__.BrowserProvider(window.ethereum);\n            const accounts = await provider.listAccounts();\n            if (accounts.length > 0) {\n                const userAddress = accounts[0].address;\n                setFormData((prev)=>({\n                        ...prev,\n                        ownerAddress: userAddress\n                    }));\n                // Check if user has DEPLOYER_ROLE and if KYC is supported\n                if (factoryAddress) {\n                    checkDeployerPermissions(provider, userAddress);\n                }\n            }\n        } catch (err) {\n            console.error(\"Error initializing wallet:\", err);\n        }\n    };\n    /**\r\n   * Check if the user has DEPLOYER_ROLE and if KYC is supported\r\n   */ const checkDeployerPermissions = async (provider, userAddress)=>{\n        try {\n            const factory = new ethers__WEBPACK_IMPORTED_MODULE_9__.Contract(factoryAddress, _contracts_SecurityTokenFactory_json__WEBPACK_IMPORTED_MODULE_5__.abi, provider);\n            // Check if user has DEPLOYER_ROLE\n            const DEPLOYER_ROLE = await factory.DEPLOYER_ROLE();\n            const hasRole = await factory.hasRole(DEPLOYER_ROLE, userAddress);\n            setHasDeployerRole(hasRole);\n            if (!hasRole) {\n                console.warn(\"Connected wallet does not have DEPLOYER_ROLE\");\n            }\n            // Check if KYC is supported\n            try {\n                await factory.whitelistWithKYCImplementation();\n                const hasKYCFunction = factory.interface.fragments.some((fragment)=>fragment.type === \"function\" && 'name' in fragment && fragment.name === \"deploySecurityTokenWithOptions\");\n                setKYCSupported(hasKYCFunction);\n            } catch (err) {\n                console.warn(\"KYC functionality not supported in this factory contract\");\n                setKYCSupported(false);\n            }\n        } catch (err) {\n            console.error(\"Error checking deployer role:\", err);\n        }\n    };\n    /**\r\n   * Handle input changes in the form\r\n   */ const handleInputChange = (e)=>{\n        const { name, value, type } = e.target;\n        let processedValue = value;\n        // Convert decimals to number\n        if (name === 'decimals') {\n            processedValue = parseInt(value, 10);\n        } else if (type === 'checkbox') {\n            processedValue = e.target.checked;\n        }\n        setFormData({\n            ...formData,\n            [name]: processedValue\n        });\n    };\n    /**\r\n   * Handle network change\r\n   */ const handleNetworkChange = (e)=>{\n        setNetwork(e.target.value);\n        console.log(\"Switched to network: \".concat(e.target.value));\n    };\n    /**\r\n   * Handle form submission\r\n   */ const handleSubmit = async (e)=>{\n        e.preventDefault();\n        await deployToken(formData);\n    };\n    /**\r\n   * Add token manually to the dashboard\r\n   */ const addTokenManually = ()=>{\n        router.push(\"/?add=\".concat(formData.symbol));\n    };\n    /**\r\n   * Get the network label for display\r\n   */ const getNetworkLabel = (networkKey)=>{\n        switch(networkKey){\n            case 'amoy':\n                return 'Amoy Testnet';\n            case 'polygon':\n                return 'Polygon Mainnet';\n            default:\n                return networkKey;\n        }\n    };\n    /**\r\n   * Get block explorer URL for token or address\r\n   */ const getBlockExplorerUrl = (network, address)=>{\n        if (network === 'amoy') {\n            return \"https://www.oklink.com/amoy/address/\".concat(address);\n        } else if (network === 'polygon') {\n            return \"https://polygonscan.com/address/\".concat(address);\n        }\n        return '#';\n    };\n    /**\r\n   * Get transaction explorer URL\r\n   */ const getTransactionExplorerUrl = (network, txHash)=>{\n        if (network === 'amoy') {\n            return \"https://www.oklink.com/amoy/tx/\".concat(txHash);\n        } else if (network === 'polygon') {\n            return \"https://polygonscan.com/tx/\".concat(txHash);\n        }\n        return '#';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/\",\n                        className: \"text-blue-600 hover:text-blue-800 mr-4\",\n                        children: \"← Back to Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold\",\n                        children: \"Create New Security Token\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-4 px-3 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800\",\n                        children: getNetworkLabel(network)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                lineNumber: 219,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 p-4 bg-green-50 border border-green-200 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-green-600 text-xl\",\n                                children: \"\\uD83C\\uDF89\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-green-800\",\n                                    children: \"Advanced Transfer Controls Now Available!\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-1 text-sm text-green-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"All new tokens automatically include advanced transfer control features:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"mt-1 list-disc list-inside space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Conditional Transfers:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                                                            lineNumber: 242,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Require approval for all transfers\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Transfer Whitelisting:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                                                            lineNumber: 243,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Additional access control layer\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Transfer Fees:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                                                            lineNumber: 244,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Configurable percentage-based fees\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-xs\",\n                                            children: [\n                                                \"\\uD83D\\uDCA1 After creating your token, visit the \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Transfer Controls\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 57\n                                                }, this),\n                                                \" page to configure these features.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                lineNumber: 230,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_6__.FactoryInfo, {\n                factoryAddress: factoryAddress,\n                network: network,\n                hasDeployerRole: hasDeployerRole\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                lineNumber: 255,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_6__.NetworkBanner, {\n                network: network\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                lineNumber: 262,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_6__.StatusNotification, {\n                type: \"error\",\n                message: error,\n                deploymentStep: deploymentStep,\n                tokenSymbol: formData.symbol,\n                onAddManually: addTokenManually\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                lineNumber: 266,\n                columnNumber: 9\n            }, this),\n            deployedToken && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_6__.TokenDeployment, {\n                deployedToken: deployedToken,\n                transactionHash: transactionHash,\n                network: network,\n                getBlockExplorerUrl: getBlockExplorerUrl,\n                getTransactionExplorerUrl: getTransactionExplorerUrl\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                lineNumber: 277,\n                columnNumber: 9\n            }, this),\n            !deployedToken && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_6__.TokenForm, {\n                formData: formData,\n                handleInputChange: handleInputChange,\n                handleNetworkChange: handleNetworkChange,\n                handleSubmit: handleSubmit,\n                isSubmitting: isSubmitting,\n                network: network,\n                getNetworkLabel: getNetworkLabel,\n                deploymentStep: deploymentStep,\n                kycSupported: kycSupported\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                lineNumber: 288,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n        lineNumber: 218,\n        columnNumber: 5\n    }, this);\n}\n_s(CreateTokenPage, \"zljWvDNxsFNKvjWLmGn+1qG78Kc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams,\n        _hooks__WEBPACK_IMPORTED_MODULE_7__.useTokenDeployment\n    ];\n});\n_c = CreateTokenPage;\nvar _c;\n$RefreshReg$(_c, \"CreateTokenPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/create-token/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/config.ts":
/*!***********************!*\
  !*** ./src/config.ts ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   contractAddresses: () => (/* binding */ contractAddresses),\n/* harmony export */   defaultNetwork: () => (/* binding */ defaultNetwork),\n/* harmony export */   getContractAddresses: () => (/* binding */ getContractAddresses),\n/* harmony export */   getKnownTokens: () => (/* binding */ getKnownTokens),\n/* harmony export */   getNetworkConfig: () => (/* binding */ getNetworkConfig),\n/* harmony export */   isKnownToken: () => (/* binding */ isKnownToken),\n/* harmony export */   knownTokens: () => (/* binding */ knownTokens),\n/* harmony export */   networkConfig: () => (/* binding */ networkConfig),\n/* harmony export */   tokenTypes: () => (/* binding */ tokenTypes),\n/* harmony export */   verifyTokenExists: () => (/* binding */ verifyTokenExists)\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n// Network configuration\nconst networkConfig = {\n    // Amoy testnet\n    amoy: {\n        chainId: 80002,\n        name: \"Amoy\",\n        rpcUrl: \"https://rpc-amoy.polygon.technology\",\n        blockExplorer: \"https://www.oklink.com/amoy\"\n    },\n    // Polygon mainnet\n    polygon: {\n        chainId: 137,\n        name: \"Polygon\",\n        rpcUrl: \"https://polygon-rpc.com\",\n        blockExplorer: \"https://polygonscan.com\"\n    }\n};\n// Default network\nconst defaultNetwork = \"amoy\";\n// Contract addresses - using the newly deployed factory address\nconst contractAddresses = {\n    // Updated factory contract address from latest deployment with decimals support\n    amoy: {\n        factory: \"0x69a6536629369F8948f47b897045929a57c630Fd\",\n        tokenImplementation: \"0xae2aA28708120CAA177e4c98CCCa0e152E30E506\",\n        whitelistImplementation: \"0x63eeE78ccc281413272bE68d9553Ae82680a0B09\",\n        whitelistWithKYCImplementation: \"0xf7c9C30Ad2E5b72F86489f26ab66cc1E79F44A7D\"\n    },\n    polygon: {\n        factory: process.env.NEXT_PUBLIC_FACTORY_ADDRESS_POLYGON || \"0x6543210987654321098765432109876543210987\"\n    }\n};\n// Known deployed tokens for fallback display (from memory)\nconst knownTokens = {\n    amoy: [\n        {\n            address: \"0x7544A3072FAA793e3f89048C31b794f171779544\",\n            name: \"Advanced Control Token\",\n            symbol: \"ACT\",\n            description: \"Security token with advanced transfer controls (conditional transfers, whitelisting, fees)\"\n        },\n        {\n            address: \"0xfccB88D208f5Ec7166ce2291138aaD5274C671dE\",\n            name: \"Augment_019\",\n            symbol: \"AUG019\",\n            description: \"Commodity token with 0 decimals, 1M max supply\"\n        },\n        {\n            address: \"0xe5F81d7dCeB8a8F97274C749773659B7288EcF90\",\n            name: \"Augment_01z\",\n            symbol: \"AUG01Z\",\n            description: \"Test token with custom configuration\"\n        },\n        {\n            address: \"0x391a0FA1498B869d0b9445596ed49b03aA8bf46e\",\n            name: \"Test Image Token\",\n            symbol: \"TIT2789\",\n            description: \"Test token with image URL support - deployed from upgraded factory\"\n        }\n    ]\n};\n// Token types for creating new tokens\nconst tokenTypes = [\n    {\n        id: \"equity\",\n        name: \"Equity\"\n    },\n    {\n        id: \"bond\",\n        name: \"Bond\"\n    },\n    {\n        id: \"debenture\",\n        name: \"Debenture\"\n    },\n    {\n        id: \"warrant\",\n        name: \"Warrant\"\n    },\n    {\n        id: \"realestate\",\n        name: \"Real Estate\"\n    },\n    {\n        id: \"carbon\",\n        name: \"Carbon Credit\"\n    },\n    {\n        id: \"commodity\",\n        name: \"Commodity\"\n    }\n];\n// Helper function to get contract addresses for the current network\nconst getContractAddresses = (network)=>{\n    return contractAddresses[network] || contractAddresses[defaultNetwork];\n};\n// Helper function to get network configuration for the current network\nconst getNetworkConfig = (network)=>{\n    return networkConfig[network] || networkConfig[defaultNetwork];\n};\n// Helper function to get known tokens for a network\nconst getKnownTokens = (network)=>{\n    return knownTokens[network] || [];\n};\n// Helper to check if a token exists in factory (in a real implementation, this would query the blockchain)\nconst verifyTokenExists = async (network, tokenAddress)=>{\n    // In a real implementation, this would:\n    // 1. Connect to the factory contract\n    // 2. Call a method or check events to verify the token's existence\n    // For demo purposes, we'll just return true\n    return true;\n};\n// Helper function to validate if an address is a known token\nconst isKnownToken = (network, tokenAddress)=>{\n    const tokens = getKnownTokens(network);\n    return tokens.some((token)=>token.address.toLowerCase() === tokenAddress.toLowerCase());\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/config.ts\n"));

/***/ })

});