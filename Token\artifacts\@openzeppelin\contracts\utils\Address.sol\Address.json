{"_format": "hh-sol-artifact-1", "contractName": "Address", "sourceName": "@openzeppelin/contracts/utils/Address.sol", "abi": [{"inputs": [{"internalType": "address", "name": "target", "type": "address"}], "name": "AddressEmptyCode", "type": "error"}], "bytecode": "0x6080806040523460175760119081601d823930815050f35b600080fdfe600080fdfea164736f6c6343000816000a", "deployedBytecode": "0x600080fdfea164736f6c6343000816000a", "linkReferences": {}, "deployedLinkReferences": {}}