const { ethers } = require("hardhat");

async function main() {
  const tokenAddress = process.env.TOKEN_ADDRESS || "******************************************";
  const checkAddress = process.env.CHECK_ADDRESS || "******************************************";
  
  console.log("🔍 Checking Token Roles and Permissions");
  console.log("=" .repeat(60));
  console.log("Token Address:", tokenAddress);
  console.log("Checking Address:", checkAddress);
  
  const [deployer] = await ethers.getSigners();
  console.log("Script running with account:", deployer.address);
  
  // Connect to the token
  const SecurityTokenMinimal = await ethers.getContractFactory("SecurityTokenMinimal");
  const token = SecurityTokenMinimal.attach(tokenAddress);
  
  try {
    console.log("\n📋 Token Information:");
    const name = await token.name();
    const symbol = await token.symbol();
    const version = await token.version();
    
    console.log("Name:", name);
    console.log("Symbol:", symbol);
    console.log("Version:", version);
    
    console.log("\n🔑 Role Checks:");
    
    // Check DEFAULT_ADMIN_ROLE
    const DEFAULT_ADMIN_ROLE = await token.DEFAULT_ADMIN_ROLE();
    console.log("DEFAULT_ADMIN_ROLE:", DEFAULT_ADMIN_ROLE);
    
    const hasDefaultAdminRole = await token.hasRole(DEFAULT_ADMIN_ROLE, checkAddress);
    console.log(`${checkAddress} has DEFAULT_ADMIN_ROLE:`, hasDefaultAdminRole);
    
    // Check AGENT_ROLE
    const AGENT_ROLE = await token.AGENT_ROLE();
    console.log("AGENT_ROLE:", AGENT_ROLE);
    
    const hasAgentRole = await token.hasRole(AGENT_ROLE, checkAddress);
    console.log(`${checkAddress} has AGENT_ROLE:`, hasAgentRole);
    
    console.log("\n👥 Agent Information:");
    const agentCount = await token.getAgentCount();
    console.log("Total agents:", agentCount.toString());
    
    const allAgents = await token.getAllAgents();
    console.log("All agents:");
    for (let i = 0; i < allAgents.length; i++) {
      console.log(`  ${i + 1}. ${allAgents[i]}`);
    }
    
    console.log("\n✅ Whitelist Status:");
    const isWhitelisted = await token.isWhitelisted(checkAddress);
    console.log(`${checkAddress} is whitelisted:`, isWhitelisted);
    
    const isKycApproved = await token.isKycApproved(checkAddress);
    console.log(`${checkAddress} is KYC approved:`, isKycApproved);
    
    const isVerified = await token.isVerified(checkAddress);
    console.log(`${checkAddress} is verified:`, isVerified);
    
    console.log("\n🔧 Contract Status:");
    const isPaused = await token.isPaused();
    console.log("Token is paused:", isPaused);
    
    // Check if we need to grant roles
    if (!hasDefaultAdminRole && !hasAgentRole) {
      console.log("\n⚠️  ISSUE DETECTED:");
      console.log(`Address ${checkAddress} does not have admin or agent roles!`);
      console.log("This explains why you cannot add agents or manage whitelist.");
      
      if (deployer.address.toLowerCase() === checkAddress.toLowerCase()) {
        console.log("\n🔧 You are the deployer, but roles might not be set correctly.");
        console.log("This could be a contract initialization issue.");
      } else {
        console.log("\n🔧 SOLUTION:");
        console.log("The deployer needs to grant you admin or agent role:");
        console.log(`export TOKEN_ADDRESS=${tokenAddress}`);
        console.log(`export TARGET_ADDRESS=${checkAddress}`);
        console.log(`export ACTION=grantAdminRole`);
        console.log("npx hardhat run scripts/manage-token-roles.js --network amoy");
      }
    } else {
      console.log("\n✅ ROLES OK:");
      console.log(`Address ${checkAddress} has the necessary permissions.`);
    }
    
  } catch (error) {
    console.error("❌ Error checking token roles:", error.message);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
