{"_format": "hh-sol-artifact-1", "contractName": "SecurityTokenSimplified", "sourceName": "contracts/SecurityTokenSimplified.sol", "abi": [{"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint8", "name": "decimals_", "type": "uint8"}, {"internalType": "uint256", "name": "maxSupply_", "type": "uint256"}, {"internalType": "address", "name": "admin", "type": "address"}, {"internalType": "string", "name": "tokenPrice_", "type": "string"}, {"internalType": "string", "name": "bonusTiers_", "type": "string"}, {"internalType": "string", "name": "tokenImageUrl_", "type": "string"}, {"internalType": "address", "name": "identityRegistry_", "type": "address"}, {"internalType": "address", "name": "compliance_", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientAllowance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "name": "ERC20InvalidApprover", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "name": "ERC20InvalidReceiver", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "ERC20InvalidSender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "name": "ERC20InvalidSpender", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "agent", "type": "address"}], "name": "AgentAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "agent", "type": "address"}], "name": "AgentRemoved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "AgreementAccepted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "admin", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "TokensFrozen", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "TokensUnfrozen", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "admin", "type": "address"}], "name": "Unpaused", "type": "event"}, {"inputs": [], "name": "AGENT_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "TRANSFER_MANAGER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "acceptAgreement", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "actionCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "agent", "type": "address"}], "name": "addAgent", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "agents", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "a", "type": "address"}], "name": "availableBalanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "bonusTiers", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "compliance", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "forcedTransfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "a", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "freezeTokens", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "frozenBalances", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "i", "type": "uint256"}], "name": "getAgentAt", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getAgentCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "a", "type": "address"}], "name": "getAgreementTimestamp", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getAllAgents", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "a", "type": "address"}], "name": "hasAcceptedAgreement", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "identityRegistry", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "isAgent", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "sig", "type": "bytes4"}], "name": "isFunctionPaused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "isPaused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "lastAction", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "maxSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "mint", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "sig", "type": "bytes4"}], "name": "pauseFunction", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "agent", "type": "address"}], "name": "removeAgent", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "secLevel", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "level", "type": "uint256"}], "name": "setSecurityLevel", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "tokenImageUrl", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "tokenPrice", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalFrozen", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "a", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "unfreezeTokens", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "sig", "type": "bytes4"}], "name": "unpauseFunction", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "version", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "pure", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}