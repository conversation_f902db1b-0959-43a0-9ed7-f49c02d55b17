// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";

/**
 * @title TransferModuleComplete
 * @dev Complete transfer module with fees, compliance, and advanced features
 */
contract TransferModuleComplete is Initializable, AccessControlUpgradeable, ReentrancyGuardUpgradeable {
    // Constants
    uint256 private constant MAX_FEE_PERCENTAGE = 1000; // 10%
    uint256 private constant FEE_PRECISION = 10000; // 100%
    uint256 private constant MAX_BATCH_SIZE = 100;
    
    // State variables
    address public securityToken;
    bool public transferFeesEnabled;
    uint256 public transferFeePercentage;
    address public feeCollector;
    
    // Advanced transfer features
    mapping(address => bool) public feeExemptAddresses;
    mapping(address => uint256) public customFeePercentages;
    mapping(address => uint256) public transferLimits;
    mapping(address => uint256) public dailyTransferAmounts;
    mapping(address => uint256) public lastTransferDay;
    
    // Transfer tracking
    uint256 public totalFeesCollected;
    uint256 public totalTransferVolume;
    mapping(address => uint256) public accountTransferVolume;
    
    // Events
    event TransferFeesEnabled(uint256 feePercentage, address feeCollector);
    event TransferFeesDisabled();
    event TransferFeeCollected(address indexed from, address indexed to, uint256 amount, uint256 fee);
    event FeeExemptionSet(address indexed account, bool exempt);
    event CustomFeeSet(address indexed account, uint256 feePercentage);
    event TransferLimitSet(address indexed account, uint256 limit);
    event TransferProcessed(address indexed from, address indexed to, uint256 amount, uint256 actualAmount);
    
    // Modifiers
    modifier onlySecurityToken() {
        require(msg.sender == securityToken, "TransferModule: caller is not the security token");
        _;
    }
    
    modifier onlyTokenAdmin() {
        require(hasRole(DEFAULT_ADMIN_ROLE, msg.sender), "TransferModule: caller is not admin");
        _;
    }
    
    /**
     * @dev Initialize the transfer module
     */
    function initialize(address _securityToken, address _admin) public initializer {
        __AccessControl_init();
        __ReentrancyGuard_init();
        
        require(_securityToken != address(0), "TransferModule: invalid token address");
        require(_admin != address(0), "TransferModule: invalid admin address");
        
        securityToken = _securityToken;
        _grantRole(DEFAULT_ADMIN_ROLE, _admin);
    }
    
    /**
     * @dev Enable transfer fees
     */
    function enableTransferFees(uint256 _feePercentage, address _feeCollector) 
        external 
        onlyTokenAdmin 
        nonReentrant 
    {
        require(_feePercentage <= MAX_FEE_PERCENTAGE, "TransferModule: fee too high");
        require(_feeCollector != address(0), "TransferModule: invalid collector");
        
        transferFeesEnabled = true;
        transferFeePercentage = _feePercentage;
        feeCollector = _feeCollector;
        
        emit TransferFeesEnabled(_feePercentage, _feeCollector);
    }
    
    /**
     * @dev Disable transfer fees
     */
    function disableTransferFees() external onlyTokenAdmin {
        transferFeesEnabled = false;
        transferFeePercentage = 0;
        feeCollector = address(0);
        
        emit TransferFeesDisabled();
    }
    
    /**
     * @dev Set fee exemption for an address
     */
    function setFeeExemption(address account, bool exempt) external onlyTokenAdmin {
        require(account != address(0), "TransferModule: invalid account");
        feeExemptAddresses[account] = exempt;
        emit FeeExemptionSet(account, exempt);
    }
    
    /**
     * @dev Set custom fee percentage for an address
     */
    function setCustomFee(address account, uint256 feePercentage) external onlyTokenAdmin {
        require(account != address(0), "TransferModule: invalid account");
        require(feePercentage <= MAX_FEE_PERCENTAGE, "TransferModule: fee too high");
        
        customFeePercentages[account] = feePercentage;
        emit CustomFeeSet(account, feePercentage);
    }
    
    /**
     * @dev Set daily transfer limit for an address
     */
    function setTransferLimit(address account, uint256 limit) external onlyTokenAdmin {
        require(account != address(0), "TransferModule: invalid account");
        transferLimits[account] = limit;
        emit TransferLimitSet(account, limit);
    }
    
    /**
     * @dev Calculate transfer fee for an address
     */
    function calculateTransferFee(address from, uint256 amount) public view returns (uint256 fee) {
        if (!transferFeesEnabled || feeExemptAddresses[from]) {
            return 0;
        }
        
        uint256 feePercentage = customFeePercentages[from] > 0 
            ? customFeePercentages[from] 
            : transferFeePercentage;
            
        return (amount * feePercentage) / FEE_PRECISION;
    }
    
    /**
     * @dev Process transfer with fees and compliance
     */
    function processTransfer(address from, address to, uint256 amount) 
        external 
        onlySecurityToken 
        nonReentrant 
        returns (uint256 actualAmount, uint256 fee) 
    {
        require(from != address(0), "TransferModule: transfer from zero address");
        require(to != address(0), "TransferModule: transfer to zero address");
        require(amount > 0, "TransferModule: transfer amount must be positive");
        
        // Check daily transfer limits
        _checkTransferLimits(from, amount);
        
        // Calculate fee
        fee = calculateTransferFee(from, amount);
        actualAmount = amount - fee;
        
        // Update tracking
        _updateTransferTracking(from, to, amount, fee);
        
        emit TransferProcessed(from, to, amount, actualAmount);
        
        if (fee > 0) {
            emit TransferFeeCollected(from, to, amount, fee);
        }
        
        return (actualAmount, fee);
    }
    
    /**
     * @dev Batch process multiple transfers
     */
    function batchProcessTransfers(
        address[] memory froms,
        address[] memory tos,
        uint256[] memory amounts
    ) external onlySecurityToken nonReentrant returns (
        uint256[] memory actualAmounts,
        uint256[] memory fees
    ) {
        require(froms.length == tos.length && tos.length == amounts.length, "TransferModule: array length mismatch");
        require(froms.length <= MAX_BATCH_SIZE, "TransferModule: batch too large");

        actualAmounts = new uint256[](froms.length);
        fees = new uint256[](froms.length);

        for (uint256 i = 0; i < froms.length; i++) {
            (actualAmounts[i], fees[i]) = this.processTransfer(froms[i], tos[i], amounts[i]);
        }

        return (actualAmounts, fees);
    }
    
    /**
     * @dev Check and update daily transfer limits
     */
    function _checkTransferLimits(address from, uint256 amount) internal {
        uint256 currentDay = block.timestamp / 86400;
        
        if (lastTransferDay[from] != currentDay) {
            dailyTransferAmounts[from] = 0;
            lastTransferDay[from] = currentDay;
        }
        
        if (transferLimits[from] > 0) {
            require(
                dailyTransferAmounts[from] + amount <= transferLimits[from],
                "TransferModule: daily transfer limit exceeded"
            );
        }
        
        dailyTransferAmounts[from] += amount;
    }
    
    /**
     * @dev Update transfer tracking
     */
    function _updateTransferTracking(address from, address to, uint256 amount, uint256 fee) internal {
        totalTransferVolume += amount;
        totalFeesCollected += fee;
        accountTransferVolume[from] += amount;
    }
    
    /**
     * @dev Get transfer fee information
     */
    function getTransferFeeInfo() external view returns (
        bool enabled, 
        uint256 percentage, 
        address collector,
        uint256 totalFees,
        uint256 totalVolume
    ) {
        return (
            transferFeesEnabled, 
            transferFeePercentage, 
            feeCollector,
            totalFeesCollected,
            totalTransferVolume
        );
    }
    
    /**
     * @dev Get account transfer info
     */
    function getAccountTransferInfo(address account) external view returns (
        bool feeExempt,
        uint256 customFee,
        uint256 dailyLimit,
        uint256 dailyUsed,
        uint256 totalVolume
    ) {
        uint256 currentDay = block.timestamp / 86400;
        uint256 dailyUsed_ = lastTransferDay[account] == currentDay ? dailyTransferAmounts[account] : 0;
        
        return (
            feeExemptAddresses[account],
            customFeePercentages[account],
            transferLimits[account],
            dailyUsed_,
            accountTransferVolume[account]
        );
    }
    
    /**
     * @dev Emergency fee collection (admin only)
     */
    function emergencyCollectFees(address to, uint256 amount) external onlyTokenAdmin nonReentrant {
        require(to != address(0), "TransferModule: invalid recipient");
        require(amount <= totalFeesCollected, "TransferModule: insufficient fees");

        totalFeesCollected -= amount;
        // Note: Actual token transfer would be handled by the main token contract
    }

    /**
     * @dev Required gap for upgradeable contracts
     */
    uint256[50] private __gap;
}
