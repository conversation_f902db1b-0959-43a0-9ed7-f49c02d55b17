/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/kyc/status/route";
exports.ids = ["app/api/kyc/status/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkyc%2Fstatus%2Froute&page=%2Fapi%2Fkyc%2Fstatus%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkyc%2Fstatus%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkyc%2Fstatus%2Froute&page=%2Fapi%2Fkyc%2Fstatus%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkyc%2Fstatus%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_tokendev_newroo_admin_panel_src_app_api_kyc_status_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/kyc/status/route.ts */ \"(rsc)/./src/app/api/kyc/status/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/kyc/status/route\",\n        pathname: \"/api/kyc/status\",\n        filename: \"route\",\n        bundlePath: \"app/api/kyc/status/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api\\\\kyc\\\\status\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_tokendev_newroo_admin_panel_src_app_api_kyc_status_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkyc%2Fstatus%2Froute&page=%2Fapi%2Fkyc%2Fstatus%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkyc%2Fstatus%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/kyc/status/route.ts":
/*!*****************************************!*\
  !*** ./src/app/api/kyc/status/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/address/checks.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/providers/provider-jsonrpc.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/contract/contract.js\");\n\n\nconst SECURITY_TOKEN_CORE_ABI = [\n    \"function isWhitelisted(address account) external view returns (bool)\",\n    \"function isVerified(address account) external view returns (bool)\",\n    \"function getVerificationStatus(address user) external view returns (bool kycApproved, bool whitelisted, bool eligible, string memory method)\"\n];\nconst KYC_CLAIMS_MODULE_ABI = [\n    \"function getVerificationStatus(address token, address user) external view returns (bool kycApproved, bool whitelisted, bool eligible, string memory method)\",\n    \"function isEligible(address token, address user) external view returns (bool)\"\n];\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const tokenAddress = searchParams.get('tokenAddress');\n        const userAddress = searchParams.get('userAddress');\n        if (!tokenAddress || !userAddress) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Token address and user address are required'\n            }, {\n                status: 400\n            });\n        }\n        // Validate addresses\n        if (!ethers__WEBPACK_IMPORTED_MODULE_1__.isAddress(tokenAddress) || !ethers__WEBPACK_IMPORTED_MODULE_1__.isAddress(userAddress)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid address format'\n            }, {\n                status: 400\n            });\n        }\n        // Get environment variables\n        const rpcUrl = process.env.AMOY_RPC_URL;\n        const kycModuleAddress = process.env.AMOY_KYC_CLAIMS_MODULE_ADDRESS;\n        if (!rpcUrl) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'RPC URL not configured'\n            }, {\n                status: 500\n            });\n        }\n        // Setup provider\n        const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.JsonRpcProvider(rpcUrl);\n        let status = {\n            kycApproved: false,\n            whitelisted: false,\n            eligible: false,\n            method: 'UNKNOWN'\n        };\n        if (kycModuleAddress && ethers__WEBPACK_IMPORTED_MODULE_1__.isAddress(kycModuleAddress)) {\n            // Try KYC Claims Module first\n            console.log('Checking status via KYC Claims Module');\n            const kycModule = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(kycModuleAddress, KYC_CLAIMS_MODULE_ABI, provider);\n            try {\n                const result = await kycModule.getVerificationStatus(tokenAddress, userAddress);\n                status = {\n                    kycApproved: result[0],\n                    whitelisted: result[1],\n                    eligible: result[2],\n                    method: result[3] || 'KYC_CLAIMS_MODULE'\n                };\n                console.log('Status retrieved via KYC Claims Module:', status);\n            } catch (moduleError) {\n                console.log('KYC Claims Module failed, falling back to direct token call:', moduleError);\n                // Fallback to direct token call\n                const tokenContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(tokenAddress, SECURITY_TOKEN_CORE_ABI, provider);\n                try {\n                    // Try the new getVerificationStatus method first\n                    const result = await tokenContract.getVerificationStatus(userAddress);\n                    status = {\n                        kycApproved: result[0],\n                        whitelisted: result[1],\n                        eligible: result[2],\n                        method: result[3] || 'SECURITY_TOKEN_CORE'\n                    };\n                    console.log('Status retrieved via SecurityTokenCore.getVerificationStatus:', status);\n                } catch (newMethodError) {\n                    console.log('New method failed, using individual calls:', newMethodError);\n                    // Fallback to individual method calls\n                    try {\n                        const [isVerified, isWhitelisted] = await Promise.all([\n                            tokenContract.isVerified(userAddress).catch(()=>false),\n                            tokenContract.isWhitelisted(userAddress).catch(()=>false)\n                        ]);\n                        status = {\n                            kycApproved: isVerified,\n                            whitelisted: isWhitelisted,\n                            eligible: isVerified && isWhitelisted,\n                            method: 'IDENTITY_MANAGER'\n                        };\n                        console.log('Status retrieved via individual calls:', status);\n                    } catch (individualError) {\n                        console.log('Individual calls failed:', individualError);\n                        status.method = 'ERROR';\n                    }\n                }\n            }\n        } else {\n            // Direct token call only\n            console.log('Using direct token call for status check');\n            const tokenContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(tokenAddress, SECURITY_TOKEN_CORE_ABI, provider);\n            try {\n                // Try the new getVerificationStatus method first\n                const result = await tokenContract.getVerificationStatus(userAddress);\n                status = {\n                    kycApproved: result[0],\n                    whitelisted: result[1],\n                    eligible: result[2],\n                    method: result[3] || 'SECURITY_TOKEN_CORE'\n                };\n                console.log('Status retrieved via SecurityTokenCore.getVerificationStatus:', status);\n            } catch (newMethodError) {\n                console.log('New method failed, using individual calls:', newMethodError);\n                // Fallback to individual method calls\n                try {\n                    const [isVerified, isWhitelisted] = await Promise.all([\n                        tokenContract.isVerified(userAddress).catch(()=>false),\n                        tokenContract.isWhitelisted(userAddress).catch(()=>false)\n                    ]);\n                    status = {\n                        kycApproved: isVerified,\n                        whitelisted: isWhitelisted,\n                        eligible: isVerified && isWhitelisted,\n                        method: 'IDENTITY_MANAGER'\n                    };\n                    console.log('Status retrieved via individual calls:', status);\n                } catch (individualError) {\n                    console.log('Individual calls failed:', individualError);\n                    status.method = 'ERROR';\n                }\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            userAddress,\n            tokenAddress,\n            ...status\n        });\n    } catch (error) {\n        console.error('Error checking verification status:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: `Failed to check verification status: ${error.message}`\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/kyc/status/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ethers","vendor-chunks/@noble","vendor-chunks/@adraffy"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkyc%2Fstatus%2Froute&page=%2Fapi%2Fkyc%2Fstatus%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkyc%2Fstatus%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();