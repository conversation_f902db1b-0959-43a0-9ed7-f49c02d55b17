{"network": "amoy", "chainId": "80002", "factoryAddress": "0x00eB03C1e791CA0e74edbdf88ea19544a7D57645", "adminAddress": "0x56f3726C92B8B92a6ab71983886F91718540d888", "deploymentHash": "0x9eabd455628c17a89915956b941ed22aaa04c5c4fe0bcd4ca868641a59dc86c5", "timestamp": "2025-06-21T05:27:31.756Z", "contractType": "SecurityTokenFactoryWithWhitelist", "architecture": "Enhanced with Whitelist Support", "securityLevel": "MAXIMUM", "features": {"allSecurityAuditFixes": true, "emergencyControls": true, "functionPausing": true, "enhancedReentrancyProtection": true, "improvedInputValidation": true, "fullKYCIntegration": true, "separateWhitelistContracts": true, "adminPanelIntegration": true, "roleBasedAccessControl": true, "agentManagement": true, "agreementTracking": true, "sizeOptimized": true}}