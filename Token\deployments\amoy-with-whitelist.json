{"network": "amoy", "chainId": "80002", "factoryAddress": "0xB23fF2F252265826754FB5F785aC8376CFBB546E", "adminAddress": "0x56f3726C92B8B92a6ab71983886F91718540d888", "deploymentHash": "0x9ef6be76bd2056a6cac5759008ece4f058d0b91dae23428273fbe12e2e29edfc", "timestamp": "2025-06-21T06:00:16.585Z", "contractType": "SecurityTokenFactoryWithWhitelist", "architecture": "Enhanced with Whitelist Support", "securityLevel": "MAXIMUM", "features": {"allSecurityAuditFixes": true, "emergencyControls": true, "functionPausing": true, "enhancedReentrancyProtection": true, "improvedInputValidation": true, "fullKYCIntegration": true, "separateWhitelistContracts": true, "adminPanelIntegration": true, "roleBasedAccessControl": true, "agentManagement": true, "agreementTracking": true, "sizeOptimized": true}}