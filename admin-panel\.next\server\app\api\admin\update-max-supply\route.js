/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/update-max-supply/route";
exports.ids = ["app/api/admin/update-max-supply/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fupdate-max-supply%2Froute&page=%2Fapi%2Fadmin%2Fupdate-max-supply%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fupdate-max-supply%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fupdate-max-supply%2Froute&page=%2Fapi%2Fadmin%2Fupdate-max-supply%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fupdate-max-supply%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_tokendev_newroo_admin_panel_src_app_api_admin_update_max_supply_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/update-max-supply/route.ts */ \"(rsc)/./src/app/api/admin/update-max-supply/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/update-max-supply/route\",\n        pathname: \"/api/admin/update-max-supply\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/update-max-supply/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api\\\\admin\\\\update-max-supply\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_tokendev_newroo_admin_panel_src_app_api_admin_update_max_supply_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fupdate-max-supply%2Froute&page=%2Fapi%2Fadmin%2Fupdate-max-supply%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fupdate-max-supply%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/update-max-supply/route.ts":
/*!******************************************************!*\
  !*** ./src/app/api/admin/update-max-supply/route.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/address/checks.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/providers/provider-jsonrpc.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/wallet/wallet.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/utils/units.js\");\n\n\nconst SECURITY_TOKEN_CORE_ABI = [\n    \"function updateMaxSupply(uint256 newMaxSupply) external\",\n    \"function maxSupply() external view returns (uint256)\",\n    \"function totalSupply() external view returns (uint256)\"\n];\nasync function POST(request) {\n    try {\n        const { tokenAddress, newMaxSupply } = await request.json();\n        if (!tokenAddress || !newMaxSupply) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Token address and new max supply are required'\n            }, {\n                status: 400\n            });\n        }\n        // Validate addresses\n        if (!ethers__WEBPACK_IMPORTED_MODULE_1__.isAddress(tokenAddress)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid token address format'\n            }, {\n                status: 400\n            });\n        }\n        // Validate max supply\n        const maxSupplyNum = parseInt(newMaxSupply);\n        if (isNaN(maxSupplyNum) || maxSupplyNum <= 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid max supply value'\n            }, {\n                status: 400\n            });\n        }\n        // Get environment variables\n        const rpcUrl = process.env.AMOY_RPC_URL;\n        const privateKey = process.env.CONTRACT_ADMIN_PRIVATE_KEY;\n        if (!rpcUrl || !privateKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Server configuration error'\n            }, {\n                status: 500\n            });\n        }\n        // Setup provider and signer\n        const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.JsonRpcProvider(rpcUrl);\n        const signer = new ethers__WEBPACK_IMPORTED_MODULE_3__.Wallet(privateKey, provider);\n        // Get token contract\n        const tokenContract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(tokenAddress, SECURITY_TOKEN_CORE_ABI, signer);\n        // Get current values for comparison\n        const currentMaxSupply = await tokenContract.maxSupply();\n        const currentTotalSupply = await tokenContract.totalSupply();\n        console.log('Current max supply:', currentMaxSupply.toString());\n        console.log('Current total supply:', currentTotalSupply.toString());\n        console.log('New max supply:', newMaxSupply);\n        // Convert to proper units (assuming 0 decimals based on our token)\n        const newMaxSupplyBN = ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits(newMaxSupply, 0);\n        // Update max supply\n        const tx = await tokenContract.updateMaxSupply(newMaxSupplyBN);\n        await tx.wait();\n        // Verify update\n        const updatedMaxSupply = await tokenContract.maxSupply();\n        console.log('Max supply updated successfully:', tx.hash);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Max supply updated successfully',\n            txHash: tx.hash,\n            oldMaxSupply: currentMaxSupply.toString(),\n            newMaxSupply: updatedMaxSupply.toString(),\n            currentTotalSupply: currentTotalSupply.toString(),\n            tokenAddress\n        });\n    } catch (error) {\n        console.error('Error updating max supply:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: `Failed to update max supply: ${error.message}`\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/update-max-supply/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ethers","vendor-chunks/@noble","vendor-chunks/@adraffy","vendor-chunks/aes-js"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fupdate-max-supply%2Froute&page=%2Fapi%2Fadmin%2Fupdate-max-supply%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fupdate-max-supply%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();