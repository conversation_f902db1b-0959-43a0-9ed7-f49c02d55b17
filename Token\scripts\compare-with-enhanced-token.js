const { ethers } = require("hardhat");

async function main() {
  // Enhanced token from previous deployment
  const enhancedTokenAddress = "******************************************"; // Augment_019
  const minimalTokenAddress = "******************************************";
  
  console.log("🔍 COMPARING ENHANCED vs MINIMAL TOKEN FEATURES");
  console.log("=" .repeat(80));
  console.log("Enhanced Token:", enhancedTokenAddress);
  console.log("Minimal Token:", minimalTokenAddress);
  
  const provider = new ethers.JsonRpcProvider("https://rpc-amoy.polygon.technology/");
  
  // Generic ABI for testing function existence
  const testABI = [
    "function name() view returns (string)",
    "function symbol() view returns (string)",
    "function decimals() view returns (uint8)",
    "function version() view returns (string)",
    "function TRANSFER_MANAGER_ROLE() view returns (bytes32)",
    "function forcedTransfer(address,address,uint256) returns (bool)",
    "function freezeTokens(address,uint256)",
    "function unfreezeTokens(address,uint256)",
    "function availableBalanceOf(address) view returns (uint256)",
    "function frozenBalances(address) view returns (uint256)",
    "function acceptAgreement()",
    "function pauseFunction(bytes4)",
    "function unpauseFunction(bytes4)",
    "function setSecurityLevel(uint256)",
    "function isWhitelisted(address) view returns (bool)",
    "function isKycApproved(address) view returns (bool)",
    "function isVerified(address) view returns (bool)",
    "function updateWhitelist(address,bool)",
    "function addToWhitelist(address)",
    "function batchAddToWhitelist(address[])",
    "function approveKyc(address)",
    "function revokeKyc(address)",
    "function isPaused() view returns (bool)",
    "function pause()",
    "function unpause()",
    "function getAllAgents() view returns (address[])",
    "function getAgentCount() view returns (uint256)",
    "function addAgent(address)",
    "function removeAgent(address)",
    "function mint(address,uint256)",
    "function identityRegistry() view returns (address)"
  ];
  
  const enhancedToken = new ethers.Contract(enhancedTokenAddress, testABI, provider);
  const minimalToken = new ethers.Contract(minimalTokenAddress, testABI, provider);
  
  const functionsToTest = [
    "name", "symbol", "decimals", "version",
    "TRANSFER_MANAGER_ROLE", "forcedTransfer", "freezeTokens", "unfreezeTokens",
    "availableBalanceOf", "frozenBalances", "acceptAgreement",
    "pauseFunction", "unpauseFunction", "setSecurityLevel",
    "isWhitelisted", "isKycApproved", "isVerified",
    "updateWhitelist", "addToWhitelist", "batchAddToWhitelist",
    "approveKyc", "revokeKyc", "isPaused", "pause", "unpause",
    "getAllAgents", "getAgentCount", "addAgent", "removeAgent",
    "mint", "identityRegistry"
  ];
  
  console.log("\n📊 FUNCTION AVAILABILITY COMPARISON");
  console.log("-" .repeat(80));
  console.log("Function Name".padEnd(25) + "Enhanced".padEnd(12) + "Minimal".padEnd(12) + "Status");
  console.log("-" .repeat(80));
  
  const results = {
    bothHave: [],
    onlyEnhanced: [],
    onlyMinimal: [],
    neitherHave: []
  };
  
  for (const funcName of functionsToTest) {
    let enhancedHas = false;
    let minimalHas = false;
    
    // Test enhanced token
    try {
      if (funcName === "forcedTransfer" || funcName === "freezeTokens" || funcName === "unfreezeTokens" || 
          funcName === "acceptAgreement" || funcName === "pauseFunction" || funcName === "unpauseFunction" ||
          funcName === "setSecurityLevel" || funcName === "updateWhitelist" || funcName === "addToWhitelist" ||
          funcName === "batchAddToWhitelist" || funcName === "approveKyc" || funcName === "revokeKyc" ||
          funcName === "pause" || funcName === "unpause" || funcName === "addAgent" || funcName === "removeAgent" ||
          funcName === "mint") {
        // For write functions, just check if they exist in the interface
        const fragment = enhancedToken.interface.getFunction(funcName);
        enhancedHas = !!fragment;
      } else {
        // For read functions, actually call them
        await enhancedToken[funcName]();
        enhancedHas = true;
      }
    } catch (error) {
      enhancedHas = false;
    }
    
    // Test minimal token
    try {
      if (funcName === "forcedTransfer" || funcName === "freezeTokens" || funcName === "unfreezeTokens" || 
          funcName === "acceptAgreement" || funcName === "pauseFunction" || funcName === "unpauseFunction" ||
          funcName === "setSecurityLevel" || funcName === "updateWhitelist" || funcName === "addToWhitelist" ||
          funcName === "batchAddToWhitelist" || funcName === "approveKyc" || funcName === "revokeKyc" ||
          funcName === "pause" || funcName === "unpause" || funcName === "addAgent" || funcName === "removeAgent" ||
          funcName === "mint") {
        // For write functions, just check if they exist in the interface
        const fragment = minimalToken.interface.getFunction(funcName);
        minimalHas = !!fragment;
      } else {
        // For read functions, actually call them
        await minimalToken[funcName]();
        minimalHas = true;
      }
    } catch (error) {
      minimalHas = false;
    }
    
    let status = "";
    if (enhancedHas && minimalHas) {
      status = "✅ Both";
      results.bothHave.push(funcName);
    } else if (enhancedHas && !minimalHas) {
      status = "❌ Lost";
      results.onlyEnhanced.push(funcName);
    } else if (!enhancedHas && minimalHas) {
      status = "✅ Added";
      results.onlyMinimal.push(funcName);
    } else {
      status = "⚪ Neither";
      results.neitherHave.push(funcName);
    }
    
    const enhancedStatus = enhancedHas ? "✅" : "❌";
    const minimalStatus = minimalHas ? "✅" : "❌";
    
    console.log(funcName.padEnd(25) + enhancedStatus.padEnd(12) + minimalStatus.padEnd(12) + status);
  }
  
  console.log("\n📈 COMPARISON SUMMARY");
  console.log("=" .repeat(80));
  
  console.log(`✅ Functions in Both Tokens: ${results.bothHave.length}`);
  if (results.bothHave.length > 0) {
    console.log(`   ${results.bothHave.join(', ')}`);
  }
  
  console.log(`\n❌ Functions Lost in Minimal: ${results.onlyEnhanced.length}`);
  if (results.onlyEnhanced.length > 0) {
    console.log(`   ${results.onlyEnhanced.join(', ')}`);
  }
  
  console.log(`\n✅ Functions Added in Minimal: ${results.onlyMinimal.length}`);
  if (results.onlyMinimal.length > 0) {
    console.log(`   ${results.onlyMinimal.join(', ')}`);
  }
  
  console.log(`\n⚪ Functions in Neither: ${results.neitherHave.length}`);
  if (results.neitherHave.length > 0) {
    console.log(`   ${results.neitherHave.join(', ')}`);
  }
  
  console.log("\n🔍 DETAILED FEATURE ANALYSIS");
  console.log("-" .repeat(80));
  
  try {
    // Compare basic info
    const enhancedName = await enhancedToken.name();
    const enhancedSymbol = await enhancedToken.symbol();
    const enhancedVersion = await enhancedToken.version();
    
    const minimalName = await minimalToken.name();
    const minimalSymbol = await minimalToken.symbol();
    const minimalVersion = await minimalToken.version();
    
    console.log("Basic Information:");
    console.log(`  Enhanced: ${enhancedName} (${enhancedSymbol}) v${enhancedVersion}`);
    console.log(`  Minimal:  ${minimalName} (${minimalSymbol}) v${minimalVersion}`);
    
    // Compare whitelist functionality
    const testAddress = "0x56f3726C92B8B92a6ab71983886F91718540d888";
    
    try {
      const enhancedWhitelisted = await enhancedToken.isWhitelisted(testAddress);
      const enhancedKyc = await enhancedToken.isKycApproved(testAddress);
      const enhancedVerified = await enhancedToken.isVerified(testAddress);
      
      console.log("\nEnhanced Token Whitelist Status:");
      console.log(`  Whitelisted: ${enhancedWhitelisted}`);
      console.log(`  KYC Approved: ${enhancedKyc}`);
      console.log(`  Verified: ${enhancedVerified}`);
    } catch (error) {
      console.log("\nEnhanced Token Whitelist: Error checking status");
    }
    
    try {
      const minimalWhitelisted = await minimalToken.isWhitelisted(testAddress);
      const minimalKyc = await minimalToken.isKycApproved(testAddress);
      const minimalVerified = await minimalToken.isVerified(testAddress);
      
      console.log("\nMinimal Token Whitelist Status:");
      console.log(`  Whitelisted: ${minimalWhitelisted}`);
      console.log(`  KYC Approved: ${minimalKyc}`);
      console.log(`  Verified: ${minimalVerified}`);
    } catch (error) {
      console.log("\nMinimal Token Whitelist: Error checking status");
    }
    
  } catch (error) {
    console.log("Error during detailed analysis:", error.message);
  }
  
  console.log("\n🎯 FINAL COMPARISON RESULT");
  console.log("=" .repeat(80));
  
  const totalFunctions = functionsToTest.length;
  const retainedFunctions = results.bothHave.length;
  const lostFunctions = results.onlyEnhanced.length;
  const addedFunctions = results.onlyMinimal.length;
  
  const retentionRate = Math.round((retainedFunctions / (retainedFunctions + lostFunctions)) * 100);
  
  console.log(`📊 Function Retention Rate: ${retentionRate}%`);
  console.log(`✅ Functions Retained: ${retainedFunctions}`);
  console.log(`❌ Functions Lost: ${lostFunctions}`);
  console.log(`✅ Functions Added: ${addedFunctions}`);
  
  console.log("\nThis comparison is based on actual deployed contract calls.");
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
