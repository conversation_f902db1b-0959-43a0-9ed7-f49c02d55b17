// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";

contract SecurityTokenUltraOptimized is ERC20, AccessControl, ReentrancyGuard {
    bytes32 public constant AGENT_ROLE = keccak256("AGENT_ROLE");
    bytes32 public constant TRANSFER_MANAGER_ROLE = keccak256("TRANSFER_MANAGER_ROLE");
    
    uint8 private _decimals;
    uint256 public maxSupply;
    string public tokenPrice;
    string public bonusTiers;
    string public tokenImageUrl;
    address public identityRegistry;
    address public compliance;
    
    address[] public agents;
    mapping(address => bool) public isAgent;
    mapping(address => uint256) private _agreements;
    mapping(address => uint256) public frozenBalances;
    mapping(address => bool) public isWhitelisted;
    mapping(uint256 => bool) public usedClaimIds;
    mapping(address => uint256[]) public accountClaims;
    
    bool private _paused;
    mapping(bytes4 => bool) private _funcPaused;
    uint256 public totalFrozen;
    uint256 public nextClaimId;
    
    bool public feesEnabled;
    uint256 public feePercent;
    address public feeCollector;
    mapping(address => bool) public feeExempt;
    mapping(address => uint256) public customFees;
    uint256 public totalFees;
    
    uint256 public secLevel;
    mapping(address => uint256) public lastAction;
    mapping(address => uint256) public actionCount;
    
    event AgentAdded(address indexed agent);
    event AgentRemoved(address indexed agent);
    event AgreementAccepted(address indexed account, uint256 timestamp);
    event TokensFrozen(address indexed account, uint256 amount);
    event TokensUnfrozen(address indexed account, uint256 amount);
    event WhitelistUpdated(address indexed account, bool whitelisted);
    event ClaimGenerated(uint256 indexed claimId, address indexed account);
    event Paused(address indexed admin);
    event Unpaused(address indexed admin);
    event FeeCollected(address indexed from, address indexed to, uint256 amount, uint256 fee);
    
    modifier onlyAgent() { require(hasRole(AGENT_ROLE, _msgSender()), "!agent"); _; }
    modifier onlyTM() { require(hasRole(TRANSFER_MANAGER_ROLE, _msgSender()), "!tm"); _; }
    modifier notPaused() { require(!_paused && !_funcPaused[msg.sig], "paused"); _; }
    modifier whitelisted(address a) { require(isWhitelisted[a], "!whitelist"); _; }
    
    constructor(
        string memory name, string memory symbol, uint8 decimals_, uint256 maxSupply_,
        address identityRegistry_, address compliance_, address admin,
        string memory tokenPrice_, string memory bonusTiers_, string memory tokenImageUrl_
    ) ERC20(name, symbol) {
        require(admin != address(0) && maxSupply_ > 0 && decimals_ <= 18, "!params");
        
        _decimals = decimals_;
        maxSupply = maxSupply_;
        tokenPrice = tokenPrice_;
        bonusTiers = bonusTiers_;
        tokenImageUrl = tokenImageUrl_;
        identityRegistry = identityRegistry_;
        compliance = compliance_;
        nextClaimId = 1000;
        secLevel = 1;
        
        _grantRole(DEFAULT_ADMIN_ROLE, admin);
        _grantRole(AGENT_ROLE, admin);
        _grantRole(TRANSFER_MANAGER_ROLE, admin);
        
        agents.push(admin);
        isAgent[admin] = true;
        isWhitelisted[admin] = true;
        
        emit AgentAdded(admin);
        emit WhitelistUpdated(admin, true);
    }
    
    function decimals() public view override returns (uint8) { return _decimals; }
    
    function availableBalanceOf(address a) public view returns (uint256) {
        uint256 total = balanceOf(a);
        uint256 frozen = frozenBalances[a];
        return total > frozen ? total - frozen : 0;
    }
    
    function mint(address to, uint256 amount) external onlyAgent nonReentrant notPaused whitelisted(to) {
        require(to != address(0) && amount > 0, "!params");
        require(totalSupply() + amount <= maxSupply, "!supply");
        _validate(msg.sig, _msgSender(), amount);
        _mint(to, amount);
    }
    
    function batchMint(address[] memory recipients, uint256[] memory amounts) external onlyAgent nonReentrant notPaused {
        require(recipients.length == amounts.length && recipients.length <= 100, "!batch");
        uint256 total = 0;
        for (uint256 i = 0; i < amounts.length; i++) {
            require(recipients[i] != address(0) && amounts[i] > 0 && isWhitelisted[recipients[i]], "!recipient");
            total += amounts[i];
        }
        require(totalSupply() + total <= maxSupply, "!supply");
        for (uint256 i = 0; i < recipients.length; i++) {
            _mint(recipients[i], amounts[i]);
        }
    }
    
    function transfer(address to, uint256 amount) public override notPaused whitelisted(to) returns (bool) {
        require(availableBalanceOf(_msgSender()) >= amount, "!balance");
        return _transferWithFees(_msgSender(), to, amount);
    }
    
    function transferFrom(address from, address to, uint256 amount) public override notPaused whitelisted(to) returns (bool) {
        require(availableBalanceOf(from) >= amount, "!balance");
        _spendAllowance(from, _msgSender(), amount);
        return _transferWithFees(from, to, amount);
    }
    
    function forcedTransfer(address from, address to, uint256 amount) external onlyTM nonReentrant notPaused returns (bool) {
        require(from != address(0) && to != address(0) && amount > 0, "!params");
        require(availableBalanceOf(from) >= amount, "!balance");
        _transfer(from, to, amount);
        return true;
    }
    
    function _transferWithFees(address from, address to, uint256 amount) internal returns (bool) {
        if (feesEnabled && !feeExempt[from] && feeCollector != address(0)) {
            uint256 feePercent_ = customFees[from] > 0 ? customFees[from] : feePercent;
            uint256 fee = (amount * feePercent_) / 10000;
            if (fee > 0) {
                _transfer(from, feeCollector, fee);
                totalFees += fee;
                emit FeeCollected(from, to, amount, fee);
                amount -= fee;
            }
        }
        _transfer(from, to, amount);
        return true;
    }
    
    function freezeTokens(address a, uint256 amount) external onlyAgent notPaused {
        require(a != address(0) && amount > 0, "!params");
        require(balanceOf(a) >= frozenBalances[a] + amount, "!balance");
        frozenBalances[a] += amount;
        totalFrozen += amount;
        emit TokensFrozen(a, amount);
    }
    
    function unfreezeTokens(address a, uint256 amount) external onlyAgent notPaused {
        require(a != address(0) && amount > 0, "!params");
        require(frozenBalances[a] >= amount, "!frozen");
        frozenBalances[a] -= amount;
        totalFrozen -= amount;
        emit TokensUnfrozen(a, amount);
    }
    
    function updateWhitelist(address a, bool w) external onlyAgent {
        require(a != address(0), "!account");
        isWhitelisted[a] = w;
        emit WhitelistUpdated(a, w);
    }
    
    function batchUpdateWhitelist(address[] memory accounts, bool[] memory statuses) external onlyAgent {
        require(accounts.length == statuses.length && accounts.length <= 100, "!batch");
        for (uint256 i = 0; i < accounts.length; i++) {
            require(accounts[i] != address(0), "!account");
            isWhitelisted[accounts[i]] = statuses[i];
            emit WhitelistUpdated(accounts[i], statuses[i]);
        }
    }
    
    function generateClaimId(address a) external onlyAgent returns (uint256 id) {
        uint256 entropy = uint256(keccak256(abi.encodePacked(block.timestamp, block.prevrandao, a, nextClaimId, totalSupply(), msg.sender)));
        id = (entropy % 1000000) + nextClaimId;
        while (usedClaimIds[id]) id++;
        usedClaimIds[id] = true;
        accountClaims[a].push(id);
        nextClaimId = id + 1;
        emit ClaimGenerated(id, a);
        return id;
    }
    
    function addAgent(address agent) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(agent != address(0) && !isAgent[agent] && agents.length < 50, "!agent");
        _grantRole(AGENT_ROLE, agent);
        agents.push(agent);
        isAgent[agent] = true;
        emit AgentAdded(agent);
    }
    
    function removeAgent(address agent) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(isAgent[agent] && agents.length > 1, "!remove");
        _revokeRole(AGENT_ROLE, agent);
        isAgent[agent] = false;
        for (uint256 i = 0; i < agents.length; i++) {
            if (agents[i] == agent) {
                agents[i] = agents[agents.length - 1];
                agents.pop();
                break;
            }
        }
        emit AgentRemoved(agent);
    }
    
    function acceptAgreement() external {
        require(_agreements[_msgSender()] == 0, "accepted");
        _agreements[_msgSender()] = block.timestamp;
        emit AgreementAccepted(_msgSender(), block.timestamp);
    }
    
    function pause() external onlyRole(DEFAULT_ADMIN_ROLE) {
        _paused = true;
        emit Paused(_msgSender());
    }
    
    function unpause() external onlyRole(DEFAULT_ADMIN_ROLE) {
        _paused = false;
        emit Unpaused(_msgSender());
    }
    
    function pauseFunction(bytes4 sig) external onlyRole(DEFAULT_ADMIN_ROLE) {
        _funcPaused[sig] = true;
    }
    
    function unpauseFunction(bytes4 sig) external onlyRole(DEFAULT_ADMIN_ROLE) {
        _funcPaused[sig] = false;
    }
    
    function enableFees(uint256 percent, address collector) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(percent <= 1000 && collector != address(0), "!fee");
        feesEnabled = true;
        feePercent = percent;
        feeCollector = collector;
    }
    
    function disableFees() external onlyRole(DEFAULT_ADMIN_ROLE) {
        feesEnabled = false;
        feePercent = 0;
        feeCollector = address(0);
    }
    
    function setFeeExemption(address a, bool exempt) external onlyRole(DEFAULT_ADMIN_ROLE) {
        feeExempt[a] = exempt;
    }
    
    function setCustomFee(address a, uint256 percent) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(percent <= 1000, "!fee");
        customFees[a] = percent;
    }
    
    function setSecurityLevel(uint256 level) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(level >= 1 && level <= 5, "!level");
        secLevel = level;
    }
    
    function _validate(bytes4, address caller, uint256 amount) internal {
        if (secLevel >= 2) {
            require(block.timestamp >= lastAction[caller] + (secLevel * 60), "rate");
        }
        if (secLevel >= 3) {
            require(actionCount[caller] < (100 / secLevel), "limit");
        }
        if (secLevel >= 4 && amount > 0) {
            require(amount <= (1000000 / secLevel), "amount");
        }
        lastAction[caller] = block.timestamp;
        actionCount[caller]++;
        if (block.timestamp % 86400 == 0) actionCount[caller] = 0;
    }
    
    // View functions
    function getAllAgents() external view returns (address[] memory) { return agents; }
    function getAgentCount() external view returns (uint256) { return agents.length; }
    function getAgentAt(uint256 i) external view returns (address) { return agents[i]; }
    function hasAcceptedAgreement(address a) external view returns (bool) { return _agreements[a] > 0; }
    function getAgreementTimestamp(address a) external view returns (uint256) { return _agreements[a]; }
    function getAccountClaims(address a) external view returns (uint256[] memory) { return accountClaims[a]; }
    function isPaused() external view returns (bool) { return _paused; }
    function isFunctionPaused(bytes4 sig) external view returns (bool) { return _funcPaused[sig]; }
    function version() external pure returns (string memory) { return "4.0.0-ultra"; }
}
