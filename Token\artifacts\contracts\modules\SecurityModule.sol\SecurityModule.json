{"_format": "hh-sol-artifact-1", "contractName": "SecurityModule", "sourceName": "contracts/modules/SecurityModule.sol", "abi": [{"inputs": [{"internalType": "address", "name": "_securityToken", "type": "address"}, {"internalType": "address", "name": "_admin", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "admin", "type": "address"}], "name": "EmergencyPaused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "admin", "type": "address"}], "name": "EmergencyUnpaused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes4", "name": "functionSelector", "type": "bytes4"}, {"indexed": true, "internalType": "address", "name": "admin", "type": "address"}], "name": "FunctionPaused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes4", "name": "functionSelector", "type": "bytes4"}, {"indexed": true, "internalType": "address", "name": "admin", "type": "address"}], "name": "FunctionUnpaused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "emergencyPause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "emergencyUnpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "isEmergencyPaused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "functionSelector", "type": "bytes4"}], "name": "isFunctionPaused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "functionSelector", "type": "bytes4"}], "name": "pauseFunction", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "securityToken", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "functionSelector", "type": "bytes4"}], "name": "unpauseFunction", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "functionSelector", "type": "bytes4"}], "name": "validateOperation", "outputs": [], "stateMutability": "view", "type": "function"}], "bytecode": "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", "deployedBytecode": "0x608060408181526004918236101561001657600080fd5b600092833560e01c91826301ffc9a71461057e5750816305b57d89146104fe578163248a9ca3146104d4578163290d10c4146104ad5781632dd1e5f81461042a5781632f2ff15d1461040c57816336568abe146103d15781634a4e3bd51461036b5781634c532de11461032d57816351858e27146102c15781636daef5f51461017c5750806391d1485414610143578063a217fddf14610129578063b84dfbd2146101015763d547741f146100ca57600080fd5b346100fd5760016100f9916100f46100e1366105d1565b9390928387528660205286200154610654565b610716565b5080f35b5080fd5b50346100fd57816003193601126100fd5760025490516001600160a01b039091168152602090f35b50346100fd57816003193601126100fd5751908152602090f35b50346100fd5760ff81602093610158366105d1565b9082528186528282206001600160a01b039091168252855220549151911615158152f35b919050346102bd57602090816003193601126102b957823563ffffffff60e01b81168091036102b5576002546001600160a01b03811633036102595760a01c60ff166102185784526003825260ff81852054166101d7578380f35b5162461bcd60e51b815291820152601f60248201527f53656375726974794d6f64756c653a2066756e6374696f6e2070617573656400604482015260649150fd5b606484848085519262461bcd60e51b845283015260248201527f53656375726974794d6f64756c653a20656d657267656e6379207061757365646044820152fd5b50505162461bcd60e51b815291820152603060248201527f53656375726974794d6f64756c653a2063616c6c6572206973206e6f7420746860448201526f329039b2b1bab934ba3c903a37b5b2b760811b606482015260849150fd5b8480fd5b8380fd5b8280fd5b5050346100fd57816003193601126100fd5760ff6102f29183805283602052808420338552602052832054166105fc565b6002805460ff60a01b1916600160a01b179055337fb8fad2fa0ed7a383e747c309ef2c4391d7b65592a48893e57ccc1fab707914568280a280f35b9050346102bd5760203660031901126102bd573563ffffffff60e01b81168091036102bd57818360ff92602095526003855220541690519015158152f35b5050346100fd57816003193601126100fd5760ff61039c9183805283602052808420338552602052832054166105fc565b6002805460ff60a01b19169055337ff5cbf596165cc457b2cd92e8d8450827ee314968160a5696402d75766fc52caf8280a280f35b8383346100fd576103e1366105d1565b91336001600160a01b038416036103fd5750906100f991610716565b5163334bd91960e11b81528490fd5b5050346100fd5760016100f9916104256100e1366105d1565b610698565b9050346102bd5760203660031901126102bd57359063ffffffff60e01b82168092036102bd578280528260205280832033845260205261046f60ff82852054166105fc565b81835260036020528220805460ff1916600117905533907f37cf9c7afb089036a9b0b180050ebd47f0fb1d71ed1e4e30271b667d420d66bc8380a380f35b5050346100fd57816003193601126100fd5760209060ff60025460a01c1690519015158152f35b9050346102bd5760203660031901126102bd57816020936001923581528085522001549051908152f35b9050346102bd5760203660031901126102bd57359063ffffffff60e01b82168092036102bd578280528260205280832033845260205261054360ff82852054166105fc565b81835260036020528220805460ff1916905533907f1a1b94b44f657b334b61a599f62cdf62aeae8e61435e97db3fd4e78ba458cba68380a380f35b8491346102bd5760203660031901126102bd573563ffffffff60e01b81168091036102bd5760209250637965db0b60e01b81149081156105c0575b5015158152f35b6301ffc9a760e01b149050836105b9565b60409060031901126105f757600435906024356001600160a01b03811681036105f75790565b600080fd5b1561060357565b60405162461bcd60e51b815260206004820152602360248201527f53656375726974794d6f64756c653a2063616c6c6572206973206e6f7420616460448201526236b4b760e91b6064820152608490fd5b80600052600060205260406000203360005260205260ff604060002054161561067a5750565b6044906040519063e2517d3f60e01b82523360048301526024820152fd5b9060009180835282602052604083209160018060a01b03169182845260205260ff6040842054161560001461071157808352826020526040832082845260205260408320600160ff198254161790557f2f8788117e7eff1d82e926ec794901d17c78024a50270940304540a733656f0d339380a4600190565b505090565b9060009180835282602052604083209160018060a01b03169182845260205260ff6040842054166000146107115780835282602052604083208284526020526040832060ff1981541690557ff6391f5c32d9c69d2a47ea670b442974b53935d1edc7fd64eb21e047a839171b339380a460019056fea164736f6c6343000816000a", "linkReferences": {}, "deployedLinkReferences": {}}