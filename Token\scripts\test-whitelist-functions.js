const { ethers } = require("hardhat");

async function main() {
  const tokenAddress = "******************************************";
  const testAddress = "******************************************";
  
  console.log("🔍 TESTING WHITELIST FUNCTIONS");
  console.log("=" .repeat(60));
  console.log("Token Address:", tokenAddress);
  console.log("Test Address:", testAddress);
  
  const [deployer] = await ethers.getSigners();
  const SecurityTokenMinimal = await ethers.getContractFactory("SecurityTokenMinimal");
  const token = SecurityTokenMinimal.attach(tokenAddress);
  
  console.log("\n📋 AVAILABLE WHITELIST FUNCTIONS:");
  
  // Test all whitelist-related functions
  const functionsToTest = [
    "isWhitelisted",
    "updateWhitelist", 
    "addToWhitelist",
    "removeFromWhitelist",
    "batchUpdateWhitelist",
    "batchAddToWhitelist",
    "isKycApproved",
    "approveKyc",
    "revokeKyc",
    "isVerified"
  ];
  
  for (const funcName of functionsToTest) {
    try {
      if (funcName === "isWhitelisted" || funcName === "isKycApproved" || funcName === "isVerified") {
        const result = await token[funcName](testAddress);
        console.log(`✅ ${funcName}(${testAddress}): ${result}`);
      } else {
        // Just check if function exists
        const fragment = token.interface.getFunction(funcName);
        console.log(`✅ ${funcName}: EXISTS`);
      }
    } catch (error) {
      console.log(`❌ ${funcName}: NOT AVAILABLE`);
    }
  }
  
  console.log("\n🔧 TESTING WHITELIST OPERATION:");
  
  try {
    // Check current status
    const isCurrentlyWhitelisted = await token.isWhitelisted(testAddress);
    const isCurrentlyKyc = await token.isKycApproved(testAddress);
    const isCurrentlyVerified = await token.isVerified(testAddress);
    
    console.log(`Current status for ${testAddress}:`);
    console.log(`  Whitelisted: ${isCurrentlyWhitelisted}`);
    console.log(`  KYC Approved: ${isCurrentlyKyc}`);
    console.log(`  Verified: ${isCurrentlyVerified}`);
    
    if (!isCurrentlyWhitelisted) {
      console.log("\n🔧 Adding to whitelist...");
      
      // Try updateWhitelist first (most common for minimal tokens)
      try {
        const tx = await token.updateWhitelist(testAddress, true);
        console.log("✅ updateWhitelist transaction submitted:", tx.hash);
        await tx.wait();
        console.log("✅ Transaction confirmed");
        
        // Check new status
        const newWhitelistStatus = await token.isWhitelisted(testAddress);
        console.log("✅ New whitelist status:", newWhitelistStatus);
        
      } catch (error) {
        console.log("❌ updateWhitelist failed:", error.message);
        
        // Try addToWhitelist as fallback
        try {
          console.log("🔧 Trying addToWhitelist...");
          const tx = await token.addToWhitelist(testAddress);
          console.log("✅ addToWhitelist transaction submitted:", tx.hash);
          await tx.wait();
          console.log("✅ Transaction confirmed");
          
          // Check new status
          const newWhitelistStatus = await token.isWhitelisted(testAddress);
          console.log("✅ New whitelist status:", newWhitelistStatus);
          
        } catch (error2) {
          console.log("❌ addToWhitelist also failed:", error2.message);
        }
      }
    } else {
      console.log("✅ Address is already whitelisted");
    }
    
    // Test KYC approval if not already approved
    if (!isCurrentlyKyc) {
      console.log("\n🔧 Approving KYC...");
      try {
        const tx = await token.approveKyc(testAddress);
        console.log("✅ approveKyc transaction submitted:", tx.hash);
        await tx.wait();
        console.log("✅ Transaction confirmed");
        
        // Check new status
        const newKycStatus = await token.isKycApproved(testAddress);
        const newVerifiedStatus = await token.isVerified(testAddress);
        console.log("✅ New KYC status:", newKycStatus);
        console.log("✅ New verified status:", newVerifiedStatus);
        
      } catch (error) {
        console.log("❌ approveKyc failed:", error.message);
      }
    } else {
      console.log("✅ Address already has KYC approval");
    }
    
  } catch (error) {
    console.error("❌ Error during whitelist testing:", error.message);
  }
  
  console.log("\n🎯 FINAL STATUS CHECK:");
  try {
    const finalWhitelisted = await token.isWhitelisted(testAddress);
    const finalKyc = await token.isKycApproved(testAddress);
    const finalVerified = await token.isVerified(testAddress);
    
    console.log(`Final status for ${testAddress}:`);
    console.log(`  Whitelisted: ${finalWhitelisted}`);
    console.log(`  KYC Approved: ${finalKyc}`);
    console.log(`  Verified: ${finalVerified}`);
    
  } catch (error) {
    console.error("❌ Error checking final status:", error.message);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
