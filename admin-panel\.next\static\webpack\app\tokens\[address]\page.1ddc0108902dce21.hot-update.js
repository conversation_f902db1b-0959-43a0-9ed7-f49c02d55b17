"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/tokens/[address]/page",{

/***/ "(app-pages-browser)/./src/utils/whitelist.ts":
/*!********************************!*\
  !*** ./src/utils/whitelist.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addToWhitelist: () => (/* binding */ addToWhitelist),\n/* harmony export */   approveKyc: () => (/* binding */ approveKyc),\n/* harmony export */   batchAddToWhitelist: () => (/* binding */ batchAddToWhitelist),\n/* harmony export */   batchApproveKyc: () => (/* binding */ batchApproveKyc),\n/* harmony export */   batchFreezeAddresses: () => (/* binding */ batchFreezeAddresses),\n/* harmony export */   batchRemoveFromWhitelist: () => (/* binding */ batchRemoveFromWhitelist),\n/* harmony export */   batchRevokeKyc: () => (/* binding */ batchRevokeKyc),\n/* harmony export */   batchUnfreezeAddresses: () => (/* binding */ batchUnfreezeAddresses),\n/* harmony export */   freezeAddress: () => (/* binding */ freezeAddress),\n/* harmony export */   isFrozen: () => (/* binding */ isFrozen),\n/* harmony export */   isKycApproved: () => (/* binding */ isKycApproved),\n/* harmony export */   isWhitelisted: () => (/* binding */ isWhitelisted),\n/* harmony export */   removeFromWhitelist: () => (/* binding */ removeFromWhitelist),\n/* harmony export */   revokeKyc: () => (/* binding */ revokeKyc),\n/* harmony export */   unfreezeAddress: () => (/* binding */ unfreezeAddress)\n/* harmony export */ });\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-jsonrpc.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var _contracts_Whitelist_json__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../contracts/Whitelist.json */ \"(app-pages-browser)/./src/contracts/Whitelist.json\");\n\n\n/**\r\n * Whitelist utility functions for interacting with the Whitelist contract\r\n */ // Map of network names to their RPC URLs\nconst RPC_URLS = {\n    amoy: 'https://rpc-amoy.polygon.technology/',\n    polygon: 'https://polygon-rpc.com',\n    unknown: 'https://rpc-amoy.polygon.technology/' // Default unknown networks to Amoy\n};\n/**\r\n * Get the actual network to use, defaults to Amoy for 'unknown' or invalid networks\r\n */ function getActualNetwork(network) {\n    return network === 'unknown' || !RPC_URLS[network] ? 'amoy' : network;\n}\n/**\r\n * Add an address to the whitelist\r\n * @param whitelistAddress The address of the whitelist contract\r\n * @param addressToWhitelist The address to be whitelisted\r\n * @param network The network to use (amoy, polygon)\r\n * @returns Transaction result object\r\n */ async function addToWhitelist(whitelistAddress, addressToWhitelist) {\n    let network = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'amoy';\n    const actualNetwork = getActualNetwork(network);\n    return callWhitelistAPI({\n        whitelistAddress,\n        action: 'addToWhitelist',\n        address: addressToWhitelist,\n        network: actualNetwork\n    });\n}\n/**\r\n * Add multiple addresses to the whitelist in a batch\r\n * @param whitelistAddress The address of the whitelist contract\r\n * @param addressesToWhitelist Array of addresses to be whitelisted\r\n * @param network The network to use (amoy, polygon)\r\n * @returns Transaction result object\r\n */ async function batchAddToWhitelist(whitelistAddress, addressesToWhitelist) {\n    let network = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'amoy';\n    const actualNetwork = getActualNetwork(network);\n    return callWhitelistAPI({\n        whitelistAddress,\n        action: 'batchAddToWhitelist',\n        addresses: addressesToWhitelist,\n        network: actualNetwork\n    });\n}\n/**\r\n * Remove an address from the whitelist\r\n * @param whitelistAddress The address of the whitelist contract\r\n * @param addressToRemove The address to be removed from whitelist\r\n * @param network The network to use (amoy, polygon)\r\n * @returns Transaction result object\r\n */ async function removeFromWhitelist(whitelistAddress, addressToRemove) {\n    let network = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'amoy';\n    const actualNetwork = getActualNetwork(network);\n    return callWhitelistAPI({\n        whitelistAddress,\n        action: 'removeFromWhitelist',\n        address: addressToRemove,\n        network: actualNetwork\n    });\n}\n/**\r\n * Remove multiple addresses from the whitelist in a batch\r\n * @param whitelistAddress The address of the whitelist contract\r\n * @param addressesToRemove Array of addresses to be removed\r\n * @param network The network to use (amoy, polygon)\r\n * @returns Transaction result object\r\n */ async function batchRemoveFromWhitelist(whitelistAddress, addressesToRemove) {\n    let network = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'amoy';\n    const actualNetwork = getActualNetwork(network);\n    return callWhitelistAPI({\n        whitelistAddress,\n        action: 'batchRemoveFromWhitelist',\n        addresses: addressesToRemove,\n        network: actualNetwork\n    });\n}\n/**\r\n * Freeze an address\r\n * @param whitelistAddress The address of the whitelist contract\r\n * @param addressToFreeze The address to be frozen\r\n * @param network The network to use (amoy, polygon)\r\n * @returns Transaction result object\r\n */ async function freezeAddress(whitelistAddress, addressToFreeze) {\n    let network = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'amoy';\n    const actualNetwork = getActualNetwork(network);\n    return callWhitelistAPI({\n        whitelistAddress,\n        action: 'freezeAddress',\n        address: addressToFreeze,\n        network: actualNetwork\n    });\n}\n/**\r\n * Unfreeze an address\r\n * @param whitelistAddress The address of the whitelist contract\r\n * @param addressToUnfreeze The address to be unfrozen\r\n * @param network The network to use (amoy, polygon)\r\n * @returns Transaction result object\r\n */ async function unfreezeAddress(whitelistAddress, addressToUnfreeze) {\n    let network = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'amoy';\n    const actualNetwork = getActualNetwork(network);\n    return callWhitelistAPI({\n        whitelistAddress,\n        action: 'unfreezeAddress',\n        address: addressToUnfreeze,\n        network: actualNetwork\n    });\n}\n/**\r\n * Freeze multiple addresses in a batch\r\n * @param whitelistAddress The address of the whitelist contract\r\n * @param addressesToFreeze Array of addresses to be frozen\r\n * @param network The network to use (amoy, polygon)\r\n * @returns Transaction result object\r\n */ async function batchFreezeAddresses(whitelistAddress, addressesToFreeze) {\n    let network = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'amoy';\n    const actualNetwork = getActualNetwork(network);\n    return callWhitelistAPI({\n        whitelistAddress,\n        action: 'batchFreezeAddresses',\n        addresses: addressesToFreeze,\n        network: actualNetwork\n    });\n}\n/**\r\n * Unfreeze multiple addresses in a batch\r\n * @param whitelistAddress The address of the whitelist contract\r\n * @param addressesToUnfreeze Array of addresses to be unfrozen\r\n * @param network The network to use (amoy, polygon)\r\n * @returns Transaction result object\r\n */ async function batchUnfreezeAddresses(whitelistAddress, addressesToUnfreeze) {\n    let network = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'amoy';\n    const actualNetwork = getActualNetwork(network);\n    return callWhitelistAPI({\n        whitelistAddress,\n        action: 'batchUnfreezeAddresses',\n        addresses: addressesToUnfreeze,\n        network: actualNetwork\n    });\n}\n/**\r\n * Check if an address is whitelisted (view function)\r\n * @param whitelistAddress The address of the whitelist contract\r\n * @param addressToCheck The address to check whitelist status\r\n * @param network The network to use (amoy, polygon)\r\n * @returns Promise resolving to boolean indicating whitelist status\r\n */ async function isWhitelisted(whitelistAddress, addressToCheck) {\n    let network = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'amoy';\n    const actualNetwork = getActualNetwork(network);\n    try {\n        const result = await callWhitelistAPI({\n            whitelistAddress,\n            action: 'isWhitelisted',\n            address: addressToCheck,\n            network: actualNetwork\n        });\n        return result.isWhitelisted;\n    } catch (error) {\n        console.error('Error checking whitelist status:', error);\n        // Fallback to direct contract call if API fails\n        return isWhitelistedDirectCall(whitelistAddress, addressToCheck, actualNetwork);\n    }\n}\n/**\r\n * Check if an address is frozen (view function)\r\n * @param whitelistAddress The address of the whitelist contract\r\n * @param addressToCheck The address to check frozen status\r\n * @param network The network to use (amoy, polygon)\r\n * @returns Promise resolving to boolean indicating frozen status\r\n */ async function isFrozen(whitelistAddress, addressToCheck) {\n    let network = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'amoy';\n    const actualNetwork = getActualNetwork(network);\n    try {\n        const result = await callWhitelistAPI({\n            whitelistAddress,\n            action: 'isFrozen',\n            address: addressToCheck,\n            network: actualNetwork\n        });\n        return result.isFrozen;\n    } catch (error) {\n        console.error('Error checking frozen status:', error);\n        // Fallback to direct contract call if API fails\n        return isFrozenDirectCall(whitelistAddress, addressToCheck, actualNetwork);\n    }\n}\n/**\r\n * Check if an address is KYC approved (view function)\r\n * @param whitelistAddress The address of the whitelist contract\r\n * @param addressToCheck The address to check KYC approval status\r\n * @param network The network to use (amoy, polygon)\r\n * @returns Promise resolving to boolean indicating KYC approval status\r\n */ async function isKycApproved(whitelistAddress, addressToCheck) {\n    let network = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'amoy';\n    const actualNetwork = getActualNetwork(network);\n    try {\n        const result = await callWhitelistAPI({\n            whitelistAddress,\n            action: 'isKycApproved',\n            address: addressToCheck,\n            network: actualNetwork\n        });\n        return result.isKycApproved;\n    } catch (error) {\n        console.error('Error checking KYC approval status:', error);\n        // Fallback to direct contract call if API fails\n        return isKycApprovedDirectCall(whitelistAddress, addressToCheck, actualNetwork);\n    }\n}\n/**\r\n * Approve KYC for an address\r\n * @param whitelistAddress The address of the whitelist contract\r\n * @param addressToApprove The address to approve KYC for\r\n * @param network The network to use (amoy, polygon)\r\n * @returns Transaction result object\r\n */ async function approveKyc(whitelistAddress, addressToApprove) {\n    let network = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'amoy';\n    const actualNetwork = getActualNetwork(network);\n    return callWhitelistAPI({\n        whitelistAddress,\n        action: 'approveKyc',\n        address: addressToApprove,\n        network: actualNetwork\n    });\n}\n/**\r\n * Revoke KYC approval for an address\r\n * @param whitelistAddress The address of the whitelist contract\r\n * @param addressToRevoke The address to revoke KYC approval from\r\n * @param network The network to use (amoy, polygon)\r\n * @returns Transaction result object\r\n */ async function revokeKyc(whitelistAddress, addressToRevoke) {\n    let network = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'amoy';\n    const actualNetwork = getActualNetwork(network);\n    return callWhitelistAPI({\n        whitelistAddress,\n        action: 'revokeKyc',\n        address: addressToRevoke,\n        network: actualNetwork\n    });\n}\n/**\r\n * Batch approve KYC for multiple addresses\r\n * @param whitelistAddress The address of the whitelist contract\r\n * @param addressesToApprove Array of addresses to approve KYC for\r\n * @param network The network to use (amoy, polygon)\r\n * @returns Transaction result object\r\n */ async function batchApproveKyc(whitelistAddress, addressesToApprove) {\n    let network = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'amoy';\n    const actualNetwork = getActualNetwork(network);\n    return callWhitelistAPI({\n        whitelistAddress,\n        action: 'batchApproveKyc',\n        addresses: addressesToApprove,\n        network: actualNetwork\n    });\n}\n/**\r\n * Batch revoke KYC approval for multiple addresses\r\n * @param whitelistAddress The address of the whitelist contract\r\n * @param addressesToRevoke Array of addresses to revoke KYC approval from\r\n * @param network The network to use (amoy, polygon)\r\n * @returns Transaction result object\r\n */ async function batchRevokeKyc(whitelistAddress, addressesToRevoke) {\n    let network = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'amoy';\n    const actualNetwork = getActualNetwork(network);\n    return callWhitelistAPI({\n        whitelistAddress,\n        action: 'batchRevokeKyc',\n        addresses: addressesToRevoke,\n        network: actualNetwork\n    });\n}\n// Private helper functions\n/**\r\n * Call the whitelist API\r\n * @param params Object containing API parameters\r\n * @returns Promise resolving to API response\r\n */ async function callWhitelistAPI(params) {\n    // Use full URL when running on server-side (Node.js environment)\n    const baseUrl =  false ? 0 : '';\n    const apiUrl = \"\".concat(baseUrl, \"/api/contracts/whitelist/direct\");\n    const response = await fetch(apiUrl, {\n        method: 'POST',\n        headers: {\n            'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(params)\n    });\n    const data = await response.json();\n    if (!response.ok) {\n        throw new Error(data.error || 'API call failed');\n    }\n    return data;\n}\n/**\r\n * Direct contract call fallback for isWhitelisted\r\n */ async function isWhitelistedDirectCall(whitelistAddress, addressToCheck) {\n    let network = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'amoy';\n    try {\n        // Get RPC URL for the specified network\n        const rpcUrl = RPC_URLS[network] || RPC_URLS.amoy;\n        // Set up provider\n        const provider = new ethers__WEBPACK_IMPORTED_MODULE_1__.JsonRpcProvider(rpcUrl);\n        // Connect to contract\n        const whitelistContract = new ethers__WEBPACK_IMPORTED_MODULE_2__.Contract(whitelistAddress, _contracts_Whitelist_json__WEBPACK_IMPORTED_MODULE_0__.abi, provider);\n        // Call view function\n        return await whitelistContract.isWhitelisted(addressToCheck);\n    } catch (error) {\n        console.error('Error in direct contract call for isWhitelisted:', error);\n        return false; // Default to false on error\n    }\n}\n/**\r\n * Direct contract call fallback for isFrozen\r\n */ async function isFrozenDirectCall(whitelistAddress, addressToCheck) {\n    let network = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'amoy';\n    try {\n        // Get RPC URL for the specified network\n        const rpcUrl = RPC_URLS[network] || RPC_URLS.amoy;\n        // Set up provider\n        const provider = new ethers__WEBPACK_IMPORTED_MODULE_1__.JsonRpcProvider(rpcUrl);\n        // Connect to contract\n        const whitelistContract = new ethers__WEBPACK_IMPORTED_MODULE_2__.Contract(whitelistAddress, _contracts_Whitelist_json__WEBPACK_IMPORTED_MODULE_0__.abi, provider);\n        // Call view function\n        return await whitelistContract.isFrozen(addressToCheck);\n    } catch (error) {\n        console.error('Error in direct contract call for isFrozen:', error);\n        return false; // Default to false on error\n    }\n}\n/**\r\n * Direct contract call fallback for isKycApproved\r\n */ async function isKycApprovedDirectCall(whitelistAddress, addressToCheck) {\n    let network = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'amoy';\n    try {\n        // Get RPC URL for the specified network\n        const rpcUrl = RPC_URLS[network] || RPC_URLS.amoy;\n        // Set up provider\n        const provider = new ethers__WEBPACK_IMPORTED_MODULE_1__.JsonRpcProvider(rpcUrl);\n        // Connect to contract\n        const whitelistContract = new ethers__WEBPACK_IMPORTED_MODULE_2__.Contract(whitelistAddress, _contracts_Whitelist_json__WEBPACK_IMPORTED_MODULE_0__.abi, provider);\n        // Call isKycApproved function\n        const result = await whitelistContract.isKycApproved(addressToCheck);\n        return result;\n    } catch (error) {\n        console.error('Error in direct contract call for isKycApproved:', error);\n        return false;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/whitelist.ts\n"));

/***/ })

});