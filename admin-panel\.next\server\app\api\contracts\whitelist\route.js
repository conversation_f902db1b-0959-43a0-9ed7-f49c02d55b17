/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/contracts/whitelist/route";
exports.ids = ["app/api/contracts/whitelist/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcontracts%2Fwhitelist%2Froute&page=%2Fapi%2Fcontracts%2Fwhitelist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontracts%2Fwhitelist%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcontracts%2Fwhitelist%2Froute&page=%2Fapi%2Fcontracts%2Fwhitelist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontracts%2Fwhitelist%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_tokendev_newroo_admin_panel_src_app_api_contracts_whitelist_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/contracts/whitelist/route.ts */ \"(rsc)/./src/app/api/contracts/whitelist/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/contracts/whitelist/route\",\n        pathname: \"/api/contracts/whitelist\",\n        filename: \"route\",\n        bundlePath: \"app/api/contracts/whitelist/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api\\\\contracts\\\\whitelist\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_tokendev_newroo_admin_panel_src_app_api_contracts_whitelist_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcontracts%2Fwhitelist%2Froute&page=%2Fapi%2Fcontracts%2Fwhitelist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontracts%2Fwhitelist%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/contracts/whitelist/route.ts":
/*!**************************************************!*\
  !*** ./src/app/api/contracts/whitelist/route.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/wallet/wallet.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/providers/provider-jsonrpc.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/constants/addresses.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/crypto/keccak.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/utils/utf8.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _contracts_SecurityToken_json__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../contracts/SecurityToken.json */ \"(rsc)/./src/contracts/SecurityToken.json\");\n/* harmony import */ var _contracts_Whitelist_json__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../contracts/Whitelist.json */ \"(rsc)/./src/contracts/Whitelist.json\");\n\n\n\n\n// Load private key from environment variable - in production, use a proper secrets management system\nconst PRIVATE_KEY = process.env.CONTRACT_ADMIN_PRIVATE_KEY;\nconst RPC_URLS = {\n    amoy: process.env.AMOY_RPC_URL || 'https://rpc-amoy.polygon.technology/',\n    polygon: process.env.POLYGON_RPC_URL || 'https://polygon-rpc.com',\n    unknown: process.env.AMOY_RPC_URL || 'https://rpc-amoy.polygon.technology/'\n};\n// Debug log for wallet derivation\nif (PRIVATE_KEY) {\n    try {\n        const wallet = new ethers__WEBPACK_IMPORTED_MODULE_3__.Wallet(PRIVATE_KEY);\n        console.log('Admin wallet derived from private key:', wallet.address);\n        // Check if this matches the expected address\n        const expectedAddress = '******************************************';\n        if (wallet.address.toLowerCase() !== expectedAddress.toLowerCase()) {\n            console.warn('WARNING: Derived wallet address does not match expected address!');\n            console.warn(`Expected: ${expectedAddress}, Derived: ${wallet.address}`);\n        } else {\n            console.log('✅ Wallet address verification passed');\n        }\n    } catch (error) {\n        console.error('Error deriving wallet from private key:', error.message);\n    }\n} else {\n    console.warn('CONTRACT_ADMIN_PRIVATE_KEY environment variable not set');\n}\n// Network chain IDs\nconst CHAIN_IDS = {\n    amoy: 80002,\n    polygon: 137,\n    unknown: 80002\n};\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { tokenAddress, action, address, addresses, network = 'amoy' } = body;\n        if (!tokenAddress || !action) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Token address and action are required'\n            }, {\n                status: 400\n            });\n        }\n        if (!PRIVATE_KEY) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'CONTRACT_ADMIN_PRIVATE_KEY environment variable not set',\n                details: 'For security reasons, the API requires a secure method to sign transactions.',\n                clientSideInstructions: true,\n                message: 'The server is not configured with admin credentials. You can still use the token management script:',\n                commandExample: `# Unix/Linux/Mac:\\nexport TOKEN_ADDRESS=${tokenAddress}\\nexport ACTION=${action}\\nexport ADDRESS=${address || ''}\\nexport ADDRESSES=${addresses ? addresses.join(',') : ''}\\nnpx hardhat run scripts/05-manage-token.js --network ${network === 'unknown' ? 'amoy' : network}\\n\\n# Windows Command Prompt:\\nset TOKEN_ADDRESS=${tokenAddress}\\nset ACTION=${action}\\nset ADDRESS=${address || ''}\\nset ADDRESSES=${addresses ? addresses.join(',') : ''}\\nnpx hardhat run scripts/05-manage-token.js --network ${network === 'unknown' ? 'amoy' : network}\\n\\n# Windows PowerShell:\\n$env:TOKEN_ADDRESS=\"${tokenAddress}\"\\n$env:ACTION=\"${action}\"\\n$env:ADDRESS=\"${address || ''}\"\\n$env:ADDRESSES=\"${addresses ? addresses.join(',') : ''}\"\\nnpx hardhat run scripts/05-manage-token.js --network ${network === 'unknown' ? 'amoy' : network}`\n            }, {\n                status: 422\n            } // 422 Unprocessable Entity\n            );\n        }\n        // Get RPC URL for the specified network, defaulting to Amoy\n        const actualNetwork = network === 'unknown' ? 'amoy' : network;\n        const rpcUrl = RPC_URLS[actualNetwork] || RPC_URLS.amoy;\n        const chainId = CHAIN_IDS[actualNetwork] || CHAIN_IDS.amoy;\n        console.log(`Using network: ${actualNetwork}, RPC URL: ${rpcUrl}, Chain ID: ${chainId}`);\n        // Validate inputs based on action\n        if ([\n            'addToWhitelist',\n            'removeFromWhitelist',\n            'freezeAddress',\n            'unfreezeAddress'\n        ].includes(action) && !address) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Address is required for this action'\n            }, {\n                status: 400\n            });\n        }\n        if (action === 'batchAddToWhitelist' && (!addresses || !Array.isArray(addresses) || addresses.length === 0)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Addresses array is required for batch operations'\n            }, {\n                status: 400\n            });\n        }\n        // Connect to the network with explicit chainId\n        const provider = new ethers__WEBPACK_IMPORTED_MODULE_4__.JsonRpcProvider(rpcUrl, {\n            chainId,\n            name: actualNetwork\n        });\n        // Ensure the network is connected and recognized\n        const network_details = await provider.getNetwork();\n        console.log(`Connected to network: ${network_details.name} (Chain ID: ${network_details.chainId})`);\n        // Create the wallet with the correctly configured provider\n        const wallet = new ethers__WEBPACK_IMPORTED_MODULE_3__.Wallet(PRIVATE_KEY, provider);\n        console.log(`Wallet address: ${wallet.address}, Wallet balance: ${await provider.getBalance(wallet.address)}`);\n        // Double-check the wallet address\n        if (wallet.address.toLowerCase() !== '******************************************'.toLowerCase()) {\n            console.error('❌ WARNING: Wallet address mismatch when connecting to provider!');\n            console.error('This could indicate an issue with the environment variable or private key format');\n        }\n        // Step 1: Connect to the token contract\n        const tokenContract = new ethers__WEBPACK_IMPORTED_MODULE_5__.Contract(tokenAddress, _contracts_SecurityToken_json__WEBPACK_IMPORTED_MODULE_1__.abi, wallet);\n        // Step 2: Check if this is an enhanced factory token with built-in whitelist\n        console.log(\"Checking token type...\");\n        // Try to get identityRegistry address\n        let whitelistAddress;\n        let whitelistContract;\n        try {\n            whitelistAddress = await tokenContract.identityRegistry();\n            console.log(`Identity registry address: ${whitelistAddress}`);\n            if (whitelistAddress && whitelistAddress !== ethers__WEBPACK_IMPORTED_MODULE_6__.ZeroAddress) {\n                // This token has a separate identity registry contract\n                whitelistContract = new ethers__WEBPACK_IMPORTED_MODULE_5__.Contract(whitelistAddress, _contracts_Whitelist_json__WEBPACK_IMPORTED_MODULE_2__.abi, wallet);\n            } else {\n                // This is an enhanced token with built-in whitelist functionality\n                console.log(\"Using token contract for whitelist operations (enhanced token)\");\n                whitelistContract = tokenContract; // Use the token contract itself\n            }\n        } catch (error) {\n            // Fallback: use the token contract itself for whitelist operations\n            console.log(\"Fallback: using token contract for whitelist operations\");\n            whitelistContract = tokenContract;\n        }\n        // Execute the requested action\n        let tx;\n        let result;\n        try {\n            switch(action){\n                case 'addToWhitelist':\n                    console.log(`Adding ${address} to whitelist...`);\n                    if (whitelistContract === tokenContract) {\n                        // Enhanced token - check what functions are available\n                        try {\n                            // Try built-in whitelist function first\n                            tx = await whitelistContract.updateWhitelist(address, true);\n                            console.log(\"✅ Used updateWhitelist function\");\n                        } catch (updateError) {\n                            console.log(\"❌ updateWhitelist not available, trying addToWhitelist\");\n                            try {\n                                tx = await whitelistContract.addToWhitelist(address);\n                                console.log(\"✅ Used addToWhitelist function\");\n                            } catch (addError) {\n                                console.log(\"❌ addToWhitelist not available either\");\n                                // For enhanced tokens without whitelist functions, we need to return an informative error\n                                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                                    success: false,\n                                    error: \"This enhanced token doesn't have whitelist management functions\",\n                                    details: \"This version of the enhanced token (3.0.0-enhanced) has KYC functionality but doesn't include whitelist management functions. Whitelist management may need to be done through the token admin or a separate contract.\",\n                                    tokenType: \"enhanced-without-whitelist\",\n                                    suggestion: \"Contact the token administrator to add addresses to the whitelist manually or upgrade to a newer token version with built-in whitelist functions.\"\n                                }, {\n                                    status: 422\n                                });\n                            }\n                        }\n                    } else {\n                        // Separate whitelist contract\n                        tx = await whitelistContract.addToWhitelist(address);\n                        console.log(\"✅ Used separate whitelist contract\");\n                    }\n                    break;\n                case 'batchAddToWhitelist':\n                    console.log(`Batch adding ${addresses.length} addresses to whitelist...`);\n                    tx = await whitelistContract.batchAddToWhitelist(addresses);\n                    break;\n                case 'removeFromWhitelist':\n                    tx = await whitelistContract.removeFromWhitelist(address);\n                    break;\n                case 'freezeAddress':\n                    tx = await whitelistContract.freezeAddress(address);\n                    break;\n                case 'unfreezeAddress':\n                    tx = await whitelistContract.unfreezeAddress(address);\n                    break;\n                case 'isWhitelisted':\n                    // This is a view function, no transaction needed\n                    if (whitelistContract === tokenContract) {\n                        // Enhanced token - check what functions are available\n                        try {\n                            result = await whitelistContract.isWhitelisted(address);\n                            console.log(\"✅ Used built-in isWhitelisted function\");\n                        } catch (error) {\n                            console.log(\"❌ isWhitelisted not available on this enhanced token\");\n                            // For enhanced tokens without whitelist functions, return a default response\n                            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                                success: true,\n                                isWhitelisted: false,\n                                note: \"This enhanced token doesn't have built-in whitelist query functions. Whitelist status cannot be determined via API.\",\n                                tokenType: \"enhanced-without-whitelist\"\n                            });\n                        }\n                    } else {\n                        // Separate whitelist contract\n                        result = await whitelistContract.isWhitelisted(address);\n                        console.log(\"✅ Used separate whitelist contract\");\n                    }\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: true,\n                        isWhitelisted: result\n                    });\n                default:\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: 'Invalid action. Supported actions: addToWhitelist, batchAddToWhitelist, removeFromWhitelist, freezeAddress, unfreezeAddress, isWhitelisted'\n                    }, {\n                        status: 400\n                    });\n            }\n            console.log(`Transaction hash: ${tx.hash}`);\n            // Wait for the transaction to be mined\n            const receipt = await tx.wait();\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                action,\n                txHash: tx.hash,\n                blockNumber: receipt.blockNumber\n            });\n        } catch (txError) {\n            console.error('Transaction error:', txError);\n            // Check if the issue might be a permissions problem\n            try {\n                const hasAgentRole = await whitelistContract.hasRole(ethers__WEBPACK_IMPORTED_MODULE_7__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_8__.toUtf8Bytes(\"AGENT_ROLE\")), wallet.address);\n                if (!hasAgentRole) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: false,\n                        error: \"The connected wallet doesn't have the AGENT_ROLE on the whitelist contract\",\n                        details: `Please grant AGENT_ROLE to ${wallet.address} on the whitelist contract (${whitelistAddress})`\n                    }, {\n                        status: 403\n                    });\n                }\n            } catch (roleCheckError) {\n                console.error('Role check error:', roleCheckError);\n            }\n            throw txError; // Re-throw to be caught by the outer catch\n        }\n    } catch (error) {\n        console.error('Error managing whitelist:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error.message || 'An unknown error occurred'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/contracts/whitelist/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/contracts/SecurityToken.json":
/*!******************************************!*\
  !*** ./src/contracts/SecurityToken.json ***!
  \******************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"abi":[{"inputs":[],"stateMutability":"nonpayable","type":"constructor"},{"inputs":[],"name":"AccessControlBadConfirmation","type":"error"},{"inputs":[{"internalType":"address","name":"account","type":"address"},{"internalType":"bytes32","name":"neededRole","type":"bytes32"}],"name":"AccessControlUnauthorizedAccount","type":"error"},{"inputs":[{"internalType":"address","name":"target","type":"address"}],"name":"AddressEmptyCode","type":"error"},{"inputs":[{"internalType":"address","name":"implementation","type":"address"}],"name":"ERC1967InvalidImplementation","type":"error"},{"inputs":[],"name":"ERC1967NonPayable","type":"error"},{"inputs":[{"internalType":"address","name":"spender","type":"address"},{"internalType":"uint256","name":"allowance","type":"uint256"},{"internalType":"uint256","name":"needed","type":"uint256"}],"name":"ERC20InsufficientAllowance","type":"error"},{"inputs":[{"internalType":"address","name":"sender","type":"address"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"uint256","name":"needed","type":"uint256"}],"name":"ERC20InsufficientBalance","type":"error"},{"inputs":[{"internalType":"address","name":"approver","type":"address"}],"name":"ERC20InvalidApprover","type":"error"},{"inputs":[{"internalType":"address","name":"receiver","type":"address"}],"name":"ERC20InvalidReceiver","type":"error"},{"inputs":[{"internalType":"address","name":"sender","type":"address"}],"name":"ERC20InvalidSender","type":"error"},{"inputs":[{"internalType":"address","name":"spender","type":"address"}],"name":"ERC20InvalidSpender","type":"error"},{"inputs":[],"name":"EnforcedPause","type":"error"},{"inputs":[],"name":"ExpectedPause","type":"error"},{"inputs":[],"name":"FailedCall","type":"error"},{"inputs":[],"name":"InvalidInitialization","type":"error"},{"inputs":[],"name":"NotInitializing","type":"error"},{"inputs":[],"name":"ReentrancyGuardReentrantCall","type":"error"},{"inputs":[],"name":"UUPSUnauthorizedCallContext","type":"error"},{"inputs":[{"internalType":"bytes32","name":"slot","type":"bytes32"}],"name":"UUPSUnsupportedProxiableUUID","type":"error"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"agent","type":"address"}],"name":"AgentAdded","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"agent","type":"address"}],"name":"AgentRemoved","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":false,"internalType":"uint256","name":"timestamp","type":"uint256"}],"name":"AgreementAccepted","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"owner","type":"address"},{"indexed":true,"internalType":"address","name":"spender","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"Approval","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"oldCompliance","type":"address"},{"indexed":true,"internalType":"address","name":"newCompliance","type":"address"}],"name":"ComplianceUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"bool","name":"enabled","type":"bool"}],"name":"ConditionalTransfersUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"admin","type":"address"}],"name":"EmergencyPaused","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"admin","type":"address"}],"name":"EmergencyUnpaused","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"from","type":"address"},{"indexed":true,"internalType":"address","name":"to","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"ForcedTransfer","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes4","name":"functionSelector","type":"bytes4"},{"indexed":true,"internalType":"address","name":"admin","type":"address"}],"name":"FunctionPaused","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes4","name":"functionSelector","type":"bytes4"},{"indexed":true,"internalType":"address","name":"admin","type":"address"}],"name":"FunctionUnpaused","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"oldRegistry","type":"address"},{"indexed":true,"internalType":"address","name":"newRegistry","type":"address"}],"name":"IdentityRegistryUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"uint64","name":"version","type":"uint64"}],"name":"Initialized","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"uint256","name":"oldMaxSupply","type":"uint256"},{"indexed":false,"internalType":"uint256","name":"newMaxSupply","type":"uint256"}],"name":"MaxSupplyUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"address","name":"account","type":"address"}],"name":"Paused","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"bytes32","name":"previousAdminRole","type":"bytes32"},{"indexed":true,"internalType":"bytes32","name":"newAdminRole","type":"bytes32"}],"name":"RoleAdminChanged","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":true,"internalType":"address","name":"sender","type":"address"}],"name":"RoleGranted","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":true,"internalType":"address","name":"sender","type":"address"}],"name":"RoleRevoked","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"string","name":"tokenImageUrl","type":"string"}],"name":"TokenImageUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"string","name":"tokenPrice","type":"string"},{"indexed":false,"internalType":"string","name":"bonusTiers","type":"string"},{"indexed":false,"internalType":"string","name":"tokenDetails","type":"string"}],"name":"TokenMetadataUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"from","type":"address"},{"indexed":true,"internalType":"address","name":"to","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"Transfer","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"transferId","type":"bytes32"},{"indexed":true,"internalType":"address","name":"from","type":"address"},{"indexed":true,"internalType":"address","name":"to","type":"address"},{"indexed":false,"internalType":"uint256","name":"amount","type":"uint256"}],"name":"TransferApproved","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"from","type":"address"},{"indexed":true,"internalType":"address","name":"to","type":"address"},{"indexed":false,"internalType":"uint256","name":"transferAmount","type":"uint256"},{"indexed":false,"internalType":"uint256","name":"feeAmount","type":"uint256"}],"name":"TransferFeeCollected","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"bool","name":"enabled","type":"bool"},{"indexed":false,"internalType":"uint256","name":"feePercentage","type":"uint256"},{"indexed":false,"internalType":"address","name":"feeCollector","type":"address"}],"name":"TransferFeesUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":false,"internalType":"bool","name":"whitelisted","type":"bool"}],"name":"TransferWhitelistAddressUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"bool","name":"enabled","type":"bool"}],"name":"TransferWhitelistUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"address","name":"account","type":"address"}],"name":"Unpaused","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"implementation","type":"address"}],"name":"Upgraded","type":"event"},{"inputs":[],"name":"AGENT_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"DEFAULT_ADMIN_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"TRANSFER_MANAGER_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"UPGRADE_INTERFACE_VERSION","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"acceptAgreement","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"agent","type":"address"}],"name":"addAgent","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"addToWhitelist","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"owner","type":"address"},{"internalType":"address","name":"spender","type":"address"}],"name":"allowance","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"spender","type":"address"},{"internalType":"uint256","name":"value","type":"uint256"}],"name":"approve","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"from","type":"address"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"uint256","name":"nonce","type":"uint256"}],"name":"approveTransfer","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"balanceOf","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address[]","name":"accounts","type":"address[]"}],"name":"batchAddToWhitelist","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"bonusTiers","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"uint256","name":"value","type":"uint256"}],"name":"burn","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"},{"internalType":"uint256","name":"value","type":"uint256"}],"name":"burnFrom","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"from","type":"address"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"}],"name":"canTransfer","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"compliance","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"conditionalTransfersEnabled","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"decimals","outputs":[{"internalType":"uint8","name":"","type":"uint8"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"emergencyPause","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"emergencyUnpause","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"uint256","name":"nonce","type":"uint256"}],"name":"executeApprovedTransfer","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"from","type":"address"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"}],"name":"forcedTransfer","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"freezeAddress","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getAgentAt","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"getAgentCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"getAgreementAcceptanceTimestamp","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"getAllAgents","outputs":[{"internalType":"address[]","name":"","type":"address[]"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"}],"name":"getRoleAdmin","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"getTransferFeeConfig","outputs":[{"internalType":"uint256","name":"feePercentage","type":"uint256"},{"internalType":"address","name":"feeCollector","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"getTransferNonce","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"grantRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"hasAcceptedAgreement","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"hasRole","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"identityRegistry","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"string","name":"name_","type":"string"},{"internalType":"string","name":"symbol_","type":"string"},{"internalType":"uint8","name":"decimals_","type":"uint8"},{"internalType":"uint256","name":"maxSupply_","type":"uint256"},{"internalType":"address","name":"identityRegistry_","type":"address"},{"internalType":"address","name":"compliance_","type":"address"},{"internalType":"address","name":"admin_","type":"address"},{"internalType":"string","name":"tokenPrice_","type":"string"},{"internalType":"string","name":"bonusTiers_","type":"string"},{"internalType":"string","name":"tokenDetails_","type":"string"},{"internalType":"string","name":"tokenImageUrl_","type":"string"}],"name":"initialize","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"investorCountry","outputs":[{"internalType":"uint16","name":"","type":"uint16"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"isAgent","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"isEmergencyPaused","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes4","name":"functionSelector","type":"bytes4"}],"name":"isFunctionPaused","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"isTransferWhitelisted","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"isVerified","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"isWhitelisted","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"maxSupply","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"}],"name":"mint","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"name","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"pause","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes4","name":"functionSelector","type":"bytes4"}],"name":"pauseFunction","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"paused","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"proxiableUUID","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"agent","type":"address"}],"name":"removeAgent","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"removeFromWhitelist","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"callerConfirmation","type":"address"}],"name":"renounceRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"revokeRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bool","name":"enabled","type":"bool"}],"name":"setConditionalTransfers","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"uint256","name":"feePercentage","type":"uint256"},{"internalType":"address","name":"feeCollector","type":"address"}],"name":"setTransferFees","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bool","name":"enabled","type":"bool"}],"name":"setTransferWhitelist","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"},{"internalType":"bool","name":"whitelisted","type":"bool"}],"name":"setTransferWhitelistAddress","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes4","name":"interfaceId","type":"bytes4"}],"name":"supportsInterface","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"symbol","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"tokenDetails","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"tokenImageUrl","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"tokenPrice","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"totalSupply","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"value","type":"uint256"}],"name":"transfer","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"transferFeesEnabled","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"from","type":"address"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"value","type":"uint256"}],"name":"transferFrom","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"transferWhitelistEnabled","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"unfreezeAddress","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"unpause","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes4","name":"functionSelector","type":"bytes4"}],"name":"unpauseFunction","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"newCompliance","type":"address"}],"name":"updateCompliance","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"newIdentityRegistry","type":"address"}],"name":"updateIdentityRegistry","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"uint256","name":"newMaxSupply","type":"uint256"}],"name":"updateMaxSupply","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"string","name":"tokenImageUrl_","type":"string"}],"name":"updateTokenImageUrl","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"string","name":"tokenPrice_","type":"string"},{"internalType":"string","name":"bonusTiers_","type":"string"},{"internalType":"string","name":"tokenDetails_","type":"string"}],"name":"updateTokenMetadata","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"newImplementation","type":"address"},{"internalType":"bytes","name":"data","type":"bytes"}],"name":"upgradeToAndCall","outputs":[],"stateMutability":"payable","type":"function"},{"inputs":[],"name":"version","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"pure","type":"function"}]}');

/***/ }),

/***/ "(rsc)/./src/contracts/Whitelist.json":
/*!**************************************!*\
  !*** ./src/contracts/Whitelist.json ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"abi":[{"inputs":[],"stateMutability":"nonpayable","type":"constructor"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"account","type":"address"}],"name":"AddedToWhitelist","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"account","type":"address"}],"name":"AddressFrozen","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"account","type":"address"}],"name":"AddressUnfrozen","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"uint8","name":"version","type":"uint8"}],"name":"Initialized","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"account","type":"address"}],"name":"KycApproved","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"account","type":"address"}],"name":"KycRevoked","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"account","type":"address"}],"name":"RemovedFromWhitelist","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"bytes32","name":"previousAdminRole","type":"bytes32"},{"indexed":true,"internalType":"bytes32","name":"newAdminRole","type":"bytes32"}],"name":"RoleAdminChanged","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":true,"internalType":"address","name":"sender","type":"address"}],"name":"RoleGranted","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":true,"internalType":"address","name":"sender","type":"address"}],"name":"RoleRevoked","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"implementation","type":"address"}],"name":"Upgraded","type":"event"},{"inputs":[],"name":"AGENT_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"DEFAULT_ADMIN_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"_agent","type":"address"}],"name":"addAgent","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"_address","type":"address"}],"name":"addToWhitelist","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address[]","name":"_addresses","type":"address[]"}],"name":"batchAddToWhitelist","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address[]","name":"_addresses","type":"address[]"}],"name":"batchFreezeAddresses","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address[]","name":"_addresses","type":"address[]"}],"name":"batchRemoveFromWhitelist","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address[]","name":"_addresses","type":"address[]"}],"name":"batchUnfreezeAddresses","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"_address","type":"address"}],"name":"freezeAddress","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"}],"name":"getRoleAdmin","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"grantRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"hasRole","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"admin","type":"address"}],"name":"initialize","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"_admin","type":"address"}],"name":"initializeWithAgent","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"_address","type":"address"}],"name":"isFrozen","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"_address","type":"address"}],"name":"isWhitelisted","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"proxiableUUID","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"_agent","type":"address"}],"name":"removeAgent","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"_address","type":"address"}],"name":"removeFromWhitelist","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"renounceRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"revokeRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes4","name":"interfaceId","type":"bytes4"}],"name":"supportsInterface","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"_address","type":"address"}],"name":"unfreezeAddress","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"newImplementation","type":"address"}],"name":"upgradeTo","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"newImplementation","type":"address"},{"internalType":"bytes","name":"data","type":"bytes"}],"name":"upgradeToAndCall","outputs":[],"stateMutability":"payable","type":"function"},{"inputs":[{"internalType":"address","name":"_address","type":"address"}],"name":"approveKyc","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address[]","name":"_addresses","type":"address[]"}],"name":"batchApproveKyc","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address[]","name":"_addresses","type":"address[]"}],"name":"batchRevokeKyc","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"_address","type":"address"}],"name":"isKycApproved","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"_address","type":"address"}],"name":"revokeKyc","outputs":[],"stateMutability":"nonpayable","type":"function"}]}');

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ethers","vendor-chunks/@noble","vendor-chunks/@adraffy","vendor-chunks/aes-js"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcontracts%2Fwhitelist%2Froute&page=%2Fapi%2Fcontracts%2Fwhitelist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontracts%2Fwhitelist%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();