"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/create-modular-token/page",{

/***/ "(app-pages-browser)/./src/app/create-modular-token/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/create-modular-token/page.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CreateModularTokenPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// Contract addresses from environment\nconst SECURITY_TOKEN_CORE_ADDRESS = \"******************************************\";\nconst UPGRADE_MANAGER_ADDRESS = \"******************************************\";\nconst MODULAR_TOKEN_FACTORY_ADDRESS = \"******************************************\";\nfunction CreateModularTokenPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    // State Management\n    const [provider, setProvider] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [signer, setSigner] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deployedToken, setDeployedToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        symbol: '',\n        decimals: 0,\n        maxSupply: '10000000',\n        adminAddress: '',\n        tokenPrice: '1.00 USD',\n        bonusTiers: 'Early Bird: 10%, Standard: 5%, Late: 0%',\n        tokenDetails: 'Security token with advanced compliance features',\n        tokenImageUrl: ''\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CreateModularTokenPage.useEffect\": ()=>{\n            initializeProvider();\n        }\n    }[\"CreateModularTokenPage.useEffect\"], []);\n    const initializeProvider = async ()=>{\n        try {\n            if ( true && window.ethereum) {\n                const provider = new ethers__WEBPACK_IMPORTED_MODULE_4__.BrowserProvider(window.ethereum);\n                await provider.send('eth_requestAccounts', []);\n                const signer = await provider.getSigner();\n                const address = await signer.getAddress();\n                setProvider(provider);\n                setSigner(signer);\n                setFormData((prev)=>({\n                        ...prev,\n                        adminAddress: address\n                    }));\n            } else {\n                setError('MetaMask not found. Please install MetaMask to create tokens.');\n            }\n        } catch (error) {\n            console.error('Error initializing provider:', error);\n            setError('Failed to connect to wallet');\n        }\n    };\n    const handleInputChange = (e)=>{\n        const { name, value, type } = e.target;\n        let processedValue = value;\n        if (name === 'decimals') {\n            processedValue = parseInt(value, 10);\n        }\n        setFormData({\n            ...formData,\n            [name]: processedValue\n        });\n    };\n    const deployModularToken = async ()=>{\n        if (!signer || !MODULAR_TOKEN_FACTORY_ADDRESS) {\n            setError('Please connect your wallet first');\n            return;\n        }\n        setIsSubmitting(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            // Import the ModularTokenFactory ABI\n            const ModularTokenFactoryABI = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_contracts_ModularTokenFactory_json\").then(__webpack_require__.t.bind(__webpack_require__, /*! ../../contracts/ModularTokenFactory.json */ \"(app-pages-browser)/./src/contracts/ModularTokenFactory.json\", 19));\n            // Create factory contract instance\n            const factory = new ethers__WEBPACK_IMPORTED_MODULE_5__.Contract(MODULAR_TOKEN_FACTORY_ADDRESS, ModularTokenFactoryABI.default, signer);\n            // Check if user has DEPLOYER_ROLE\n            const DEPLOYER_ROLE = await factory.DEPLOYER_ROLE();\n            const userAddress = await signer.getAddress();\n            const hasDeployerRole = await factory.hasRole(DEPLOYER_ROLE, userAddress);\n            if (!hasDeployerRole) {\n                setError('You do not have permission to deploy tokens. Please contact an administrator to grant you the DEPLOYER_ROLE.');\n                return;\n            }\n            // Convert max supply to proper units\n            const maxSupplyWei = ethers__WEBPACK_IMPORTED_MODULE_6__.parseUnits(formData.maxSupply, formData.decimals);\n            // Deploy the token through the factory\n            console.log('Deploying token with params:', {\n                name: formData.name,\n                symbol: formData.symbol,\n                decimals: formData.decimals,\n                maxSupply: formData.maxSupply,\n                admin: formData.adminAddress,\n                tokenPrice: formData.tokenPrice,\n                bonusTiers: formData.bonusTiers,\n                tokenDetails: formData.tokenDetails,\n                tokenImageUrl: formData.tokenImageUrl\n            });\n            const tx = await factory.deployToken(formData.name, formData.symbol, formData.decimals, maxSupplyWei, formData.adminAddress, formData.tokenPrice, formData.bonusTiers, formData.tokenDetails, formData.tokenImageUrl);\n            console.log('Transaction sent:', tx.hash);\n            setSuccess('Transaction sent! Waiting for confirmation...');\n            const receipt = await tx.wait();\n            console.log('Transaction confirmed:', receipt);\n            // Get the deployed token address from the event\n            const event = receipt.logs.find((log)=>{\n                try {\n                    const parsed = factory.interface.parseLog(log);\n                    return parsed.name === 'TokenDeployed';\n                } catch (e) {\n                    return false;\n                }\n            });\n            if (event) {\n                const parsedEvent = factory.interface.parseLog(event);\n                const tokenAddress = parsedEvent.args.tokenAddress;\n                setDeployedToken({\n                    address: tokenAddress,\n                    name: formData.name,\n                    symbol: formData.symbol,\n                    transactionHash: tx.hash\n                });\n                setSuccess(\"Modular token deployed successfully! Address: \".concat(tokenAddress));\n            } else {\n                setError('Token deployed but could not find deployment event. Please check the transaction.');\n            }\n        } catch (error) {\n            console.error('Error deploying modular token:', error);\n            // Parse common error messages\n            let errorMessage = error.message;\n            if (error.message.includes('AccessControlUnauthorizedAccount')) {\n                errorMessage = 'You do not have permission to deploy tokens. Please contact an administrator.';\n            } else if (error.message.includes('execution reverted')) {\n                errorMessage = 'Transaction failed. Please check your inputs and try again.';\n            }\n            setError(\"Failed to deploy modular token: \".concat(errorMessage));\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // Validation\n        if (!formData.name || !formData.symbol || !formData.adminAddress) {\n            setError('Please fill in all required fields');\n            return;\n        }\n        if (formData.decimals < 0 || formData.decimals > 18) {\n            setError('Decimals must be between 0 and 18');\n            return;\n        }\n        await deployModularToken();\n    };\n    if (!provider || !signer) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"text-blue-600 hover:text-blue-800 mr-4\",\n                            children: \"← Back to Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold\",\n                            children: \"Create Modular Security Token\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                    lineNumber: 222,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold mb-2\",\n                            children: \"Connect Wallet\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"Please connect your MetaMask wallet to create modular tokens.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: initializeProvider,\n                            className: \"bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\",\n                            children: \"Connect Wallet\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n            lineNumber: 221,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/\",\n                        className: \"text-blue-600 hover:text-blue-800 mr-4\",\n                        children: \"← Back to Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold\",\n                        children: \"Create Modular Security Token\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 251,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-4 px-3 py-1 text-xs font-medium rounded-full bg-purple-100 text-purple-800\",\n                        children: \"Amoy Testnet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 252,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                lineNumber: 247,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 p-4 bg-purple-50 border border-purple-200 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-purple-600 text-xl\",\n                                children: \"\\uD83D\\uDE80\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-purple-800\",\n                                    children: \"Next-Generation Modular Architecture\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-1 text-sm text-purple-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Create tokens using our advanced modular architecture with:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"mt-1 list-disc list-inside space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Upgradeable Contracts:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Future-proof with secure upgrade mechanisms\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Modular Design:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                            lineNumber: 271,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Add new features without redeployment\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Enhanced Security:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                            lineNumber: 272,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Timelock protection and emergency controls\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Gas Optimization:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Efficient proxy pattern implementation\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                lineNumber: 258,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-sm font-medium text-blue-800 mb-2\",\n                        children: \"Modular Architecture Contracts\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-blue-700 space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"ModularTokenFactory:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 font-mono text-xs\",\n                                        children: MODULAR_TOKEN_FACTORY_ADDRESS\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"SecurityTokenCore Implementation:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 font-mono text-xs\",\n                                        children: SECURITY_TOKEN_CORE_ADDRESS\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"UpgradeManager:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 font-mono text-xs\",\n                                        children: UPGRADE_MANAGER_ADDRESS\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                lineNumber: 281,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-red-600 mr-2\",\n                            children: \"⚠️\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                    lineNumber: 302,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                lineNumber: 301,\n                columnNumber: 9\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-green-600 mr-2\",\n                            children: \"✅\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: success\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                    lineNumber: 312,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                lineNumber: 311,\n                columnNumber: 9\n            }, this),\n            deployedToken && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 bg-green-50 border border-green-200 rounded-lg p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-green-800 mb-4\",\n                        children: \"\\uD83C\\uDF89 Modular Token Created Successfully!\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Token Name:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: deployedToken.name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Symbol:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: deployedToken.symbol\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Contract Address:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-mono text-xs\",\n                                        children: deployedToken.address\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>window.open(\"https://amoy.polygonscan.com/address/\".concat(deployedToken.address), '_blank'),\n                                className: \"bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded text-sm\",\n                                children: \"View on PolygonScan\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/modular-tokens\",\n                                className: \"bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded text-sm\",\n                                children: \"Manage Token\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                lineNumber: 321,\n                columnNumber: 9\n            }, this),\n            !deployedToken && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold mb-4\",\n                        children: \"Token Configuration\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"name\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Token Name *\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                id: \"name\",\n                                                name: \"name\",\n                                                value: formData.name,\n                                                onChange: handleInputChange,\n                                                placeholder: \"e.g., Augment Security Token\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"symbol\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Token Symbol *\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                id: \"symbol\",\n                                                name: \"symbol\",\n                                                value: formData.symbol,\n                                                onChange: handleInputChange,\n                                                placeholder: \"e.g., AST\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"decimals\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Decimals *\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                id: \"decimals\",\n                                                name: \"decimals\",\n                                                value: formData.decimals,\n                                                onChange: handleInputChange,\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                required: true,\n                                                children: [\n                                                    ...Array(19)\n                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: i,\n                                                        children: [\n                                                            i,\n                                                            \" decimals\"\n                                                        ]\n                                                    }, i, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                        lineNumber: 406,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"maxSupply\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Maximum Supply *\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 412,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                id: \"maxSupply\",\n                                                name: \"maxSupply\",\n                                                value: formData.maxSupply,\n                                                onChange: handleInputChange,\n                                                placeholder: \"10000000\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 415,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"adminAddress\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Admin Address *\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        id: \"adminAddress\",\n                                        name: \"adminAddress\",\n                                        value: formData.adminAddress,\n                                        onChange: handleInputChange,\n                                        placeholder: \"0x...\",\n                                        className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 428,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"tokenPrice\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Token Price\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 446,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                id: \"tokenPrice\",\n                                                name: \"tokenPrice\",\n                                                value: formData.tokenPrice,\n                                                onChange: handleInputChange,\n                                                placeholder: \"1.00 USD\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 449,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"tokenImageUrl\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Token Image URL\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 461,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"url\",\n                                                id: \"tokenImageUrl\",\n                                                name: \"tokenImageUrl\",\n                                                value: formData.tokenImageUrl,\n                                                onChange: handleInputChange,\n                                                placeholder: \"https://...\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"bonusTiers\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Bonus Tiers\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 477,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        id: \"bonusTiers\",\n                                        name: \"bonusTiers\",\n                                        value: formData.bonusTiers,\n                                        onChange: handleInputChange,\n                                        rows: 2,\n                                        placeholder: \"Early Bird: 10%, Standard: 5%, Late: 0%\",\n                                        className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 480,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 476,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"tokenDetails\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Token Details\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 492,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        id: \"tokenDetails\",\n                                        name: \"tokenDetails\",\n                                        value: formData.tokenDetails,\n                                        onChange: handleInputChange,\n                                        rows: 3,\n                                        placeholder: \"Describe your security token...\",\n                                        className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 491,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: isSubmitting,\n                                    className: \"w-full bg-purple-600 hover:bg-purple-700 text-white font-bold py-3 px-4 rounded disabled:opacity-50\",\n                                    children: isSubmitting ? '🔄 Creating Modular Token...' : '🚀 Create Modular Token'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                    lineNumber: 507,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 506,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                lineNumber: 356,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-yellow-600 text-xl\",\n                                children: \"⚠️\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 523,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 522,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-yellow-800\",\n                                    children: \"Development Notice\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                    lineNumber: 526,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-1 text-sm text-yellow-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"This is a demonstration of the modular token creation interface. In a production environment, this would deploy new proxy instances of the modular architecture contracts.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                            lineNumber: 530,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1\",\n                                            children: \"Currently, it references the existing deployed contracts for demonstration purposes.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                            lineNumber: 531,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                    lineNumber: 529,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 525,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                    lineNumber: 521,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                lineNumber: 520,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n        lineNumber: 246,\n        columnNumber: 5\n    }, this);\n}\n_s(CreateModularTokenPage, \"nvH3z261AgqD36O3Af5uhAJl7Os=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = CreateModularTokenPage;\nvar _c;\n$RefreshReg$(_c, \"CreateModularTokenPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/create-modular-token/page.tsx\n"));

/***/ })

});