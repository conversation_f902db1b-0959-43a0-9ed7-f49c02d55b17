{"_format": "hh-sol-artifact-1", "contractName": "IdentityRegistry", "sourceName": "contracts/IdentityRegistry.sol", "abi": [{"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"inputs": [{"internalType": "address", "name": "target", "type": "address"}], "name": "AddressEmptyCode", "type": "error"}, {"inputs": [{"internalType": "address", "name": "implementation", "type": "address"}], "name": "ERC1967InvalidImplementation", "type": "error"}, {"inputs": [], "name": "ERC1967Non<PERSON>ayable", "type": "error"}, {"inputs": [], "name": "FailedCall", "type": "error"}, {"inputs": [], "name": "InvalidInitialization", "type": "error"}, {"inputs": [], "name": "NotInitializing", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"inputs": [], "name": "UUPSUnauthorizedCallContext", "type": "error"}, {"inputs": [{"internalType": "bytes32", "name": "slot", "type": "bytes32"}], "name": "UUPSUnsupportedProxiableUUID", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}], "name": "AddressFrozen", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}], "name": "AddressUnfrozen", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "oldRegistry", "type": "address"}, {"indexed": true, "internalType": "address", "name": "newRegistry", "type": "address"}], "name": "ClaimRegistryUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint16", "name": "country", "type": "uint16"}], "name": "CountryRestricted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint16", "name": "country", "type": "uint16"}], "name": "CountryUnrestricted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "userAddress", "type": "address"}, {"indexed": true, "internalType": "uint16", "name": "country", "type": "uint16"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "IdentityRegistered", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "userAddress", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "IdentityRemoved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "userAddress", "type": "address"}, {"indexed": true, "internalType": "uint16", "name": "country", "type": "uint16"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "IdentityUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "version", "type": "uint64"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}], "name": "KycApproved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}], "name": "KycRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "claimTopic", "type": "uint256"}], "name": "RequiredClaimTopicAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "claimTopic", "type": "uint256"}], "name": "RequiredClaimTopicRemoved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "implementation", "type": "address"}], "name": "Upgraded", "type": "event"}, {"inputs": [], "name": "AGENT_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "COMPLIANCE_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "UPGRADE_INTERFACE_VERSION", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "claimTopic", "type": "uint256"}], "name": "addRequiredClaimTopic", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "approveKyc", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "accounts", "type": "address[]"}], "name": "batchAdd<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "accounts", "type": "address[]"}], "name": "batchApproveKyc", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "accounts", "type": "address[]"}], "name": "batchFreezeAddresses", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "accounts", "type": "address[]"}], "name": "batchRemoveFrom<PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "accounts", "type": "address[]"}], "name": "batchRevokeKyc", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "accounts", "type": "address[]"}], "name": "batchUnfreezeAddresses", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}], "name": "canTransfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "claimRegistry", "outputs": [{"internalType": "contract ClaimRegistry", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "freezeAddress", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint16", "name": "country", "type": "uint16"}], "name": "getCountryInvestorCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "userAddress", "type": "address"}], "name": "getIdentity", "outputs": [{"components": [{"internalType": "bool", "name": "isVerified", "type": "bool"}, {"internalType": "bool", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "bool"}, {"internalType": "bool", "name": "isKycApproved", "type": "bool"}, {"internalType": "bool", "name": "isFrozen", "type": "bool"}, {"internalType": "uint16", "name": "country", "type": "uint16"}, {"internalType": "uint256", "name": "registeredAt", "type": "uint256"}, {"internalType": "uint256", "name": "updatedAt", "type": "uint256"}], "internalType": "struct IdentityRegistry.Identity", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getRequiredClaimTopics", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "index", "type": "uint256"}], "name": "getVerifiedAddressByIndex", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getVerifiedAddressCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "start", "type": "uint256"}, {"internalType": "uint256", "name": "limit", "type": "uint256"}], "name": "getVerifiedAddresses", "outputs": [{"internalType": "address[]", "name": "addresses", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "userAddress", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "admin", "type": "address"}, {"internalType": "address", "name": "_claimRegistry", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "userAddress", "type": "address"}], "name": "investorCountry", "outputs": [{"internalType": "uint16", "name": "", "type": "uint16"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint16", "name": "country", "type": "uint16"}], "name": "isCountryRestricted", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "isFrozen", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "isKycApproved", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "userAddress", "type": "address"}], "name": "isVerified", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "proxiableUUID", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "userAddress", "type": "address"}, {"internalType": "uint16", "name": "country", "type": "uint16"}], "name": "registerIdentity", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "userAddress", "type": "address"}], "name": "removeIdentity", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "claimTopic", "type": "uint256"}], "name": "removeRequiredClaimTopic", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint16", "name": "country", "type": "uint16"}], "name": "restrictCountry", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "revokeKyc", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "unfreeze<PERSON>ddress", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint16", "name": "country", "type": "uint16"}], "name": "unrestrictCountry", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newClaimRegistry", "type": "address"}], "name": "updateClaimRegistry", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "userAddress", "type": "address"}, {"internalType": "uint16", "name": "country", "type": "uint16"}], "name": "updateIdentity", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "upgradeToAndCall", "outputs": [], "stateMutability": "payable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}