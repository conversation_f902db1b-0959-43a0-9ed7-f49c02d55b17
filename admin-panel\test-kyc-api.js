// Simple test script for KYC API endpoints
const testUserAddress = "******************************************";
const tokenAddress = "******************************************";

async function testKYCApproval() {
  console.log("🧪 Testing KYC Approval API...");
  
  try {
    const response = await fetch('http://localhost:6677/api/kyc/approve', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        tokenAddress: tokenAddress,
        userAddress: testUserAddress,
      }),
    });

    const result = await response.json();
    console.log("✅ KYC Approval Response:", result);
  } catch (error) {
    console.error("❌ KYC Approval Error:", error);
  }
}

async function testWhitelistAdd() {
  console.log("🧪 Testing Whitelist Add API...");
  
  try {
    const response = await fetch('http://localhost:6677/api/kyc/whitelist', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        tokenAddress: tokenAddress,
        userAddress: testUserAddress,
      }),
    });

    const result = await response.json();
    console.log("✅ Whitelist Add Response:", result);
  } catch (error) {
    console.error("❌ Whitelist Add Error:", error);
  }
}

async function testStatusCheck() {
  console.log("🧪 Testing Status Check API...");
  
  try {
    const response = await fetch(`http://localhost:6677/api/kyc/status?tokenAddress=${tokenAddress}&userAddress=${testUserAddress}`);
    const result = await response.json();
    console.log("✅ Status Check Response:", result);
  } catch (error) {
    console.error("❌ Status Check Error:", error);
  }
}

async function testClaimIssuance() {
  console.log("🧪 Testing Claim Issuance API...");
  
  try {
    const response = await fetch('http://localhost:6677/api/kyc/issue-claim', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userAddress: testUserAddress,
        topicId: "10101010000001",
        data: "KYC Verification Test",
      }),
    });

    const result = await response.json();
    console.log("✅ Claim Issuance Response:", result);
  } catch (error) {
    console.error("❌ Claim Issuance Error:", error);
  }
}

// Run tests
console.log("🚀 Starting KYC API Tests...");
console.log("Test User:", testUserAddress);
console.log("Token Address:", tokenAddress);
console.log("=" .repeat(50));

// Run tests sequentially
testStatusCheck()
  .then(() => testKYCApproval())
  .then(() => testWhitelistAdd())
  .then(() => testClaimIssuance())
  .then(() => testStatusCheck())
  .then(() => {
    console.log("=" .repeat(50));
    console.log("🎉 All tests completed!");
  })
  .catch(error => {
    console.error("❌ Test suite failed:", error);
  });
