import { ethers } from 'ethers';
import { NextRequest, NextResponse } from 'next/server';

const RPC_URLS = {
  amoy: process.env.AMOY_RPC_URL || 'https://rpc-amoy.polygon.technology/',
  polygon: process.env.POLYGON_RPC_URL || 'https://polygon-rpc.com',
  unknown: process.env.AMOY_RPC_URL || 'https://rpc-amoy.polygon.technology/',
};

// Network chain IDs
const CHAIN_IDS = {
  amoy: 80002,
  polygon: 137,
  unknown: 80002,
};

// Vault addresses where frozen tokens are stored
const VAULT_ADDRESSES = {
  amoy: process.env.AMOY_VAULT_ADDRESS || '******************************************',
  polygon: process.env.POLYGON_VAULT_ADDRESS || '******************************************',
  unknown: process.env.AMOY_VAULT_ADDRESS || '******************************************'
};

// Minimal ABI for tokens with built-in whitelist
const MINIMAL_TOKEN_ABI = [
  "function decimals() view returns (uint8)",
  "function balanceOf(address) view returns (uint256)",
  "function isWhitelisted(address) view returns (bool)",
  "function isKycApproved(address) view returns (bool)",
  "function isVerified(address) view returns (bool)"
];

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const tokenAddress = searchParams.get('tokenAddress');
    const network = searchParams.get('network') || 'amoy';
    
    if (!tokenAddress) {
      return NextResponse.json(
        { error: 'Token address is required' },
        { status: 400 }
      );
    }

    // Get RPC URL for the specified network, defaulting to Amoy
    const actualNetwork = network === 'unknown' ? 'amoy' : network;
    const rpcUrl = RPC_URLS[actualNetwork as keyof typeof RPC_URLS] || RPC_URLS.amoy;
    const chainId = CHAIN_IDS[actualNetwork as keyof typeof CHAIN_IDS] || CHAIN_IDS.amoy;
    const vaultAddress = VAULT_ADDRESSES[actualNetwork as keyof typeof VAULT_ADDRESSES] || VAULT_ADDRESSES.amoy;
    
    // Connect to the network with explicit chainId
    const provider = new ethers.JsonRpcProvider(rpcUrl, {
      chainId,
      name: actualNetwork
    });
    
    // Connect to the token contract
    const tokenContract = new ethers.Contract(
      tokenAddress,
      MINIMAL_TOKEN_ABI,
      provider
    );
    
    // Get token decimals
    let decimals;
    try {
      decimals = await tokenContract.decimals();
    } catch (err) {
      console.error('Error getting token decimals:', err);
      return NextResponse.json(
        { error: 'Could not get token decimals. Make sure the token address is correct.' },
        { status: 400 }
      );
    }
    
    // For minimal tokens, we'll use a simplified approach
    // Since we can't easily enumerate all whitelisted addresses without events,
    // we'll check some known addresses and return them if they're whitelisted

    const knownAddresses = [
      '******************************************', // Admin address
      '******************************************', // Test address from debug script
    ];

    const whitelistedAddressList = [];

    // Check each known address
    for (const address of knownAddresses) {
      try {
        const isWhitelisted = await tokenContract.isWhitelisted(address);
        if (isWhitelisted) {
          let isKycApproved = false;
          try {
            isKycApproved = await tokenContract.isKycApproved(address);
          } catch (err) {
            console.log(`Could not check KYC for ${address}`);
          }

          whitelistedAddressList.push({
            address,
            kycApproved: isKycApproved,
            frozen: false // Minimal token doesn't have individual address freezing
          });
        }
      } catch (err) {
        console.log(`Could not check whitelist status for ${address}:`, err);
      }
    }

    // For minimal tokens, we'll skip frozen token tracking for now
    // since it requires complex event analysis
    const frozenTokensMap = new Map();
    
    // Fetch token balances for each whitelisted address
    const addressesWithBalances = await Promise.all(
      whitelistedAddressList.map(async (item) => {
        try {
          const balanceRaw = await tokenContract.balanceOf(item.address);
          const balance = ethers.formatUnits(balanceRaw, decimals);
          
          // Get the frozen tokens for this address
          const frozenTokensRaw = frozenTokensMap.get(item.address) || BigInt(0);
          const frozenTokens = ethers.formatUnits(frozenTokensRaw, decimals);
          
          // Double-check whitelist and KYC status from contract
          let isWhitelisted = false;
          let isKycApproved = false;
          let isVerified = false;

          try {
            isWhitelisted = await tokenContract.isWhitelisted(item.address);
          } catch (err) {
            console.log(`Could not check whitelist status for ${item.address}:`, err);
          }

          try {
            isKycApproved = await tokenContract.isKycApproved(item.address);
          } catch (err) {
            console.log(`Could not check KYC status for ${item.address}:`, err);
          }

          try {
            isVerified = await tokenContract.isVerified(item.address);
          } catch (err) {
            console.log(`Could not check verified status for ${item.address}:`, err);
          }
          
          return {
            address: item.address,
            frozen: item.frozen,
            balance,
            frozenTokens,
            isWhitelisted,
            isKycApproved,
            isVerified
          };
        } catch (error) {
          console.error(`Error fetching balance for ${item.address}:`, error);
          return {
            address: item.address,
            frozen: item.frozen,
            balance: '0',
            frozenTokens: '0',
            isWhitelisted: false,
            isKycApproved: false,
            isVerified: false
          };
        }
      })
    );
    
    // Filter to only include addresses that are actually whitelisted
    const verifiedWhitelistedAddresses = addressesWithBalances.filter(addr => addr.isWhitelisted);
    
    return NextResponse.json({
      success: true,
      whitelistedAddresses: verifiedWhitelistedAddresses,
      vaultAddress,
      totalAddresses: verifiedWhitelistedAddresses.length
    });
    
  } catch (error: any) {
    console.error('Error getting whitelisted addresses from token:', error);
    return NextResponse.json(
      { error: error.message || 'An unknown error occurred' },
      { status: 500 }
    );
  }
}
