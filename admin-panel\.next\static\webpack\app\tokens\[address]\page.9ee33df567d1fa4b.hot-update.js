"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/tokens/[address]/page",{

/***/ "(app-pages-browser)/./src/config.ts":
/*!***********************!*\
  !*** ./src/config.ts ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   contractAddresses: () => (/* binding */ contractAddresses),\n/* harmony export */   defaultNetwork: () => (/* binding */ defaultNetwork),\n/* harmony export */   getContractAddresses: () => (/* binding */ getContractAddresses),\n/* harmony export */   getKnownTokens: () => (/* binding */ getKnownTokens),\n/* harmony export */   getNetworkConfig: () => (/* binding */ getNetworkConfig),\n/* harmony export */   isKnownToken: () => (/* binding */ isKnownToken),\n/* harmony export */   knownTokens: () => (/* binding */ knownTokens),\n/* harmony export */   networkConfig: () => (/* binding */ networkConfig),\n/* harmony export */   tokenTypes: () => (/* binding */ tokenTypes),\n/* harmony export */   verifyTokenExists: () => (/* binding */ verifyTokenExists)\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n// Network configuration\nconst networkConfig = {\n    // Amoy testnet\n    amoy: {\n        chainId: 80002,\n        name: \"Amoy\",\n        rpcUrl: \"https://rpc-amoy.polygon.technology\",\n        blockExplorer: \"https://www.oklink.com/amoy\"\n    },\n    // Polygon mainnet\n    polygon: {\n        chainId: 137,\n        name: \"Polygon\",\n        rpcUrl: \"https://polygon-rpc.com\",\n        blockExplorer: \"https://polygonscan.com\"\n    }\n};\n// Default network\nconst defaultNetwork = \"amoy\";\n// Contract addresses - using the current production-ready factory\nconst contractAddresses = {\n    // Current production factory (verified and working)\n    amoy: {\n        factory: \"0x0359527C9EFC960bCd64Cb4928b62cf0B8FAa0b1\",\n        // 🔐 SECURITY LEVEL: HIGH - ALL AUDIT FIXES IMPLEMENTED\n        // ✅ Emergency pause controls\n        // ✅ Function-specific pausing\n        // ✅ Enhanced reentrancy protection\n        // ✅ Improved input validation\n        // ✅ Role-based access control\n        // ✅ Agent management\n        // ✅ Agreement tracking\n        // ✅ Token enumeration\n        // ✅ Configurable decimals\n        // ✅ All critical security audit fixes implemented\n        // ✅ Production ready with enhanced security\n        //\n        // Previous factory (backup): \"0x69a6536629369F8948f47b897045929a57c630Fd\"\n        tokenImplementation: \"0xae2aA28708120CAA177e4c98CCCa0e152E30E506\",\n        whitelistImplementation: \"0x63eeE78ccc281413272bE68d9553Ae82680a0B09\",\n        whitelistWithKYCImplementation: \"0xf7c9C30Ad2E5b72F86489f26ab66cc1E79F44A7D\"\n    },\n    polygon: {\n        factory: process.env.NEXT_PUBLIC_FACTORY_ADDRESS_POLYGON || \"0x6543210987654321098765432109876543210987\"\n    }\n};\n// Known deployed tokens for fallback display (from memory)\nconst knownTokens = {\n    amoy: [\n        {\n            address: \"0x7544A3072FAA793e3f89048C31b794f171779544\",\n            name: \"Advanced Control Token\",\n            symbol: \"ACT\",\n            description: \"Security token with advanced transfer controls (conditional transfers, whitelisting, fees)\"\n        },\n        {\n            address: \"0xfccB88D208f5Ec7166ce2291138aaD5274C671dE\",\n            name: \"Augment_019\",\n            symbol: \"AUG019\",\n            description: \"Commodity token with 0 decimals, 1M max supply\"\n        },\n        {\n            address: \"0xe5F81d7dCeB8a8F97274C749773659B7288EcF90\",\n            name: \"Augment_01z\",\n            symbol: \"AUG01Z\",\n            description: \"Test token with custom configuration\"\n        },\n        {\n            address: \"0x391a0FA1498B869d0b9445596ed49b03aA8bf46e\",\n            name: \"Test Image Token\",\n            symbol: \"TIT2789\",\n            description: \"Test token with image URL support - deployed from upgraded factory\"\n        }\n    ]\n};\n// Token types for creating new tokens\nconst tokenTypes = [\n    {\n        id: \"equity\",\n        name: \"Equity\"\n    },\n    {\n        id: \"bond\",\n        name: \"Bond\"\n    },\n    {\n        id: \"debenture\",\n        name: \"Debenture\"\n    },\n    {\n        id: \"warrant\",\n        name: \"Warrant\"\n    },\n    {\n        id: \"realestate\",\n        name: \"Real Estate\"\n    },\n    {\n        id: \"carbon\",\n        name: \"Carbon Credit\"\n    },\n    {\n        id: \"commodity\",\n        name: \"Commodity\"\n    }\n];\n// Helper function to get contract addresses for the current network\nconst getContractAddresses = (network)=>{\n    return contractAddresses[network] || contractAddresses[defaultNetwork];\n};\n// Helper function to get network configuration for the current network\nconst getNetworkConfig = (network)=>{\n    return networkConfig[network] || networkConfig[defaultNetwork];\n};\n// Helper function to get known tokens for a network\nconst getKnownTokens = (network)=>{\n    return knownTokens[network] || [];\n};\n// Helper to check if a token exists in factory (in a real implementation, this would query the blockchain)\nconst verifyTokenExists = async (network, tokenAddress)=>{\n    // In a real implementation, this would:\n    // 1. Connect to the factory contract\n    // 2. Call a method or check events to verify the token's existence\n    // For demo purposes, we'll just return true\n    return true;\n};\n// Helper function to validate if an address is a known token\nconst isKnownToken = (network, tokenAddress)=>{\n    const tokens = getKnownTokens(network);\n    return tokens.some((token)=>token.address.toLowerCase() === tokenAddress.toLowerCase());\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/config.ts\n"));

/***/ })

});