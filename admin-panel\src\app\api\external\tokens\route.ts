import { NextRequest, NextResponse } from 'next/server';
import { ethers } from 'ethers';

// Import ABIs
import SecurityTokenCoreABI from '@/contracts/SecurityTokenCore.json';
import ModularTokenFactoryABI from '@/contracts/ModularTokenFactory.json';

// Contract addresses
const MODULAR_TOKEN_FACTORY_ADDRESS = process.env.NEXT_PUBLIC_AMOY_MODULAR_TOKEN_FACTORY_ADDRESS;
const RPC_URL = 'https://rpc-amoy.polygon.technology/';

interface TokenData {
  address: string;
  name: string;
  symbol: string;
  version: string;
  totalSupply: string;
  maxSupply: string;
  decimals: number;
  price: string;
  bonusTiers: string;
  isPaused: boolean;
  totalInvestors: number;
  issuer: {
    name: string;
    address: string;
    website?: string;
    description?: string;
  };
  metadata: {
    deployedAt: string;
    lastUpdated: string;
    network: string;
    contractType: string;
  };
  compliance: {
    kycRequired: boolean;
    whitelistEnabled: boolean;
    claimsRequired: string[];
  };
  statistics: {
    totalTransactions: number;
    averageTransactionValue: string;
    largestTransaction: string;
    activeInvestors: number;
  };
}

// Initialize provider
const provider = new ethers.JsonRpcProvider(RPC_URL);

// Get token information from blockchain
async function getTokenInfo(tokenAddress: string): Promise<TokenData | null> {
  try {
    const tokenContract = new ethers.Contract(tokenAddress, SecurityTokenCoreABI.abi, provider);

    // Get basic token information
    const [
      name,
      symbol,
      version,
      totalSupply,
      maxSupply,
      decimals,
      metadata,
      isPaused
    ] = await Promise.all([
      tokenContract.name(),
      tokenContract.symbol(),
      tokenContract.version(),
      tokenContract.totalSupply(),
      tokenContract.maxSupply(),
      tokenContract.decimals(),
      tokenContract.getTokenMetadata(),
      tokenContract.paused()
    ]);

    // Get deployment block for timestamp
    const deploymentBlock = await provider.getBlockNumber();
    const block = await provider.getBlock(deploymentBlock);

    // Format the response
    const tokenData: TokenData = {
      address: tokenAddress,
      name,
      symbol,
      version,
      totalSupply: ethers.formatUnits(totalSupply, decimals),
      maxSupply: ethers.formatUnits(maxSupply, decimals),
      decimals: Number(decimals), // Convert BigInt to number
      price: metadata[0] || 'Not set',
      bonusTiers: metadata[1] || 'Not set',
      isPaused,
      totalInvestors: 0, // Would need to calculate from events
      issuer: {
        name: 'Token Issuer', // Would come from database
        address: '******************************************', // Admin address
        website: 'https://tokenissuer.com',
        description: 'Professional token issuer'
      },
      metadata: {
        deployedAt: new Date(block?.timestamp ? Number(block.timestamp) * 1000 : Date.now()).toISOString(),
        lastUpdated: new Date().toISOString(),
        network: 'Polygon Amoy Testnet',
        contractType: 'ERC-3643 Security Token'
      },
      compliance: {
        kycRequired: true,
        whitelistEnabled: true,
        claimsRequired: ['10101010000001', '10101010000004']
      },
      statistics: {
        totalTransactions: 0, // Would calculate from events
        averageTransactionValue: '0',
        largestTransaction: '0',
        activeInvestors: 0
      }
    };

    return tokenData;
  } catch (error) {
    console.error('Error fetching token info:', error);
    return null;
  }
}

// Get all deployed tokens
async function getAllTokens(): Promise<TokenData[]> {
  try {
    // For now, return hardcoded data for the known deployed token
    const knownTokenAddress = "0xbC7CfcC7ffB09747a381ed82369589706159a255";
    const tokenInfo = await getTokenInfo(knownTokenAddress);

    if (tokenInfo) {
      return [tokenInfo];
    }

    return [];
  } catch (error) {
    console.error('Error fetching all tokens:', error);
    // Return mock data if blockchain call fails
    return [{
      address: "0xbC7CfcC7ffB09747a381ed82369589706159a255",
      name: "Augment Security Token",
      symbol: "AST",
      version: "4.0.0",
      totalSupply: "1000000",
      maxSupply: "*********",
      decimals: 0,
      price: "9.99 USD",
      bonusTiers: "Ultra Early: 70%, Super Early: 50%, Early: 35%, Standard: 25%, Late: 15%",
      isPaused: false,
      totalInvestors: 25,
      issuer: {
        name: "Augment Technologies",
        address: "******************************************",
        website: "https://augment.com",
        description: "Leading blockchain technology company"
      },
      metadata: {
        deployedAt: "2024-01-15T10:30:00Z",
        lastUpdated: new Date().toISOString(),
        network: "Polygon Amoy Testnet",
        contractType: "ERC-3643 Security Token"
      },
      compliance: {
        kycRequired: true,
        whitelistEnabled: true,
        claimsRequired: ["10101010000001", "10101010000004"]
      },
      statistics: {
        totalTransactions: 150,
        averageTransactionValue: "5000",
        largestTransaction: "50000",
        activeInvestors: 20
      }
    }];
  }
}

// GET /api/external/tokens - Get all tokens or specific token
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const address = searchParams.get('address');
    const format = searchParams.get('format') || 'json';

    // CORS headers for external API access
    const headers = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Content-Type': 'application/json',
      'Cache-Control': 'public, max-age=300' // 5 minutes cache
    };

    if (address) {
      // Get specific token
      const tokenInfo = await getTokenInfo(address);
      
      if (!tokenInfo) {
        return NextResponse.json(
          { 
            error: 'Token not found',
            message: `No token found at address ${address}`,
            code: 'TOKEN_NOT_FOUND'
          },
          { status: 404, headers }
        );
      }

      return NextResponse.json(
        {
          success: true,
          data: tokenInfo,
          timestamp: new Date().toISOString(),
          version: '1.0.0'
        },
        { headers }
      );
    } else {
      // Get all tokens
      const tokens = await getAllTokens();
      
      const response = {
        success: true,
        data: {
          tokens,
          totalCount: tokens.length,
          summary: {
            totalSupply: tokens.reduce((sum, token) => 
              sum + parseFloat(token.totalSupply), 0
            ).toString(),
            totalInvestors: tokens.reduce((sum, token) => 
              sum + token.totalInvestors, 0
            ),
            activeTokens: tokens.filter(token => !token.isPaused).length,
            pausedTokens: tokens.filter(token => token.isPaused).length
          }
        },
        timestamp: new Date().toISOString(),
        version: '1.0.0'
      };

      return NextResponse.json(response, { headers });
    }
  } catch (error: any) {
    console.error('External tokens API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: error.message,
        code: 'INTERNAL_ERROR',
        timestamp: new Date().toISOString()
      },
      { 
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Content-Type': 'application/json'
        }
      }
    );
  }
}

// OPTIONS for CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
