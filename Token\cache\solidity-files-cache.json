{"_format": "hh-sol-cache-2", "files": {"D:\\github\\tokendev-newroo\\Token\\contracts\\base\\BaseIdentityRegistry.sol": {"lastModificationDate": 1750460122618, "contentHash": "08b7358c7ee930af3f7940c41a3bd3df", "sourceName": "contracts/base/BaseIdentityRegistry.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol", "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol", "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol", "../interfaces/IIdentityRegistry.sol"], "versionPragmas": ["^0.8.22"], "artifacts": ["BaseIdentityRegistry"]}, "D:\\github\\tokendev-newroo\\Token\\contracts\\interfaces\\IIdentityRegistry.sol": {"lastModificationDate": 1748029019891, "contentHash": "d691b13735926a52f60c2009a6be8ed9", "sourceName": "contracts/interfaces/IIdentityRegistry.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.22"], "artifacts": ["IIdentityRegistry"]}, "D:\\github\\tokendev-newroo\\Token\\node_modules\\@openzeppelin\\contracts-upgradeable\\utils\\ReentrancyGuardUpgradeable.sol": {"lastModificationDate": 1748024245939, "contentHash": "24562076b21756e3358ede5e844008ce", "sourceName": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["../proxy/utils/Initializable.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ReentrancyGuardUpgradeable"]}, "D:\\github\\tokendev-newroo\\Token\\node_modules\\@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol": {"lastModificationDate": 1748024245908, "contentHash": "cdbbb56cde3edf99d1426db8d104bb84", "sourceName": "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/access/IAccessControl.sol", "../utils/ContextUpgradeable.sol", "../utils/introspection/ERC165Upgradeable.sol", "../proxy/utils/Initializable.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["AccessControlUpgradeable"]}, "D:\\github\\tokendev-newroo\\Token\\node_modules\\@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol": {"lastModificationDate": 1748024245936, "contentHash": "2acae54ad209714bca07edf4bae32f72", "sourceName": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["Initializable"]}, "D:\\github\\tokendev-newroo\\Token\\node_modules\\@openzeppelin\\contracts-upgradeable\\proxy\\utils\\UUPSUpgradeable.sol": {"lastModificationDate": 1748024245940, "contentHash": "08db0b4bea3d52072346c326c5b857b7", "sourceName": "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/interfaces/draft-IERC1822.sol", "@openzeppelin/contracts/proxy/ERC1967/ERC1967Utils.sol", "./Initializable.sol"], "versionPragmas": ["^0.8.22"], "artifacts": ["UUPSUpgradeable"]}, "D:\\github\\tokendev-newroo\\Token\\node_modules\\@openzeppelin\\contracts\\access\\IAccessControl.sol": {"lastModificationDate": 1748024245990, "contentHash": "80621031deacf7066ec81277f9b1463a", "sourceName": "@openzeppelin/contracts/access/IAccessControl.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["IAccessControl"]}, "D:\\github\\tokendev-newroo\\Token\\node_modules\\@openzeppelin\\contracts-upgradeable\\utils\\ContextUpgradeable.sol": {"lastModificationDate": 1748024245910, "contentHash": "c28aaa25d083a9a55bd9ec9e0b785122", "sourceName": "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["../proxy/utils/Initializable.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ContextUpgradeable"]}, "D:\\github\\tokendev-newroo\\Token\\node_modules\\@openzeppelin\\contracts-upgradeable\\utils\\introspection\\ERC165Upgradeable.sol": {"lastModificationDate": 1748024245917, "contentHash": "61838d7249e45e3941fc2a45d1091448", "sourceName": "@openzeppelin/contracts-upgradeable/utils/introspection/ERC165Upgradeable.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/utils/introspection/IERC165.sol", "../../proxy/utils/Initializable.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC165Upgradeable"]}, "D:\\github\\tokendev-newroo\\Token\\node_modules\\@openzeppelin\\contracts\\utils\\introspection\\IERC165.sol": {"lastModificationDate": 1748024245996, "contentHash": "bf0119eb2a570f219729ff38b6cd1df8", "sourceName": "@openzeppelin/contracts/utils/introspection/IERC165.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["IERC165"]}, "D:\\github\\tokendev-newroo\\Token\\node_modules\\@openzeppelin\\contracts\\proxy\\ERC1967\\ERC1967Utils.sol": {"lastModificationDate": 1748024245973, "contentHash": "c023a5b23ee35b40b3085e550ff3367f", "sourceName": "@openzeppelin/contracts/proxy/ERC1967/ERC1967Utils.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["../beacon/IBeacon.sol", "../../interfaces/IERC1967.sol", "../../utils/Address.sol", "../../utils/StorageSlot.sol"], "versionPragmas": ["^0.8.22"], "artifacts": ["ERC1967Utils"]}, "D:\\github\\tokendev-newroo\\Token\\node_modules\\@openzeppelin\\contracts\\interfaces\\draft-IERC1822.sol": {"lastModificationDate": 1748024245962, "contentHash": "64a2b7a96950f0641c477993f3029477", "sourceName": "@openzeppelin/contracts/interfaces/draft-IERC1822.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["IERC1822Proxiable"]}, "D:\\github\\tokendev-newroo\\Token\\node_modules\\@openzeppelin\\contracts\\interfaces\\IERC1967.sol": {"lastModificationDate": 1748024245997, "contentHash": "4e17c826a54a8ee1a185449d4695d07d", "sourceName": "@openzeppelin/contracts/interfaces/IERC1967.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["IERC1967"]}, "D:\\github\\tokendev-newroo\\Token\\node_modules\\@openzeppelin\\contracts\\utils\\Address.sol": {"lastModificationDate": 1748024245951, "contentHash": "5b9a49c25d7edbc48ffcbd2c7e8a40ef", "sourceName": "@openzeppelin/contracts/utils/Address.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["./Errors.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["Address"]}, "D:\\github\\tokendev-newroo\\Token\\node_modules\\@openzeppelin\\contracts\\proxy\\beacon\\IBeacon.sol": {"lastModificationDate": 1748024245992, "contentHash": "0a5c323fd908535580597848b8e550fb", "sourceName": "@openzeppelin/contracts/proxy/beacon/IBeacon.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["IBeacon"]}, "D:\\github\\tokendev-newroo\\Token\\node_modules\\@openzeppelin\\contracts\\utils\\StorageSlot.sol": {"lastModificationDate": 1748024246018, "contentHash": "e656d64c4ce918f3d13030b91c935134", "sourceName": "@openzeppelin/contracts/utils/StorageSlot.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["StorageSlot"]}, "D:\\github\\tokendev-newroo\\Token\\node_modules\\@openzeppelin\\contracts\\utils\\Errors.sol": {"lastModificationDate": 1748024245982, "contentHash": "6b5eac2b85500c3012977849cfd633d8", "sourceName": "@openzeppelin/contracts/utils/Errors.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["Errors"]}, "D:\\github\\tokendev-newroo\\Token\\contracts\\WhitelistWithKYC.sol": {"lastModificationDate": 1748029019890, "contentHash": "4183305bcf09c8ec5d58a55aaa4eb28f", "sourceName": "contracts/WhitelistWithKYC.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["./base/BaseIdentityRegistry.sol", "./base/BaseKYCRegistry.sol", "./interfaces/ICompleteWhitelist.sol"], "versionPragmas": ["^0.8.22"], "artifacts": ["WhitelistWithKYC"]}, "D:\\github\\tokendev-newroo\\Token\\contracts\\interfaces\\ICompleteWhitelist.sol": {"lastModificationDate": 1748029019891, "contentHash": "b7225685173a13e96c36f62562400e79", "sourceName": "contracts/interfaces/ICompleteWhitelist.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["./IIdentityRegistry.sol", "./IKYCRegistry.sol"], "versionPragmas": ["^0.8.22"], "artifacts": ["ICompleteW<PERSON>elist"]}, "D:\\github\\tokendev-newroo\\Token\\contracts\\base\\BaseKYCRegistry.sol": {"lastModificationDate": 1748029019891, "contentHash": "03a5192db60278b569eaecf7667a5dac", "sourceName": "contracts/base/BaseKYCRegistry.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol", "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol", "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol", "../interfaces/IKYCRegistry.sol"], "versionPragmas": ["^0.8.22"], "artifacts": ["BaseKYCRegistry"]}, "D:\\github\\tokendev-newroo\\Token\\contracts\\interfaces\\IKYCRegistry.sol": {"lastModificationDate": 1748029019892, "contentHash": "2c45c0f22161bf052d0d5c5502f5eeb8", "sourceName": "contracts/interfaces/IKYCRegistry.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.22"], "artifacts": ["IKYCRegistry"]}, "D:\\github\\tokendev-newroo\\Token\\node_modules\\@openzeppelin\\contracts\\proxy\\ERC1967\\ERC1967Proxy.sol": {"lastModificationDate": 1748024245972, "contentHash": "07e13052976ae13746455b10ebf3cff9", "sourceName": "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["../Proxy.sol", "./ERC1967Utils.sol"], "versionPragmas": ["^0.8.22"], "artifacts": ["ERC1967Proxy"]}, "D:\\github\\tokendev-newroo\\Token\\node_modules\\@openzeppelin\\contracts\\proxy\\Proxy.sol": {"lastModificationDate": 1748024246013, "contentHash": "35308c2fa7be99a959494c37e8257c16", "sourceName": "@openzeppelin/contracts/proxy/Proxy.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["Proxy"]}, "D:\\github\\tokendev-newroo\\Token\\contracts\\SecurityTokenFactory.sol": {"lastModificationDate": 1750462462714, "contentHash": "25762de802cd9649ca91f8fab7f49dfd", "sourceName": "contracts/SecurityTokenFactory.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/access/AccessControl.sol", "@openzeppelin/contracts/utils/ReentrancyGuard.sol", "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol", "./SecurityToken.sol", "./Whitelist.sol", "./WhitelistWithKYC.sol", "./Compliance.sol", "./interfaces/ISecurityToken.sol", "./interfaces/ICompleteWhitelist.sol"], "versionPragmas": ["^0.8.22"], "artifacts": ["SecurityTokenFactory"]}, "D:\\github\\tokendev-newroo\\Token\\contracts\\SecurityToken.sol": {"lastModificationDate": 1750462216015, "contentHash": "7fef717f568cd0b925a816d17d5698f2", "sourceName": "contracts/SecurityToken.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol", "@openzeppelin/contracts-upgradeable/token/ERC20/extensions/ERC20BurnableUpgradeable.sol", "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol", "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol", "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol", "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol", "./interfaces/ICompleteWhitelist.sol", "./interfaces/ISecurityToken.sol", "./IdentityRegistry.sol", "./Compliance.sol"], "versionPragmas": ["^0.8.22"], "artifacts": ["SecurityToken"]}, "D:\\github\\tokendev-newroo\\Token\\contracts\\Whitelist.sol": {"lastModificationDate": 1748029019890, "contentHash": "0bc4691ef1b14188646be4aefcaf58fd", "sourceName": "contracts/Whitelist.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["./base/BaseIdentityRegistry.sol", "./interfaces/IIdentityRegistry.sol", "./interfaces/IKYCRegistry.sol", "./interfaces/ICompleteWhitelist.sol"], "versionPragmas": ["^0.8.22"], "artifacts": ["Whitelist"]}, "D:\\github\\tokendev-newroo\\Token\\contracts\\interfaces\\ISecurityToken.sol": {"lastModificationDate": 1750461312460, "contentHash": "93ebc0b2821b7c0f03642c078a2ea712", "sourceName": "contracts/interfaces/ISecurityToken.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol", "@openzeppelin/contracts/access/IAccessControl.sol"], "versionPragmas": ["^0.8.22"], "artifacts": ["ISecurityToken"]}, "D:\\github\\tokendev-newroo\\Token\\node_modules\\@openzeppelin\\contracts\\access\\AccessControl.sol": {"lastModificationDate": 1748024245948, "contentHash": "849b15469d8e2bd01b49e6c632e448e7", "sourceName": "@openzeppelin/contracts/access/AccessControl.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["./IAccessControl.sol", "../utils/Context.sol", "../utils/introspection/ERC165.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["AccessControl"]}, "D:\\github\\tokendev-newroo\\Token\\node_modules\\@openzeppelin\\contracts\\utils\\ReentrancyGuard.sol": {"lastModificationDate": 1748024246014, "contentHash": "190613e556d509d9e9a0ea43dc5d891d", "sourceName": "@openzeppelin/contracts/utils/ReentrancyGuard.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["Reentrancy<PERSON><PERSON>"]}, "D:\\github\\tokendev-newroo\\Token\\node_modules\\@openzeppelin\\contracts-upgradeable\\utils\\PausableUpgradeable.sol": {"lastModificationDate": 1748024245938, "contentHash": "a77dad8312dfaddab72fab403ee0c0a6", "sourceName": "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["../utils/ContextUpgradeable.sol", "../proxy/utils/Initializable.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["PausableUpgradeable"]}, "D:\\github\\tokendev-newroo\\Token\\node_modules\\@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol": {"lastModificationDate": 1748024245919, "contentHash": "d98c902f3c91d73bb9f688ad15065ab8", "sourceName": "@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/token/ERC20/IERC20.sol", "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol", "../../utils/ContextUpgradeable.sol", "@openzeppelin/contracts/interfaces/draft-IERC6093.sol", "../../proxy/utils/Initializable.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC20Upgradeable"]}, "D:\\github\\tokendev-newroo\\Token\\node_modules\\@openzeppelin\\contracts-upgradeable\\token\\ERC20\\extensions\\ERC20BurnableUpgradeable.sol": {"lastModificationDate": 1748024245917, "contentHash": "1715e45b25825865aadbbd2205cbfe06", "sourceName": "@openzeppelin/contracts-upgradeable/token/ERC20/extensions/ERC20BurnableUpgradeable.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["../ERC20Upgradeable.sol", "../../../utils/ContextUpgradeable.sol", "../../../proxy/utils/Initializable.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC20BurnableUpgradeable"]}, "D:\\github\\tokendev-newroo\\Token\\node_modules\\@openzeppelin\\contracts\\token\\ERC20\\extensions\\IERC20Metadata.sol": {"lastModificationDate": 1748024245998, "contentHash": "794db3115001aa372c79326fcfd44b1f", "sourceName": "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["../IERC20.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["IERC20Metadata"]}, "D:\\github\\tokendev-newroo\\Token\\node_modules\\@openzeppelin\\contracts\\token\\ERC20\\IERC20.sol": {"lastModificationDate": 1748024245998, "contentHash": "8f19f64d2adadf448840908bbaf431c8", "sourceName": "@openzeppelin/contracts/token/ERC20/IERC20.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["IERC20"]}, "D:\\github\\tokendev-newroo\\Token\\node_modules\\@openzeppelin\\contracts\\interfaces\\draft-IERC6093.sol": {"lastModificationDate": 1748024245962, "contentHash": "267d92fe4de67b1bdb3302c08f387dbf", "sourceName": "@openzeppelin/contracts/interfaces/draft-IERC6093.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["IERC1155Errors", "IERC20Errors", "IERC721Errors"]}, "D:\\github\\tokendev-newroo\\Token\\node_modules\\@openzeppelin\\contracts\\utils\\Context.sol": {"lastModificationDate": 1748024245957, "contentHash": "67bfbc07588eb8683b3fd8f6f909563e", "sourceName": "@openzeppelin/contracts/utils/Context.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["Context"]}, "D:\\github\\tokendev-newroo\\Token\\node_modules\\@openzeppelin\\contracts\\utils\\introspection\\ERC165.sol": {"lastModificationDate": 1748024245971, "contentHash": "7c03c1e37c3dc24eafb76dc2b8a5c3a6", "sourceName": "@openzeppelin/contracts/utils/introspection/ERC165.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["./IERC165.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC165"]}, "D:\\github\\tokendev-newroo\\Token\\contracts\\WhitelistV2.sol": {"lastModificationDate": 1748029019890, "contentHash": "b0ce38408e126f872431af2e9d906041", "sourceName": "contracts/WhitelistV2.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol", "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol", "./interfaces/IWhitelist.sol"], "versionPragmas": ["^0.8.22"], "artifacts": ["WhitelistV2"]}, "D:\\github\\tokendev-newroo\\Token\\contracts\\interfaces\\IWhitelist.sol": {"lastModificationDate": 1748018521870, "contentHash": "7642e85e88e86a6d7d9241193fd364a7", "sourceName": "contracts/interfaces/IWhitelist.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.22"], "artifacts": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "D:\\github\\tokendev-newroo\\Token\\contracts\\WhitelistWithClaims.sol": {"lastModificationDate": 1750184118498, "contentHash": "18071abc783aa92357a2cc2c9ea4ca3d", "sourceName": "contracts/WhitelistWithClaims.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["./base/BaseIdentityRegistry.sol", "./base/BaseKYCRegistry.sol", "./interfaces/ICompleteWhitelist.sol", "./ClaimRegistry.sol"], "versionPragmas": ["^0.8.22"], "artifacts": ["WhitelistWithClaims"]}, "D:\\github\\tokendev-newroo\\Token\\contracts\\ClaimRegistry.sol": {"lastModificationDate": 1750459512534, "contentHash": "c9ab769f8e261e38cff5ebb3d476df15", "sourceName": "contracts/ClaimRegistry.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol", "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol"], "versionPragmas": ["^0.8.22"], "artifacts": ["ClaimRegistry"]}, "D:\\github\\tokendev-newroo\\Token\\contracts\\IdentityRegistry.sol": {"lastModificationDate": 1750436005158, "contentHash": "10d25ca01efc9b2f1e773f8371f84f0f", "sourceName": "contracts/IdentityRegistry.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol", "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol", "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol", "./interfaces/IIdentityRegistry.sol", "./interfaces/IKYCRegistry.sol", "./ClaimRegistry.sol"], "versionPragmas": ["^0.8.22"], "artifacts": ["IdentityRegistry"]}, "D:\\github\\tokendev-newroo\\Token\\contracts\\Compliance.sol": {"lastModificationDate": 1750459838418, "contentHash": "19c69eeef40691641202fe78faee5f2a", "sourceName": "contracts/Compliance.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol", "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol", "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol", "./IdentityRegistry.sol"], "versionPragmas": ["^0.8.22"], "artifacts": ["Compliance"]}, "D:\\github\\tokendev-newroo\\Token\\contracts\\SimpleClaimRegistry.sol": {"lastModificationDate": 1750291255652, "contentHash": "1ecffd30ead374b78ad0da0c3c72552f", "sourceName": "contracts/SimpleClaimRegistry.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/access/AccessControl.sol"], "versionPragmas": ["^0.8.19"], "artifacts": ["SimpleClaimRegistry"]}, "D:\\github\\tokendev-newroo\\Token\\contracts\\interfaces\\ICompliance.sol": {"lastModificationDate": 1750461289304, "contentHash": "5bcecbd29b1c9cd8172a7effa92112bc", "sourceName": "contracts/interfaces/ICompliance.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.22"], "artifacts": ["ICompliance"]}, "D:\\github\\tokendev-newroo\\Token\\contracts\\SecurityTokenFactoryLite.sol": {"lastModificationDate": 1750480002727, "contentHash": "6c9674deac95fe27409f894d7395f504", "sourceName": "contracts/SecurityTokenFactoryLite.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/access/AccessControl.sol", "@openzeppelin/contracts/utils/ReentrancyGuard.sol", "./SecurityTokenLite.sol", "./IdentityRegistry.sol", "./Compliance.sol", "./ClaimRegistry.sol"], "versionPragmas": ["^0.8.22"], "artifacts": ["SecurityTokenFactoryLite"]}, "D:\\github\\tokendev-newroo\\Token\\contracts\\SecurityTokenLite.sol": {"lastModificationDate": 1750479000348, "contentHash": "60b1bc0cb75e10844208a22fc54cf6aa", "sourceName": "contracts/SecurityTokenLite.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/token/ERC20/ERC20.sol", "@openzeppelin/contracts/access/AccessControl.sol", "@openzeppelin/contracts/utils/ReentrancyGuard.sol"], "versionPragmas": ["^0.8.22"], "artifacts": ["SecurityTokenLite"]}, "D:\\github\\tokendev-newroo\\Token\\node_modules\\@openzeppelin\\contracts\\token\\ERC20\\ERC20.sol": {"lastModificationDate": 1748024245973, "contentHash": "57d79df281f57bbb1b09214c7914f877", "sourceName": "@openzeppelin/contracts/token/ERC20/ERC20.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["./IERC20.sol", "./extensions/IERC20Metadata.sol", "../../utils/Context.sol", "../../interfaces/draft-IERC6093.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC20"]}, "D:\\github\\tokendev-newroo\\Token\\contracts\\modules\\TransferModule.sol": {"lastModificationDate": 1750466039197, "contentHash": "c12df5972e23cd882635b76daa3808ec", "sourceName": "contracts/modules/TransferModule.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/access/AccessControl.sol", "@openzeppelin/contracts/utils/ReentrancyGuard.sol"], "versionPragmas": ["^0.8.22"], "artifacts": ["TransferModule"]}, "D:\\github\\tokendev-newroo\\Token\\contracts\\modules\\SecurityModule.sol": {"lastModificationDate": 1750466019138, "contentHash": "e80eb6eff74aac9771daf3176d359ffc", "sourceName": "contracts/modules/SecurityModule.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 1}, "viaIR": true, "metadata": {"bytecodeHash": "none"}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/access/AccessControl.sol", "@openzeppelin/contracts/utils/ReentrancyGuard.sol"], "versionPragmas": ["^0.8.22"], "artifacts": ["SecurityModule"]}}}