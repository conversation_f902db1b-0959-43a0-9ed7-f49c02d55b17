'use client';

import React, { useState, useEffect } from 'react';
import { ethers } from 'ethers';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

// Contract addresses from environment
const SECURITY_TOKEN_CORE_ADDRESS = process.env.NEXT_PUBLIC_AMOY_SECURITY_TOKEN_CORE_ADDRESS;
const UPGRADE_MANAGER_ADDRESS = process.env.NEXT_PUBLIC_AMOY_UPGRADE_MANAGER_ADDRESS;
const MODULAR_TOKEN_FACTORY_ADDRESS = process.env.NEXT_PUBLIC_AMOY_MODULAR_TOKEN_FACTORY_ADDRESS;

interface TokenFormData {
  name: string;
  symbol: string;
  decimals: number;
  maxSupply: string;
  adminAddress: string;
  tokenPrice: string;
  bonusTiers: string;
  tokenDetails: string;
  tokenImageUrl: string;
}

interface DeployedToken {
  address: string;
  name: string;
  symbol: string;
  transactionHash: string;
}

export default function CreateModularTokenPage() {
  const router = useRouter();
  
  // State Management
  const [provider, setProvider] = useState<ethers.BrowserProvider | null>(null);
  const [signer, setSigner] = useState<ethers.JsonRpcSigner | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [deployedToken, setDeployedToken] = useState<DeployedToken | null>(null);
  
  const [formData, setFormData] = useState<TokenFormData>({
    name: '',
    symbol: '',
    decimals: 0,
    maxSupply: '********',
    adminAddress: '',
    tokenPrice: '1.00 USD',
    bonusTiers: 'Early Bird: 10%, Standard: 5%, Late: 0%',
    tokenDetails: 'Security token with advanced compliance features',
    tokenImageUrl: ''
  });

  useEffect(() => {
    initializeProvider();
  }, []);

  const initializeProvider = async () => {
    try {
      if (typeof window !== 'undefined' && window.ethereum) {
        const provider = new ethers.BrowserProvider(window.ethereum);
        await provider.send('eth_requestAccounts', []);
        const signer = await provider.getSigner();
        const address = await signer.getAddress();
        
        setProvider(provider);
        setSigner(signer);
        setFormData(prev => ({ ...prev, adminAddress: address }));
      } else {
        setError('MetaMask not found. Please install MetaMask to create tokens.');
      }
    } catch (error) {
      console.error('Error initializing provider:', error);
      setError('Failed to connect to wallet');
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;
    
    let processedValue: any = value;
    
    if (name === 'decimals') {
      processedValue = parseInt(value, 10);
    }
    
    setFormData({
      ...formData,
      [name]: processedValue
    });
  };

  const deployModularToken = async () => {
    if (!signer || !MODULAR_TOKEN_FACTORY_ADDRESS) {
      setError('Please connect your wallet first');
      return;
    }

    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      // Import the ModularTokenFactory ABI
      const ModularTokenFactoryABI = await import('../../contracts/ModularTokenFactory.json');

      // Create factory contract instance
      const factory = new ethers.Contract(
        MODULAR_TOKEN_FACTORY_ADDRESS,
        ModularTokenFactoryABI.default,
        signer
      );

      // Check if user has DEPLOYER_ROLE
      const DEPLOYER_ROLE = await factory.DEPLOYER_ROLE();
      const userAddress = await signer.getAddress();
      const hasDeployerRole = await factory.hasRole(DEPLOYER_ROLE, userAddress);

      if (!hasDeployerRole) {
        setError('You do not have permission to deploy tokens. Please contact an administrator to grant you the DEPLOYER_ROLE.');
        return;
      }

      // Convert max supply to proper units
      const maxSupplyWei = ethers.parseUnits(formData.maxSupply, formData.decimals);

      // Deploy the token through the factory
      console.log('Deploying token with params:', {
        name: formData.name,
        symbol: formData.symbol,
        decimals: formData.decimals,
        maxSupply: formData.maxSupply,
        admin: formData.adminAddress,
        tokenPrice: formData.tokenPrice,
        bonusTiers: formData.bonusTiers,
        tokenDetails: formData.tokenDetails,
        tokenImageUrl: formData.tokenImageUrl
      });

      const tx = await factory.deployToken(
        formData.name,
        formData.symbol,
        formData.decimals,
        maxSupplyWei,
        formData.adminAddress,
        formData.tokenPrice,
        formData.bonusTiers,
        formData.tokenDetails,
        formData.tokenImageUrl
      );

      console.log('Transaction sent:', tx.hash);
      setSuccess('Transaction sent! Waiting for confirmation...');

      const receipt = await tx.wait();
      console.log('Transaction confirmed:', receipt);

      // Get the deployed token address from the event
      const event = receipt.logs.find((log: any) => {
        try {
          const parsed = factory.interface.parseLog(log);
          return parsed.name === 'TokenDeployed';
        } catch {
          return false;
        }
      });

      if (event) {
        const parsedEvent = factory.interface.parseLog(event);
        const tokenAddress = parsedEvent.args.tokenAddress;

        setDeployedToken({
          address: tokenAddress,
          name: formData.name,
          symbol: formData.symbol,
          transactionHash: tx.hash
        });

        setSuccess(`Modular token deployed successfully! Address: ${tokenAddress}`);
      } else {
        setError('Token deployed but could not find deployment event. Please check the transaction.');
      }

    } catch (error: any) {
      console.error('Error deploying modular token:', error);

      // Parse common error messages
      let errorMessage = error.message;
      if (error.message.includes('AccessControlUnauthorizedAccount')) {
        errorMessage = 'You do not have permission to deploy tokens. Please contact an administrator.';
      } else if (error.message.includes('execution reverted')) {
        errorMessage = 'Transaction failed. Please check your inputs and try again.';
      }

      setError(`Failed to deploy modular token: ${errorMessage}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validation
    if (!formData.name || !formData.symbol || !formData.adminAddress) {
      setError('Please fill in all required fields');
      return;
    }
    
    if (formData.decimals < 0 || formData.decimals > 18) {
      setError('Decimals must be between 0 and 18');
      return;
    }
    
    await deployModularToken();
  };

  if (!provider || !signer) {
    return (
      <div>
        <div className="mb-6 flex items-center">
          <Link href="/" className="text-blue-600 hover:text-blue-800 mr-4">
            &larr; Back to Dashboard
          </Link>
          <h1 className="text-3xl font-bold">Create Modular Security Token</h1>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-bold mb-2">Connect Wallet</h2>
          <p className="text-gray-600 mb-4">
            Please connect your MetaMask wallet to create modular tokens.
          </p>
          <button 
            onClick={initializeProvider}
            className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
          >
            Connect Wallet
          </button>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-6 flex items-center">
        <Link href="/" className="text-blue-600 hover:text-blue-800 mr-4">
          &larr; Back to Dashboard
        </Link>
        <h1 className="text-3xl font-bold">Create Modular Security Token</h1>
        <span className="ml-4 px-3 py-1 text-xs font-medium rounded-full bg-purple-100 text-purple-800">
          Amoy Testnet
        </span>
      </div>

      {/* Modular Architecture Notice */}
      <div className="mb-6 p-4 bg-purple-50 border border-purple-200 rounded-lg">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <span className="text-purple-600 text-xl">🚀</span>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-purple-800">
              Next-Generation Modular Architecture
            </h3>
            <div className="mt-1 text-sm text-purple-700">
              <p>Create tokens using our advanced modular architecture with:</p>
              <ul className="mt-1 list-disc list-inside space-y-1">
                <li><strong>Upgradeable Contracts:</strong> Future-proof with secure upgrade mechanisms</li>
                <li><strong>Modular Design:</strong> Add new features without redeployment</li>
                <li><strong>Enhanced Security:</strong> Timelock protection and emergency controls</li>
                <li><strong>Gas Optimization:</strong> Efficient proxy pattern implementation</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Contract Information */}
      <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h3 className="text-sm font-medium text-blue-800 mb-2">Modular Architecture Contracts</h3>
        <div className="text-sm text-blue-700 space-y-1">
          <div>
            <span className="font-medium">ModularTokenFactory:</span>
            <span className="ml-2 font-mono text-xs">{MODULAR_TOKEN_FACTORY_ADDRESS}</span>
          </div>
          <div>
            <span className="font-medium">SecurityTokenCore Implementation:</span>
            <span className="ml-2 font-mono text-xs">{SECURITY_TOKEN_CORE_ADDRESS}</span>
          </div>
          <div>
            <span className="font-medium">UpgradeManager:</span>
            <span className="ml-2 font-mono text-xs">{UPGRADE_MANAGER_ADDRESS}</span>
          </div>
        </div>
      </div>

      {/* Error Notification */}
      {error && (
        <div className="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <div className="flex items-center">
            <span className="text-red-600 mr-2">⚠️</span>
            <span>{error}</span>
          </div>
        </div>
      )}

      {/* Success Notification */}
      {success && (
        <div className="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
          <div className="flex items-center">
            <span className="text-green-600 mr-2">✅</span>
            <span>{success}</span>
          </div>
        </div>
      )}

      {/* Deployed Token Result */}
      {deployedToken && (
        <div className="mb-6 bg-green-50 border border-green-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-green-800 mb-4">🎉 Modular Token Created Successfully!</h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="font-medium">Token Name:</span>
              <span>{deployedToken.name}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Symbol:</span>
              <span>{deployedToken.symbol}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Contract Address:</span>
              <span className="font-mono text-xs">{deployedToken.address}</span>
            </div>
          </div>
          <div className="mt-4 flex gap-2">
            <button
              onClick={() => window.open(`https://amoy.polygonscan.com/address/${deployedToken.address}`, '_blank')}
              className="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded text-sm"
            >
              View on PolygonScan
            </button>
            <Link
              href="/modular-tokens"
              className="bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded text-sm"
            >
              Manage Token
            </Link>
          </div>
        </div>
      )}

      {/* Token Creation Form */}
      {!deployedToken && (
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-bold mb-4">Token Configuration</h2>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                  Token Name *
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="e.g., Augment Security Token"
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  required
                />
              </div>
              
              <div>
                <label htmlFor="symbol" className="block text-sm font-medium text-gray-700">
                  Token Symbol *
                </label>
                <input
                  type="text"
                  id="symbol"
                  name="symbol"
                  value={formData.symbol}
                  onChange={handleInputChange}
                  placeholder="e.g., AST"
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  required
                />
              </div>
              
              <div>
                <label htmlFor="decimals" className="block text-sm font-medium text-gray-700">
                  Decimals *
                </label>
                <select
                  id="decimals"
                  name="decimals"
                  value={formData.decimals}
                  onChange={handleInputChange}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  required
                >
                  {[...Array(19)].map((_, i) => (
                    <option key={i} value={i}>{i} decimals</option>
                  ))}
                </select>
              </div>
              
              <div>
                <label htmlFor="maxSupply" className="block text-sm font-medium text-gray-700">
                  Maximum Supply *
                </label>
                <input
                  type="number"
                  id="maxSupply"
                  name="maxSupply"
                  value={formData.maxSupply}
                  onChange={handleInputChange}
                  placeholder="********"
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  required
                />
              </div>
            </div>
            
            <div>
              <label htmlFor="adminAddress" className="block text-sm font-medium text-gray-700">
                Admin Address *
              </label>
              <input
                type="text"
                id="adminAddress"
                name="adminAddress"
                value={formData.adminAddress}
                onChange={handleInputChange}
                placeholder="0x..."
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                required
              />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="tokenPrice" className="block text-sm font-medium text-gray-700">
                  Token Price
                </label>
                <input
                  type="text"
                  id="tokenPrice"
                  name="tokenPrice"
                  value={formData.tokenPrice}
                  onChange={handleInputChange}
                  placeholder="1.00 USD"
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              
              <div>
                <label htmlFor="tokenImageUrl" className="block text-sm font-medium text-gray-700">
                  Token Image URL
                </label>
                <input
                  type="url"
                  id="tokenImageUrl"
                  name="tokenImageUrl"
                  value={formData.tokenImageUrl}
                  onChange={handleInputChange}
                  placeholder="https://..."
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
            
            <div>
              <label htmlFor="bonusTiers" className="block text-sm font-medium text-gray-700">
                Bonus Tiers
              </label>
              <textarea
                id="bonusTiers"
                name="bonusTiers"
                value={formData.bonusTiers}
                onChange={handleInputChange}
                rows={2}
                placeholder="Early Bird: 10%, Standard: 5%, Late: 0%"
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            
            <div>
              <label htmlFor="tokenDetails" className="block text-sm font-medium text-gray-700">
                Token Details
              </label>
              <textarea
                id="tokenDetails"
                name="tokenDetails"
                value={formData.tokenDetails}
                onChange={handleInputChange}
                rows={3}
                placeholder="Describe your security token..."
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            
            <div className="pt-4">
              <button
                type="submit"
                disabled={isSubmitting}
                className="w-full bg-purple-600 hover:bg-purple-700 text-white font-bold py-3 px-4 rounded disabled:opacity-50"
              >
                {isSubmitting ? '🔄 Creating Modular Token...' : '🚀 Create Modular Token'}
              </button>
            </div>
          </form>
        </div>
      )}
      
      {/* Production Ready Notice */}
      <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <span className="text-green-600 text-xl">✅</span>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-green-800">
              Production Ready
            </h3>
            <div className="mt-1 text-sm text-green-700">
              <p>This interface deploys real modular security tokens using our factory contract. Each token is a new proxy instance with full upgrade capabilities.</p>
              <p className="mt-1">All tokens created here are fully functional and ready for production use on Amoy testnet.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
