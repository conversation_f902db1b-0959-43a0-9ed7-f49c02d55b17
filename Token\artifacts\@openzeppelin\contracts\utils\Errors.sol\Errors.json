{"_format": "hh-sol-artifact-1", "contractName": "Errors", "sourceName": "@openzeppelin/contracts/utils/Errors.sol", "abi": [{"inputs": [], "name": "FailedCall", "type": "error"}, {"inputs": [], "name": "FailedDeployment", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "MissingPrecompile", "type": "error"}], "bytecode": "0x60808060405234601757603a9081601d823930815050f35b600080fdfe600080fdfea264697066735822122039ed580232ab736c15c1c551ad7052d241f461ea3c4d71cdc312790fd2fb3f5064736f6c63430008160033", "deployedBytecode": "0x600080fdfea264697066735822122039ed580232ab736c15c1c551ad7052d241f461ea3c4d71cdc312790fd2fb3f5064736f6c63430008160033", "linkReferences": {}, "deployedLinkReferences": {}}