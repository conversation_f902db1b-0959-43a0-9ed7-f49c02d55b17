{"manifestVersion": "3.2", "proxies": [{"address": "0x7c04b9cB950652a5f4699bC19DA3846f2ae152bB", "txHash": "0x8f49575cdbba0475120d4dbcb28bbac259a9e4c435bec7396bc43e2b25240d6c", "kind": "uups"}, {"address": "0x516238C9f951DC21c8dE2C8FAAe476C265Cd741D", "txHash": "0xb08e5e5be07278d1b2b4c19bd43c885cc97632539847b537ed49860716349bb7", "kind": "uups"}, {"address": "0x29cb357b34831F38Ca8DC1c15A45B36c19dDCc7f", "txHash": "0xcb49127fe7f6e0c17d2e727acb19a37d9158162b8536a3ebd2ef27c1a178a234", "kind": "uups"}, {"address": "0x3B2676F95C045fDE89884A9528f1B1F7BB9F63A6", "txHash": "0x37d4e57bfc98a264745e63cfaac412fe03b595a372bcd5ec7abd76434cc58a3a", "kind": "uups"}, {"address": "0x651BdA6c84743BC6099C16a5D81c039AB02E5682", "txHash": "0xc1805803ed73f6a1fa6254675621046ddcd9505ccf4dd39228b6af0fc3efa08c", "kind": "uups"}, {"address": "0xE14f9Cc205bC45E718DDC0E4A4900b41e66e4041", "txHash": "0x5983f2ad81e1087e9658ad19fd1a0ef1382e2e87dc13943b7bf88f7fabd28675", "kind": "uups"}, {"address": "0xafcdF8d6bcCf5BF5004c633C41a30D202B10b951", "txHash": "0x844cba4122e7c379086104d181d30f00566b1cc794618eb440aaa581146688ac", "kind": "uups"}, {"address": "0x129E04323E4c9bFBD097489473d8523E4015Bfc3", "txHash": "0x0e593b041394b70c611b6ec872d6c911aff132b370adf0943129504b497cabd4", "kind": "uups"}, {"address": "0xA2b73E82712AE920D5CBA00f32496592FcCBD1F2", "txHash": "0x57bcefcac8e383a69c2ab5633a0e7e05c3b730800b6b08997979135c14531262", "kind": "uups"}], "impls": {"495d52da6f87065b02d685ed742c140a03fd1b313d3e253fcb9fb250cc5e3312": {"address": "0x284B53bBD6Be2bF17E42aB97B1b99B249643258D", "txHash": "0xc1398afb0c9dd1d38f3a0431e3d26f1b3a4fdda841339adb93679ead90181618", "layout": {"solcVersion": "0.8.22", "storage": [{"label": "claims", "offset": 0, "slot": "0", "type": "t_mapping(t_address,t_mapping(t_uint256,t_mapping(t_bytes32,t_struct(Claim)1914_storage)))", "contract": "ClaimRegistry", "src": "contracts\\ClaimRegistry.sol:35"}, {"label": "claimIds", "offset": 0, "slot": "1", "type": "t_mapping(t_address,t_mapping(t_uint256,t_array(t_bytes32)dyn_storage))", "contract": "ClaimRegistry", "src": "contracts\\ClaimRegistry.sol:38"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "offset": 0, "slot": "2", "type": "t_mapping(t_address,t_mapping(t_uint256,t_bool))", "contract": "ClaimRegistry", "src": "contracts\\ClaimRegistry.sol:41"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)24_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)34_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(InitializableStorage)145_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)24_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_array(t_bytes32)dyn_storage": {"label": "bytes32[]", "numberOfBytes": "32"}, "t_bytes_storage": {"label": "bytes", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_uint256,t_array(t_bytes32)dyn_storage))": {"label": "mapping(address => mapping(uint256 => bytes32[]))", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_uint256,t_bool))": {"label": "mapping(address => mapping(uint256 => bool))", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_uint256,t_mapping(t_bytes32,t_struct(Claim)1914_storage)))": {"label": "mapping(address => mapping(uint256 => mapping(bytes32 => struct ClaimRegistry.Claim)))", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(Claim)1914_storage)": {"label": "mapping(bytes32 => struct ClaimRegistry.Claim)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_array(t_bytes32)dyn_storage)": {"label": "mapping(uint256 => bytes32[])", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_bool)": {"label": "mapping(uint256 => bool)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_mapping(t_bytes32,t_struct(Claim)1914_storage))": {"label": "mapping(uint256 => mapping(bytes32 => struct ClaimRegistry.Claim))", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(Claim)1914_storage": {"label": "struct ClaimRegistry.Claim", "members": [{"label": "claimType", "type": "t_uint256", "offset": 0, "slot": "0"}, {"label": "issuer", "type": "t_address", "offset": 0, "slot": "1"}, {"label": "signature", "type": "t_bytes_storage", "offset": 0, "slot": "2"}, {"label": "data", "type": "t_bytes_storage", "offset": 0, "slot": "3"}, {"label": "uri", "type": "t_string_storage", "offset": 0, "slot": "4"}, {"label": "issuedAt", "type": "t_uint256", "offset": 0, "slot": "5"}, {"label": "expiresAt", "type": "t_uint256", "offset": 0, "slot": "6"}, {"label": "revoked", "type": "t_bool", "offset": 0, "slot": "7"}], "numberOfBytes": "256"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}}, "namespaces": {"erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:61", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "caad62e8ab9e3b9f375ce0c69d31a8c07ff8b227608af6c5d5f3b8883479e859": {"address": "0xf5b9671368f4C0a582105ff020908f387f827a57", "txHash": "0x049de41040337f424b3d5dc578d2776b1089967248cba864eae3746434cee07a", "layout": {"solcVersion": "0.8.22", "storage": [{"label": "_identities", "offset": 0, "slot": "0", "type": "t_mapping(t_address,t_struct(Identity)5224_storage)", "contract": "IdentityRegistry", "src": "contracts\\IdentityRegistry.sol:41"}, {"label": "_countryInvestorCount", "offset": 0, "slot": "1", "type": "t_mapping(t_uint16,t_uint256)", "contract": "IdentityRegistry", "src": "contracts\\IdentityRegistry.sol:42"}, {"label": "_restrictedCountries", "offset": 0, "slot": "2", "type": "t_mapping(t_uint16,t_bool)", "contract": "IdentityRegistry", "src": "contracts\\IdentityRegistry.sol:43"}, {"label": "_verifiedAddresses", "offset": 0, "slot": "3", "type": "t_array(t_address)dyn_storage", "contract": "IdentityRegistry", "src": "contracts\\IdentityRegistry.sol:46"}, {"label": "_verifiedAddressIndex", "offset": 0, "slot": "4", "type": "t_mapping(t_address,t_uint256)", "contract": "IdentityRegistry", "src": "contracts\\IdentityRegistry.sol:47"}, {"label": "claimRegistry", "offset": 0, "slot": "5", "type": "t_contract(ClaimRegistry)3841", "contract": "IdentityRegistry", "src": "contracts\\IdentityRegistry.sol:50"}, {"label": "_requiredClaimTopics", "offset": 0, "slot": "6", "type": "t_array(t_uint256)dyn_storage", "contract": "IdentityRegistry", "src": "contracts\\IdentityRegistry.sol:53"}, {"label": "_isRequiredClaimTopic", "offset": 0, "slot": "7", "type": "t_mapping(t_uint256,t_bool)", "contract": "IdentityRegistry", "src": "contracts\\IdentityRegistry.sol:54"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)24_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)34_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(InitializableStorage)145_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)586_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)24_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_array(t_address)dyn_storage": {"label": "address[]", "numberOfBytes": "32"}, "t_array(t_uint256)dyn_storage": {"label": "uint256[]", "numberOfBytes": "32"}, "t_contract(ClaimRegistry)3841": {"label": "contract ClaimRegistry", "numberOfBytes": "20"}, "t_mapping(t_address,t_struct(Identity)5224_storage)": {"label": "mapping(address => struct IdentityRegistry.Identity)", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_uint16,t_bool)": {"label": "mapping(uint16 => bool)", "numberOfBytes": "32"}, "t_mapping(t_uint16,t_uint256)": {"label": "mapping(uint16 => uint256)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_bool)": {"label": "mapping(uint256 => bool)", "numberOfBytes": "32"}, "t_struct(Identity)5224_storage": {"label": "struct IdentityRegistry.Identity", "members": [{"label": "isVerified", "type": "t_bool", "offset": 0, "slot": "0"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "t_bool", "offset": 1, "slot": "0"}, {"label": "isKycApproved", "type": "t_bool", "offset": 2, "slot": "0"}, {"label": "isFrozen", "type": "t_bool", "offset": 3, "slot": "0"}, {"label": "country", "type": "t_uint16", "offset": 4, "slot": "0"}, {"label": "registeredAt", "type": "t_uint256", "offset": 0, "slot": "1"}, {"label": "updatedAt", "type": "t_uint256", "offset": 0, "slot": "2"}], "numberOfBytes": "96"}, "t_uint16": {"label": "uint16", "numberOfBytes": "2"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ReentrancyGuardUpgradeable.sol:43", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:61", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "119433ed1e3555bdf0ad71e3b3fda8a4589bba3ce27b4ace712bb0d314b8010f": {"address": "0x2E97eFACa4517047B2B91e74D56B1eBe0BEAFD48", "txHash": "0x1cc19117e90d7e19488c1e94b1a220ff00febfd34fbe9277039312c89911ecc7", "layout": {"solcVersion": "0.8.22", "storage": [{"label": "identityRegistry", "offset": 0, "slot": "0", "type": "t_contract(IdentityRegistry)6894", "contract": "Compliance", "src": "contracts\\Compliance.sol:44"}, {"label": "tokenAddress", "offset": 0, "slot": "1", "type": "t_address", "contract": "Compliance", "src": "contracts\\Compliance.sol:45"}, {"label": "_complianceRules", "offset": 0, "slot": "2", "type": "t_mapping(t_bytes32,t_struct(ComplianceRule)3884_storage)", "contract": "Compliance", "src": "contracts\\Compliance.sol:48"}, {"label": "_activeRuleIds", "offset": 0, "slot": "3", "type": "t_array(t_bytes32)dyn_storage", "contract": "Compliance", "src": "contracts\\Compliance.sol:49"}, {"label": "_isActiveRule", "offset": 0, "slot": "4", "type": "t_mapping(t_bytes32,t_bool)", "contract": "Compliance", "src": "contracts\\Compliance.sol:50"}, {"label": "_transferData", "offset": 0, "slot": "5", "type": "t_struct(TransferData)3897_storage", "contract": "Compliance", "src": "contracts\\Compliance.sol:53"}, {"label": "_holderBalances", "offset": 0, "slot": "9", "type": "t_mapping(t_address,t_uint256)", "contract": "Compliance", "src": "contracts\\Compliance.sol:56"}, {"label": "_holders", "offset": 0, "slot": "10", "type": "t_array(t_address)dyn_storage", "contract": "Compliance", "src": "contracts\\Compliance.sol:57"}, {"label": "_holderIndex", "offset": 0, "slot": "11", "type": "t_mapping(t_address,t_uint256)", "contract": "Compliance", "src": "contracts\\Compliance.sol:58"}, {"label": "_totalHolders", "offset": 0, "slot": "12", "type": "t_uint256", "contract": "Compliance", "src": "contracts\\Compliance.sol:59"}, {"label": "_countryHolderCount", "offset": 0, "slot": "13", "type": "t_mapping(t_uint16,t_uint256)", "contract": "Compliance", "src": "contracts\\Compliance.sol:62"}, {"label": "_countryHolders", "offset": 0, "slot": "14", "type": "t_mapping(t_uint16,t_array(t_address)dyn_storage)", "contract": "Compliance", "src": "contracts\\Compliance.sol:63"}, {"label": "_countryHolderIndex", "offset": 0, "slot": "15", "type": "t_mapping(t_uint16,t_mapping(t_address,t_uint256))", "contract": "Compliance", "src": "contracts\\Compliance.sol:64"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)24_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)34_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(InitializableStorage)145_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)586_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)24_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_array(t_address)dyn_storage": {"label": "address[]", "numberOfBytes": "32"}, "t_array(t_bytes32)dyn_storage": {"label": "bytes32[]", "numberOfBytes": "32"}, "t_contract(IdentityRegistry)6894": {"label": "contract IdentityRegistry", "numberOfBytes": "20"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_bool)": {"label": "mapping(bytes32 => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(ComplianceRule)3884_storage)": {"label": "mapping(bytes32 => struct Compliance.ComplianceRule)", "numberOfBytes": "32"}, "t_mapping(t_uint16,t_array(t_address)dyn_storage)": {"label": "mapping(uint16 => address[])", "numberOfBytes": "32"}, "t_mapping(t_uint16,t_bool)": {"label": "mapping(uint16 => bool)", "numberOfBytes": "32"}, "t_mapping(t_uint16,t_mapping(t_address,t_uint256))": {"label": "mapping(uint16 => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_uint16,t_uint256)": {"label": "mapping(uint16 => uint256)", "numberOfBytes": "32"}, "t_struct(ComplianceRule)3884_storage": {"label": "struct Compliance.ComplianceRule", "members": [{"label": "isActive", "type": "t_bool", "offset": 0, "slot": "0"}, {"label": "maxHolders", "type": "t_uint256", "offset": 0, "slot": "1"}, {"label": "maxTokensPerHolder", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "maxTotalSupply", "type": "t_uint256", "offset": 0, "slot": "3"}, {"label": "countryLimits", "type": "t_mapping(t_uint16,t_uint256)", "offset": 0, "slot": "4"}, {"label": "restrictedCountries", "type": "t_mapping(t_uint16,t_bool)", "offset": 0, "slot": "5"}], "numberOfBytes": "192"}, "t_struct(TransferData)3897_storage": {"label": "struct Compliance.TransferData", "members": [{"label": "totalTransfers", "type": "t_uint256", "offset": 0, "slot": "0"}, {"label": "lastTransferTime", "type": "t_uint256", "offset": 0, "slot": "1"}, {"label": "transferCount", "type": "t_mapping(t_address,t_uint256)", "offset": 0, "slot": "2"}, {"label": "lastTransferTimeByAddress", "type": "t_mapping(t_address,t_uint256)", "offset": 0, "slot": "3"}], "numberOfBytes": "128"}, "t_uint16": {"label": "uint16", "numberOfBytes": "2"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ReentrancyGuardUpgradeable.sol:43", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:61", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "395e6d3666b96aa20d391510de89c302a358f81a4311d80240a8853d82e237f2": {"address": "0xdF80BFB83BA309d03da8317d5Fe8B80a869D7282", "layout": {"solcVersion": "0.8.22", "storage": [{"label": "_maxSupply", "offset": 0, "slot": "0", "type": "t_uint256", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:36"}, {"label": "_decimals", "offset": 0, "slot": "1", "type": "t_uint8", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:39"}, {"label": "_tokenPrice", "offset": 0, "slot": "2", "type": "t_string_storage", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:42"}, {"label": "_bonusTiers", "offset": 0, "slot": "3", "type": "t_string_storage", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:43"}, {"label": "_tokenDetails", "offset": 0, "slot": "4", "type": "t_string_storage", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:44"}, {"label": "_tokenImageUrl", "offset": 0, "slot": "5", "type": "t_string_storage", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:45"}, {"label": "_identityRegistry", "offset": 0, "slot": "6", "type": "t_contract(IdentityRegistry)7962", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:48"}, {"label": "_compliance", "offset": 0, "slot": "7", "type": "t_contract(Compliance)6228", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:49"}, {"label": "_<PERSON><PERSON><PERSON><PERSON><PERSON>", "offset": 0, "slot": "8", "type": "t_contract(ICompleteWhitelist)15480", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:52"}, {"label": "_forcedTransferInProgress", "offset": 20, "slot": "8", "type": "t_bool", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:55"}, {"label": "_approvedTransferInProgress", "offset": 21, "slot": "8", "type": "t_bool", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:58"}, {"label": "_agentList", "offset": 0, "slot": "9", "type": "t_array(t_address)dyn_storage", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:64"}, {"label": "_agentListIndex", "offset": 0, "slot": "10", "type": "t_mapping(t_address,t_uint256)", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:66"}, {"label": "_conditionalTransfersEnabled", "offset": 0, "slot": "11", "type": "t_bool", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:78"}, {"label": "_transferApprovals", "offset": 0, "slot": "12", "type": "t_mapping(t_bytes32,t_bool)", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:79"}, {"label": "_transferNonces", "offset": 0, "slot": "13", "type": "t_mapping(t_address,t_uint256)", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:80"}, {"label": "_transfer<PERSON><PERSON><PERSON>stEnabled", "offset": 0, "slot": "14", "type": "t_bool", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:83"}, {"label": "_transfer<PERSON><PERSON><PERSON><PERSON>", "offset": 0, "slot": "15", "type": "t_mapping(t_address,t_bool)", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:84"}, {"label": "_transferFeesEnabled", "offset": 0, "slot": "16", "type": "t_bool", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:87"}, {"label": "_transferFeePercentage", "offset": 0, "slot": "17", "type": "t_uint256", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:88"}, {"label": "_feeCollector", "offset": 0, "slot": "18", "type": "t_address", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:89"}, {"label": "_agreementAcceptances", "offset": 0, "slot": "19", "type": "t_mapping(t_address,t_uint256)", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:92"}, {"label": "_emergencyPaused", "offset": 0, "slot": "20", "type": "t_bool", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:95"}, {"label": "_functionPaused", "offset": 0, "slot": "21", "type": "t_mapping(t_bytes4,t_bool)", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:96"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)24_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)34_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ERC20Storage)294_storage": {"label": "struct ERC20Upgradeable.ERC20Storage", "members": [{"label": "_balances", "type": "t_mapping(t_address,t_uint256)", "offset": 0, "slot": "0"}, {"label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "offset": 0, "slot": "1"}, {"label": "_totalSupply", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "_name", "type": "t_string_storage", "offset": 0, "slot": "3"}, {"label": "_symbol", "type": "t_string_storage", "offset": 0, "slot": "4"}], "numberOfBytes": "160"}, "t_struct(InitializableStorage)145_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PausableStorage)522_storage": {"label": "struct PausableUpgradeable.PausableStorage", "members": [{"label": "_paused", "type": "t_bool", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)586_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)24_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_array(t_address)dyn_storage": {"label": "address[]", "numberOfBytes": "32"}, "t_bytes4": {"label": "bytes4", "numberOfBytes": "4"}, "t_contract(Compliance)6228": {"label": "contract Compliance", "numberOfBytes": "20"}, "t_contract(ICompleteWhitelist)15480": {"label": "contract ICompleteWhitelist", "numberOfBytes": "20"}, "t_contract(IdentityRegistry)7962": {"label": "contract IdentityRegistry", "numberOfBytes": "20"}, "t_mapping(t_bytes32,t_bool)": {"label": "mapping(bytes32 => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes4,t_bool)": {"label": "mapping(bytes4 => bool)", "numberOfBytes": "32"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ReentrancyGuardUpgradeable.sol:43", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:61", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Pausable": [{"contract": "PausableUpgradeable", "label": "_paused", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\utils\\PausableUpgradeable.sol:21", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.ERC20": [{"contract": "ERC20Upgradeable", "label": "_balances", "type": "t_mapping(t_address,t_uint256)", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:33", "offset": 0, "slot": "0"}, {"contract": "ERC20Upgradeable", "label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:35", "offset": 0, "slot": "1"}, {"contract": "ERC20Upgradeable", "label": "_totalSupply", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:37", "offset": 0, "slot": "2"}, {"contract": "ERC20Upgradeable", "label": "_name", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:39", "offset": 0, "slot": "3"}, {"contract": "ERC20Upgradeable", "label": "_symbol", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:40", "offset": 0, "slot": "4"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}}}}