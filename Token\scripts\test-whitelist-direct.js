const { ethers } = require("hardhat");

async function main() {
  const tokenAddress = "******************************************";
  const testAddress = "******************************************";
  
  console.log("🔧 DIRECT WHITELIST TEST");
  console.log("=" .repeat(50));
  console.log("Token Address:", tokenAddress);
  console.log("Test Address:", testAddress);
  
  const [deployer] = await ethers.getSigners();
  console.log("Using account:", deployer.address);
  
  const SecurityTokenMinimal = await ethers.getContractFactory("SecurityTokenMinimal");
  const token = SecurityTokenMinimal.attach(tokenAddress);
  
  try {
    // Check current status
    console.log("\n📋 CURRENT STATUS:");
    const isWhitelisted = await token.isWhitelisted(testAddress);
    const isKyc = await token.isKycApproved(testAddress);
    const isVerified = await token.isVerified(testAddress);
    
    console.log(`Whitelisted: ${isWhitelisted}`);
    console.log(`KYC Approved: ${isKyc}`);
    console.log(`Verified: ${isVerified}`);
    
    // Test addToWhitelist function
    if (!isWhitelisted) {
      console.log("\n🔧 TESTING addToWhitelist():");
      
      try {
        const tx = await token.addToWhitelist(testAddress);
        console.log("✅ Transaction submitted:", tx.hash);
        
        const receipt = await tx.wait();
        console.log("✅ Transaction confirmed in block:", receipt.blockNumber);
        console.log("✅ Gas used:", receipt.gasUsed.toString());
        
        // Check new status
        const newWhitelistStatus = await token.isWhitelisted(testAddress);
        console.log("✅ New whitelist status:", newWhitelistStatus);
        
      } catch (error) {
        console.log("❌ addToWhitelist failed:", error.message);
        
        // Try updateWhitelist as fallback
        console.log("\n🔧 TESTING updateWhitelist():");
        try {
          const tx = await token.updateWhitelist(testAddress, true);
          console.log("✅ updateWhitelist transaction submitted:", tx.hash);
          
          const receipt = await tx.wait();
          console.log("✅ Transaction confirmed in block:", receipt.blockNumber);
          
          const newWhitelistStatus = await token.isWhitelisted(testAddress);
          console.log("✅ New whitelist status:", newWhitelistStatus);
          
        } catch (error2) {
          console.log("❌ updateWhitelist also failed:", error2.message);
        }
      }
    } else {
      console.log("✅ Address is already whitelisted");
    }
    
    // Test KYC approval
    if (!isKyc) {
      console.log("\n🔧 TESTING approveKyc():");
      try {
        const tx = await token.approveKyc(testAddress);
        console.log("✅ KYC transaction submitted:", tx.hash);
        
        const receipt = await tx.wait();
        console.log("✅ Transaction confirmed in block:", receipt.blockNumber);
        
        const newKycStatus = await token.isKycApproved(testAddress);
        const newVerifiedStatus = await token.isVerified(testAddress);
        console.log("✅ New KYC status:", newKycStatus);
        console.log("✅ New verified status:", newVerifiedStatus);
        
      } catch (error) {
        console.log("❌ approveKyc failed:", error.message);
      }
    } else {
      console.log("✅ Address already has KYC approval");
    }
    
    console.log("\n🎯 FINAL STATUS:");
    const finalWhitelisted = await token.isWhitelisted(testAddress);
    const finalKyc = await token.isKycApproved(testAddress);
    const finalVerified = await token.isVerified(testAddress);
    
    console.log(`Final Whitelisted: ${finalWhitelisted}`);
    console.log(`Final KYC: ${finalKyc}`);
    console.log(`Final Verified: ${finalVerified}`);
    
  } catch (error) {
    console.error("❌ Error during test:", error.message);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
