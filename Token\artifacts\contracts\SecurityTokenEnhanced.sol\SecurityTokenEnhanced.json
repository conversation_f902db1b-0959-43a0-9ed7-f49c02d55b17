{"_format": "hh-sol-artifact-1", "contractName": "SecurityTokenEnhanced", "sourceName": "contracts/SecurityTokenEnhanced.sol", "abi": [{"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint8", "name": "decimals_", "type": "uint8"}, {"internalType": "uint256", "name": "maxSupply_", "type": "uint256"}, {"internalType": "address", "name": "admin", "type": "address"}, {"internalType": "string", "name": "tokenPrice_", "type": "string"}, {"internalType": "string", "name": "bonusTiers_", "type": "string"}, {"internalType": "string", "name": "tokenImageUrl_", "type": "string"}, {"internalType": "address", "name": "identityRegistry_", "type": "address"}, {"internalType": "address", "name": "compliance_", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientAllowance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "name": "ERC20InvalidApprover", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "name": "ERC20InvalidReceiver", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "ERC20InvalidSender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "name": "ERC20InvalidSpender", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "agent", "type": "address"}], "name": "AgentAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "agent", "type": "address"}], "name": "AgentRemoved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "AgreementAccepted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "admin", "type": "address"}], "name": "EmergencyPaused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "admin", "type": "address"}], "name": "EmergencyUnpaused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [], "name": "AGENT_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "TRANSFER_MANAGER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "acceptAgreement", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "agent", "type": "address"}], "name": "addAgent", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "agents", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "bonusTiers", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "compliance", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "emergencyPause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "emergencyUnpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "forcedTransfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "index", "type": "uint256"}], "name": "getAgentAt", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getAgentCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "getAgreementAcceptanceTimestamp", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getAllAgents", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "hasAcceptedAgreement", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "identityRegistry", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "isAgent", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "isEmergencyPaused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "functionSelector", "type": "bytes4"}], "name": "isFunctionPaused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "maxSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "mint", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "functionSelector", "type": "bytes4"}], "name": "pauseFunction", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "agent", "type": "address"}], "name": "removeAgent", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "tokenImageUrl", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "tokenPrice", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "functionSelector", "type": "bytes4"}], "name": "unpauseFunction", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "version", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "pure", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}