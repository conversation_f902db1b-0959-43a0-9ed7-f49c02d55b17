{"_format": "hh-sol-artifact-1", "contractName": "SecurityTokenEnhanced", "sourceName": "contracts/SecurityTokenEnhanced.sol", "abi": [{"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint8", "name": "decimals_", "type": "uint8"}, {"internalType": "uint256", "name": "maxSupply_", "type": "uint256"}, {"internalType": "address", "name": "admin", "type": "address"}, {"internalType": "string", "name": "tokenPrice_", "type": "string"}, {"internalType": "string", "name": "bonusTiers_", "type": "string"}, {"internalType": "string", "name": "tokenImageUrl_", "type": "string"}, {"internalType": "address", "name": "identityRegistry_", "type": "address"}, {"internalType": "address", "name": "compliance_", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientAllowance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "name": "ERC20InvalidApprover", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "name": "ERC20InvalidReceiver", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "ERC20InvalidSender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "name": "ERC20InvalidSpender", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "agent", "type": "address"}], "name": "AgentAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "agent", "type": "address"}], "name": "AgentRemoved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "AgreementAccepted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "admin", "type": "address"}], "name": "EmergencyPaused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "admin", "type": "address"}], "name": "EmergencyUnpaused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [], "name": "AGENT_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "TRANSFER_MANAGER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "acceptAgreement", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "agent", "type": "address"}], "name": "addAgent", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "agents", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "bonusTiers", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "compliance", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "emergencyPause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "emergencyUnpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "forcedTransfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "index", "type": "uint256"}], "name": "getAgentAt", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getAgentCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "getAgreementAcceptanceTimestamp", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getAllAgents", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "hasAcceptedAgreement", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "identityRegistry", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "isAgent", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "isEmergencyPaused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "functionSelector", "type": "bytes4"}], "name": "isFunctionPaused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "maxSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "mint", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "functionSelector", "type": "bytes4"}], "name": "pauseFunction", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "agent", "type": "address"}], "name": "removeAgent", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "tokenImageUrl", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "tokenPrice", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "functionSelector", "type": "bytes4"}], "name": "unpauseFunction", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "version", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "pure", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}