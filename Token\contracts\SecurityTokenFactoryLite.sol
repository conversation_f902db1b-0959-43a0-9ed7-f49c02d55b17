// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "./SecurityTokenLite.sol";
import "./IdentityRegistry.sol";
import "./Compliance.sol";
import "./ClaimRegistry.sol";

/**
 * @title SecurityTokenFactoryLite
 * @dev Lightweight factory for deploying security tokens with all security features
 * Optimized for contract size while maintaining full functionality
 */
contract SecurityTokenFactoryLite is AccessControl, ReentrancyGuard {
    // Constants
    bytes32 public constant DEPLOYER_ROLE = keccak256("DEPLOYER_ROLE");
    uint256 private constant MAX_BATCH_SIZE = 10;
    
    // Implementation contracts
    address public securityTokenImplementation;
    address public whitelistImplementation;
    address public whitelistWithKYCImplementation;
    
    // Deployed tokens tracking
    address[] public deployedTokens;
    mapping(string => address) public tokensBySymbol;
    mapping(address => bool) public isDeployedToken;
    
    // Events
    event TokenDeployed(
        address indexed tokenAddress,
        address indexed identityRegistryAddress,
        address indexed complianceAddress,
        string name,
        string symbol,
        uint8 decimals,
        uint256 maxSupply,
        address admin,
        bool hasKYC,
        string tokenImageUrl
    );
    
    /**
     * @dev Constructor
     */
    constructor(address admin) {
        require(admin != address(0), "SecurityTokenFactory: invalid admin address");
        
        // Deploy implementation contracts
        securityTokenImplementation = address(new SecurityTokenLite(
            "Implementation", "IMPL", 18, 1, admin, "", "", "", "", address(0), address(0)
        ));

        // Note: Whitelist implementations would be deployed here
        // For now, we'll deploy them per token to avoid constructor issues
        whitelistImplementation = address(0);
        whitelistWithKYCImplementation = address(0);
        
        // Set up roles
        _grantRole(DEFAULT_ADMIN_ROLE, admin);
        _grantRole(DEPLOYER_ROLE, admin);
    }
    
    /**
     * @dev Deploy a new security token
     */
    function deploySecurityToken(
        string memory name,
        string memory symbol,
        uint8 decimals,
        uint256 maxSupply,
        address admin,
        string memory tokenPrice,
        string memory bonusTiers,
        string memory tokenDetails,
        string memory tokenImageUrl
    ) external onlyRole(DEPLOYER_ROLE) nonReentrant returns (address tokenAddress, address identityRegistryAddress, address complianceAddress) {
        return _deployToken(name, symbol, decimals, maxSupply, admin, tokenPrice, bonusTiers, tokenDetails, tokenImageUrl, false);
    }
    
    /**
     * @dev Deploy a new security token with KYC support
     */
    function deploySecurityTokenWithOptions(
        string memory name,
        string memory symbol,
        uint8 decimals,
        uint256 maxSupply,
        address admin,
        string memory tokenPrice,
        string memory bonusTiers,
        string memory tokenDetails,
        string memory tokenImageUrl,
        bool withKYC
    ) external onlyRole(DEPLOYER_ROLE) nonReentrant returns (address tokenAddress, address identityRegistryAddress, address complianceAddress) {
        return _deployToken(name, symbol, decimals, maxSupply, admin, tokenPrice, bonusTiers, tokenDetails, tokenImageUrl, withKYC);
    }
    
    /**
     * @dev Internal function to deploy token
     */
    function _deployToken(
        string memory name,
        string memory symbol,
        uint8 decimals,
        uint256 maxSupply,
        address admin,
        string memory tokenPrice,
        string memory bonusTiers,
        string memory tokenDetails,
        string memory tokenImageUrl,
        bool withKYC
    ) internal returns (address tokenAddress, address identityRegistryAddress, address complianceAddress) {
        // Validate inputs
        require(bytes(name).length > 0, "SecurityTokenFactory: name cannot be empty");
        require(bytes(symbol).length > 0, "SecurityTokenFactory: symbol cannot be empty");
        require(admin != address(0), "SecurityTokenFactory: invalid admin address");
        require(maxSupply > 0, "SecurityTokenFactory: max supply must be positive");
        require(decimals <= 18, "SecurityTokenFactory: decimals too high");
        require(tokensBySymbol[symbol] == address(0), "SecurityTokenFactory: symbol already exists");
        
        // Deploy claim registry first
        ClaimRegistry claimRegistry = new ClaimRegistry();
        claimRegistry.initialize(admin);

        // Deploy identity registry
        IdentityRegistry identityRegistry = new IdentityRegistry();
        identityRegistry.initialize(admin, address(claimRegistry));
        identityRegistryAddress = address(identityRegistry);

        // Deploy compliance contract
        Compliance complianceContract = new Compliance();
        complianceContract.initialize(admin, identityRegistryAddress);
        complianceAddress = address(complianceContract);
        
        // Deploy token
        SecurityTokenLite token = new SecurityTokenLite(
            name,
            symbol,
            decimals,
            maxSupply,
            admin,
            tokenPrice,
            bonusTiers,
            tokenDetails,
            tokenImageUrl,
            identityRegistryAddress,
            complianceAddress
        );
        
        tokenAddress = address(token);
        
        // Track deployment
        deployedTokens.push(tokenAddress);
        tokensBySymbol[symbol] = tokenAddress;
        isDeployedToken[tokenAddress] = true;
        
        emit TokenDeployed(
            tokenAddress,
            identityRegistryAddress,
            complianceAddress,
            name,
            symbol,
            decimals,
            maxSupply,
            admin,
            withKYC,
            tokenImageUrl
        );
        
        return (tokenAddress, identityRegistryAddress, complianceAddress);
    }
    
    /**
     * @dev Add a new deployer
     */
    function addDeployer(address deployer) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(deployer != address(0), "SecurityTokenFactory: invalid deployer address");
        _grantRole(DEPLOYER_ROLE, deployer);
    }
    
    /**
     * @dev Remove a deployer
     */
    function removeDeployer(address deployer) external onlyRole(DEFAULT_ADMIN_ROLE) {
        _revokeRole(DEPLOYER_ROLE, deployer);
    }
    
    /**
     * @dev Get token address by symbol
     */
    function getTokenAddressBySymbol(string memory symbol) external view returns (address) {
        return tokensBySymbol[symbol];
    }
    
    /**
     * @dev Get all deployed tokens
     */
    function getAllDeployedTokens() external view returns (address[] memory) {
        return deployedTokens;
    }
    
    /**
     * @dev Get deployed token by index
     */
    function getDeployedToken(uint256 index) external view returns (address) {
        require(index < deployedTokens.length, "SecurityTokenFactory: index out of bounds");
        return deployedTokens[index];
    }
    
    /**
     * @dev Get token count
     */
    function getTokenCount() external view returns (uint256) {
        return deployedTokens.length;
    }
    
    /**
     * @dev Batch get tokens
     */
    function getTokensBatch(uint256 start, uint256 count) external view returns (address[] memory) {
        require(start < deployedTokens.length, "SecurityTokenFactory: start index out of bounds");
        require(count <= MAX_BATCH_SIZE, "SecurityTokenFactory: batch size too large");
        
        uint256 end = start + count;
        if (end > deployedTokens.length) {
            end = deployedTokens.length;
        }
        
        address[] memory batch = new address[](end - start);
        for (uint256 i = start; i < end; i++) {
            batch[i - start] = deployedTokens[i];
        }
        
        return batch;
    }
}
