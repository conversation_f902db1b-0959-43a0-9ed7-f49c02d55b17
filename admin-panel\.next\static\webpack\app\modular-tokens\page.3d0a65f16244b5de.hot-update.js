"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/modular-tokens/page",{

/***/ "(app-pages-browser)/./src/app/modular-tokens/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/modular-tokens/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ModularTokensPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useModularToken__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../hooks/useModularToken */ \"(app-pages-browser)/./src/hooks/useModularToken.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n// Import custom hook\n\n// Contract addresses from environment\nconst SECURITY_TOKEN_CORE_ADDRESS = \"******************************************\";\nconst UPGRADE_MANAGER_ADDRESS = \"******************************************\";\nfunction ModularTokensPage() {\n    _s();\n    // Use the custom hook for modular token functionality\n    const { provider, signer, tokenInfo, upgradeInfo, pendingUpgrades, upgradeHistory, loading, error, initializeProvider, mintTokens, togglePause, scheduleUpgrade, toggleEmergencyMode, refreshData, setError } = (0,_hooks_useModularToken__WEBPACK_IMPORTED_MODULE_2__.useModularToken)();\n    // Local state for UI\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mintAmount, setMintAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [mintAddress, setMintAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [upgradeDescription, setUpgradeDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newImplementationAddress, setNewImplementationAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [actionLoading, setActionLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    // KYC & Claims state\n    const [kycUserAddress, setKycUserAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [claimUserAddress, setClaimUserAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [claimTopicId, setClaimTopicId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('10101010000001');\n    const [claimData, setClaimData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [checkUserAddress, setCheckUserAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [verificationStatus, setVerificationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Clear success message after 5 seconds\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"ModularTokensPage.useEffect\": ()=>{\n            if (success) {\n                const timer = setTimeout({\n                    \"ModularTokensPage.useEffect.timer\": ()=>setSuccess(null)\n                }[\"ModularTokensPage.useEffect.timer\"], 5000);\n                return ({\n                    \"ModularTokensPage.useEffect\": ()=>clearTimeout(timer)\n                })[\"ModularTokensPage.useEffect\"];\n            }\n        }\n    }[\"ModularTokensPage.useEffect\"], [\n        success\n    ]);\n    // Clear error message after 10 seconds\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"ModularTokensPage.useEffect\": ()=>{\n            if (error) {\n                const timer = setTimeout({\n                    \"ModularTokensPage.useEffect.timer\": ()=>setError(null)\n                }[\"ModularTokensPage.useEffect.timer\"], 10000);\n                return ({\n                    \"ModularTokensPage.useEffect\": ()=>clearTimeout(timer)\n                })[\"ModularTokensPage.useEffect\"];\n            }\n        }\n    }[\"ModularTokensPage.useEffect\"], [\n        error,\n        setError\n    ]);\n    const handleMint = async ()=>{\n        if (!mintAddress || !mintAmount) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            await mintTokens(mintAddress, mintAmount);\n            setSuccess(\"Successfully minted \".concat(mintAmount, \" tokens to \").concat(mintAddress));\n            setMintAmount('');\n            setMintAddress('');\n            await refreshData(); // Refresh token info\n        } catch (error) {\n            console.error('Error minting tokens:', error);\n            setError(\"Failed to mint tokens: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handlePauseToggle = async ()=>{\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            await togglePause();\n            setSuccess(\"Token \".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'unpaused' : 'paused', \" successfully\"));\n            await refreshData(); // Refresh token info\n        } catch (error) {\n            console.error('Error toggling pause:', error);\n            setError(\"Failed to \".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'unpause' : 'pause', \" token: \").concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleScheduleUpgrade = async ()=>{\n        if (!newImplementationAddress || !upgradeDescription) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            await scheduleUpgrade(newImplementationAddress, upgradeDescription);\n            setSuccess('Upgrade scheduled successfully! It will be executable after the 48-hour timelock.');\n            setNewImplementationAddress('');\n            setUpgradeDescription('');\n            await refreshData(); // Refresh upgrade info\n        } catch (error) {\n            console.error('Error scheduling upgrade:', error);\n            setError(\"Failed to schedule upgrade: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleEmergencyModeToggle = async ()=>{\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            await toggleEmergencyMode();\n            setSuccess(\"Emergency mode \".concat((upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? 'deactivated' : 'activated', \" successfully\"));\n            await refreshData(); // Refresh upgrade info\n        } catch (error) {\n            console.error('Error toggling emergency mode:', error);\n            setError(\"Failed to \".concat((upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? 'deactivate' : 'activate', \" emergency mode: \").concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    // KYC & Claims handlers\n    const handleApproveKYC = async ()=>{\n        if (!kycUserAddress) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            // Call the KYC approval API\n            const response = await fetch('/api/kyc/approve', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenAddress: SECURITY_TOKEN_CORE_ADDRESS,\n                    userAddress: kycUserAddress\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to approve KYC');\n            }\n            setSuccess(\"KYC approved successfully for \".concat(kycUserAddress));\n            setKycUserAddress('');\n        } catch (error) {\n            console.error('Error approving KYC:', error);\n            setError(\"Failed to approve KYC: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleAddToWhitelist = async ()=>{\n        if (!kycUserAddress) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            // Call the whitelist API\n            const response = await fetch('/api/kyc/whitelist', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenAddress: SECURITY_TOKEN_CORE_ADDRESS,\n                    userAddress: kycUserAddress\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to add to whitelist');\n            }\n            setSuccess(\"User added to whitelist successfully: \".concat(kycUserAddress));\n            setKycUserAddress('');\n        } catch (error) {\n            console.error('Error adding to whitelist:', error);\n            setError(\"Failed to add to whitelist: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleIssueClaim = async ()=>{\n        if (!claimUserAddress || !claimTopicId) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            // Call the claims API\n            const response = await fetch('/api/kyc/issue-claim', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    userAddress: claimUserAddress,\n                    topicId: claimTopicId,\n                    data: claimData || ''\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to issue claim');\n            }\n            const result = await response.json();\n            setSuccess(\"Claim issued successfully! Claim ID: \".concat(result.claimId));\n            setClaimUserAddress('');\n            setClaimData('');\n        } catch (error) {\n            console.error('Error issuing claim:', error);\n            setError(\"Failed to issue claim: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleCheckStatus = async ()=>{\n        if (!checkUserAddress) return;\n        setActionLoading(true);\n        setError(null);\n        try {\n            // Call the status check API\n            const response = await fetch(\"/api/kyc/status?tokenAddress=\".concat(SECURITY_TOKEN_CORE_ADDRESS, \"&userAddress=\").concat(checkUserAddress));\n            if (!response.ok) {\n                throw new Error('Failed to check status');\n            }\n            const status = await response.json();\n            setVerificationStatus(status);\n        } catch (error) {\n            console.error('Error checking status:', error);\n            setError(\"Failed to check status: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    // Utility functions\n    const formatAddress = (address)=>{\n        return \"\".concat(address.slice(0, 6), \"...\").concat(address.slice(-4));\n    };\n    const formatTimestamp = (timestamp)=>{\n        return new Date(timestamp * 1000).toLocaleString();\n    };\n    const getTimeUntilExecution = (executeTime)=>{\n        const now = Math.floor(Date.now() / 1000);\n        const timeLeft = executeTime - now;\n        if (timeLeft <= 0) return 'Ready to execute';\n        const hours = Math.floor(timeLeft / 3600);\n        const minutes = Math.floor(timeLeft % 3600 / 60);\n        return \"\".concat(hours, \"h \").concat(minutes, \"m remaining\");\n    };\n    if (!provider || !signer) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold mb-2\",\n                        children: \"Connect Wallet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: \"Please connect your MetaMask wallet to manage modular tokens.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: initializeProvider,\n                        className: \"bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\",\n                        children: \"Connect Wallet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                lineNumber: 295,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n            lineNumber: 294,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold\",\n                                children: \"Modular Token Management\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Manage your modular ERC-3643 security tokens and upgrades\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat((upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'),\n                                children: (upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? \"Emergency Mode Active\" : \"Normal Mode\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 11\n                            }, this),\n                            tokenInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(tokenInfo.paused ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'),\n                                children: tokenInfo.paused ? \"Paused\" : \"Active\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: refreshData,\n                                disabled: loading,\n                                className: \"bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-1 px-3 rounded text-sm disabled:opacity-50\",\n                                children: [\n                                    loading ? '🔄' : '↻',\n                                    \" Refresh\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                lineNumber: 313,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-red-600 mr-2\",\n                            children: \"⚠️\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                            lineNumber: 350,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                    lineNumber: 349,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                lineNumber: 348,\n                columnNumber: 9\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-green-600 mr-2\",\n                            children: \"✅\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                            lineNumber: 359,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: success\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                            lineNumber: 360,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                    lineNumber: 358,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                lineNumber: 357,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"-mb-px flex space-x-8\",\n                            children: [\n                                'overview',\n                                'operations',\n                                'kyc-claims',\n                                'upgrades',\n                                'history'\n                            ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(tab),\n                                    className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === tab ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                    children: tab === 'kyc-claims' ? 'KYC & Claims' : tab.charAt(0).toUpperCase() + tab.slice(1).replace('-', ' ')\n                                }, tab, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                            lineNumber: 368,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                        lineNumber: 367,\n                        columnNumber: 9\n                    }, this),\n                    activeTab === 'overview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg shadow p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium mb-2\",\n                                                children: \"Token Information\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-4\",\n                                                children: \"Current token details and status\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 391,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: tokenInfo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Name:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 396,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: tokenInfo.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 397,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 395,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Symbol:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 400,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: tokenInfo.symbol\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 401,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 399,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Version:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 404,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: tokenInfo.version\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 405,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 403,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Total Supply:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 408,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: tokenInfo.totalSupply\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 409,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 407,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Max Supply:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 412,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: tokenInfo.maxSupply\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 413,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 411,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Decimals:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 416,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: tokenInfo.decimals\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 417,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 415,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Status:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 420,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(tokenInfo.paused ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'),\n                                                                    children: tokenInfo.paused ? \"Paused\" : \"Active\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 421,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 419,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 432,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Loading token information...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 433,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 392,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg shadow p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium mb-2\",\n                                                children: \"Contract Addresses\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-4\",\n                                                children: \"Deployed contract addresses on Amoy testnet\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"SecurityTokenCore:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 444,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500 font-mono\",\n                                                                children: SECURITY_TOKEN_CORE_ADDRESS\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 445,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"UpgradeManager:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 450,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500 font-mono\",\n                                                                children: UPGRADE_MANAGER_ADDRESS\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 451,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"pt-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>window.open(\"https://amoy.polygonscan.com/address/\".concat(SECURITY_TOKEN_CORE_ADDRESS), '_blank'),\n                                                            className: \"bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-1 px-3 rounded text-sm\",\n                                                            children: \"View on PolygonScan\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 456,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 455,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 388,\n                                columnNumber: 13\n                            }, this),\n                            (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.metadata) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium mb-2\",\n                                        children: \"Token Metadata\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 469,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-4\",\n                                        children: \"Additional token information and configuration\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Price:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 473,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: tokenInfo.metadata.tokenPrice\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 474,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Bonus Tiers:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 477,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: tokenInfo.metadata.bonusTiers\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 478,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Details:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 481,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 mt-1\",\n                                                        children: tokenInfo.metadata.tokenDetails\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 482,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 480,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 471,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 468,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                        lineNumber: 387,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'operations' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium mb-2\",\n                                            children: \"Mint Tokens\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 496,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"Mint new tokens to a specified address\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 497,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"mintAddress\",\n                                                            className: \"block text-sm font-medium text-gray-700\",\n                                                            children: \"Recipient Address\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 500,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            id: \"mintAddress\",\n                                                            type: \"text\",\n                                                            placeholder: \"0x...\",\n                                                            value: mintAddress,\n                                                            onChange: (e)=>setMintAddress(e.target.value),\n                                                            className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 501,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"mintAmount\",\n                                                            className: \"block text-sm font-medium text-gray-700\",\n                                                            children: \"Amount\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 511,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            id: \"mintAmount\",\n                                                            type: \"number\",\n                                                            placeholder: \"1000\",\n                                                            value: mintAmount,\n                                                            onChange: (e)=>setMintAmount(e.target.value),\n                                                            className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 512,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleMint,\n                                                    disabled: actionLoading || !mintAddress || !mintAmount,\n                                                    className: \"w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50\",\n                                                    children: [\n                                                        actionLoading ? '🔄 ' : '',\n                                                        \"Mint Tokens\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 521,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                    lineNumber: 495,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium mb-2\",\n                                            children: \"Token Controls\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 532,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"Pause/unpause token transfers and emergency controls\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 533,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handlePauseToggle,\n                                                    disabled: actionLoading,\n                                                    className: \"w-full font-bold py-2 px-4 rounded disabled:opacity-50 \".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'bg-green-600 hover:bg-green-700 text-white' : 'bg-red-600 hover:bg-red-700 text-white'),\n                                                    children: [\n                                                        actionLoading ? '🔄 ' : '',\n                                                        (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'Unpause Token' : 'Pause Token'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 535,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleEmergencyModeToggle,\n                                                    disabled: actionLoading,\n                                                    className: \"w-full font-bold py-2 px-4 rounded disabled:opacity-50 \".concat((upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? 'bg-green-600 hover:bg-green-700 text-white' : 'bg-red-600 hover:bg-red-700 text-white'),\n                                                    children: [\n                                                        actionLoading ? '🔄 ' : '',\n                                                        (upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: \"✅ Deactivate Emergency Mode\"\n                                                        }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: \"⚡ Activate Emergency Mode\"\n                                                        }, void 0, false)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 548,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 534,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                    lineNumber: 531,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                            lineNumber: 494,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                        lineNumber: 493,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'kyc-claims' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-600 text-xl\",\n                                                children: \"\\uD83D\\uDD10\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 575,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 574,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-medium text-blue-800\",\n                                                    children: \"On-Chain KYC & Claims System\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 578,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-1 text-sm text-blue-700\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"Manage KYC verification and custom claims using Tokeny-style Topic IDs:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 582,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"mt-1 list-disc list-inside space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: \"KYC Verification:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 584,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \" Topic ID 10101010000001\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 584,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: \"General Qualification:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 585,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \" Topic ID 10101010000004\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 585,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: \"Accredited Investor:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 586,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \" Topic ID 10101010000002\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 586,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: \"Custom Claims:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 587,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \" Like 10101010000648 for specific KYC status\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 587,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 583,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 581,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 577,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                    lineNumber: 573,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 572,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-yellow-600 text-xl\",\n                                                children: \"⚙️\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 597,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-medium text-yellow-800\",\n                                                    children: \"System Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 600,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-1 text-sm text-yellow-700\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-2 mt-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-green-600 mr-1\",\n                                                                            children: \"✅\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 606,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Claims Issuance\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 607,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 605,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-green-600 mr-1\",\n                                                                            children: \"✅\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 610,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Status Checking\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 611,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 609,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-yellow-600 mr-1\",\n                                                                            children: \"⚠️\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 614,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Module Integration\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 615,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 613,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 604,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"mt-2 text-xs\",\n                                                            children: \"Claims system is fully operational. KYC/Whitelist functions require module registration.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 618,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 603,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 599,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                    lineNumber: 595,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 594,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg shadow p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium mb-2\",\n                                                children: [\n                                                    \"KYC Management\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded\",\n                                                        children: \"Module Required\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 630,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 628,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-4\",\n                                                children: \"Approve KYC and add users to whitelist (requires module registration)\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 632,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"kycUserAddress\",\n                                                                className: \"block text-sm font-medium text-gray-700\",\n                                                                children: \"User Address\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 635,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                id: \"kycUserAddress\",\n                                                                type: \"text\",\n                                                                placeholder: \"0x...\",\n                                                                value: kycUserAddress,\n                                                                onChange: (e)=>setKycUserAddress(e.target.value),\n                                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 636,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 634,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleApproveKYC,\n                                                                disabled: actionLoading || !kycUserAddress,\n                                                                className: \"bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50\",\n                                                                children: [\n                                                                    actionLoading ? '🔄 ' : '✅ ',\n                                                                    \"Approve KYC\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 646,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleAddToWhitelist,\n                                                                disabled: actionLoading || !kycUserAddress,\n                                                                className: \"bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50\",\n                                                                children: [\n                                                                    actionLoading ? '🔄 ' : '📋 ',\n                                                                    \"Add to Whitelist\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 653,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 645,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 633,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 627,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg shadow p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium mb-2\",\n                                                children: [\n                                                    \"Custom Claims\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded\",\n                                                        children: \"✅ Working\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 667,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 665,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-4\",\n                                                children: \"Issue custom claims with specific Topic IDs (fully operational)\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 669,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"claimUserAddress\",\n                                                                className: \"block text-sm font-medium text-gray-700\",\n                                                                children: \"User Address\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 672,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                id: \"claimUserAddress\",\n                                                                type: \"text\",\n                                                                placeholder: \"0x...\",\n                                                                value: claimUserAddress,\n                                                                onChange: (e)=>setClaimUserAddress(e.target.value),\n                                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 673,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 671,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"claimTopicId\",\n                                                                className: \"block text-sm font-medium text-gray-700\",\n                                                                children: \"Topic ID\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 683,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                id: \"claimTopicId\",\n                                                                value: claimTopicId,\n                                                                onChange: (e)=>setClaimTopicId(e.target.value),\n                                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"10101010000001\",\n                                                                        children: \"10101010000001 - KYC Verification\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 690,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"10101010000002\",\n                                                                        children: \"10101010000002 - Accredited Investor\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 691,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"10101010000003\",\n                                                                        children: \"10101010000003 - Jurisdiction Compliance\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 692,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"10101010000004\",\n                                                                        children: \"10101010000004 - General Qualification\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 693,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"10101010000648\",\n                                                                        children: \"10101010000648 - Custom KYC Status\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 694,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 684,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 682,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"claimData\",\n                                                                className: \"block text-sm font-medium text-gray-700\",\n                                                                children: \"Claim Data (Optional)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 698,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                id: \"claimData\",\n                                                                placeholder: \"Additional claim information...\",\n                                                                value: claimData,\n                                                                onChange: (e)=>setClaimData(e.target.value),\n                                                                rows: 2,\n                                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 699,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 697,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleIssueClaim,\n                                                        disabled: actionLoading || !claimUserAddress || !claimTopicId,\n                                                        className: \"w-full bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50\",\n                                                        children: [\n                                                            actionLoading ? '🔄 ' : '🏷️ ',\n                                                            \"Issue Custom Claim\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 708,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 670,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 664,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 626,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium mb-2\",\n                                        children: [\n                                            \"Verification Status Checker\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded\",\n                                                children: \"✅ Working\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 722,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 720,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-4\",\n                                        children: \"Check KYC and claims status for any user (fully operational)\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 724,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"checkUserAddress\",\n                                                                className: \"block text-sm font-medium text-gray-700\",\n                                                                children: \"User Address\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 728,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                id: \"checkUserAddress\",\n                                                                type: \"text\",\n                                                                placeholder: \"0x...\",\n                                                                value: checkUserAddress,\n                                                                onChange: (e)=>setCheckUserAddress(e.target.value),\n                                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 729,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 727,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-end\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleCheckStatus,\n                                                            disabled: actionLoading || !checkUserAddress,\n                                                            className: \"bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50\",\n                                                            children: [\n                                                                actionLoading ? '🔄 ' : '🔍 ',\n                                                                \"Check Status\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 739,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 738,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 726,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border rounded-lg p-4 bg-gray-50\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: verificationStatus ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium mb-2\",\n                                                                children: [\n                                                                    \"Verification Status for \",\n                                                                    checkUserAddress.slice(0, 10),\n                                                                    \"...\",\n                                                                    checkUserAddress.slice(-8)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 754,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-2 space-y-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"KYC Approved:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                                lineNumber: 757,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: verificationStatus.kycApproved ? 'text-green-600 font-medium' : 'text-red-600',\n                                                                                children: verificationStatus.kycApproved ? '✅ Yes' : '❌ No'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                                lineNumber: 758,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 756,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"Whitelisted:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                                lineNumber: 763,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: verificationStatus.whitelisted ? 'text-green-600 font-medium' : 'text-red-600',\n                                                                                children: verificationStatus.whitelisted ? '✅ Yes' : '❌ No'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                                lineNumber: 764,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 762,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"Eligible:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                                lineNumber: 769,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: verificationStatus.eligible ? 'text-green-600 font-medium' : 'text-red-600',\n                                                                                children: verificationStatus.eligible ? '✅ Yes' : '❌ No'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                                lineNumber: 770,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 768,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"Method:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                                lineNumber: 775,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-blue-600 font-medium\",\n                                                                                children: verificationStatus.method\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                                lineNumber: 776,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 774,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 755,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"Status results will appear here after checking a user address.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 782,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-2 space-y-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"KYC Approved:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                                lineNumber: 785,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-400\",\n                                                                                children: \"-\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                                lineNumber: 786,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 784,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"Whitelisted:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                                lineNumber: 789,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-400\",\n                                                                                children: \"-\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                                lineNumber: 790,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 788,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"Eligible:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                                lineNumber: 793,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-400\",\n                                                                                children: \"-\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                                lineNumber: 794,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 792,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"Method:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                                lineNumber: 797,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-400\",\n                                                                                children: \"-\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                                lineNumber: 798,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 796,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 783,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 751,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 750,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 725,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 719,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium mb-2\",\n                                        children: \"Token Claims Configuration\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 809,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-4\",\n                                        children: \"Configure which claims are required for this token\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 810,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"checkbox\",\n                                                                    className: \"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 815,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"ml-2 text-sm\",\n                                                                    children: \"Enable KYC Verification\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 816,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 814,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 813,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"checkbox\",\n                                                                    className: \"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 821,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"ml-2 text-sm\",\n                                                                    children: \"Enable Claims System\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 822,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 820,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 819,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 812,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"Required Claims\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 828,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"checkbox\",\n                                                                        className: \"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 831,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 text-sm\",\n                                                                        children: \"KYC Verification (10101010000001)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 832,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 830,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"checkbox\",\n                                                                        className: \"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 835,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 text-sm\",\n                                                                        children: \"General Qualification (10101010000004)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 836,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 834,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"checkbox\",\n                                                                        className: \"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 839,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 text-sm\",\n                                                                        children: \"Accredited Investor (10101010000002)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 840,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 838,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 829,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 827,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                disabled: actionLoading,\n                                                className: \"w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50\",\n                                                children: [\n                                                    actionLoading ? '🔄 ' : '⚙️ ',\n                                                    \"Update Token Configuration\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 845,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 811,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 808,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                        lineNumber: 571,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'upgrades' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium mb-2\",\n                                            children: \"Schedule Upgrade\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 860,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"Schedule a new implementation upgrade with 48-hour timelock\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 861,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"newImplementation\",\n                                                            className: \"block text-sm font-medium text-gray-700\",\n                                                            children: \"New Implementation Address\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 866,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            id: \"newImplementation\",\n                                                            type: \"text\",\n                                                            placeholder: \"0x...\",\n                                                            value: newImplementationAddress,\n                                                            onChange: (e)=>setNewImplementationAddress(e.target.value),\n                                                            className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 867,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 865,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"upgradeDescription\",\n                                                            className: \"block text-sm font-medium text-gray-700\",\n                                                            children: \"Upgrade Description\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 877,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            id: \"upgradeDescription\",\n                                                            placeholder: \"Describe the changes in this upgrade...\",\n                                                            value: upgradeDescription,\n                                                            onChange: (e)=>setUpgradeDescription(e.target.value),\n                                                            rows: 3,\n                                                            className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 878,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 876,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleScheduleUpgrade,\n                                                    disabled: actionLoading || !newImplementationAddress || !upgradeDescription,\n                                                    className: \"w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50\",\n                                                    children: [\n                                                        actionLoading ? '🔄 ' : '⏰ ',\n                                                        \"Schedule Upgrade (48h Timelock)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 887,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 864,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                    lineNumber: 859,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium mb-2\",\n                                            children: \"Pending Upgrades\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 899,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"Upgrades waiting for timelock expiration or execution\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 900,\n                                            columnNumber: 17\n                                        }, this),\n                                        pendingUpgrades.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 text-center py-4\",\n                                            children: \"No pending upgrades\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 904,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: pendingUpgrades.map((upgrade)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border rounded-lg p-3 space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-start\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: upgrade.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 912,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(upgrade.executed ? 'bg-green-100 text-green-800' : upgrade.cancelled ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'),\n                                                                    children: upgrade.executed ? \"Executed\" : upgrade.cancelled ? \"Cancelled\" : \"Pending\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 915,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 911,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-500 space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        \"Implementation: \",\n                                                                        upgrade.newImplementation.slice(0, 10),\n                                                                        \"...\",\n                                                                        upgrade.newImplementation.slice(-8)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 926,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        \"Execute Time: \",\n                                                                        new Date(upgrade.executeTime * 1000).toLocaleString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 927,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: getTimeUntilExecution(upgrade.executeTime)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 928,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 925,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, upgrade.upgradeId, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 910,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 908,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                    lineNumber: 898,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                            lineNumber: 858,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                        lineNumber: 857,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'history' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium mb-2\",\n                                    children: \"Upgrade History\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                    lineNumber: 942,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: \"Complete history of all upgrades performed on this token\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                    lineNumber: 943,\n                                    columnNumber: 15\n                                }, this),\n                                upgradeHistory.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 text-center py-4\",\n                                    children: \"No upgrade history available\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                    lineNumber: 947,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: upgradeHistory.map((record, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border rounded-lg p-4 space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium\",\n                                                                    children: record.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 956,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: [\n                                                                        new Date(record.timestamp * 1000).toLocaleString(),\n                                                                        \" by \",\n                                                                        record.executor.slice(0, 10),\n                                                                        \"...\",\n                                                                        record.executor.slice(-8)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 957,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 955,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(record.isEmergency ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'),\n                                                                    children: record.isEmergency ? \"Emergency\" : \"Scheduled\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 962,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                record.version && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800\",\n                                                                    children: [\n                                                                        \"v\",\n                                                                        record.version\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 968,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 961,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 954,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-500 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                \"From: \",\n                                                                record.oldImplementation.slice(0, 10),\n                                                                \"...\",\n                                                                record.oldImplementation.slice(-8)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 975,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                \"To: \",\n                                                                record.newImplementation.slice(0, 10),\n                                                                \"...\",\n                                                                record.newImplementation.slice(-8)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 976,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 974,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 953,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                    lineNumber: 951,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                            lineNumber: 941,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                        lineNumber: 940,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                lineNumber: 365,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n        lineNumber: 312,\n        columnNumber: 5\n    }, this);\n}\n_s(ModularTokensPage, \"QAYwK3XwuosE+3JgQGWcjchibQU=\", false, function() {\n    return [\n        _hooks_useModularToken__WEBPACK_IMPORTED_MODULE_2__.useModularToken\n    ];\n});\n_c = ModularTokensPage;\nvar _c;\n$RefreshReg$(_c, \"ModularTokensPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvbW9kdWxhci10b2tlbnMvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUV3QztBQUV4QyxxQkFBcUI7QUFDeUM7QUFFOUQsc0NBQXNDO0FBQ3RDLE1BQU1HLDhCQUE4QkMsNENBQXdEO0FBQzVGLE1BQU1HLDBCQUEwQkgsNENBQW9EO0FBRXJFLFNBQVNLOztJQUN0QixzREFBc0Q7SUFDdEQsTUFBTSxFQUNKQyxRQUFRLEVBQ1JDLE1BQU0sRUFDTkMsU0FBUyxFQUNUQyxXQUFXLEVBQ1hDLGVBQWUsRUFDZkMsY0FBYyxFQUNkQyxPQUFPLEVBQ1BDLEtBQUssRUFDTEMsa0JBQWtCLEVBQ2xCQyxVQUFVLEVBQ1ZDLFdBQVcsRUFDWEMsZUFBZSxFQUNmQyxtQkFBbUIsRUFDbkJDLFdBQVcsRUFDWEMsUUFBUSxFQUNULEdBQUd0Qix1RUFBZUE7SUFFbkIscUJBQXFCO0lBQ3JCLE1BQU0sQ0FBQ3VCLFNBQVNDLFdBQVcsR0FBR3pCLCtDQUFRQSxDQUFnQjtJQUN0RCxNQUFNLENBQUMwQixZQUFZQyxjQUFjLEdBQUczQiwrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUM0QixhQUFhQyxlQUFlLEdBQUc3QiwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUM4QixvQkFBb0JDLHNCQUFzQixHQUFHL0IsK0NBQVFBLENBQUM7SUFDN0QsTUFBTSxDQUFDZ0MsMEJBQTBCQyw0QkFBNEIsR0FBR2pDLCtDQUFRQSxDQUFDO0lBQ3pFLE1BQU0sQ0FBQ2tDLGVBQWVDLGlCQUFpQixHQUFHbkMsK0NBQVFBLENBQUM7SUFDbkQsTUFBTSxDQUFDb0MsV0FBV0MsYUFBYSxHQUFHckMsK0NBQVFBLENBQUM7SUFFM0MscUJBQXFCO0lBQ3JCLE1BQU0sQ0FBQ3NDLGdCQUFnQkMsa0JBQWtCLEdBQUd2QywrQ0FBUUEsQ0FBQztJQUNyRCxNQUFNLENBQUN3QyxrQkFBa0JDLG9CQUFvQixHQUFHekMsK0NBQVFBLENBQUM7SUFDekQsTUFBTSxDQUFDMEMsY0FBY0MsZ0JBQWdCLEdBQUczQywrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUM0QyxXQUFXQyxhQUFhLEdBQUc3QywrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUM4QyxrQkFBa0JDLG9CQUFvQixHQUFHL0MsK0NBQVFBLENBQUM7SUFDekQsTUFBTSxDQUFDZ0Qsb0JBQW9CQyxzQkFBc0IsR0FBR2pELCtDQUFRQSxDQUFDO0lBRTdELHdDQUF3QztJQUN4Q0Qsc0RBQWU7dUNBQUM7WUFDZCxJQUFJeUIsU0FBUztnQkFDWCxNQUFNMkIsUUFBUUM7eURBQVcsSUFBTTNCLFdBQVc7d0RBQU87Z0JBQ2pEO21EQUFPLElBQU00QixhQUFhRjs7WUFDNUI7UUFDRjtzQ0FBRztRQUFDM0I7S0FBUTtJQUVaLHVDQUF1QztJQUN2Q3pCLHNEQUFlO3VDQUFDO1lBQ2QsSUFBSWlCLE9BQU87Z0JBQ1QsTUFBTW1DLFFBQVFDO3lEQUFXLElBQU03QixTQUFTO3dEQUFPO2dCQUMvQzttREFBTyxJQUFNOEIsYUFBYUY7O1lBQzVCO1FBQ0Y7c0NBQUc7UUFBQ25DO1FBQU9PO0tBQVM7SUFFcEIsTUFBTStCLGFBQWE7UUFDakIsSUFBSSxDQUFDMUIsZUFBZSxDQUFDRixZQUFZO1FBRWpDUyxpQkFBaUI7UUFDakJaLFNBQVM7UUFDVEUsV0FBVztRQUVYLElBQUk7WUFDRixNQUFNUCxXQUFXVSxhQUFhRjtZQUM5QkQsV0FBVyx1QkFBK0NHLE9BQXhCRixZQUFXLGVBQXlCLE9BQVpFO1lBQzFERCxjQUFjO1lBQ2RFLGVBQWU7WUFDZixNQUFNUCxlQUFlLHFCQUFxQjtRQUM1QyxFQUFFLE9BQU9OLE9BQVk7WUFDbkJ1QyxRQUFRdkMsS0FBSyxDQUFDLHlCQUF5QkE7WUFDdkNPLFNBQVMsMEJBQXdDLE9BQWRQLE1BQU13QyxPQUFPO1FBQ2xELFNBQVU7WUFDUnJCLGlCQUFpQjtRQUNuQjtJQUNGO0lBRUEsTUFBTXNCLG9CQUFvQjtRQUN4QnRCLGlCQUFpQjtRQUNqQlosU0FBUztRQUNURSxXQUFXO1FBRVgsSUFBSTtZQUNGLE1BQU1OO1lBQ05NLFdBQVcsU0FBbUQsT0FBMUNkLENBQUFBLHNCQUFBQSxnQ0FBQUEsVUFBVytDLE1BQU0sSUFBRyxhQUFhLFVBQVM7WUFDOUQsTUFBTXBDLGVBQWUscUJBQXFCO1FBQzVDLEVBQUUsT0FBT04sT0FBWTtZQUNuQnVDLFFBQVF2QyxLQUFLLENBQUMseUJBQXlCQTtZQUN2Q08sU0FBUyxhQUErRFAsT0FBbERMLENBQUFBLHNCQUFBQSxnQ0FBQUEsVUFBVytDLE1BQU0sSUFBRyxZQUFZLFNBQVEsWUFBd0IsT0FBZDFDLE1BQU13QyxPQUFPO1FBQ3ZGLFNBQVU7WUFDUnJCLGlCQUFpQjtRQUNuQjtJQUNGO0lBRUEsTUFBTXdCLHdCQUF3QjtRQUM1QixJQUFJLENBQUMzQiw0QkFBNEIsQ0FBQ0Ysb0JBQW9CO1FBRXRESyxpQkFBaUI7UUFDakJaLFNBQVM7UUFDVEUsV0FBVztRQUVYLElBQUk7WUFDRixNQUFNTCxnQkFBZ0JZLDBCQUEwQkY7WUFDaERMLFdBQVc7WUFDWFEsNEJBQTRCO1lBQzVCRixzQkFBc0I7WUFDdEIsTUFBTVQsZUFBZSx1QkFBdUI7UUFDOUMsRUFBRSxPQUFPTixPQUFZO1lBQ25CdUMsUUFBUXZDLEtBQUssQ0FBQyw2QkFBNkJBO1lBQzNDTyxTQUFTLCtCQUE2QyxPQUFkUCxNQUFNd0MsT0FBTztRQUN2RCxTQUFVO1lBQ1JyQixpQkFBaUI7UUFDbkI7SUFDRjtJQUVBLE1BQU15Qiw0QkFBNEI7UUFDaEN6QixpQkFBaUI7UUFDakJaLFNBQVM7UUFDVEUsV0FBVztRQUVYLElBQUk7WUFDRixNQUFNSjtZQUNOSSxXQUFXLGtCQUFpRixPQUEvRGIsQ0FBQUEsd0JBQUFBLGtDQUFBQSxZQUFhaUQsbUJBQW1CLElBQUcsZ0JBQWdCLGFBQVk7WUFDNUYsTUFBTXZDLGVBQWUsdUJBQXVCO1FBQzlDLEVBQUUsT0FBT04sT0FBWTtZQUNuQnVDLFFBQVF2QyxLQUFLLENBQUMsa0NBQWtDQTtZQUNoRE8sU0FBUyxhQUE2RlAsT0FBaEZKLENBQUFBLHdCQUFBQSxrQ0FBQUEsWUFBYWlELG1CQUFtQixJQUFHLGVBQWUsWUFBVyxxQkFBaUMsT0FBZDdDLE1BQU13QyxPQUFPO1FBQ3JILFNBQVU7WUFDUnJCLGlCQUFpQjtRQUNuQjtJQUNGO0lBRUEsd0JBQXdCO0lBQ3hCLE1BQU0yQixtQkFBbUI7UUFDdkIsSUFBSSxDQUFDeEIsZ0JBQWdCO1FBRXJCSCxpQkFBaUI7UUFDakJaLFNBQVM7UUFDVEUsV0FBVztRQUVYLElBQUk7WUFDRiw0QkFBNEI7WUFDNUIsTUFBTXNDLFdBQVcsTUFBTUMsTUFBTSxvQkFBb0I7Z0JBQy9DQyxRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7Z0JBQ0FDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztvQkFDbkJDLGNBQWNwRTtvQkFDZHFFLGFBQWFqQztnQkFDZjtZQUNGO1lBRUEsSUFBSSxDQUFDeUIsU0FBU1MsRUFBRSxFQUFFO2dCQUNoQixNQUFNLElBQUlDLE1BQU07WUFDbEI7WUFFQWhELFdBQVcsaUNBQWdELE9BQWZhO1lBQzVDQyxrQkFBa0I7UUFDcEIsRUFBRSxPQUFPdkIsT0FBWTtZQUNuQnVDLFFBQVF2QyxLQUFLLENBQUMsd0JBQXdCQTtZQUN0Q08sU0FBUywwQkFBd0MsT0FBZFAsTUFBTXdDLE9BQU87UUFDbEQsU0FBVTtZQUNSckIsaUJBQWlCO1FBQ25CO0lBQ0Y7SUFFQSxNQUFNdUMsdUJBQXVCO1FBQzNCLElBQUksQ0FBQ3BDLGdCQUFnQjtRQUVyQkgsaUJBQWlCO1FBQ2pCWixTQUFTO1FBQ1RFLFdBQVc7UUFFWCxJQUFJO1lBQ0YseUJBQXlCO1lBQ3pCLE1BQU1zQyxXQUFXLE1BQU1DLE1BQU0sc0JBQXNCO2dCQUNqREMsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxnQkFBZ0I7Z0JBQ2xCO2dCQUNBQyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7b0JBQ25CQyxjQUFjcEU7b0JBQ2RxRSxhQUFhakM7Z0JBQ2Y7WUFDRjtZQUVBLElBQUksQ0FBQ3lCLFNBQVNTLEVBQUUsRUFBRTtnQkFDaEIsTUFBTSxJQUFJQyxNQUFNO1lBQ2xCO1lBRUFoRCxXQUFXLHlDQUF3RCxPQUFmYTtZQUNwREMsa0JBQWtCO1FBQ3BCLEVBQUUsT0FBT3ZCLE9BQVk7WUFDbkJ1QyxRQUFRdkMsS0FBSyxDQUFDLDhCQUE4QkE7WUFDNUNPLFNBQVMsK0JBQTZDLE9BQWRQLE1BQU13QyxPQUFPO1FBQ3ZELFNBQVU7WUFDUnJCLGlCQUFpQjtRQUNuQjtJQUNGO0lBRUEsTUFBTXdDLG1CQUFtQjtRQUN2QixJQUFJLENBQUNuQyxvQkFBb0IsQ0FBQ0UsY0FBYztRQUV4Q1AsaUJBQWlCO1FBQ2pCWixTQUFTO1FBQ1RFLFdBQVc7UUFFWCxJQUFJO1lBQ0Ysc0JBQXNCO1lBQ3RCLE1BQU1zQyxXQUFXLE1BQU1DLE1BQU0sd0JBQXdCO2dCQUNuREMsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxnQkFBZ0I7Z0JBQ2xCO2dCQUNBQyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7b0JBQ25CRSxhQUFhL0I7b0JBQ2JvQyxTQUFTbEM7b0JBQ1RtQyxNQUFNakMsYUFBYTtnQkFDckI7WUFDRjtZQUVBLElBQUksQ0FBQ21CLFNBQVNTLEVBQUUsRUFBRTtnQkFDaEIsTUFBTSxJQUFJQyxNQUFNO1lBQ2xCO1lBRUEsTUFBTUssU0FBUyxNQUFNZixTQUFTZ0IsSUFBSTtZQUNsQ3RELFdBQVcsd0NBQXVELE9BQWZxRCxPQUFPRSxPQUFPO1lBQ2pFdkMsb0JBQW9CO1lBQ3BCSSxhQUFhO1FBQ2YsRUFBRSxPQUFPN0IsT0FBWTtZQUNuQnVDLFFBQVF2QyxLQUFLLENBQUMsd0JBQXdCQTtZQUN0Q08sU0FBUywwQkFBd0MsT0FBZFAsTUFBTXdDLE9BQU87UUFDbEQsU0FBVTtZQUNSckIsaUJBQWlCO1FBQ25CO0lBQ0Y7SUFFQSxNQUFNOEMsb0JBQW9CO1FBQ3hCLElBQUksQ0FBQ25DLGtCQUFrQjtRQUV2QlgsaUJBQWlCO1FBQ2pCWixTQUFTO1FBRVQsSUFBSTtZQUNGLDRCQUE0QjtZQUM1QixNQUFNd0MsV0FBVyxNQUFNQyxNQUFNLGdDQUEyRWxCLE9BQTNDNUMsNkJBQTRCLGlCQUFnQyxPQUFqQjRDO1lBRXhHLElBQUksQ0FBQ2lCLFNBQVNTLEVBQUUsRUFBRTtnQkFDaEIsTUFBTSxJQUFJQyxNQUFNO1lBQ2xCO1lBRUEsTUFBTVMsU0FBUyxNQUFNbkIsU0FBU2dCLElBQUk7WUFDbEM5QixzQkFBc0JpQztRQUN4QixFQUFFLE9BQU9sRSxPQUFZO1lBQ25CdUMsUUFBUXZDLEtBQUssQ0FBQywwQkFBMEJBO1lBQ3hDTyxTQUFTLDJCQUF5QyxPQUFkUCxNQUFNd0MsT0FBTztRQUNuRCxTQUFVO1lBQ1JyQixpQkFBaUI7UUFDbkI7SUFDRjtJQUVBLG9CQUFvQjtJQUNwQixNQUFNZ0QsZ0JBQWdCLENBQUNDO1FBQ3JCLE9BQU8sR0FBNEJBLE9BQXpCQSxRQUFRQyxLQUFLLENBQUMsR0FBRyxJQUFHLE9BQXVCLE9BQWxCRCxRQUFRQyxLQUFLLENBQUMsQ0FBQztJQUNwRDtJQUVBLE1BQU1DLGtCQUFrQixDQUFDQztRQUN2QixPQUFPLElBQUlDLEtBQUtELFlBQVksTUFBTUUsY0FBYztJQUNsRDtJQUVBLE1BQU1DLHdCQUF3QixDQUFDQztRQUM3QixNQUFNQyxNQUFNQyxLQUFLQyxLQUFLLENBQUNOLEtBQUtJLEdBQUcsS0FBSztRQUNwQyxNQUFNRyxXQUFXSixjQUFjQztRQUUvQixJQUFJRyxZQUFZLEdBQUcsT0FBTztRQUUxQixNQUFNQyxRQUFRSCxLQUFLQyxLQUFLLENBQUNDLFdBQVc7UUFDcEMsTUFBTUUsVUFBVUosS0FBS0MsS0FBSyxDQUFDLFdBQVksT0FBUTtRQUUvQyxPQUFPLEdBQWFHLE9BQVZELE9BQU0sTUFBWSxPQUFSQyxTQUFRO0lBQzlCO0lBRUEsSUFBSSxDQUFDeEYsWUFBWSxDQUFDQyxRQUFRO1FBQ3hCLHFCQUNFLDhEQUFDd0Y7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDQzt3QkFBR0QsV0FBVTtrQ0FBeUI7Ozs7OztrQ0FDdkMsOERBQUNFO3dCQUFFRixXQUFVO2tDQUFxQjs7Ozs7O2tDQUdsQyw4REFBQ0c7d0JBQ0NDLFNBQVN0Rjt3QkFDVGtGLFdBQVU7a0NBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBTVQ7SUFFQSxxQkFDRSw4REFBQ0Q7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7OzBDQUNDLDhEQUFDTTtnQ0FBR0wsV0FBVTswQ0FBcUI7Ozs7OzswQ0FDbkMsOERBQUNFO2dDQUFFRixXQUFVOzBDQUFnQjs7Ozs7Ozs7Ozs7O2tDQUkvQiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDTTtnQ0FBS04sV0FBVyw0REFJaEIsT0FIQ3ZGLENBQUFBLHdCQUFBQSxrQ0FBQUEsWUFBYWlELG1CQUFtQixJQUM1Qiw0QkFDQTswQ0FFSGpELENBQUFBLHdCQUFBQSxrQ0FBQUEsWUFBYWlELG1CQUFtQixJQUFHLDBCQUEwQjs7Ozs7OzRCQUUvRGxELDJCQUNDLDhEQUFDOEY7Z0NBQUtOLFdBQVcsNERBSWhCLE9BSEN4RixVQUFVK0MsTUFBTSxHQUNaLDRCQUNBOzBDQUVIL0MsVUFBVStDLE1BQU0sR0FBRyxXQUFXOzs7Ozs7MENBR25DLDhEQUFDNEM7Z0NBQ0NDLFNBQVNqRjtnQ0FDVG9GLFVBQVUzRjtnQ0FDVm9GLFdBQVU7O29DQUVUcEYsVUFBVSxPQUFPO29DQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBSzNCQyx1QkFDQyw4REFBQ2tGO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNNOzRCQUFLTixXQUFVO3NDQUFvQjs7Ozs7O3NDQUNwQyw4REFBQ007c0NBQU16Rjs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFLWlEseUJBQ0MsOERBQUMwRTtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDTTs0QkFBS04sV0FBVTtzQ0FBc0I7Ozs7OztzQ0FDdEMsOERBQUNNO3NDQUFNakY7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQUtiLDhEQUFDMEU7Z0JBQUlDLFdBQVU7O2tDQUViLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ1E7NEJBQUlSLFdBQVU7c0NBQ1o7Z0NBQUM7Z0NBQVk7Z0NBQWM7Z0NBQWM7Z0NBQVk7NkJBQVUsQ0FBQ1MsR0FBRyxDQUFDLENBQUNDLG9CQUNwRSw4REFBQ1A7b0NBRUNDLFNBQVMsSUFBTWxFLGFBQWF3RTtvQ0FDNUJWLFdBQVcsNENBSVYsT0FIQy9ELGNBQWN5RSxNQUNWLGtDQUNBOzhDQUdMQSxRQUFRLGVBQWUsaUJBQWlCQSxJQUFJQyxNQUFNLENBQUMsR0FBR0MsV0FBVyxLQUFLRixJQUFJeEIsS0FBSyxDQUFDLEdBQUcyQixPQUFPLENBQUMsS0FBSzttQ0FSNUZIOzs7Ozs7Ozs7Ozs7Ozs7b0JBZVp6RSxjQUFjLDRCQUNiLDhEQUFDOEQ7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ2M7Z0RBQUdkLFdBQVU7MERBQTJCOzs7Ozs7MERBQ3pDLDhEQUFDRTtnREFBRUYsV0FBVTswREFBcUI7Ozs7OzswREFDbEMsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUNaeEYsMEJBQ0M7O3NFQUNFLDhEQUFDdUY7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDTTtvRUFBS04sV0FBVTs4RUFBYzs7Ozs7OzhFQUM5Qiw4REFBQ007OEVBQU05RixVQUFVdUcsSUFBSTs7Ozs7Ozs7Ozs7O3NFQUV2Qiw4REFBQ2hCOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ007b0VBQUtOLFdBQVU7OEVBQWM7Ozs7Ozs4RUFDOUIsOERBQUNNOzhFQUFNOUYsVUFBVXdHLE1BQU07Ozs7Ozs7Ozs7OztzRUFFekIsOERBQUNqQjs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNNO29FQUFLTixXQUFVOzhFQUFjOzs7Ozs7OEVBQzlCLDhEQUFDTTs4RUFBTTlGLFVBQVV5RyxPQUFPOzs7Ozs7Ozs7Ozs7c0VBRTFCLDhEQUFDbEI7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDTTtvRUFBS04sV0FBVTs4RUFBYzs7Ozs7OzhFQUM5Qiw4REFBQ007OEVBQU05RixVQUFVMEcsV0FBVzs7Ozs7Ozs7Ozs7O3NFQUU5Qiw4REFBQ25COzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ007b0VBQUtOLFdBQVU7OEVBQWM7Ozs7Ozs4RUFDOUIsOERBQUNNOzhFQUFNOUYsVUFBVTJHLFNBQVM7Ozs7Ozs7Ozs7OztzRUFFNUIsOERBQUNwQjs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNNO29FQUFLTixXQUFVOzhFQUFjOzs7Ozs7OEVBQzlCLDhEQUFDTTs4RUFBTTlGLFVBQVU0RyxRQUFROzs7Ozs7Ozs7Ozs7c0VBRTNCLDhEQUFDckI7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDTTtvRUFBS04sV0FBVTs4RUFBYzs7Ozs7OzhFQUM5Qiw4REFBQ007b0VBQUtOLFdBQVcsNERBSWhCLE9BSEN4RixVQUFVK0MsTUFBTSxHQUNaLDRCQUNBOzhFQUVIL0MsVUFBVStDLE1BQU0sR0FBRyxXQUFXOzs7Ozs7Ozs7Ozs7O2lGQUtyQyw4REFBQ3dDO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0Q7NERBQUlDLFdBQVU7Ozs7OztzRUFDZiw4REFBQ007c0VBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQU1kLDhEQUFDUDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNjO2dEQUFHZCxXQUFVOzBEQUEyQjs7Ozs7OzBEQUN6Qyw4REFBQ0U7Z0RBQUVGLFdBQVU7MERBQXFCOzs7Ozs7MERBQ2xDLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNEOzswRUFDQyw4REFBQ087Z0VBQUtOLFdBQVU7MEVBQWM7Ozs7OzswRUFDOUIsOERBQUNEO2dFQUFJQyxXQUFVOzBFQUNaakc7Ozs7Ozs7Ozs7OztrRUFHTCw4REFBQ2dHOzswRUFDQyw4REFBQ087Z0VBQUtOLFdBQVU7MEVBQWM7Ozs7OzswRUFDOUIsOERBQUNEO2dFQUFJQyxXQUFVOzBFQUNaN0Y7Ozs7Ozs7Ozs7OztrRUFHTCw4REFBQzRGO3dEQUFJQyxXQUFVO2tFQUNiLDRFQUFDRzs0REFDQ0MsU0FBUyxJQUFNaUIsT0FBT0MsSUFBSSxDQUFDLHdDQUFvRSxPQUE1QnZILDhCQUErQjs0REFDbEdpRyxXQUFVO3NFQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs0QkFRUnhGLENBQUFBLHNCQUFBQSxnQ0FBQUEsVUFBVytHLFFBQVEsbUJBQ2xCLDhEQUFDeEI7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDYzt3Q0FBR2QsV0FBVTtrREFBMkI7Ozs7OztrREFDekMsOERBQUNFO3dDQUFFRixXQUFVO2tEQUFxQjs7Ozs7O2tEQUNsQyw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNNO3dEQUFLTixXQUFVO2tFQUFjOzs7Ozs7a0VBQzlCLDhEQUFDTTtrRUFBTTlGLFVBQVUrRyxRQUFRLENBQUNDLFVBQVU7Ozs7Ozs7Ozs7OzswREFFdEMsOERBQUN6QjtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNNO3dEQUFLTixXQUFVO2tFQUFjOzs7Ozs7a0VBQzlCLDhEQUFDTTtrRUFBTTlGLFVBQVUrRyxRQUFRLENBQUNFLFVBQVU7Ozs7Ozs7Ozs7OzswREFFdEMsOERBQUMxQjs7a0VBQ0MsOERBQUNPO3dEQUFLTixXQUFVO2tFQUFjOzs7Ozs7a0VBQzlCLDhEQUFDRTt3REFBRUYsV0FBVTtrRUFDVnhGLFVBQVUrRyxRQUFRLENBQUNHLFlBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztvQkFTN0N6RixjQUFjLDhCQUNiLDhEQUFDOEQ7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDYzs0Q0FBR2QsV0FBVTtzREFBMkI7Ozs7OztzREFDekMsOERBQUNFOzRDQUFFRixXQUFVO3NEQUFxQjs7Ozs7O3NEQUNsQyw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDs7c0VBQ0MsOERBQUM0Qjs0REFBTUMsU0FBUTs0REFBYzVCLFdBQVU7c0VBQTBDOzs7Ozs7c0VBQ2pGLDhEQUFDNkI7NERBQ0NDLElBQUc7NERBQ0hDLE1BQUs7NERBQ0xDLGFBQVk7NERBQ1pDLE9BQU94Rzs0REFDUHlHLFVBQVUsQ0FBQ0MsSUFBTXpHLGVBQWV5RyxFQUFFQyxNQUFNLENBQUNILEtBQUs7NERBQzlDakMsV0FBVTs7Ozs7Ozs7Ozs7OzhEQUdkLDhEQUFDRDs7c0VBQ0MsOERBQUM0Qjs0REFBTUMsU0FBUTs0REFBYTVCLFdBQVU7c0VBQTBDOzs7Ozs7c0VBQ2hGLDhEQUFDNkI7NERBQ0NDLElBQUc7NERBQ0hDLE1BQUs7NERBQ0xDLGFBQVk7NERBQ1pDLE9BQU8xRzs0REFDUDJHLFVBQVUsQ0FBQ0MsSUFBTTNHLGNBQWMyRyxFQUFFQyxNQUFNLENBQUNILEtBQUs7NERBQzdDakMsV0FBVTs7Ozs7Ozs7Ozs7OzhEQUdkLDhEQUFDRztvREFDQ0MsU0FBU2pEO29EQUNUb0QsVUFBVXhFLGlCQUFpQixDQUFDTixlQUFlLENBQUNGO29EQUM1Q3lFLFdBQVU7O3dEQUVUakUsZ0JBQWdCLFFBQVE7d0RBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBS2xDLDhEQUFDZ0U7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDYzs0Q0FBR2QsV0FBVTtzREFBMkI7Ozs7OztzREFDekMsOERBQUNFOzRDQUFFRixXQUFVO3NEQUFxQjs7Ozs7O3NEQUNsQyw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRztvREFDQ0MsU0FBUzlDO29EQUNUaUQsVUFBVXhFO29EQUNWaUUsV0FBVywwREFJVixPQUhDeEYsQ0FBQUEsc0JBQUFBLGdDQUFBQSxVQUFXK0MsTUFBTSxJQUNiLCtDQUNBOzt3REFHTHhCLGdCQUFnQixRQUFRO3dEQUN4QnZCLENBQUFBLHNCQUFBQSxnQ0FBQUEsVUFBVytDLE1BQU0sSUFBRyxrQkFBa0I7Ozs7Ozs7OERBR3pDLDhEQUFDNEM7b0RBQ0NDLFNBQVMzQztvREFDVDhDLFVBQVV4RTtvREFDVmlFLFdBQVcsMERBSVYsT0FIQ3ZGLENBQUFBLHdCQUFBQSxrQ0FBQUEsWUFBYWlELG1CQUFtQixJQUM1QiwrQ0FDQTs7d0RBR0wzQixnQkFBZ0IsUUFBUTt3REFDeEJ0QixDQUFBQSx3QkFBQUEsa0NBQUFBLFlBQWFpRCxtQkFBbUIsa0JBQy9CO3NFQUFFOzBGQUVGO3NFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O29CQVNmekIsY0FBYyw4QkFDYiw4REFBQzhEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVU7c0RBQ2IsNEVBQUNNO2dEQUFLTixXQUFVOzBEQUF3Qjs7Ozs7Ozs7Ozs7c0RBRTFDLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNjO29EQUFHZCxXQUFVOzhEQUFvQzs7Ozs7OzhEQUdsRCw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRTtzRUFBRTs7Ozs7O3NFQUNILDhEQUFDbUM7NERBQUdyQyxXQUFVOzs4RUFDWiw4REFBQ3NDOztzRkFBRyw4REFBQ0M7c0ZBQU87Ozs7Ozt3RUFBMEI7Ozs7Ozs7OEVBQ3RDLDhEQUFDRDs7c0ZBQUcsOERBQUNDO3NGQUFPOzs7Ozs7d0VBQStCOzs7Ozs7OzhFQUMzQyw4REFBQ0Q7O3NGQUFHLDhEQUFDQztzRkFBTzs7Ozs7O3dFQUE2Qjs7Ozs7Ozs4RUFDekMsOERBQUNEOztzRkFBRyw4REFBQ0M7c0ZBQU87Ozs7Ozt3RUFBdUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FPN0MsOERBQUN4QztnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ007Z0RBQUtOLFdBQVU7MERBQTBCOzs7Ozs7Ozs7OztzREFFNUMsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ2M7b0RBQUdkLFdBQVU7OERBQXNDOzs7Ozs7OERBR3BELDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ0Q7b0VBQUlDLFdBQVU7O3NGQUNiLDhEQUFDTTs0RUFBS04sV0FBVTtzRkFBc0I7Ozs7OztzRkFDdEMsOERBQUNNO3NGQUFLOzs7Ozs7Ozs7Ozs7OEVBRVIsOERBQUNQO29FQUFJQyxXQUFVOztzRkFDYiw4REFBQ007NEVBQUtOLFdBQVU7c0ZBQXNCOzs7Ozs7c0ZBQ3RDLDhEQUFDTTtzRkFBSzs7Ozs7Ozs7Ozs7OzhFQUVSLDhEQUFDUDtvRUFBSUMsV0FBVTs7c0ZBQ2IsOERBQUNNOzRFQUFLTixXQUFVO3NGQUF1Qjs7Ozs7O3NGQUN2Qyw4REFBQ007c0ZBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzRUFHViw4REFBQ0o7NERBQUVGLFdBQVU7c0VBQWU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQVFwQyw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNjO2dEQUFHZCxXQUFVOztvREFBMkI7a0VBRXZDLDhEQUFDTTt3REFBS04sV0FBVTtrRUFBK0Q7Ozs7Ozs7Ozs7OzswREFFakYsOERBQUNFO2dEQUFFRixXQUFVOzBEQUFxQjs7Ozs7OzBEQUNsQyw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRDs7MEVBQ0MsOERBQUM0QjtnRUFBTUMsU0FBUTtnRUFBaUI1QixXQUFVOzBFQUEwQzs7Ozs7OzBFQUNwRiw4REFBQzZCO2dFQUNDQyxJQUFHO2dFQUNIQyxNQUFLO2dFQUNMQyxhQUFZO2dFQUNaQyxPQUFPOUY7Z0VBQ1ArRixVQUFVLENBQUNDLElBQU0vRixrQkFBa0IrRixFQUFFQyxNQUFNLENBQUNILEtBQUs7Z0VBQ2pEakMsV0FBVTs7Ozs7Ozs7Ozs7O2tFQUdkLDhEQUFDRDt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNHO2dFQUNDQyxTQUFTekM7Z0VBQ1Q0QyxVQUFVeEUsaUJBQWlCLENBQUNJO2dFQUM1QjZELFdBQVU7O29FQUVUakUsZ0JBQWdCLFFBQVE7b0VBQUs7Ozs7Ozs7MEVBRWhDLDhEQUFDb0U7Z0VBQ0NDLFNBQVM3QjtnRUFDVGdDLFVBQVV4RSxpQkFBaUIsQ0FBQ0k7Z0VBQzVCNkQsV0FBVTs7b0VBRVRqRSxnQkFBZ0IsUUFBUTtvRUFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFNdkMsOERBQUNnRTt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNjO2dEQUFHZCxXQUFVOztvREFBMkI7a0VBRXZDLDhEQUFDTTt3REFBS04sV0FBVTtrRUFBNkQ7Ozs7Ozs7Ozs7OzswREFFL0UsOERBQUNFO2dEQUFFRixXQUFVOzBEQUFxQjs7Ozs7OzBEQUNsQyw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRDs7MEVBQ0MsOERBQUM0QjtnRUFBTUMsU0FBUTtnRUFBbUI1QixXQUFVOzBFQUEwQzs7Ozs7OzBFQUN0Riw4REFBQzZCO2dFQUNDQyxJQUFHO2dFQUNIQyxNQUFLO2dFQUNMQyxhQUFZO2dFQUNaQyxPQUFPNUY7Z0VBQ1A2RixVQUFVLENBQUNDLElBQU03RixvQkFBb0I2RixFQUFFQyxNQUFNLENBQUNILEtBQUs7Z0VBQ25EakMsV0FBVTs7Ozs7Ozs7Ozs7O2tFQUdkLDhEQUFDRDs7MEVBQ0MsOERBQUM0QjtnRUFBTUMsU0FBUTtnRUFBZTVCLFdBQVU7MEVBQTBDOzs7Ozs7MEVBQ2xGLDhEQUFDd0M7Z0VBQ0NWLElBQUc7Z0VBQ0hHLE9BQU8xRjtnRUFDUDJGLFVBQVUsQ0FBQ0MsSUFBTTNGLGdCQUFnQjJGLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSztnRUFDL0NqQyxXQUFVOztrRkFFViw4REFBQ3lDO3dFQUFPUixPQUFNO2tGQUFpQjs7Ozs7O2tGQUMvQiw4REFBQ1E7d0VBQU9SLE9BQU07a0ZBQWlCOzs7Ozs7a0ZBQy9CLDhEQUFDUTt3RUFBT1IsT0FBTTtrRkFBaUI7Ozs7OztrRkFDL0IsOERBQUNRO3dFQUFPUixPQUFNO2tGQUFpQjs7Ozs7O2tGQUMvQiw4REFBQ1E7d0VBQU9SLE9BQU07a0ZBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBR25DLDhEQUFDbEM7OzBFQUNDLDhEQUFDNEI7Z0VBQU1DLFNBQVE7Z0VBQVk1QixXQUFVOzBFQUEwQzs7Ozs7OzBFQUMvRSw4REFBQzBDO2dFQUNDWixJQUFHO2dFQUNIRSxhQUFZO2dFQUNaQyxPQUFPeEY7Z0VBQ1B5RixVQUFVLENBQUNDLElBQU16RixhQUFheUYsRUFBRUMsTUFBTSxDQUFDSCxLQUFLO2dFQUM1Q1UsTUFBTTtnRUFDTjNDLFdBQVU7Ozs7Ozs7Ozs7OztrRUFHZCw4REFBQ0c7d0RBQ0NDLFNBQVM1Qjt3REFDVCtCLFVBQVV4RSxpQkFBaUIsQ0FBQ00sb0JBQW9CLENBQUNFO3dEQUNqRHlELFdBQVU7OzREQUVUakUsZ0JBQWdCLFFBQVE7NERBQU87Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBTXhDLDhEQUFDZ0U7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDYzt3Q0FBR2QsV0FBVTs7NENBQTJCOzBEQUV2Qyw4REFBQ007Z0RBQUtOLFdBQVU7MERBQTZEOzs7Ozs7Ozs7Ozs7a0RBRS9FLDhEQUFDRTt3Q0FBRUYsV0FBVTtrREFBcUI7Ozs7OztrREFDbEMsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRDt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUMyQjtnRUFBTUMsU0FBUTtnRUFBbUI1QixXQUFVOzBFQUEwQzs7Ozs7OzBFQUN0Riw4REFBQzZCO2dFQUNDQyxJQUFHO2dFQUNIQyxNQUFLO2dFQUNMQyxhQUFZO2dFQUNaQyxPQUFPdEY7Z0VBQ1B1RixVQUFVLENBQUNDLElBQU12RixvQkFBb0J1RixFQUFFQyxNQUFNLENBQUNILEtBQUs7Z0VBQ25EakMsV0FBVTs7Ozs7Ozs7Ozs7O2tFQUdkLDhEQUFDRDt3REFBSUMsV0FBVTtrRUFDYiw0RUFBQ0c7NERBQ0NDLFNBQVN0Qjs0REFDVHlCLFVBQVV4RSxpQkFBaUIsQ0FBQ1k7NERBQzVCcUQsV0FBVTs7Z0VBRVRqRSxnQkFBZ0IsUUFBUTtnRUFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQU1yQyw4REFBQ2dFO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDRDtvREFBSUMsV0FBVTs4REFDWm5ELG1DQUNDOzswRUFDRSw4REFBQ3FEO2dFQUFFRixXQUFVOztvRUFBbUI7b0VBQXlCckQsaUJBQWlCdUMsS0FBSyxDQUFDLEdBQUc7b0VBQUk7b0VBQUl2QyxpQkFBaUJ1QyxLQUFLLENBQUMsQ0FBQzs7Ozs7OzswRUFDbkgsOERBQUNhO2dFQUFJQyxXQUFVOztrRkFDYiw4REFBQ0Q7d0VBQUlDLFdBQVU7OzBGQUNiLDhEQUFDTTswRkFBSzs7Ozs7OzBGQUNOLDhEQUFDQTtnRkFBS04sV0FBV25ELG1CQUFtQitGLFdBQVcsR0FBRywrQkFBK0I7MEZBQzlFL0YsbUJBQW1CK0YsV0FBVyxHQUFHLFVBQVU7Ozs7Ozs7Ozs7OztrRkFHaEQsOERBQUM3Qzt3RUFBSUMsV0FBVTs7MEZBQ2IsOERBQUNNOzBGQUFLOzs7Ozs7MEZBQ04sOERBQUNBO2dGQUFLTixXQUFXbkQsbUJBQW1CZ0csV0FBVyxHQUFHLCtCQUErQjswRkFDOUVoRyxtQkFBbUJnRyxXQUFXLEdBQUcsVUFBVTs7Ozs7Ozs7Ozs7O2tGQUdoRCw4REFBQzlDO3dFQUFJQyxXQUFVOzswRkFDYiw4REFBQ007MEZBQUs7Ozs7OzswRkFDTiw4REFBQ0E7Z0ZBQUtOLFdBQVduRCxtQkFBbUJpRyxRQUFRLEdBQUcsK0JBQStCOzBGQUMzRWpHLG1CQUFtQmlHLFFBQVEsR0FBRyxVQUFVOzs7Ozs7Ozs7Ozs7a0ZBRzdDLDhEQUFDL0M7d0VBQUlDLFdBQVU7OzBGQUNiLDhEQUFDTTswRkFBSzs7Ozs7OzBGQUNOLDhEQUFDQTtnRkFBS04sV0FBVTswRkFBNkJuRCxtQkFBbUJpQixNQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3FGQUs1RTs7MEVBQ0UsOERBQUNvQzswRUFBRTs7Ozs7OzBFQUNILDhEQUFDSDtnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUNEO3dFQUFJQyxXQUFVOzswRkFDYiw4REFBQ007MEZBQUs7Ozs7OzswRkFDTiw4REFBQ0E7Z0ZBQUtOLFdBQVU7MEZBQWdCOzs7Ozs7Ozs7Ozs7a0ZBRWxDLDhEQUFDRDt3RUFBSUMsV0FBVTs7MEZBQ2IsOERBQUNNOzBGQUFLOzs7Ozs7MEZBQ04sOERBQUNBO2dGQUFLTixXQUFVOzBGQUFnQjs7Ozs7Ozs7Ozs7O2tGQUVsQyw4REFBQ0Q7d0VBQUlDLFdBQVU7OzBGQUNiLDhEQUFDTTswRkFBSzs7Ozs7OzBGQUNOLDhEQUFDQTtnRkFBS04sV0FBVTswRkFBZ0I7Ozs7Ozs7Ozs7OztrRkFFbEMsOERBQUNEO3dFQUFJQyxXQUFVOzswRkFDYiw4REFBQ007MEZBQUs7Ozs7OzswRkFDTiw4REFBQ0E7Z0ZBQUtOLFdBQVU7MEZBQWdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBVWhELDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNjO3dDQUFHZCxXQUFVO2tEQUEyQjs7Ozs7O2tEQUN6Qyw4REFBQ0U7d0NBQUVGLFdBQVU7a0RBQXFCOzs7Ozs7a0RBQ2xDLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0Q7a0VBQ0MsNEVBQUM0Qjs0REFBTTNCLFdBQVU7OzhFQUNmLDhEQUFDNkI7b0VBQU1FLE1BQUs7b0VBQVcvQixXQUFVOzs7Ozs7OEVBQ2pDLDhEQUFDTTtvRUFBS04sV0FBVTs4RUFBZTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBR25DLDhEQUFDRDtrRUFDQyw0RUFBQzRCOzREQUFNM0IsV0FBVTs7OEVBQ2YsOERBQUM2QjtvRUFBTUUsTUFBSztvRUFBVy9CLFdBQVU7Ozs7Ozs4RUFDakMsOERBQUNNO29FQUFLTixXQUFVOzhFQUFlOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFLckMsOERBQUNEOztrRUFDQyw4REFBQzRCO3dEQUFNM0IsV0FBVTtrRUFBK0M7Ozs7OztrRUFDaEUsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQzJCO2dFQUFNM0IsV0FBVTs7a0ZBQ2YsOERBQUM2Qjt3RUFBTUUsTUFBSzt3RUFBVy9CLFdBQVU7Ozs7OztrRkFDakMsOERBQUNNO3dFQUFLTixXQUFVO2tGQUFlOzs7Ozs7Ozs7Ozs7MEVBRWpDLDhEQUFDMkI7Z0VBQU0zQixXQUFVOztrRkFDZiw4REFBQzZCO3dFQUFNRSxNQUFLO3dFQUFXL0IsV0FBVTs7Ozs7O2tGQUNqQyw4REFBQ007d0VBQUtOLFdBQVU7a0ZBQWU7Ozs7Ozs7Ozs7OzswRUFFakMsOERBQUMyQjtnRUFBTTNCLFdBQVU7O2tGQUNmLDhEQUFDNkI7d0VBQU1FLE1BQUs7d0VBQVcvQixXQUFVOzs7Ozs7a0ZBQ2pDLDhEQUFDTTt3RUFBS04sV0FBVTtrRkFBZTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQUtyQyw4REFBQ0c7Z0RBQ0NJLFVBQVV4RTtnREFDVmlFLFdBQVU7O29EQUVUakUsZ0JBQWdCLFFBQVE7b0RBQU07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7b0JBT3hDRSxjQUFjLDRCQUNiLDhEQUFDOEQ7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDYzs0Q0FBR2QsV0FBVTtzREFBMkI7Ozs7OztzREFDekMsOERBQUNFOzRDQUFFRixXQUFVO3NEQUFxQjs7Ozs7O3NEQUdsQyw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDs7c0VBQ0MsOERBQUM0Qjs0REFBTUMsU0FBUTs0REFBb0I1QixXQUFVO3NFQUEwQzs7Ozs7O3NFQUN2Riw4REFBQzZCOzREQUNDQyxJQUFHOzREQUNIQyxNQUFLOzREQUNMQyxhQUFZOzREQUNaQyxPQUFPcEc7NERBQ1BxRyxVQUFVLENBQUNDLElBQU1yRyw0QkFBNEJxRyxFQUFFQyxNQUFNLENBQUNILEtBQUs7NERBQzNEakMsV0FBVTs7Ozs7Ozs7Ozs7OzhEQUdkLDhEQUFDRDs7c0VBQ0MsOERBQUM0Qjs0REFBTUMsU0FBUTs0REFBcUI1QixXQUFVO3NFQUEwQzs7Ozs7O3NFQUN4Riw4REFBQzBDOzREQUNDWixJQUFHOzREQUNIRSxhQUFZOzREQUNaQyxPQUFPdEc7NERBQ1B1RyxVQUFVLENBQUNDLElBQU12RyxzQkFBc0J1RyxFQUFFQyxNQUFNLENBQUNILEtBQUs7NERBQ3JEVSxNQUFNOzREQUNOM0MsV0FBVTs7Ozs7Ozs7Ozs7OzhEQUdkLDhEQUFDRztvREFDQ0MsU0FBUzVDO29EQUNUK0MsVUFBVXhFLGlCQUFpQixDQUFDRiw0QkFBNEIsQ0FBQ0Y7b0RBQ3pEcUUsV0FBVTs7d0RBRVRqRSxnQkFBZ0IsUUFBUTt3REFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FNcEMsOERBQUNnRTtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNjOzRDQUFHZCxXQUFVO3NEQUEyQjs7Ozs7O3NEQUN6Qyw4REFBQ0U7NENBQUVGLFdBQVU7c0RBQXFCOzs7Ozs7d0NBR2pDdEYsZ0JBQWdCcUksTUFBTSxLQUFLLGtCQUMxQiw4REFBQzdDOzRDQUFFRixXQUFVO3NEQUFpQzs7Ozs7aUVBSTlDLDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFDWnRGLGdCQUFnQitGLEdBQUcsQ0FBQyxDQUFDdUMsd0JBQ3BCLDhEQUFDakQ7b0RBQTRCQyxXQUFVOztzRUFDckMsOERBQUNEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ0Q7b0VBQUlDLFdBQVU7OEVBQ1pnRCxRQUFRQyxXQUFXOzs7Ozs7OEVBRXRCLDhEQUFDM0M7b0VBQUtOLFdBQVcsNERBSWhCLE9BSENnRCxRQUFRRSxRQUFRLEdBQUcsZ0NBQ25CRixRQUFRRyxTQUFTLEdBQUcsNEJBQ3BCOzhFQUVDSCxRQUFRRSxRQUFRLEdBQUcsYUFDbkJGLFFBQVFHLFNBQVMsR0FBRyxjQUNwQjs7Ozs7Ozs7Ozs7O3NFQUdMLDhEQUFDcEQ7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDRDs7d0VBQUk7d0VBQWlCaUQsUUFBUUksaUJBQWlCLENBQUNsRSxLQUFLLENBQUMsR0FBRzt3RUFBSTt3RUFBSThELFFBQVFJLGlCQUFpQixDQUFDbEUsS0FBSyxDQUFDLENBQUM7Ozs7Ozs7OEVBQ2xHLDhEQUFDYTs7d0VBQUk7d0VBQWUsSUFBSVYsS0FBSzJELFFBQVF4RCxXQUFXLEdBQUcsTUFBTUYsY0FBYzs7Ozs7Ozs4RUFDdkUsOERBQUNTOzhFQUFLUixzQkFBc0J5RCxRQUFReEQsV0FBVzs7Ozs7Ozs7Ozs7OzttREFsQnpDd0QsUUFBUUssU0FBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O29CQTZCeENwSCxjQUFjLDJCQUNiLDhEQUFDOEQ7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ2M7b0NBQUdkLFdBQVU7OENBQTJCOzs7Ozs7OENBQ3pDLDhEQUFDRTtvQ0FBRUYsV0FBVTs4Q0FBcUI7Ozs7OztnQ0FHakNyRixlQUFlb0ksTUFBTSxLQUFLLGtCQUN6Qiw4REFBQzdDO29DQUFFRixXQUFVOzhDQUFpQzs7Ozs7eURBSTlDLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDWnJGLGVBQWU4RixHQUFHLENBQUMsQ0FBQzZDLFFBQVFDLHNCQUMzQiw4REFBQ3hEOzRDQUFnQkMsV0FBVTs7OERBQ3pCLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ0Q7b0VBQUlDLFdBQVU7OEVBQWVzRCxPQUFPTCxXQUFXOzs7Ozs7OEVBQ2hELDhEQUFDbEQ7b0VBQUlDLFdBQVU7O3dFQUNaLElBQUlYLEtBQUtpRSxPQUFPbEUsU0FBUyxHQUFHLE1BQU1FLGNBQWM7d0VBQUc7d0VBQUtnRSxPQUFPRSxRQUFRLENBQUN0RSxLQUFLLENBQUMsR0FBRzt3RUFBSTt3RUFBSW9FLE9BQU9FLFFBQVEsQ0FBQ3RFLEtBQUssQ0FBQyxDQUFDOzs7Ozs7Ozs7Ozs7O3NFQUdySCw4REFBQ2E7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDTTtvRUFBS04sV0FBVyw0REFFaEIsT0FEQ3NELE9BQU9HLFdBQVcsR0FBRyw0QkFBNEI7OEVBRWhESCxPQUFPRyxXQUFXLEdBQUcsY0FBYzs7Ozs7O2dFQUVyQ0gsT0FBT3JDLE9BQU8sa0JBQ2IsOERBQUNYO29FQUFLTixXQUFVOzt3RUFBcUY7d0VBQ2pHc0QsT0FBT3JDLE9BQU87Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBS3hCLDhEQUFDbEI7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRDs7Z0VBQUk7Z0VBQU91RCxPQUFPSSxpQkFBaUIsQ0FBQ3hFLEtBQUssQ0FBQyxHQUFHO2dFQUFJO2dFQUFJb0UsT0FBT0ksaUJBQWlCLENBQUN4RSxLQUFLLENBQUMsQ0FBQzs7Ozs7OztzRUFDdEYsOERBQUNhOztnRUFBSTtnRUFBS3VELE9BQU9GLGlCQUFpQixDQUFDbEUsS0FBSyxDQUFDLEdBQUc7Z0VBQUk7Z0VBQUlvRSxPQUFPRixpQkFBaUIsQ0FBQ2xFLEtBQUssQ0FBQyxDQUFDOzs7Ozs7Ozs7Ozs7OzsyQ0F2QjlFcUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQW1DOUI7R0FoOUJ3QmxKOztRQWtCbEJQLG1FQUFlQTs7O0tBbEJHTyIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcdG9rZW5kZXYtbmV3cm9vXFxhZG1pbi1wYW5lbFxcc3JjXFxhcHBcXG1vZHVsYXItdG9rZW5zXFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcblxuLy8gSW1wb3J0IGN1c3RvbSBob29rXG5pbXBvcnQgeyB1c2VNb2R1bGFyVG9rZW4gfSBmcm9tICcuLi8uLi9ob29rcy91c2VNb2R1bGFyVG9rZW4nO1xuXG4vLyBDb250cmFjdCBhZGRyZXNzZXMgZnJvbSBlbnZpcm9ubWVudFxuY29uc3QgU0VDVVJJVFlfVE9LRU5fQ09SRV9BRERSRVNTID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQU1PWV9TRUNVUklUWV9UT0tFTl9DT1JFX0FERFJFU1M7XG5jb25zdCBVUEdSQURFX01BTkFHRVJfQUREUkVTUyA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FNT1lfVVBHUkFERV9NQU5BR0VSX0FERFJFU1M7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIE1vZHVsYXJUb2tlbnNQYWdlKCkge1xuICAvLyBVc2UgdGhlIGN1c3RvbSBob29rIGZvciBtb2R1bGFyIHRva2VuIGZ1bmN0aW9uYWxpdHlcbiAgY29uc3Qge1xuICAgIHByb3ZpZGVyLFxuICAgIHNpZ25lcixcbiAgICB0b2tlbkluZm8sXG4gICAgdXBncmFkZUluZm8sXG4gICAgcGVuZGluZ1VwZ3JhZGVzLFxuICAgIHVwZ3JhZGVIaXN0b3J5LFxuICAgIGxvYWRpbmcsXG4gICAgZXJyb3IsXG4gICAgaW5pdGlhbGl6ZVByb3ZpZGVyLFxuICAgIG1pbnRUb2tlbnMsXG4gICAgdG9nZ2xlUGF1c2UsXG4gICAgc2NoZWR1bGVVcGdyYWRlLFxuICAgIHRvZ2dsZUVtZXJnZW5jeU1vZGUsXG4gICAgcmVmcmVzaERhdGEsXG4gICAgc2V0RXJyb3JcbiAgfSA9IHVzZU1vZHVsYXJUb2tlbigpO1xuXG4gIC8vIExvY2FsIHN0YXRlIGZvciBVSVxuICBjb25zdCBbc3VjY2Vzcywgc2V0U3VjY2Vzc10gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW21pbnRBbW91bnQsIHNldE1pbnRBbW91bnRdID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbbWludEFkZHJlc3MsIHNldE1pbnRBZGRyZXNzXSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW3VwZ3JhZGVEZXNjcmlwdGlvbiwgc2V0VXBncmFkZURlc2NyaXB0aW9uXSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW25ld0ltcGxlbWVudGF0aW9uQWRkcmVzcywgc2V0TmV3SW1wbGVtZW50YXRpb25BZGRyZXNzXSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW2FjdGlvbkxvYWRpbmcsIHNldEFjdGlvbkxvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbYWN0aXZlVGFiLCBzZXRBY3RpdmVUYWJdID0gdXNlU3RhdGUoJ292ZXJ2aWV3Jyk7XG5cbiAgLy8gS1lDICYgQ2xhaW1zIHN0YXRlXG4gIGNvbnN0IFtreWNVc2VyQWRkcmVzcywgc2V0S3ljVXNlckFkZHJlc3NdID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbY2xhaW1Vc2VyQWRkcmVzcywgc2V0Q2xhaW1Vc2VyQWRkcmVzc10gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFtjbGFpbVRvcGljSWQsIHNldENsYWltVG9waWNJZF0gPSB1c2VTdGF0ZSgnMTAxMDEwMTAwMDAwMDEnKTtcbiAgY29uc3QgW2NsYWltRGF0YSwgc2V0Q2xhaW1EYXRhXSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW2NoZWNrVXNlckFkZHJlc3MsIHNldENoZWNrVXNlckFkZHJlc3NdID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbdmVyaWZpY2F0aW9uU3RhdHVzLCBzZXRWZXJpZmljYXRpb25TdGF0dXNdID0gdXNlU3RhdGUobnVsbCk7XG5cbiAgLy8gQ2xlYXIgc3VjY2VzcyBtZXNzYWdlIGFmdGVyIDUgc2Vjb25kc1xuICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChzdWNjZXNzKSB7XG4gICAgICBjb25zdCB0aW1lciA9IHNldFRpbWVvdXQoKCkgPT4gc2V0U3VjY2VzcyhudWxsKSwgNTAwMCk7XG4gICAgICByZXR1cm4gKCkgPT4gY2xlYXJUaW1lb3V0KHRpbWVyKTtcbiAgICB9XG4gIH0sIFtzdWNjZXNzXSk7XG5cbiAgLy8gQ2xlYXIgZXJyb3IgbWVzc2FnZSBhZnRlciAxMCBzZWNvbmRzXG4gIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKGVycm9yKSB7XG4gICAgICBjb25zdCB0aW1lciA9IHNldFRpbWVvdXQoKCkgPT4gc2V0RXJyb3IobnVsbCksIDEwMDAwKTtcbiAgICAgIHJldHVybiAoKSA9PiBjbGVhclRpbWVvdXQodGltZXIpO1xuICAgIH1cbiAgfSwgW2Vycm9yLCBzZXRFcnJvcl0pO1xuXG4gIGNvbnN0IGhhbmRsZU1pbnQgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFtaW50QWRkcmVzcyB8fCAhbWludEFtb3VudCkgcmV0dXJuO1xuXG4gICAgc2V0QWN0aW9uTG9hZGluZyh0cnVlKTtcbiAgICBzZXRFcnJvcihudWxsKTtcbiAgICBzZXRTdWNjZXNzKG51bGwpO1xuXG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IG1pbnRUb2tlbnMobWludEFkZHJlc3MsIG1pbnRBbW91bnQpO1xuICAgICAgc2V0U3VjY2VzcyhgU3VjY2Vzc2Z1bGx5IG1pbnRlZCAke21pbnRBbW91bnR9IHRva2VucyB0byAke21pbnRBZGRyZXNzfWApO1xuICAgICAgc2V0TWludEFtb3VudCgnJyk7XG4gICAgICBzZXRNaW50QWRkcmVzcygnJyk7XG4gICAgICBhd2FpdCByZWZyZXNoRGF0YSgpOyAvLyBSZWZyZXNoIHRva2VuIGluZm9cbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBtaW50aW5nIHRva2VuczonLCBlcnJvcik7XG4gICAgICBzZXRFcnJvcihgRmFpbGVkIHRvIG1pbnQgdG9rZW5zOiAke2Vycm9yLm1lc3NhZ2V9YCk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldEFjdGlvbkxvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVQYXVzZVRvZ2dsZSA9IGFzeW5jICgpID0+IHtcbiAgICBzZXRBY3Rpb25Mb2FkaW5nKHRydWUpO1xuICAgIHNldEVycm9yKG51bGwpO1xuICAgIHNldFN1Y2Nlc3MobnVsbCk7XG5cbiAgICB0cnkge1xuICAgICAgYXdhaXQgdG9nZ2xlUGF1c2UoKTtcbiAgICAgIHNldFN1Y2Nlc3MoYFRva2VuICR7dG9rZW5JbmZvPy5wYXVzZWQgPyAndW5wYXVzZWQnIDogJ3BhdXNlZCd9IHN1Y2Nlc3NmdWxseWApO1xuICAgICAgYXdhaXQgcmVmcmVzaERhdGEoKTsgLy8gUmVmcmVzaCB0b2tlbiBpbmZvXG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgdG9nZ2xpbmcgcGF1c2U6JywgZXJyb3IpO1xuICAgICAgc2V0RXJyb3IoYEZhaWxlZCB0byAke3Rva2VuSW5mbz8ucGF1c2VkID8gJ3VucGF1c2UnIDogJ3BhdXNlJ30gdG9rZW46ICR7ZXJyb3IubWVzc2FnZX1gKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0QWN0aW9uTG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVNjaGVkdWxlVXBncmFkZSA9IGFzeW5jICgpID0+IHtcbiAgICBpZiAoIW5ld0ltcGxlbWVudGF0aW9uQWRkcmVzcyB8fCAhdXBncmFkZURlc2NyaXB0aW9uKSByZXR1cm47XG5cbiAgICBzZXRBY3Rpb25Mb2FkaW5nKHRydWUpO1xuICAgIHNldEVycm9yKG51bGwpO1xuICAgIHNldFN1Y2Nlc3MobnVsbCk7XG5cbiAgICB0cnkge1xuICAgICAgYXdhaXQgc2NoZWR1bGVVcGdyYWRlKG5ld0ltcGxlbWVudGF0aW9uQWRkcmVzcywgdXBncmFkZURlc2NyaXB0aW9uKTtcbiAgICAgIHNldFN1Y2Nlc3MoJ1VwZ3JhZGUgc2NoZWR1bGVkIHN1Y2Nlc3NmdWxseSEgSXQgd2lsbCBiZSBleGVjdXRhYmxlIGFmdGVyIHRoZSA0OC1ob3VyIHRpbWVsb2NrLicpO1xuICAgICAgc2V0TmV3SW1wbGVtZW50YXRpb25BZGRyZXNzKCcnKTtcbiAgICAgIHNldFVwZ3JhZGVEZXNjcmlwdGlvbignJyk7XG4gICAgICBhd2FpdCByZWZyZXNoRGF0YSgpOyAvLyBSZWZyZXNoIHVwZ3JhZGUgaW5mb1xuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHNjaGVkdWxpbmcgdXBncmFkZTonLCBlcnJvcik7XG4gICAgICBzZXRFcnJvcihgRmFpbGVkIHRvIHNjaGVkdWxlIHVwZ3JhZGU6ICR7ZXJyb3IubWVzc2FnZX1gKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0QWN0aW9uTG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUVtZXJnZW5jeU1vZGVUb2dnbGUgPSBhc3luYyAoKSA9PiB7XG4gICAgc2V0QWN0aW9uTG9hZGluZyh0cnVlKTtcbiAgICBzZXRFcnJvcihudWxsKTtcbiAgICBzZXRTdWNjZXNzKG51bGwpO1xuXG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IHRvZ2dsZUVtZXJnZW5jeU1vZGUoKTtcbiAgICAgIHNldFN1Y2Nlc3MoYEVtZXJnZW5jeSBtb2RlICR7dXBncmFkZUluZm8/LmVtZXJnZW5jeU1vZGVBY3RpdmUgPyAnZGVhY3RpdmF0ZWQnIDogJ2FjdGl2YXRlZCd9IHN1Y2Nlc3NmdWxseWApO1xuICAgICAgYXdhaXQgcmVmcmVzaERhdGEoKTsgLy8gUmVmcmVzaCB1cGdyYWRlIGluZm9cbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciB0b2dnbGluZyBlbWVyZ2VuY3kgbW9kZTonLCBlcnJvcik7XG4gICAgICBzZXRFcnJvcihgRmFpbGVkIHRvICR7dXBncmFkZUluZm8/LmVtZXJnZW5jeU1vZGVBY3RpdmUgPyAnZGVhY3RpdmF0ZScgOiAnYWN0aXZhdGUnfSBlbWVyZ2VuY3kgbW9kZTogJHtlcnJvci5tZXNzYWdlfWApO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRBY3Rpb25Mb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gS1lDICYgQ2xhaW1zIGhhbmRsZXJzXG4gIGNvbnN0IGhhbmRsZUFwcHJvdmVLWUMgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFreWNVc2VyQWRkcmVzcykgcmV0dXJuO1xuXG4gICAgc2V0QWN0aW9uTG9hZGluZyh0cnVlKTtcbiAgICBzZXRFcnJvcihudWxsKTtcbiAgICBzZXRTdWNjZXNzKG51bGwpO1xuXG4gICAgdHJ5IHtcbiAgICAgIC8vIENhbGwgdGhlIEtZQyBhcHByb3ZhbCBBUElcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkva3ljL2FwcHJvdmUnLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xuICAgICAgICAgIHRva2VuQWRkcmVzczogU0VDVVJJVFlfVE9LRU5fQ09SRV9BRERSRVNTLFxuICAgICAgICAgIHVzZXJBZGRyZXNzOiBreWNVc2VyQWRkcmVzcyxcbiAgICAgICAgfSksXG4gICAgICB9KTtcblxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0ZhaWxlZCB0byBhcHByb3ZlIEtZQycpO1xuICAgICAgfVxuXG4gICAgICBzZXRTdWNjZXNzKGBLWUMgYXBwcm92ZWQgc3VjY2Vzc2Z1bGx5IGZvciAke2t5Y1VzZXJBZGRyZXNzfWApO1xuICAgICAgc2V0S3ljVXNlckFkZHJlc3MoJycpO1xuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGFwcHJvdmluZyBLWUM6JywgZXJyb3IpO1xuICAgICAgc2V0RXJyb3IoYEZhaWxlZCB0byBhcHByb3ZlIEtZQzogJHtlcnJvci5tZXNzYWdlfWApO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRBY3Rpb25Mb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlQWRkVG9XaGl0ZWxpc3QgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFreWNVc2VyQWRkcmVzcykgcmV0dXJuO1xuXG4gICAgc2V0QWN0aW9uTG9hZGluZyh0cnVlKTtcbiAgICBzZXRFcnJvcihudWxsKTtcbiAgICBzZXRTdWNjZXNzKG51bGwpO1xuXG4gICAgdHJ5IHtcbiAgICAgIC8vIENhbGwgdGhlIHdoaXRlbGlzdCBBUElcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkva3ljL3doaXRlbGlzdCcsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgICAgdG9rZW5BZGRyZXNzOiBTRUNVUklUWV9UT0tFTl9DT1JFX0FERFJFU1MsXG4gICAgICAgICAgdXNlckFkZHJlc3M6IGt5Y1VzZXJBZGRyZXNzLFxuICAgICAgICB9KSxcbiAgICAgIH0pO1xuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignRmFpbGVkIHRvIGFkZCB0byB3aGl0ZWxpc3QnKTtcbiAgICAgIH1cblxuICAgICAgc2V0U3VjY2VzcyhgVXNlciBhZGRlZCB0byB3aGl0ZWxpc3Qgc3VjY2Vzc2Z1bGx5OiAke2t5Y1VzZXJBZGRyZXNzfWApO1xuICAgICAgc2V0S3ljVXNlckFkZHJlc3MoJycpO1xuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGFkZGluZyB0byB3aGl0ZWxpc3Q6JywgZXJyb3IpO1xuICAgICAgc2V0RXJyb3IoYEZhaWxlZCB0byBhZGQgdG8gd2hpdGVsaXN0OiAke2Vycm9yLm1lc3NhZ2V9YCk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldEFjdGlvbkxvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVJc3N1ZUNsYWltID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmICghY2xhaW1Vc2VyQWRkcmVzcyB8fCAhY2xhaW1Ub3BpY0lkKSByZXR1cm47XG5cbiAgICBzZXRBY3Rpb25Mb2FkaW5nKHRydWUpO1xuICAgIHNldEVycm9yKG51bGwpO1xuICAgIHNldFN1Y2Nlc3MobnVsbCk7XG5cbiAgICB0cnkge1xuICAgICAgLy8gQ2FsbCB0aGUgY2xhaW1zIEFQSVxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9reWMvaXNzdWUtY2xhaW0nLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xuICAgICAgICAgIHVzZXJBZGRyZXNzOiBjbGFpbVVzZXJBZGRyZXNzLFxuICAgICAgICAgIHRvcGljSWQ6IGNsYWltVG9waWNJZCxcbiAgICAgICAgICBkYXRhOiBjbGFpbURhdGEgfHwgJycsXG4gICAgICAgIH0pLFxuICAgICAgfSk7XG5cbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdGYWlsZWQgdG8gaXNzdWUgY2xhaW0nKTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgc2V0U3VjY2VzcyhgQ2xhaW0gaXNzdWVkIHN1Y2Nlc3NmdWxseSEgQ2xhaW0gSUQ6ICR7cmVzdWx0LmNsYWltSWR9YCk7XG4gICAgICBzZXRDbGFpbVVzZXJBZGRyZXNzKCcnKTtcbiAgICAgIHNldENsYWltRGF0YSgnJyk7XG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgaXNzdWluZyBjbGFpbTonLCBlcnJvcik7XG4gICAgICBzZXRFcnJvcihgRmFpbGVkIHRvIGlzc3VlIGNsYWltOiAke2Vycm9yLm1lc3NhZ2V9YCk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldEFjdGlvbkxvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVDaGVja1N0YXR1cyA9IGFzeW5jICgpID0+IHtcbiAgICBpZiAoIWNoZWNrVXNlckFkZHJlc3MpIHJldHVybjtcblxuICAgIHNldEFjdGlvbkxvYWRpbmcodHJ1ZSk7XG4gICAgc2V0RXJyb3IobnVsbCk7XG5cbiAgICB0cnkge1xuICAgICAgLy8gQ2FsbCB0aGUgc3RhdHVzIGNoZWNrIEFQSVxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgL2FwaS9reWMvc3RhdHVzP3Rva2VuQWRkcmVzcz0ke1NFQ1VSSVRZX1RPS0VOX0NPUkVfQUREUkVTU30mdXNlckFkZHJlc3M9JHtjaGVja1VzZXJBZGRyZXNzfWApO1xuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignRmFpbGVkIHRvIGNoZWNrIHN0YXR1cycpO1xuICAgICAgfVxuXG4gICAgICBjb25zdCBzdGF0dXMgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICBzZXRWZXJpZmljYXRpb25TdGF0dXMoc3RhdHVzKTtcbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjaGVja2luZyBzdGF0dXM6JywgZXJyb3IpO1xuICAgICAgc2V0RXJyb3IoYEZhaWxlZCB0byBjaGVjayBzdGF0dXM6ICR7ZXJyb3IubWVzc2FnZX1gKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0QWN0aW9uTG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIFV0aWxpdHkgZnVuY3Rpb25zXG4gIGNvbnN0IGZvcm1hdEFkZHJlc3MgPSAoYWRkcmVzczogc3RyaW5nKSA9PiB7XG4gICAgcmV0dXJuIGAke2FkZHJlc3Muc2xpY2UoMCwgNil9Li4uJHthZGRyZXNzLnNsaWNlKC00KX1gO1xuICB9O1xuXG4gIGNvbnN0IGZvcm1hdFRpbWVzdGFtcCA9ICh0aW1lc3RhbXA6IG51bWJlcikgPT4ge1xuICAgIHJldHVybiBuZXcgRGF0ZSh0aW1lc3RhbXAgKiAxMDAwKS50b0xvY2FsZVN0cmluZygpO1xuICB9O1xuXG4gIGNvbnN0IGdldFRpbWVVbnRpbEV4ZWN1dGlvbiA9IChleGVjdXRlVGltZTogbnVtYmVyKSA9PiB7XG4gICAgY29uc3Qgbm93ID0gTWF0aC5mbG9vcihEYXRlLm5vdygpIC8gMTAwMCk7XG4gICAgY29uc3QgdGltZUxlZnQgPSBleGVjdXRlVGltZSAtIG5vdztcblxuICAgIGlmICh0aW1lTGVmdCA8PSAwKSByZXR1cm4gJ1JlYWR5IHRvIGV4ZWN1dGUnO1xuXG4gICAgY29uc3QgaG91cnMgPSBNYXRoLmZsb29yKHRpbWVMZWZ0IC8gMzYwMCk7XG4gICAgY29uc3QgbWludXRlcyA9IE1hdGguZmxvb3IoKHRpbWVMZWZ0ICUgMzYwMCkgLyA2MCk7XG5cbiAgICByZXR1cm4gYCR7aG91cnN9aCAke21pbnV0ZXN9bSByZW1haW5pbmdgO1xuICB9O1xuXG4gIGlmICghcHJvdmlkZXIgfHwgIXNpZ25lcikge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIHAtNlwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93IHAtNlwiPlxuICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCBtYi0yXCI+Q29ubmVjdCBXYWxsZXQ8L2gyPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbWItNFwiPlxuICAgICAgICAgICAgUGxlYXNlIGNvbm5lY3QgeW91ciBNZXRhTWFzayB3YWxsZXQgdG8gbWFuYWdlIG1vZHVsYXIgdG9rZW5zLlxuICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXtpbml0aWFsaXplUHJvdmlkZXJ9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ibHVlLTYwMCBob3ZlcjpiZy1ibHVlLTcwMCB0ZXh0LXdoaXRlIGZvbnQtYm9sZCBweS0yIHB4LTQgcm91bmRlZFwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgQ29ubmVjdCBXYWxsZXRcbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIHAtNiBzcGFjZS15LTZcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyXCI+XG4gICAgICAgIDxkaXY+XG4gICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZFwiPk1vZHVsYXIgVG9rZW4gTWFuYWdlbWVudDwvaDE+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgTWFuYWdlIHlvdXIgbW9kdWxhciBFUkMtMzY0MyBzZWN1cml0eSB0b2tlbnMgYW5kIHVwZ3JhZGVzXG4gICAgICAgICAgPC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC0yXCI+XG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgaW5saW5lLWZsZXggcHgtMiBweS0xIHRleHQteHMgZm9udC1zZW1pYm9sZCByb3VuZGVkLWZ1bGwgJHtcbiAgICAgICAgICAgIHVwZ3JhZGVJbmZvPy5lbWVyZ2VuY3lNb2RlQWN0aXZlXG4gICAgICAgICAgICAgID8gJ2JnLXJlZC0xMDAgdGV4dC1yZWQtODAwJ1xuICAgICAgICAgICAgICA6ICdiZy1ncmF5LTEwMCB0ZXh0LWdyYXktODAwJ1xuICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgIHt1cGdyYWRlSW5mbz8uZW1lcmdlbmN5TW9kZUFjdGl2ZSA/IFwiRW1lcmdlbmN5IE1vZGUgQWN0aXZlXCIgOiBcIk5vcm1hbCBNb2RlXCJ9XG4gICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgIHt0b2tlbkluZm8gJiYgKFxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgaW5saW5lLWZsZXggcHgtMiBweS0xIHRleHQteHMgZm9udC1zZW1pYm9sZCByb3VuZGVkLWZ1bGwgJHtcbiAgICAgICAgICAgICAgdG9rZW5JbmZvLnBhdXNlZFxuICAgICAgICAgICAgICAgID8gJ2JnLXJlZC0xMDAgdGV4dC1yZWQtODAwJ1xuICAgICAgICAgICAgICAgIDogJ2JnLWdyZWVuLTEwMCB0ZXh0LWdyZWVuLTgwMCdcbiAgICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgICAge3Rva2VuSW5mby5wYXVzZWQgPyBcIlBhdXNlZFwiIDogXCJBY3RpdmVcIn1cbiAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICApfVxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIG9uQ2xpY2s9e3JlZnJlc2hEYXRhfVxuICAgICAgICAgICAgZGlzYWJsZWQ9e2xvYWRpbmd9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ncmF5LTEwMCBob3ZlcjpiZy1ncmF5LTIwMCB0ZXh0LWdyYXktNzAwIGZvbnQtbWVkaXVtIHB5LTEgcHgtMyByb3VuZGVkIHRleHQtc20gZGlzYWJsZWQ6b3BhY2l0eS01MFwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAge2xvYWRpbmcgPyAn8J+UhCcgOiAn4oa7J30gUmVmcmVzaFxuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7ZXJyb3IgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXJlZC0xMDAgYm9yZGVyIGJvcmRlci1yZWQtNDAwIHRleHQtcmVkLTcwMCBweC00IHB5LTMgcm91bmRlZFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtcmVkLTYwMCBtci0yXCI+4pqg77iPPC9zcGFuPlxuICAgICAgICAgICAgPHNwYW4+e2Vycm9yfTwvc3Bhbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICB7c3VjY2VzcyAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JlZW4tMTAwIGJvcmRlciBib3JkZXItZ3JlZW4tNDAwIHRleHQtZ3JlZW4tNzAwIHB4LTQgcHktMyByb3VuZGVkXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmVlbi02MDAgbXItMlwiPuKchTwvc3Bhbj5cbiAgICAgICAgICAgIDxzcGFuPntzdWNjZXNzfTwvc3Bhbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICB7LyogVGFiIE5hdmlnYXRpb24gKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgICAgPG5hdiBjbGFzc05hbWU9XCItbWItcHggZmxleCBzcGFjZS14LThcIj5cbiAgICAgICAgICAgIHtbJ292ZXJ2aWV3JywgJ29wZXJhdGlvbnMnLCAna3ljLWNsYWltcycsICd1cGdyYWRlcycsICdoaXN0b3J5J10ubWFwKCh0YWIpID0+IChcbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIGtleT17dGFifVxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEFjdGl2ZVRhYih0YWIpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHB5LTIgcHgtMSBib3JkZXItYi0yIGZvbnQtbWVkaXVtIHRleHQtc20gJHtcbiAgICAgICAgICAgICAgICAgIGFjdGl2ZVRhYiA9PT0gdGFiXG4gICAgICAgICAgICAgICAgICAgID8gJ2JvcmRlci1ibHVlLTUwMCB0ZXh0LWJsdWUtNjAwJ1xuICAgICAgICAgICAgICAgICAgICA6ICdib3JkZXItdHJhbnNwYXJlbnQgdGV4dC1ncmF5LTUwMCBob3Zlcjp0ZXh0LWdyYXktNzAwIGhvdmVyOmJvcmRlci1ncmF5LTMwMCdcbiAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHt0YWIgPT09ICdreWMtY2xhaW1zJyA/ICdLWUMgJiBDbGFpbXMnIDogdGFiLmNoYXJBdCgwKS50b1VwcGVyQ2FzZSgpICsgdGFiLnNsaWNlKDEpLnJlcGxhY2UoJy0nLCAnICcpfVxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvbmF2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogVGFiIENvbnRlbnQgKi99XG4gICAgICAgIHthY3RpdmVUYWIgPT09ICdvdmVydmlldycgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdyBwLTZcIj5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSBtYi0yXCI+VG9rZW4gSW5mb3JtYXRpb248L2gzPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbWItNFwiPkN1cnJlbnQgdG9rZW4gZGV0YWlscyBhbmQgc3RhdHVzPC9wPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICB7dG9rZW5JbmZvID8gKFxuICAgICAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+TmFtZTo8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj57dG9rZW5JbmZvLm5hbWV9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+U3ltYm9sOjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPnt0b2tlbkluZm8uc3ltYm9sfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPlZlcnNpb246PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+e3Rva2VuSW5mby52ZXJzaW9ufTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPlRvdGFsIFN1cHBseTo8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj57dG9rZW5JbmZvLnRvdGFsU3VwcGx5fTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPk1heCBTdXBwbHk6PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+e3Rva2VuSW5mby5tYXhTdXBwbHl9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+RGVjaW1hbHM6PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+e3Rva2VuSW5mby5kZWNpbWFsc308L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj5TdGF0dXM6PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgaW5saW5lLWZsZXggcHgtMiBweS0xIHRleHQteHMgZm9udC1zZW1pYm9sZCByb3VuZGVkLWZ1bGwgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdG9rZW5JbmZvLnBhdXNlZFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLXJlZC0xMDAgdGV4dC1yZWQtODAwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogJ2JnLWdyZWVuLTEwMCB0ZXh0LWdyZWVuLTgwMCdcbiAgICAgICAgICAgICAgICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge3Rva2VuSW5mby5wYXVzZWQgPyBcIlBhdXNlZFwiIDogXCJBY3RpdmVcIn1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTQgdy00IGJvcmRlci1iLTIgYm9yZGVyLWJsdWUtNjAwXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4+TG9hZGluZyB0b2tlbiBpbmZvcm1hdGlvbi4uLjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93IHAtNlwiPlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIG1iLTJcIj5Db250cmFjdCBBZGRyZXNzZXM8L2gzPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbWItNFwiPkRlcGxveWVkIGNvbnRyYWN0IGFkZHJlc3NlcyBvbiBBbW95IHRlc3RuZXQ8L3A+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+U2VjdXJpdHlUb2tlbkNvcmU6PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMCBmb250LW1vbm9cIj5cbiAgICAgICAgICAgICAgICAgICAgICB7U0VDVVJJVFlfVE9LRU5fQ09SRV9BRERSRVNTfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj5VcGdyYWRlTWFuYWdlcjo8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwIGZvbnQtbW9ub1wiPlxuICAgICAgICAgICAgICAgICAgICAgIHtVUEdSQURFX01BTkFHRVJfQUREUkVTU31cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHQtMlwiPlxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gd2luZG93Lm9wZW4oYGh0dHBzOi8vYW1veS5wb2x5Z29uc2Nhbi5jb20vYWRkcmVzcy8ke1NFQ1VSSVRZX1RPS0VOX0NPUkVfQUREUkVTU31gLCAnX2JsYW5rJyl9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctZ3JheS0xMDAgaG92ZXI6YmctZ3JheS0yMDAgdGV4dC1ncmF5LTcwMCBmb250LW1lZGl1bSBweS0xIHB4LTMgcm91bmRlZCB0ZXh0LXNtXCJcbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIFZpZXcgb24gUG9seWdvblNjYW5cbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAge3Rva2VuSW5mbz8ubWV0YWRhdGEgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93IHAtNlwiPlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIG1iLTJcIj5Ub2tlbiBNZXRhZGF0YTwvaDM+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBtYi00XCI+QWRkaXRpb25hbCB0b2tlbiBpbmZvcm1hdGlvbiBhbmQgY29uZmlndXJhdGlvbjwvcD5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPlByaWNlOjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4+e3Rva2VuSW5mby5tZXRhZGF0YS50b2tlblByaWNlfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPkJvbnVzIFRpZXJzOjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4+e3Rva2VuSW5mby5tZXRhZGF0YS5ib251c1RpZXJzfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj5EZXRhaWxzOjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwIG10LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7dG9rZW5JbmZvLm1ldGFkYXRhLnRva2VuRGV0YWlsc31cbiAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cblxuICAgICAgICB7YWN0aXZlVGFiID09PSAnb3BlcmF0aW9ucycgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdyBwLTZcIj5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSBtYi0yXCI+TWludCBUb2tlbnM8L2gzPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbWItNFwiPk1pbnQgbmV3IHRva2VucyB0byBhIHNwZWNpZmllZCBhZGRyZXNzPC9wPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8bGFiZWwgaHRtbEZvcj1cIm1pbnRBZGRyZXNzXCIgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwXCI+UmVjaXBpZW50IEFkZHJlc3M8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICBpZD1cIm1pbnRBZGRyZXNzXCJcbiAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCIweC4uLlwiXG4gICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e21pbnRBZGRyZXNzfVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0TWludEFkZHJlc3MoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm10LTEgYmxvY2sgdy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgc2hhZG93LXNtIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci1ibHVlLTUwMFwiXG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxsYWJlbCBodG1sRm9yPVwibWludEFtb3VudFwiIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMFwiPkFtb3VudDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgIGlkPVwibWludEFtb3VudFwiXG4gICAgICAgICAgICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXG4gICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCIxMDAwXCJcbiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17bWludEFtb3VudH1cbiAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldE1pbnRBbW91bnQoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm10LTEgYmxvY2sgdy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgc2hhZG93LXNtIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci1ibHVlLTUwMFwiXG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlTWludH1cbiAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2FjdGlvbkxvYWRpbmcgfHwgIW1pbnRBZGRyZXNzIHx8ICFtaW50QW1vdW50fVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctYmx1ZS02MDAgaG92ZXI6YmctYmx1ZS03MDAgdGV4dC13aGl0ZSBmb250LWJvbGQgcHktMiBweC00IHJvdW5kZWQgZGlzYWJsZWQ6b3BhY2l0eS01MFwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIHthY3Rpb25Mb2FkaW5nID8gJ/CflIQgJyA6ICcnfU1pbnQgVG9rZW5zXG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdyBwLTZcIj5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSBtYi0yXCI+VG9rZW4gQ29udHJvbHM8L2gzPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbWItNFwiPlBhdXNlL3VucGF1c2UgdG9rZW4gdHJhbnNmZXJzIGFuZCBlbWVyZ2VuY3kgY29udHJvbHM8L3A+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlUGF1c2VUb2dnbGV9XG4gICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXthY3Rpb25Mb2FkaW5nfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B3LWZ1bGwgZm9udC1ib2xkIHB5LTIgcHgtNCByb3VuZGVkIGRpc2FibGVkOm9wYWNpdHktNTAgJHtcbiAgICAgICAgICAgICAgICAgICAgICB0b2tlbkluZm8/LnBhdXNlZFxuICAgICAgICAgICAgICAgICAgICAgICAgPyAnYmctZ3JlZW4tNjAwIGhvdmVyOmJnLWdyZWVuLTcwMCB0ZXh0LXdoaXRlJ1xuICAgICAgICAgICAgICAgICAgICAgICAgOiAnYmctcmVkLTYwMCBob3ZlcjpiZy1yZWQtNzAwIHRleHQtd2hpdGUnXG4gICAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICB7YWN0aW9uTG9hZGluZyA/ICfwn5SEICcgOiAnJ31cbiAgICAgICAgICAgICAgICAgICAge3Rva2VuSW5mbz8ucGF1c2VkID8gJ1VucGF1c2UgVG9rZW4nIDogJ1BhdXNlIFRva2VuJ31cbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuXG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUVtZXJnZW5jeU1vZGVUb2dnbGV9XG4gICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXthY3Rpb25Mb2FkaW5nfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B3LWZ1bGwgZm9udC1ib2xkIHB5LTIgcHgtNCByb3VuZGVkIGRpc2FibGVkOm9wYWNpdHktNTAgJHtcbiAgICAgICAgICAgICAgICAgICAgICB1cGdyYWRlSW5mbz8uZW1lcmdlbmN5TW9kZUFjdGl2ZVxuICAgICAgICAgICAgICAgICAgICAgICAgPyAnYmctZ3JlZW4tNjAwIGhvdmVyOmJnLWdyZWVuLTcwMCB0ZXh0LXdoaXRlJ1xuICAgICAgICAgICAgICAgICAgICAgICAgOiAnYmctcmVkLTYwMCBob3ZlcjpiZy1yZWQtNzAwIHRleHQtd2hpdGUnXG4gICAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICB7YWN0aW9uTG9hZGluZyA/ICfwn5SEICcgOiAnJ31cbiAgICAgICAgICAgICAgICAgICAge3VwZ3JhZGVJbmZvPy5lbWVyZ2VuY3lNb2RlQWN0aXZlID8gKFxuICAgICAgICAgICAgICAgICAgICAgIDw+4pyFIERlYWN0aXZhdGUgRW1lcmdlbmN5IE1vZGU8Lz5cbiAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICA8PuKaoSBBY3RpdmF0ZSBFbWVyZ2VuY3kgTW9kZTwvPlxuICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG5cbiAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ2t5Yy1jbGFpbXMnICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ibHVlLTUwIGJvcmRlciBib3JkZXItYmx1ZS0yMDAgcm91bmRlZC1sZyBwLTRcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LXNocmluay0wXCI+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNjAwIHRleHQteGxcIj7wn5SQPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWwtM1wiPlxuICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ibHVlLTgwMFwiPlxuICAgICAgICAgICAgICAgICAgICBPbi1DaGFpbiBLWUMgJiBDbGFpbXMgU3lzdGVtXG4gICAgICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0xIHRleHQtc20gdGV4dC1ibHVlLTcwMFwiPlxuICAgICAgICAgICAgICAgICAgICA8cD5NYW5hZ2UgS1lDIHZlcmlmaWNhdGlvbiBhbmQgY3VzdG9tIGNsYWltcyB1c2luZyBUb2tlbnktc3R5bGUgVG9waWMgSURzOjwvcD5cbiAgICAgICAgICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT1cIm10LTEgbGlzdC1kaXNjIGxpc3QtaW5zaWRlIHNwYWNlLXktMVwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxsaT48c3Ryb25nPktZQyBWZXJpZmljYXRpb246PC9zdHJvbmc+IFRvcGljIElEIDEwMTAxMDEwMDAwMDAxPC9saT5cbiAgICAgICAgICAgICAgICAgICAgICA8bGk+PHN0cm9uZz5HZW5lcmFsIFF1YWxpZmljYXRpb246PC9zdHJvbmc+IFRvcGljIElEIDEwMTAxMDEwMDAwMDA0PC9saT5cbiAgICAgICAgICAgICAgICAgICAgICA8bGk+PHN0cm9uZz5BY2NyZWRpdGVkIEludmVzdG9yOjwvc3Ryb25nPiBUb3BpYyBJRCAxMDEwMTAxMDAwMDAwMjwvbGk+XG4gICAgICAgICAgICAgICAgICAgICAgPGxpPjxzdHJvbmc+Q3VzdG9tIENsYWltczo8L3N0cm9uZz4gTGlrZSAxMDEwMTAxMDAwMDY0OCBmb3Igc3BlY2lmaWMgS1lDIHN0YXR1czwvbGk+XG4gICAgICAgICAgICAgICAgICAgIDwvdWw+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy15ZWxsb3ctNTAgYm9yZGVyIGJvcmRlci15ZWxsb3ctMjAwIHJvdW5kZWQtbGcgcC00XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC1zaHJpbmstMFwiPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC15ZWxsb3ctNjAwIHRleHQteGxcIj7impnvuI88L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtbC0zXCI+XG4gICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXllbGxvdy04MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgU3lzdGVtIFN0YXR1c1xuICAgICAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMSB0ZXh0LXNtIHRleHQteWVsbG93LTcwMFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTMgZ2FwLTIgbXQtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tNjAwIG1yLTFcIj7inIU8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj5DbGFpbXMgSXNzdWFuY2U8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmVlbi02MDAgbXItMVwiPuKchTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPlN0YXR1cyBDaGVja2luZzwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXllbGxvdy02MDAgbXItMVwiPuKaoO+4jzwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPk1vZHVsZSBJbnRlZ3JhdGlvbjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTIgdGV4dC14c1wiPlxuICAgICAgICAgICAgICAgICAgICAgIENsYWltcyBzeXN0ZW0gaXMgZnVsbHkgb3BlcmF0aW9uYWwuIEtZQy9XaGl0ZWxpc3QgZnVuY3Rpb25zIHJlcXVpcmUgbW9kdWxlIHJlZ2lzdHJhdGlvbi5cbiAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBsZzpncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93IHAtNlwiPlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgIEtZQyBNYW5hZ2VtZW50XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC0yIHRleHQteHMgYmcteWVsbG93LTEwMCB0ZXh0LXllbGxvdy04MDAgcHgtMiBweS0xIHJvdW5kZWRcIj5Nb2R1bGUgUmVxdWlyZWQ8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIG1iLTRcIj5BcHByb3ZlIEtZQyBhbmQgYWRkIHVzZXJzIHRvIHdoaXRlbGlzdCAocmVxdWlyZXMgbW9kdWxlIHJlZ2lzdHJhdGlvbik8L3A+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxsYWJlbCBodG1sRm9yPVwia3ljVXNlckFkZHJlc3NcIiBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDBcIj5Vc2VyIEFkZHJlc3M8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICBpZD1cImt5Y1VzZXJBZGRyZXNzXCJcbiAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCIweC4uLlwiXG4gICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2t5Y1VzZXJBZGRyZXNzfVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0S3ljVXNlckFkZHJlc3MoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm10LTEgYmxvY2sgdy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgc2hhZG93LXNtIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci1ibHVlLTUwMFwiXG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQXBwcm92ZUtZQ31cbiAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17YWN0aW9uTG9hZGluZyB8fCAha3ljVXNlckFkZHJlc3N9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctZ3JlZW4tNjAwIGhvdmVyOmJnLWdyZWVuLTcwMCB0ZXh0LXdoaXRlIGZvbnQtYm9sZCBweS0yIHB4LTQgcm91bmRlZCBkaXNhYmxlZDpvcGFjaXR5LTUwXCJcbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIHthY3Rpb25Mb2FkaW5nID8gJ/CflIQgJyA6ICfinIUgJ31BcHByb3ZlIEtZQ1xuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUFkZFRvV2hpdGVsaXN0fVxuICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXthY3Rpb25Mb2FkaW5nIHx8ICFreWNVc2VyQWRkcmVzc31cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ibHVlLTYwMCBob3ZlcjpiZy1ibHVlLTcwMCB0ZXh0LXdoaXRlIGZvbnQtYm9sZCBweS0yIHB4LTQgcm91bmRlZCBkaXNhYmxlZDpvcGFjaXR5LTUwXCJcbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIHthY3Rpb25Mb2FkaW5nID8gJ/CflIQgJyA6ICfwn5OLICd9QWRkIHRvIFdoaXRlbGlzdFxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93IHAtNlwiPlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgIEN1c3RvbSBDbGFpbXNcbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTIgdGV4dC14cyBiZy1ncmVlbi0xMDAgdGV4dC1ncmVlbi04MDAgcHgtMiBweS0xIHJvdW5kZWRcIj7inIUgV29ya2luZzwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbWItNFwiPklzc3VlIGN1c3RvbSBjbGFpbXMgd2l0aCBzcGVjaWZpYyBUb3BpYyBJRHMgKGZ1bGx5IG9wZXJhdGlvbmFsKTwvcD5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGh0bWxGb3I9XCJjbGFpbVVzZXJBZGRyZXNzXCIgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwXCI+VXNlciBBZGRyZXNzPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgaWQ9XCJjbGFpbVVzZXJBZGRyZXNzXCJcbiAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCIweC4uLlwiXG4gICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2NsYWltVXNlckFkZHJlc3N9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRDbGFpbVVzZXJBZGRyZXNzKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtdC0xIGJsb2NrIHctZnVsbCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIHNoYWRvdy1zbSBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy1ibHVlLTUwMCBmb2N1czpib3JkZXItYmx1ZS01MDBcIlxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8bGFiZWwgaHRtbEZvcj1cImNsYWltVG9waWNJZFwiIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMFwiPlRvcGljIElEPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPHNlbGVjdFxuICAgICAgICAgICAgICAgICAgICAgIGlkPVwiY2xhaW1Ub3BpY0lkXCJcbiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Y2xhaW1Ub3BpY0lkfVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Q2xhaW1Ub3BpY0lkKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtdC0xIGJsb2NrIHctZnVsbCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIHNoYWRvdy1zbSBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy1ibHVlLTUwMCBmb2N1czpib3JkZXItYmx1ZS01MDBcIlxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIjEwMTAxMDEwMDAwMDAxXCI+MTAxMDEwMTAwMDAwMDEgLSBLWUMgVmVyaWZpY2F0aW9uPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIjEwMTAxMDEwMDAwMDAyXCI+MTAxMDEwMTAwMDAwMDIgLSBBY2NyZWRpdGVkIEludmVzdG9yPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIjEwMTAxMDEwMDAwMDAzXCI+MTAxMDEwMTAwMDAwMDMgLSBKdXJpc2RpY3Rpb24gQ29tcGxpYW5jZTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCIxMDEwMTAxMDAwMDAwNFwiPjEwMTAxMDEwMDAwMDA0IC0gR2VuZXJhbCBRdWFsaWZpY2F0aW9uPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIjEwMTAxMDEwMDAwNjQ4XCI+MTAxMDEwMTAwMDA2NDggLSBDdXN0b20gS1lDIFN0YXR1czwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGh0bWxGb3I9XCJjbGFpbURhdGFcIiBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDBcIj5DbGFpbSBEYXRhIChPcHRpb25hbCk8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8dGV4dGFyZWFcbiAgICAgICAgICAgICAgICAgICAgICBpZD1cImNsYWltRGF0YVwiXG4gICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJBZGRpdGlvbmFsIGNsYWltIGluZm9ybWF0aW9uLi4uXCJcbiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Y2xhaW1EYXRhfVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Q2xhaW1EYXRhKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICByb3dzPXsyfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm10LTEgYmxvY2sgdy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgc2hhZG93LXNtIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci1ibHVlLTUwMFwiXG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlSXNzdWVDbGFpbX1cbiAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2FjdGlvbkxvYWRpbmcgfHwgIWNsYWltVXNlckFkZHJlc3MgfHwgIWNsYWltVG9waWNJZH1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJnLXB1cnBsZS02MDAgaG92ZXI6YmctcHVycGxlLTcwMCB0ZXh0LXdoaXRlIGZvbnQtYm9sZCBweS0yIHB4LTQgcm91bmRlZCBkaXNhYmxlZDpvcGFjaXR5LTUwXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAge2FjdGlvbkxvYWRpbmcgPyAn8J+UhCAnIDogJ/Cfj7fvuI8gJ31Jc3N1ZSBDdXN0b20gQ2xhaW1cbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93IHAtNlwiPlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSBtYi0yXCI+XG4gICAgICAgICAgICAgICAgVmVyaWZpY2F0aW9uIFN0YXR1cyBDaGVja2VyXG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibWwtMiB0ZXh0LXhzIGJnLWdyZWVuLTEwMCB0ZXh0LWdyZWVuLTgwMCBweC0yIHB5LTEgcm91bmRlZFwiPuKchSBXb3JraW5nPC9zcGFuPlxuICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIG1iLTRcIj5DaGVjayBLWUMgYW5kIGNsYWltcyBzdGF0dXMgZm9yIGFueSB1c2VyIChmdWxseSBvcGVyYXRpb25hbCk8L3A+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC00XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICA8bGFiZWwgaHRtbEZvcj1cImNoZWNrVXNlckFkZHJlc3NcIiBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDBcIj5Vc2VyIEFkZHJlc3M8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICBpZD1cImNoZWNrVXNlckFkZHJlc3NcIlxuICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIjB4Li4uXCJcbiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Y2hlY2tVc2VyQWRkcmVzc31cbiAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldENoZWNrVXNlckFkZHJlc3MoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm10LTEgYmxvY2sgdy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgc2hhZG93LXNtIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci1ibHVlLTUwMFwiXG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1lbmRcIj5cbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUNoZWNrU3RhdHVzfVxuICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXthY3Rpb25Mb2FkaW5nIHx8ICFjaGVja1VzZXJBZGRyZXNzfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdyYXktNjAwIGhvdmVyOmJnLWdyYXktNzAwIHRleHQtd2hpdGUgZm9udC1ib2xkIHB5LTIgcHgtNCByb3VuZGVkIGRpc2FibGVkOm9wYWNpdHktNTBcIlxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAge2FjdGlvbkxvYWRpbmcgPyAn8J+UhCAnIDogJ/CflI0gJ31DaGVjayBTdGF0dXNcbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIHsvKiBTdGF0dXMgUmVzdWx0cyAqL31cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJvcmRlciByb3VuZGVkLWxnIHAtNCBiZy1ncmF5LTUwXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICB7dmVyaWZpY2F0aW9uU3RhdHVzID8gKFxuICAgICAgICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJmb250LW1lZGl1bSBtYi0yXCI+VmVyaWZpY2F0aW9uIFN0YXR1cyBmb3Ige2NoZWNrVXNlckFkZHJlc3Muc2xpY2UoMCwgMTApfS4uLntjaGVja1VzZXJBZGRyZXNzLnNsaWNlKC04KX08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTIgc3BhY2UteS0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj5LWUMgQXBwcm92ZWQ6PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17dmVyaWZpY2F0aW9uU3RhdHVzLmt5Y0FwcHJvdmVkID8gJ3RleHQtZ3JlZW4tNjAwIGZvbnQtbWVkaXVtJyA6ICd0ZXh0LXJlZC02MDAnfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHt2ZXJpZmljYXRpb25TdGF0dXMua3ljQXBwcm92ZWQgPyAn4pyFIFllcycgOiAn4p2MIE5vJ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+V2hpdGVsaXN0ZWQ6PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17dmVyaWZpY2F0aW9uU3RhdHVzLndoaXRlbGlzdGVkID8gJ3RleHQtZ3JlZW4tNjAwIGZvbnQtbWVkaXVtJyA6ICd0ZXh0LXJlZC02MDAnfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHt2ZXJpZmljYXRpb25TdGF0dXMud2hpdGVsaXN0ZWQgPyAn4pyFIFllcycgOiAn4p2MIE5vJ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+RWxpZ2libGU6PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17dmVyaWZpY2F0aW9uU3RhdHVzLmVsaWdpYmxlID8gJ3RleHQtZ3JlZW4tNjAwIGZvbnQtbWVkaXVtJyA6ICd0ZXh0LXJlZC02MDAnfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHt2ZXJpZmljYXRpb25TdGF0dXMuZWxpZ2libGUgPyAn4pyFIFllcycgOiAn4p2MIE5vJ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+TWV0aG9kOjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNjAwIGZvbnQtbWVkaXVtXCI+e3ZlcmlmaWNhdGlvblN0YXR1cy5tZXRob2R9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cD5TdGF0dXMgcmVzdWx0cyB3aWxsIGFwcGVhciBoZXJlIGFmdGVyIGNoZWNraW5nIGEgdXNlciBhZGRyZXNzLjwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMiBzcGFjZS15LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPktZQyBBcHByb3ZlZDo8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMFwiPi08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+V2hpdGVsaXN0ZWQ6PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDBcIj4tPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPkVsaWdpYmxlOjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwXCI+LTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj5NZXRob2Q6PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDBcIj4tPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3cgcC02XCI+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIG1iLTJcIj5Ub2tlbiBDbGFpbXMgQ29uZmlndXJhdGlvbjwvaDM+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbWItNFwiPkNvbmZpZ3VyZSB3aGljaCBjbGFpbXMgYXJlIHJlcXVpcmVkIGZvciB0aGlzIHRva2VuPC9wPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGlucHV0IHR5cGU9XCJjaGVja2JveFwiIGNsYXNzTmFtZT1cInJvdW5kZWQgYm9yZGVyLWdyYXktMzAwIHRleHQtYmx1ZS02MDAgc2hhZG93LXNtIGZvY3VzOmJvcmRlci1ibHVlLTMwMCBmb2N1czpyaW5nIGZvY3VzOnJpbmctYmx1ZS0yMDAgZm9jdXM6cmluZy1vcGFjaXR5LTUwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC0yIHRleHQtc21cIj5FbmFibGUgS1lDIFZlcmlmaWNhdGlvbjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGlucHV0IHR5cGU9XCJjaGVja2JveFwiIGNsYXNzTmFtZT1cInJvdW5kZWQgYm9yZGVyLWdyYXktMzAwIHRleHQtYmx1ZS02MDAgc2hhZG93LXNtIGZvY3VzOmJvcmRlci1ibHVlLTMwMCBmb2N1czpyaW5nIGZvY3VzOnJpbmctYmx1ZS0yMDAgZm9jdXM6cmluZy1vcGFjaXR5LTUwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC0yIHRleHQtc21cIj5FbmFibGUgQ2xhaW1zIFN5c3RlbTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMlwiPlJlcXVpcmVkIENsYWltczwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8aW5wdXQgdHlwZT1cImNoZWNrYm94XCIgY2xhc3NOYW1lPVwicm91bmRlZCBib3JkZXItZ3JheS0zMDAgdGV4dC1ibHVlLTYwMCBzaGFkb3ctc20gZm9jdXM6Ym9yZGVyLWJsdWUtMzAwIGZvY3VzOnJpbmcgZm9jdXM6cmluZy1ibHVlLTIwMCBmb2N1czpyaW5nLW9wYWNpdHktNTBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTIgdGV4dC1zbVwiPktZQyBWZXJpZmljYXRpb24gKDEwMTAxMDEwMDAwMDAxKTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGlucHV0IHR5cGU9XCJjaGVja2JveFwiIGNsYXNzTmFtZT1cInJvdW5kZWQgYm9yZGVyLWdyYXktMzAwIHRleHQtYmx1ZS02MDAgc2hhZG93LXNtIGZvY3VzOmJvcmRlci1ibHVlLTMwMCBmb2N1czpyaW5nIGZvY3VzOnJpbmctYmx1ZS0yMDAgZm9jdXM6cmluZy1vcGFjaXR5LTUwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC0yIHRleHQtc21cIj5HZW5lcmFsIFF1YWxpZmljYXRpb24gKDEwMTAxMDEwMDAwMDA0KTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGlucHV0IHR5cGU9XCJjaGVja2JveFwiIGNsYXNzTmFtZT1cInJvdW5kZWQgYm9yZGVyLWdyYXktMzAwIHRleHQtYmx1ZS02MDAgc2hhZG93LXNtIGZvY3VzOmJvcmRlci1ibHVlLTMwMCBmb2N1czpyaW5nIGZvY3VzOnJpbmctYmx1ZS0yMDAgZm9jdXM6cmluZy1vcGFjaXR5LTUwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC0yIHRleHQtc21cIj5BY2NyZWRpdGVkIEludmVzdG9yICgxMDEwMTAxMDAwMDAwMik8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXthY3Rpb25Mb2FkaW5nfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJnLWJsdWUtNjAwIGhvdmVyOmJnLWJsdWUtNzAwIHRleHQtd2hpdGUgZm9udC1ib2xkIHB5LTIgcHgtNCByb3VuZGVkIGRpc2FibGVkOm9wYWNpdHktNTBcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIHthY3Rpb25Mb2FkaW5nID8gJ/CflIQgJyA6ICfimpnvuI8gJ31VcGRhdGUgVG9rZW4gQ29uZmlndXJhdGlvblxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuXG4gICAgICAgIHthY3RpdmVUYWIgPT09ICd1cGdyYWRlcycgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbGc6Z3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdyBwLTZcIj5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSBtYi0yXCI+U2NoZWR1bGUgVXBncmFkZTwvaDM+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBtYi00XCI+XG4gICAgICAgICAgICAgICAgICBTY2hlZHVsZSBhIG5ldyBpbXBsZW1lbnRhdGlvbiB1cGdyYWRlIHdpdGggNDgtaG91ciB0aW1lbG9ja1xuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGh0bWxGb3I9XCJuZXdJbXBsZW1lbnRhdGlvblwiIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMFwiPk5ldyBJbXBsZW1lbnRhdGlvbiBBZGRyZXNzPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgaWQ9XCJuZXdJbXBsZW1lbnRhdGlvblwiXG4gICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiMHguLi5cIlxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtuZXdJbXBsZW1lbnRhdGlvbkFkZHJlc3N9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXROZXdJbXBsZW1lbnRhdGlvbkFkZHJlc3MoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm10LTEgYmxvY2sgdy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgc2hhZG93LXNtIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci1ibHVlLTUwMFwiXG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxsYWJlbCBodG1sRm9yPVwidXBncmFkZURlc2NyaXB0aW9uXCIgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwXCI+VXBncmFkZSBEZXNjcmlwdGlvbjwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDx0ZXh0YXJlYVxuICAgICAgICAgICAgICAgICAgICAgIGlkPVwidXBncmFkZURlc2NyaXB0aW9uXCJcbiAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkRlc2NyaWJlIHRoZSBjaGFuZ2VzIGluIHRoaXMgdXBncmFkZS4uLlwiXG4gICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3VwZ3JhZGVEZXNjcmlwdGlvbn1cbiAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFVwZ3JhZGVEZXNjcmlwdGlvbihlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgcm93cz17M31cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtdC0xIGJsb2NrIHctZnVsbCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIHNoYWRvdy1zbSBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy1ibHVlLTUwMCBmb2N1czpib3JkZXItYmx1ZS01MDBcIlxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVNjaGVkdWxlVXBncmFkZX1cbiAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2FjdGlvbkxvYWRpbmcgfHwgIW5ld0ltcGxlbWVudGF0aW9uQWRkcmVzcyB8fCAhdXBncmFkZURlc2NyaXB0aW9ufVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctYmx1ZS02MDAgaG92ZXI6YmctYmx1ZS03MDAgdGV4dC13aGl0ZSBmb250LWJvbGQgcHktMiBweC00IHJvdW5kZWQgZGlzYWJsZWQ6b3BhY2l0eS01MFwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIHthY3Rpb25Mb2FkaW5nID8gJ/CflIQgJyA6ICfij7AgJ31cbiAgICAgICAgICAgICAgICAgICAgU2NoZWR1bGUgVXBncmFkZSAoNDhoIFRpbWVsb2NrKVxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3cgcC02XCI+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gbWItMlwiPlBlbmRpbmcgVXBncmFkZXM8L2gzPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbWItNFwiPlxuICAgICAgICAgICAgICAgICAgVXBncmFkZXMgd2FpdGluZyBmb3IgdGltZWxvY2sgZXhwaXJhdGlvbiBvciBleGVjdXRpb25cbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAge3BlbmRpbmdVcGdyYWRlcy5sZW5ndGggPT09IDAgPyAoXG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwIHRleHQtY2VudGVyIHB5LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgTm8gcGVuZGluZyB1cGdyYWRlc1xuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktM1wiPlxuICAgICAgICAgICAgICAgICAgICB7cGVuZGluZ1VwZ3JhZGVzLm1hcCgodXBncmFkZSkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXt1cGdyYWRlLnVwZ3JhZGVJZH0gY2xhc3NOYW1lPVwiYm9yZGVyIHJvdW5kZWQtbGcgcC0zIHNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1zdGFydFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dXBncmFkZS5kZXNjcmlwdGlvbn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YGlubGluZS1mbGV4IHB4LTIgcHktMSB0ZXh0LXhzIGZvbnQtc2VtaWJvbGQgcm91bmRlZC1mdWxsICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdXBncmFkZS5leGVjdXRlZCA/ICdiZy1ncmVlbi0xMDAgdGV4dC1ncmVlbi04MDAnIDpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB1cGdyYWRlLmNhbmNlbGxlZCA/ICdiZy1yZWQtMTAwIHRleHQtcmVkLTgwMCcgOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICdiZy15ZWxsb3ctMTAwIHRleHQteWVsbG93LTgwMCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHt1cGdyYWRlLmV4ZWN1dGVkID8gXCJFeGVjdXRlZFwiIDpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdXBncmFkZS5jYW5jZWxsZWQgPyBcIkNhbmNlbGxlZFwiIDpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXCJQZW5kaW5nXCJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDAgc3BhY2UteS0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+SW1wbGVtZW50YXRpb246IHt1cGdyYWRlLm5ld0ltcGxlbWVudGF0aW9uLnNsaWNlKDAsIDEwKX0uLi57dXBncmFkZS5uZXdJbXBsZW1lbnRhdGlvbi5zbGljZSgtOCl9PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+RXhlY3V0ZSBUaW1lOiB7bmV3IERhdGUodXBncmFkZS5leGVjdXRlVGltZSAqIDEwMDApLnRvTG9jYWxlU3RyaW5nKCl9PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+e2dldFRpbWVVbnRpbEV4ZWN1dGlvbih1cGdyYWRlLmV4ZWN1dGVUaW1lKX08L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG5cbiAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ2hpc3RvcnknICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdyBwLTZcIj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gbWItMlwiPlVwZ3JhZGUgSGlzdG9yeTwvaDM+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbWItNFwiPlxuICAgICAgICAgICAgICAgIENvbXBsZXRlIGhpc3Rvcnkgb2YgYWxsIHVwZ3JhZGVzIHBlcmZvcm1lZCBvbiB0aGlzIHRva2VuXG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAge3VwZ3JhZGVIaXN0b3J5Lmxlbmd0aCA9PT0gMCA/IChcbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwIHRleHQtY2VudGVyIHB5LTRcIj5cbiAgICAgICAgICAgICAgICAgIE5vIHVwZ3JhZGUgaGlzdG9yeSBhdmFpbGFibGVcbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgIHt1cGdyYWRlSGlzdG9yeS5tYXAoKHJlY29yZCwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJib3JkZXIgcm91bmRlZC1sZyBwLTQgc3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1zdGFydFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPntyZWNvcmQuZGVzY3JpcHRpb259PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge25ldyBEYXRlKHJlY29yZC50aW1lc3RhbXAgKiAxMDAwKS50b0xvY2FsZVN0cmluZygpfSBieSB7cmVjb3JkLmV4ZWN1dG9yLnNsaWNlKDAsIDEwKX0uLi57cmVjb3JkLmV4ZWN1dG9yLnNsaWNlKC04KX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2BpbmxpbmUtZmxleCBweC0yIHB5LTEgdGV4dC14cyBmb250LXNlbWlib2xkIHJvdW5kZWQtZnVsbCAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlY29yZC5pc0VtZXJnZW5jeSA/ICdiZy1yZWQtMTAwIHRleHQtcmVkLTgwMCcgOiAnYmctZ3JlZW4tMTAwIHRleHQtZ3JlZW4tODAwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge3JlY29yZC5pc0VtZXJnZW5jeSA/IFwiRW1lcmdlbmN5XCIgOiBcIlNjaGVkdWxlZFwifVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtyZWNvcmQudmVyc2lvbiAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggcHgtMiBweS0xIHRleHQteHMgZm9udC1zZW1pYm9sZCByb3VuZGVkLWZ1bGwgYmctZ3JheS0xMDAgdGV4dC1ncmF5LTgwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdntyZWNvcmQudmVyc2lvbn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMCBzcGFjZS15LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+RnJvbToge3JlY29yZC5vbGRJbXBsZW1lbnRhdGlvbi5zbGljZSgwLCAxMCl9Li4ue3JlY29yZC5vbGRJbXBsZW1lbnRhdGlvbi5zbGljZSgtOCl9PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlRvOiB7cmVjb3JkLm5ld0ltcGxlbWVudGF0aW9uLnNsaWNlKDAsIDEwKX0uLi57cmVjb3JkLm5ld0ltcGxlbWVudGF0aW9uLnNsaWNlKC04KX08L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZU1vZHVsYXJUb2tlbiIsIlNFQ1VSSVRZX1RPS0VOX0NPUkVfQUREUkVTUyIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19BTU9ZX1NFQ1VSSVRZX1RPS0VOX0NPUkVfQUREUkVTUyIsIlVQR1JBREVfTUFOQUdFUl9BRERSRVNTIiwiTkVYVF9QVUJMSUNfQU1PWV9VUEdSQURFX01BTkFHRVJfQUREUkVTUyIsIk1vZHVsYXJUb2tlbnNQYWdlIiwicHJvdmlkZXIiLCJzaWduZXIiLCJ0b2tlbkluZm8iLCJ1cGdyYWRlSW5mbyIsInBlbmRpbmdVcGdyYWRlcyIsInVwZ3JhZGVIaXN0b3J5IiwibG9hZGluZyIsImVycm9yIiwiaW5pdGlhbGl6ZVByb3ZpZGVyIiwibWludFRva2VucyIsInRvZ2dsZVBhdXNlIiwic2NoZWR1bGVVcGdyYWRlIiwidG9nZ2xlRW1lcmdlbmN5TW9kZSIsInJlZnJlc2hEYXRhIiwic2V0RXJyb3IiLCJzdWNjZXNzIiwic2V0U3VjY2VzcyIsIm1pbnRBbW91bnQiLCJzZXRNaW50QW1vdW50IiwibWludEFkZHJlc3MiLCJzZXRNaW50QWRkcmVzcyIsInVwZ3JhZGVEZXNjcmlwdGlvbiIsInNldFVwZ3JhZGVEZXNjcmlwdGlvbiIsIm5ld0ltcGxlbWVudGF0aW9uQWRkcmVzcyIsInNldE5ld0ltcGxlbWVudGF0aW9uQWRkcmVzcyIsImFjdGlvbkxvYWRpbmciLCJzZXRBY3Rpb25Mb2FkaW5nIiwiYWN0aXZlVGFiIiwic2V0QWN0aXZlVGFiIiwia3ljVXNlckFkZHJlc3MiLCJzZXRLeWNVc2VyQWRkcmVzcyIsImNsYWltVXNlckFkZHJlc3MiLCJzZXRDbGFpbVVzZXJBZGRyZXNzIiwiY2xhaW1Ub3BpY0lkIiwic2V0Q2xhaW1Ub3BpY0lkIiwiY2xhaW1EYXRhIiwic2V0Q2xhaW1EYXRhIiwiY2hlY2tVc2VyQWRkcmVzcyIsInNldENoZWNrVXNlckFkZHJlc3MiLCJ2ZXJpZmljYXRpb25TdGF0dXMiLCJzZXRWZXJpZmljYXRpb25TdGF0dXMiLCJ1c2VFZmZlY3QiLCJ0aW1lciIsInNldFRpbWVvdXQiLCJjbGVhclRpbWVvdXQiLCJoYW5kbGVNaW50IiwiY29uc29sZSIsIm1lc3NhZ2UiLCJoYW5kbGVQYXVzZVRvZ2dsZSIsInBhdXNlZCIsImhhbmRsZVNjaGVkdWxlVXBncmFkZSIsImhhbmRsZUVtZXJnZW5jeU1vZGVUb2dnbGUiLCJlbWVyZ2VuY3lNb2RlQWN0aXZlIiwiaGFuZGxlQXBwcm92ZUtZQyIsInJlc3BvbnNlIiwiZmV0Y2giLCJtZXRob2QiLCJoZWFkZXJzIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJ0b2tlbkFkZHJlc3MiLCJ1c2VyQWRkcmVzcyIsIm9rIiwiRXJyb3IiLCJoYW5kbGVBZGRUb1doaXRlbGlzdCIsImhhbmRsZUlzc3VlQ2xhaW0iLCJ0b3BpY0lkIiwiZGF0YSIsInJlc3VsdCIsImpzb24iLCJjbGFpbUlkIiwiaGFuZGxlQ2hlY2tTdGF0dXMiLCJzdGF0dXMiLCJmb3JtYXRBZGRyZXNzIiwiYWRkcmVzcyIsInNsaWNlIiwiZm9ybWF0VGltZXN0YW1wIiwidGltZXN0YW1wIiwiRGF0ZSIsInRvTG9jYWxlU3RyaW5nIiwiZ2V0VGltZVVudGlsRXhlY3V0aW9uIiwiZXhlY3V0ZVRpbWUiLCJub3ciLCJNYXRoIiwiZmxvb3IiLCJ0aW1lTGVmdCIsImhvdXJzIiwibWludXRlcyIsImRpdiIsImNsYXNzTmFtZSIsImgyIiwicCIsImJ1dHRvbiIsIm9uQ2xpY2siLCJoMSIsInNwYW4iLCJkaXNhYmxlZCIsIm5hdiIsIm1hcCIsInRhYiIsImNoYXJBdCIsInRvVXBwZXJDYXNlIiwicmVwbGFjZSIsImgzIiwibmFtZSIsInN5bWJvbCIsInZlcnNpb24iLCJ0b3RhbFN1cHBseSIsIm1heFN1cHBseSIsImRlY2ltYWxzIiwid2luZG93Iiwib3BlbiIsIm1ldGFkYXRhIiwidG9rZW5QcmljZSIsImJvbnVzVGllcnMiLCJ0b2tlbkRldGFpbHMiLCJsYWJlbCIsImh0bWxGb3IiLCJpbnB1dCIsImlkIiwidHlwZSIsInBsYWNlaG9sZGVyIiwidmFsdWUiLCJvbkNoYW5nZSIsImUiLCJ0YXJnZXQiLCJ1bCIsImxpIiwic3Ryb25nIiwic2VsZWN0Iiwib3B0aW9uIiwidGV4dGFyZWEiLCJyb3dzIiwia3ljQXBwcm92ZWQiLCJ3aGl0ZWxpc3RlZCIsImVsaWdpYmxlIiwibGVuZ3RoIiwidXBncmFkZSIsImRlc2NyaXB0aW9uIiwiZXhlY3V0ZWQiLCJjYW5jZWxsZWQiLCJuZXdJbXBsZW1lbnRhdGlvbiIsInVwZ3JhZGVJZCIsInJlY29yZCIsImluZGV4IiwiZXhlY3V0b3IiLCJpc0VtZXJnZW5jeSIsIm9sZEltcGxlbWVudGF0aW9uIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modular-tokens/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useModularToken.ts":
/*!**************************************!*\
  !*** ./src/hooks/useModularToken.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useModularToken: () => (/* binding */ useModularToken)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/crypto/keccak.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/utf8.js\");\n/* harmony import */ var _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contracts/SecurityTokenCore.json */ \"(app-pages-browser)/./src/contracts/SecurityTokenCore.json\");\n/* harmony import */ var _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contracts/UpgradeManager.json */ \"(app-pages-browser)/./src/contracts/UpgradeManager.json\");\n/* __next_internal_client_entry_do_not_use__ useModularToken auto */ \n\n// Import ABIs\n\n\n// Contract addresses from environment\nconst SECURITY_TOKEN_CORE_ADDRESS = \"******************************************\";\nconst UPGRADE_MANAGER_ADDRESS = \"******************************************\";\nfunction useModularToken() {\n    const [provider, setProvider] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [signer, setSigner] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [tokenInfo, setTokenInfo] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [upgradeInfo, setUpgradeInfo] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [pendingUpgrades, setPendingUpgrades] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [upgradeHistory, setUpgradeHistory] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // Initialize provider and signer\n    const initializeProvider = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[initializeProvider]\": async ()=>{\n            try {\n                if ( true && window.ethereum) {\n                    const provider = new ethers__WEBPACK_IMPORTED_MODULE_3__.BrowserProvider(window.ethereum);\n                    await provider.send('eth_requestAccounts', []);\n                    const signer = await provider.getSigner();\n                    setProvider(provider);\n                    setSigner(signer);\n                    return {\n                        provider,\n                        signer\n                    };\n                } else {\n                    throw new Error('MetaMask not found');\n                }\n            } catch (error) {\n                console.error('Error initializing provider:', error);\n                setError('Failed to connect to wallet');\n                return null;\n            }\n        }\n    }[\"useModularToken.useCallback[initializeProvider]\"], []);\n    // Load token information\n    const loadTokenInfo = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[loadTokenInfo]\": async ()=>{\n            if (!provider || !SECURITY_TOKEN_CORE_ADDRESS) return;\n            try {\n                setLoading(true);\n                const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_1__, provider);\n                const [name, symbol, version, totalSupply, maxSupply, decimals, paused, metadata] = await Promise.all([\n                    contract.name(),\n                    contract.symbol(),\n                    contract.version(),\n                    contract.totalSupply(),\n                    contract.maxSupply(),\n                    contract.decimals(),\n                    contract.paused(),\n                    contract.getTokenMetadata()\n                ]);\n                setTokenInfo({\n                    name,\n                    symbol,\n                    version,\n                    totalSupply: ethers__WEBPACK_IMPORTED_MODULE_5__.formatUnits(totalSupply, decimals),\n                    maxSupply: ethers__WEBPACK_IMPORTED_MODULE_5__.formatUnits(maxSupply, decimals),\n                    decimals,\n                    paused,\n                    metadata: {\n                        tokenPrice: metadata[0],\n                        bonusTiers: metadata[1],\n                        tokenDetails: metadata[2],\n                        tokenImageUrl: metadata[3]\n                    }\n                });\n            } catch (error) {\n                console.error('Error loading token info:', error);\n                setError('Failed to load token information');\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useModularToken.useCallback[loadTokenInfo]\"], [\n        provider\n    ]);\n    // Load upgrade information\n    const loadUpgradeInfo = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[loadUpgradeInfo]\": async ()=>{\n            if (!provider || !UPGRADE_MANAGER_ADDRESS) return;\n            try {\n                setLoading(true);\n                const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__, provider);\n                const [emergencyModeActive, registeredModules, upgradeDelay, emergencyModeDuration, pendingUpgradeIds] = await Promise.all([\n                    contract.isEmergencyModeActive(),\n                    contract.getRegisteredModules(),\n                    contract.UPGRADE_DELAY(),\n                    contract.EMERGENCY_MODE_DURATION(),\n                    contract.getPendingUpgradeIds()\n                ]);\n                setUpgradeInfo({\n                    emergencyModeActive,\n                    registeredModules,\n                    upgradeDelay: Number(upgradeDelay),\n                    emergencyModeDuration: Number(emergencyModeDuration)\n                });\n                // Load pending upgrades\n                const pendingUpgradesData = await Promise.all(pendingUpgradeIds.map({\n                    \"useModularToken.useCallback[loadUpgradeInfo]\": async (id)=>{\n                        const upgrade = await contract.pendingUpgrades(id);\n                        return {\n                            upgradeId: id,\n                            moduleId: upgrade.moduleId,\n                            proxy: upgrade.proxy,\n                            newImplementation: upgrade.newImplementation,\n                            executeTime: Number(upgrade.executeTime),\n                            executed: upgrade.executed,\n                            cancelled: upgrade.cancelled,\n                            description: upgrade.description\n                        };\n                    }\n                }[\"useModularToken.useCallback[loadUpgradeInfo]\"]));\n                setPendingUpgrades(pendingUpgradesData);\n                // Load upgrade history for SecurityTokenCore\n                const SECURITY_TOKEN_CORE_ID = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"SECURITY_TOKEN_CORE\"));\n                const history = await contract.getUpgradeHistory(SECURITY_TOKEN_CORE_ID);\n                setUpgradeHistory(history.map({\n                    \"useModularToken.useCallback[loadUpgradeInfo]\": (record)=>({\n                            oldImplementation: record.oldImplementation,\n                            newImplementation: record.newImplementation,\n                            timestamp: Number(record.timestamp),\n                            executor: record.executor,\n                            version: record.version,\n                            isEmergency: record.isEmergency,\n                            description: record.description\n                        })\n                }[\"useModularToken.useCallback[loadUpgradeInfo]\"]));\n            } catch (error) {\n                console.error('Error loading upgrade info:', error);\n                setError('Failed to load upgrade information');\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useModularToken.useCallback[loadUpgradeInfo]\"], [\n        provider\n    ]);\n    // Mint tokens\n    const mintTokens = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[mintTokens]\": async (address, amount)=>{\n            if (!signer || !SECURITY_TOKEN_CORE_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_1__, signer);\n            const decimals = (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals) || 0;\n            const amountWei = ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits(amount, decimals);\n            const tx = await contract.mint(address, amountWei);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[mintTokens]\"], [\n        signer,\n        tokenInfo\n    ]);\n    // Toggle pause state\n    const togglePause = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[togglePause]\": async ()=>{\n            if (!signer || !SECURITY_TOKEN_CORE_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_1__, signer);\n            const tx = (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? await contract.unpause() : await contract.pause();\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[togglePause]\"], [\n        signer,\n        tokenInfo\n    ]);\n    // Schedule upgrade\n    const scheduleUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[scheduleUpgrade]\": async (implementationAddress, description)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__, signer);\n            const SECURITY_TOKEN_CORE_ID = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"SECURITY_TOKEN_CORE\"));\n            const tx = await contract.scheduleUpgrade(SECURITY_TOKEN_CORE_ID, implementationAddress, description);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[scheduleUpgrade]\"], [\n        signer\n    ]);\n    // Execute upgrade\n    const executeUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[executeUpgrade]\": async (upgradeId)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__, signer);\n            const tx = await contract.executeUpgrade(upgradeId);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[executeUpgrade]\"], [\n        signer\n    ]);\n    // Emergency upgrade\n    const emergencyUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[emergencyUpgrade]\": async (implementationAddress, description)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__, signer);\n            const SECURITY_TOKEN_CORE_ID = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"SECURITY_TOKEN_CORE\"));\n            const tx = await contract.emergencyUpgrade(SECURITY_TOKEN_CORE_ID, implementationAddress, description);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[emergencyUpgrade]\"], [\n        signer\n    ]);\n    // Toggle emergency mode\n    const toggleEmergencyMode = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[toggleEmergencyMode]\": async ()=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__, signer);\n            const tx = (upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? await contract.deactivateEmergencyMode() : await contract.activateEmergencyMode();\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[toggleEmergencyMode]\"], [\n        signer,\n        upgradeInfo\n    ]);\n    // Cancel upgrade\n    const cancelUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[cancelUpgrade]\": async (upgradeId)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__, signer);\n            const tx = await contract.cancelUpgrade(upgradeId);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[cancelUpgrade]\"], [\n        signer\n    ]);\n    // Refresh all data\n    const refreshData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[refreshData]\": async ()=>{\n            await Promise.all([\n                loadTokenInfo(),\n                loadUpgradeInfo()\n            ]);\n        }\n    }[\"useModularToken.useCallback[refreshData]\"], [\n        loadTokenInfo,\n        loadUpgradeInfo\n    ]);\n    // Initialize on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useModularToken.useEffect\": ()=>{\n            initializeProvider();\n        }\n    }[\"useModularToken.useEffect\"], [\n        initializeProvider\n    ]);\n    // Load data when provider is ready\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useModularToken.useEffect\": ()=>{\n            if (provider && signer) {\n                refreshData();\n            }\n        }\n    }[\"useModularToken.useEffect\"], [\n        provider,\n        signer,\n        refreshData\n    ]);\n    return {\n        // State\n        provider,\n        signer,\n        tokenInfo,\n        upgradeInfo,\n        pendingUpgrades,\n        upgradeHistory,\n        loading,\n        error,\n        // Actions\n        initializeProvider,\n        loadTokenInfo,\n        loadUpgradeInfo,\n        mintTokens,\n        togglePause,\n        scheduleUpgrade,\n        executeUpgrade,\n        emergencyUpgrade,\n        toggleEmergencyMode,\n        cancelUpgrade,\n        refreshData,\n        // Utilities\n        setError,\n        setLoading\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useModularToken.ts\n"));

/***/ })

});