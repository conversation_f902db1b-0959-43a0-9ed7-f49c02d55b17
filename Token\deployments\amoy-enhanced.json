{"network": "amoy", "chainId": "80002", "factoryAddress": "0x46ee45ceDadBbA424365bD23dcc02AdF668345B8", "adminAddress": "0x56f3726C92B8B92a6ab71983886F91718540d888", "deploymentHash": "0x3109a4ab79f9d676256e4214a31becc8a34062b3271984d34649baa1e294fba7", "timestamp": "2025-06-21T04:31:22.353Z", "contractType": "SecurityTokenFactoryEnhanced", "architecture": "Enhanced", "securityLevel": "HIGH", "features": {"emergencyControls": true, "functionPausing": true, "enhancedReentrancyProtection": true, "improvedInputValidation": true, "roleBasedAccessControl": true, "agentManagement": true, "agreementTracking": true, "enumerationSupport": true, "gasOptimized": true, "sizeOptimized": true}, "securityAuditFixes": {"criticalIssues": "Fixed", "highPriorityIssues": "Fixed", "mediumPriorityIssues": "Fixed", "bestPractices": "Implemented"}}