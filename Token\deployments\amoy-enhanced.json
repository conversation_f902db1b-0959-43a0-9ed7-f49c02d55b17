{"network": "amoy", "chainId": "80002", "factoryAddress": "0x9BC856A01A192B8c7cf86251C837282EBaA2C4af", "adminAddress": "0x56f3726C92B8B92a6ab71983886F91718540d888", "deploymentHash": "0xa0c3b1169ac75c21e15a87267620811697fca9f185edb6f1e4c03c1dfaba1017", "timestamp": "2025-06-21T04:58:36.204Z", "contractType": "SecurityTokenFactoryEnhanced", "architecture": "Enhanced", "securityLevel": "HIGH", "features": {"emergencyControls": true, "functionPausing": true, "enhancedReentrancyProtection": true, "improvedInputValidation": true, "roleBasedAccessControl": true, "agentManagement": true, "agreementTracking": true, "enumerationSupport": true, "gasOptimized": true, "sizeOptimized": true}, "securityAuditFixes": {"criticalIssues": "Fixed", "highPriorityIssues": "Fixed", "mediumPriorityIssues": "Fixed", "bestPractices": "Implemented"}}