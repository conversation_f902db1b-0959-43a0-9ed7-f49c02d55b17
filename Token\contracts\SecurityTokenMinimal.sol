// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/access/AccessControl.sol";

/**
 * @title SecurityTokenMinimal
 * @dev Ultra-minimal security token with essential whitelist functionality
 * Optimized for size while maintaining core KYC and whitelist features
 */
contract SecurityTokenMinimal is ERC20, AccessControl {
    bytes32 public constant AGENT_ROLE = keccak256("AGENT_ROLE");
    
    uint8 private _decimals;
    uint256 public maxSupply;
    string public tokenPrice;
    string public bonusTiers;
    string public tokenImageUrl;
    
    // Core whitelist and KYC mappings
    mapping(address => bool) private _whitelisted;
    mapping(address => bool) private _kycApproved;
    
    address[] public agents;
    bool private _paused;
    
    event AddressWhitelisted(address indexed account);
    event AddressRemovedFromWhitelist(address indexed account);
    event KycApproved(address indexed account);
    event KycRevoked(address indexed account);
    
    modifier onlyAgent() { 
        require(hasRole(AGENT_ROLE, _msgSender()), "Not agent"); 
        _; 
    }
    
    modifier notPaused() { 
        require(!_paused, "Paused"); 
        _; 
    }
    
    modifier onlyWhitelisted(address account) {
        require(_whitelisted[account] && _kycApproved[account], "Not verified");
        _;
    }
    
    constructor(
        string memory name,
        string memory symbol,
        uint8 decimals_,
        uint256 maxSupply_,
        address admin,
        string memory tokenPrice_,
        string memory bonusTiers_,
        string memory tokenImageUrl_,
        address, // identityRegistry placeholder
        address  // compliance placeholder
    ) ERC20(name, symbol) {
        require(admin != address(0) && maxSupply_ > 0, "Invalid params");
        
        _decimals = decimals_;
        maxSupply = maxSupply_;
        tokenPrice = tokenPrice_;
        bonusTiers = bonusTiers_;
        tokenImageUrl = tokenImageUrl_;
        
        _grantRole(DEFAULT_ADMIN_ROLE, admin);
        _grantRole(AGENT_ROLE, admin);
        
        agents.push(admin);
        
        // Auto-whitelist and approve KYC for admin
        _whitelisted[admin] = true;
        _kycApproved[admin] = true;
        
        emit AddressWhitelisted(admin);
        emit KycApproved(admin);
    }
    
    function decimals() public view override returns (uint8) { 
        return _decimals; 
    }
    
    function version() external pure returns (string memory) { 
        return "5.0.0-minimal-whitelist"; 
    }
    
    // Essential whitelist functions
    function isWhitelisted(address account) external view returns (bool) {
        return _whitelisted[account];
    }
    
    function isKycApproved(address account) external view returns (bool) {
        return _kycApproved[account];
    }
    
    function isVerified(address account) external view returns (bool) {
        return _whitelisted[account] && _kycApproved[account];
    }
    
    function updateWhitelist(address account, bool status) external onlyAgent {
        _whitelisted[account] = status;
        if (status) {
            emit AddressWhitelisted(account);
        } else {
            emit AddressRemovedFromWhitelist(account);
        }
    }
    
    function batchUpdateWhitelist(address[] calldata accounts, bool[] calldata statuses) external onlyAgent {
        require(accounts.length == statuses.length && accounts.length <= 20, "Invalid batch");
        
        for (uint256 i = 0; i < accounts.length; i++) {
            _whitelisted[accounts[i]] = statuses[i];
            if (statuses[i]) {
                emit AddressWhitelisted(accounts[i]);
            } else {
                emit AddressRemovedFromWhitelist(accounts[i]);
            }
        }
    }
    
    function approveKyc(address account) external onlyAgent {
        _kycApproved[account] = true;
        emit KycApproved(account);
    }
    
    function revokeKyc(address account) external onlyAgent {
        _kycApproved[account] = false;
        emit KycRevoked(account);
    }
    
    // Legacy compatibility functions
    function addToWhitelist(address account) external onlyAgent {
        _whitelisted[account] = true;
        emit AddressWhitelisted(account);
    }
    
    function removeFromWhitelist(address account) external onlyAgent {
        _whitelisted[account] = false;
        emit AddressRemovedFromWhitelist(account);
    }
    
    function batchAddToWhitelist(address[] calldata accounts) external onlyAgent {
        require(accounts.length <= 20, "Batch too large");
        for (uint256 i = 0; i < accounts.length; i++) {
            _whitelisted[accounts[i]] = true;
            emit AddressWhitelisted(accounts[i]);
        }
    }
    
    // Token functions with KYC checks
    function mint(address to, uint256 amount) external onlyAgent notPaused onlyWhitelisted(to) {
        require(totalSupply() + amount <= maxSupply, "Exceeds max supply");
        _mint(to, amount);
    }
    
    function transfer(address to, uint256 amount) public override notPaused onlyWhitelisted(to) returns (bool) {
        require(_whitelisted[_msgSender()] && _kycApproved[_msgSender()], "Sender not verified");
        return super.transfer(to, amount);
    }
    
    function transferFrom(address from, address to, uint256 amount) public override notPaused onlyWhitelisted(from) onlyWhitelisted(to) returns (bool) {
        return super.transferFrom(from, to, amount);
    }
    
    // Minimal agent management
    function addAgent(address agent) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(agent != address(0) && agents.length < 10, "Invalid agent");
        _grantRole(AGENT_ROLE, agent);
        agents.push(agent);
    }
    
    function removeAgent(address agent) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(agents.length > 1, "Cannot remove last agent");
        _revokeRole(AGENT_ROLE, agent);
        for (uint256 i = 0; i < agents.length; i++) {
            if (agents[i] == agent) {
                agents[i] = agents[agents.length - 1];
                agents.pop();
                break;
            }
        }
    }
    
    // Emergency controls
    function pause() external onlyRole(DEFAULT_ADMIN_ROLE) {
        _paused = true;
    }
    
    function unpause() external onlyRole(DEFAULT_ADMIN_ROLE) {
        _paused = false;
    }
    
    // View functions
    function getAllAgents() external view returns (address[] memory) { 
        return agents; 
    }
    
    function getAgentCount() external view returns (uint256) { 
        return agents.length; 
    }
    
    function isPaused() external view returns (bool) { 
        return _paused; 
    }
    
    // Placeholder for identityRegistry compatibility
    function identityRegistry() external pure returns (address) {
        return address(0);
    }
}
