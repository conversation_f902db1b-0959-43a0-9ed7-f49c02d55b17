"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_utils_whitelist_ts";
exports.ids = ["_rsc_src_utils_whitelist_ts"];
exports.modules = {

/***/ "(rsc)/./src/contracts/Whitelist.json":
/*!**************************************!*\
  !*** ./src/contracts/Whitelist.json ***!
  \**************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"abi":[{"inputs":[],"stateMutability":"nonpayable","type":"constructor"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"account","type":"address"}],"name":"AddedToWhitelist","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"account","type":"address"}],"name":"AddressFrozen","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"account","type":"address"}],"name":"AddressUnfrozen","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"uint8","name":"version","type":"uint8"}],"name":"Initialized","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"account","type":"address"}],"name":"KycApproved","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"account","type":"address"}],"name":"KycRevoked","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"account","type":"address"}],"name":"RemovedFromWhitelist","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"bytes32","name":"previousAdminRole","type":"bytes32"},{"indexed":true,"internalType":"bytes32","name":"newAdminRole","type":"bytes32"}],"name":"RoleAdminChanged","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":true,"internalType":"address","name":"sender","type":"address"}],"name":"RoleGranted","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":true,"internalType":"address","name":"sender","type":"address"}],"name":"RoleRevoked","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"implementation","type":"address"}],"name":"Upgraded","type":"event"},{"inputs":[],"name":"AGENT_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"DEFAULT_ADMIN_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"_agent","type":"address"}],"name":"addAgent","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"_address","type":"address"}],"name":"addToWhitelist","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address[]","name":"_addresses","type":"address[]"}],"name":"batchAddToWhitelist","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address[]","name":"_addresses","type":"address[]"}],"name":"batchFreezeAddresses","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address[]","name":"_addresses","type":"address[]"}],"name":"batchRemoveFromWhitelist","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address[]","name":"_addresses","type":"address[]"}],"name":"batchUnfreezeAddresses","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"_address","type":"address"}],"name":"freezeAddress","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"}],"name":"getRoleAdmin","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"grantRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"hasRole","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"admin","type":"address"}],"name":"initialize","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"_admin","type":"address"}],"name":"initializeWithAgent","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"_address","type":"address"}],"name":"isFrozen","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"_address","type":"address"}],"name":"isWhitelisted","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"proxiableUUID","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"_agent","type":"address"}],"name":"removeAgent","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"_address","type":"address"}],"name":"removeFromWhitelist","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"renounceRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"revokeRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes4","name":"interfaceId","type":"bytes4"}],"name":"supportsInterface","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"_address","type":"address"}],"name":"unfreezeAddress","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"newImplementation","type":"address"}],"name":"upgradeTo","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"newImplementation","type":"address"},{"internalType":"bytes","name":"data","type":"bytes"}],"name":"upgradeToAndCall","outputs":[],"stateMutability":"payable","type":"function"},{"inputs":[{"internalType":"address","name":"_address","type":"address"}],"name":"approveKyc","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address[]","name":"_addresses","type":"address[]"}],"name":"batchApproveKyc","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address[]","name":"_addresses","type":"address[]"}],"name":"batchRevokeKyc","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"_address","type":"address"}],"name":"isKycApproved","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"_address","type":"address"}],"name":"revokeKyc","outputs":[],"stateMutability":"nonpayable","type":"function"}]}');

/***/ }),

/***/ "(rsc)/./src/utils/whitelist.ts":
/*!********************************!*\
  !*** ./src/utils/whitelist.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addToWhitelist: () => (/* binding */ addToWhitelist),\n/* harmony export */   approveKyc: () => (/* binding */ approveKyc),\n/* harmony export */   batchAddToWhitelist: () => (/* binding */ batchAddToWhitelist),\n/* harmony export */   batchApproveKyc: () => (/* binding */ batchApproveKyc),\n/* harmony export */   batchFreezeAddresses: () => (/* binding */ batchFreezeAddresses),\n/* harmony export */   batchRemoveFromWhitelist: () => (/* binding */ batchRemoveFromWhitelist),\n/* harmony export */   batchRevokeKyc: () => (/* binding */ batchRevokeKyc),\n/* harmony export */   batchUnfreezeAddresses: () => (/* binding */ batchUnfreezeAddresses),\n/* harmony export */   freezeAddress: () => (/* binding */ freezeAddress),\n/* harmony export */   isFrozen: () => (/* binding */ isFrozen),\n/* harmony export */   isKycApproved: () => (/* binding */ isKycApproved),\n/* harmony export */   isWhitelisted: () => (/* binding */ isWhitelisted),\n/* harmony export */   removeFromWhitelist: () => (/* binding */ removeFromWhitelist),\n/* harmony export */   revokeKyc: () => (/* binding */ revokeKyc),\n/* harmony export */   unfreezeAddress: () => (/* binding */ unfreezeAddress)\n/* harmony export */ });\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/providers/provider-jsonrpc.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var _contracts_Whitelist_json__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../contracts/Whitelist.json */ \"(rsc)/./src/contracts/Whitelist.json\");\n\n\n/**\r\n * Whitelist utility functions for interacting with the Whitelist contract\r\n */ // Map of network names to their RPC URLs\nconst RPC_URLS = {\n    amoy: 'https://rpc-amoy.polygon.technology/',\n    polygon: 'https://polygon-rpc.com',\n    unknown: 'https://rpc-amoy.polygon.technology/' // Default unknown networks to Amoy\n};\n/**\r\n * Get the actual network to use, defaults to Amoy for 'unknown' or invalid networks\r\n */ function getActualNetwork(network) {\n    return network === 'unknown' || !RPC_URLS[network] ? 'amoy' : network;\n}\n/**\r\n * Add an address to the whitelist\r\n * @param whitelistAddress The address of the whitelist contract\r\n * @param addressToWhitelist The address to be whitelisted\r\n * @param network The network to use (amoy, polygon)\r\n * @returns Transaction result object\r\n */ async function addToWhitelist(whitelistAddress, addressToWhitelist, network = 'amoy') {\n    const actualNetwork = getActualNetwork(network);\n    return callWhitelistAPI({\n        whitelistAddress,\n        action: 'addToWhitelist',\n        address: addressToWhitelist,\n        network: actualNetwork\n    });\n}\n/**\r\n * Add multiple addresses to the whitelist in a batch\r\n * @param whitelistAddress The address of the whitelist contract\r\n * @param addressesToWhitelist Array of addresses to be whitelisted\r\n * @param network The network to use (amoy, polygon)\r\n * @returns Transaction result object\r\n */ async function batchAddToWhitelist(whitelistAddress, addressesToWhitelist, network = 'amoy') {\n    const actualNetwork = getActualNetwork(network);\n    return callWhitelistAPI({\n        whitelistAddress,\n        action: 'batchAddToWhitelist',\n        addresses: addressesToWhitelist,\n        network: actualNetwork\n    });\n}\n/**\r\n * Remove an address from the whitelist\r\n * @param whitelistAddress The address of the whitelist contract\r\n * @param addressToRemove The address to be removed from whitelist\r\n * @param network The network to use (amoy, polygon)\r\n * @returns Transaction result object\r\n */ async function removeFromWhitelist(whitelistAddress, addressToRemove, network = 'amoy') {\n    const actualNetwork = getActualNetwork(network);\n    return callWhitelistAPI({\n        whitelistAddress,\n        action: 'removeFromWhitelist',\n        address: addressToRemove,\n        network: actualNetwork\n    });\n}\n/**\r\n * Remove multiple addresses from the whitelist in a batch\r\n * @param whitelistAddress The address of the whitelist contract\r\n * @param addressesToRemove Array of addresses to be removed\r\n * @param network The network to use (amoy, polygon)\r\n * @returns Transaction result object\r\n */ async function batchRemoveFromWhitelist(whitelistAddress, addressesToRemove, network = 'amoy') {\n    const actualNetwork = getActualNetwork(network);\n    return callWhitelistAPI({\n        whitelistAddress,\n        action: 'batchRemoveFromWhitelist',\n        addresses: addressesToRemove,\n        network: actualNetwork\n    });\n}\n/**\r\n * Freeze an address\r\n * @param whitelistAddress The address of the whitelist contract\r\n * @param addressToFreeze The address to be frozen\r\n * @param network The network to use (amoy, polygon)\r\n * @returns Transaction result object\r\n */ async function freezeAddress(whitelistAddress, addressToFreeze, network = 'amoy') {\n    const actualNetwork = getActualNetwork(network);\n    return callWhitelistAPI({\n        whitelistAddress,\n        action: 'freezeAddress',\n        address: addressToFreeze,\n        network: actualNetwork\n    });\n}\n/**\r\n * Unfreeze an address\r\n * @param whitelistAddress The address of the whitelist contract\r\n * @param addressToUnfreeze The address to be unfrozen\r\n * @param network The network to use (amoy, polygon)\r\n * @returns Transaction result object\r\n */ async function unfreezeAddress(whitelistAddress, addressToUnfreeze, network = 'amoy') {\n    const actualNetwork = getActualNetwork(network);\n    return callWhitelistAPI({\n        whitelistAddress,\n        action: 'unfreezeAddress',\n        address: addressToUnfreeze,\n        network: actualNetwork\n    });\n}\n/**\r\n * Freeze multiple addresses in a batch\r\n * @param whitelistAddress The address of the whitelist contract\r\n * @param addressesToFreeze Array of addresses to be frozen\r\n * @param network The network to use (amoy, polygon)\r\n * @returns Transaction result object\r\n */ async function batchFreezeAddresses(whitelistAddress, addressesToFreeze, network = 'amoy') {\n    const actualNetwork = getActualNetwork(network);\n    return callWhitelistAPI({\n        whitelistAddress,\n        action: 'batchFreezeAddresses',\n        addresses: addressesToFreeze,\n        network: actualNetwork\n    });\n}\n/**\r\n * Unfreeze multiple addresses in a batch\r\n * @param whitelistAddress The address of the whitelist contract\r\n * @param addressesToUnfreeze Array of addresses to be unfrozen\r\n * @param network The network to use (amoy, polygon)\r\n * @returns Transaction result object\r\n */ async function batchUnfreezeAddresses(whitelistAddress, addressesToUnfreeze, network = 'amoy') {\n    const actualNetwork = getActualNetwork(network);\n    return callWhitelistAPI({\n        whitelistAddress,\n        action: 'batchUnfreezeAddresses',\n        addresses: addressesToUnfreeze,\n        network: actualNetwork\n    });\n}\n/**\r\n * Check if an address is whitelisted (view function)\r\n * @param whitelistAddress The address of the whitelist contract\r\n * @param addressToCheck The address to check whitelist status\r\n * @param network The network to use (amoy, polygon)\r\n * @returns Promise resolving to boolean indicating whitelist status\r\n */ async function isWhitelisted(whitelistAddress, addressToCheck, network = 'amoy') {\n    const actualNetwork = getActualNetwork(network);\n    try {\n        const result = await callWhitelistAPI({\n            whitelistAddress,\n            action: 'isWhitelisted',\n            address: addressToCheck,\n            network: actualNetwork\n        });\n        return result.isWhitelisted;\n    } catch (error) {\n        console.error('Error checking whitelist status:', error);\n        // Fallback to direct contract call if API fails\n        return isWhitelistedDirectCall(whitelistAddress, addressToCheck, actualNetwork);\n    }\n}\n/**\r\n * Check if an address is frozen (view function)\r\n * @param whitelistAddress The address of the whitelist contract\r\n * @param addressToCheck The address to check frozen status\r\n * @param network The network to use (amoy, polygon)\r\n * @returns Promise resolving to boolean indicating frozen status\r\n */ async function isFrozen(whitelistAddress, addressToCheck, network = 'amoy') {\n    const actualNetwork = getActualNetwork(network);\n    try {\n        const result = await callWhitelistAPI({\n            whitelistAddress,\n            action: 'isFrozen',\n            address: addressToCheck,\n            network: actualNetwork\n        });\n        return result.isFrozen;\n    } catch (error) {\n        console.error('Error checking frozen status:', error);\n        // Fallback to direct contract call if API fails\n        return isFrozenDirectCall(whitelistAddress, addressToCheck, actualNetwork);\n    }\n}\n/**\r\n * Check if an address is KYC approved (view function)\r\n * @param whitelistAddress The address of the whitelist contract\r\n * @param addressToCheck The address to check KYC approval status\r\n * @param network The network to use (amoy, polygon)\r\n * @returns Promise resolving to boolean indicating KYC approval status\r\n */ async function isKycApproved(whitelistAddress, addressToCheck, network = 'amoy') {\n    const actualNetwork = getActualNetwork(network);\n    try {\n        const result = await callWhitelistAPI({\n            whitelistAddress,\n            action: 'isKycApproved',\n            address: addressToCheck,\n            network: actualNetwork\n        });\n        return result.isKycApproved;\n    } catch (error) {\n        console.error('Error checking KYC approval status:', error);\n        // Fallback to direct contract call if API fails\n        return isKycApprovedDirectCall(whitelistAddress, addressToCheck, actualNetwork);\n    }\n}\n/**\r\n * Approve KYC for an address\r\n * @param whitelistAddress The address of the whitelist contract\r\n * @param addressToApprove The address to approve KYC for\r\n * @param network The network to use (amoy, polygon)\r\n * @returns Transaction result object\r\n */ async function approveKyc(whitelistAddress, addressToApprove, network = 'amoy') {\n    const actualNetwork = getActualNetwork(network);\n    return callWhitelistAPI({\n        whitelistAddress,\n        action: 'approveKyc',\n        address: addressToApprove,\n        network: actualNetwork\n    });\n}\n/**\r\n * Revoke KYC approval for an address\r\n * @param whitelistAddress The address of the whitelist contract\r\n * @param addressToRevoke The address to revoke KYC approval from\r\n * @param network The network to use (amoy, polygon)\r\n * @returns Transaction result object\r\n */ async function revokeKyc(whitelistAddress, addressToRevoke, network = 'amoy') {\n    const actualNetwork = getActualNetwork(network);\n    return callWhitelistAPI({\n        whitelistAddress,\n        action: 'revokeKyc',\n        address: addressToRevoke,\n        network: actualNetwork\n    });\n}\n/**\r\n * Batch approve KYC for multiple addresses\r\n * @param whitelistAddress The address of the whitelist contract\r\n * @param addressesToApprove Array of addresses to approve KYC for\r\n * @param network The network to use (amoy, polygon)\r\n * @returns Transaction result object\r\n */ async function batchApproveKyc(whitelistAddress, addressesToApprove, network = 'amoy') {\n    const actualNetwork = getActualNetwork(network);\n    return callWhitelistAPI({\n        whitelistAddress,\n        action: 'batchApproveKyc',\n        addresses: addressesToApprove,\n        network: actualNetwork\n    });\n}\n/**\r\n * Batch revoke KYC approval for multiple addresses\r\n * @param whitelistAddress The address of the whitelist contract\r\n * @param addressesToRevoke Array of addresses to revoke KYC approval from\r\n * @param network The network to use (amoy, polygon)\r\n * @returns Transaction result object\r\n */ async function batchRevokeKyc(whitelistAddress, addressesToRevoke, network = 'amoy') {\n    const actualNetwork = getActualNetwork(network);\n    return callWhitelistAPI({\n        whitelistAddress,\n        action: 'batchRevokeKyc',\n        addresses: addressesToRevoke,\n        network: actualNetwork\n    });\n}\n// Private helper functions\n/**\r\n * Call the whitelist API\r\n * @param params Object containing API parameters\r\n * @returns Promise resolving to API response\r\n */ async function callWhitelistAPI(params) {\n    // Use full URL when running on server-side (Node.js environment)\n    const baseUrl =  true ? 'http://localhost:6677' : 0;\n    const apiUrl = `${baseUrl}/api/contracts/whitelist/direct`;\n    const response = await fetch(apiUrl, {\n        method: 'POST',\n        headers: {\n            'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(params)\n    });\n    const data = await response.json();\n    if (!response.ok) {\n        throw new Error(data.error || 'API call failed');\n    }\n    return data;\n}\n/**\r\n * Direct contract call fallback for isWhitelisted\r\n */ async function isWhitelistedDirectCall(whitelistAddress, addressToCheck, network = 'amoy') {\n    try {\n        // Get RPC URL for the specified network\n        const rpcUrl = RPC_URLS[network] || RPC_URLS.amoy;\n        // Set up provider\n        const provider = new ethers__WEBPACK_IMPORTED_MODULE_1__.JsonRpcProvider(rpcUrl);\n        // Connect to contract\n        const whitelistContract = new ethers__WEBPACK_IMPORTED_MODULE_2__.Contract(whitelistAddress, _contracts_Whitelist_json__WEBPACK_IMPORTED_MODULE_0__.abi, provider);\n        // Call view function\n        return await whitelistContract.isWhitelisted(addressToCheck);\n    } catch (error) {\n        console.error('Error in direct contract call for isWhitelisted:', error);\n        return false; // Default to false on error\n    }\n}\n/**\r\n * Direct contract call fallback for isFrozen\r\n */ async function isFrozenDirectCall(whitelistAddress, addressToCheck, network = 'amoy') {\n    try {\n        // Get RPC URL for the specified network\n        const rpcUrl = RPC_URLS[network] || RPC_URLS.amoy;\n        // Set up provider\n        const provider = new ethers__WEBPACK_IMPORTED_MODULE_1__.JsonRpcProvider(rpcUrl);\n        // Connect to contract\n        const whitelistContract = new ethers__WEBPACK_IMPORTED_MODULE_2__.Contract(whitelistAddress, _contracts_Whitelist_json__WEBPACK_IMPORTED_MODULE_0__.abi, provider);\n        // Call view function\n        return await whitelistContract.isFrozen(addressToCheck);\n    } catch (error) {\n        console.error('Error in direct contract call for isFrozen:', error);\n        return false; // Default to false on error\n    }\n}\n/**\r\n * Direct contract call fallback for isKycApproved\r\n */ async function isKycApprovedDirectCall(whitelistAddress, addressToCheck, network = 'amoy') {\n    try {\n        // Get RPC URL for the specified network\n        const rpcUrl = RPC_URLS[network] || RPC_URLS.amoy;\n        // Set up provider\n        const provider = new ethers__WEBPACK_IMPORTED_MODULE_1__.JsonRpcProvider(rpcUrl);\n        // Connect to contract\n        const whitelistContract = new ethers__WEBPACK_IMPORTED_MODULE_2__.Contract(whitelistAddress, _contracts_Whitelist_json__WEBPACK_IMPORTED_MODULE_0__.abi, provider);\n        // Call isKycApproved function\n        const result = await whitelistContract.isKycApproved(addressToCheck);\n        return result;\n    } catch (error) {\n        console.error('Error in direct contract call for isKycApproved:', error);\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/whitelist.ts\n");

/***/ })

};
;