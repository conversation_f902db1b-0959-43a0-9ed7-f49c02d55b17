{"_format": "hh-sol-artifact-1", "contractName": "SecurityTokenOptimized", "sourceName": "contracts/SecurityTokenOptimized.sol", "abi": [{"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientAllowance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "name": "ERC20InvalidApprover", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "name": "ERC20InvalidReceiver", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "ERC20InvalidSender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "name": "ERC20InvalidSpender", "type": "error"}, {"inputs": [], "name": "InvalidInitialization", "type": "error"}, {"inputs": [], "name": "NotInitializing", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "agent", "type": "address"}], "name": "AgentAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "agent", "type": "address"}], "name": "AgentRemoved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "AgreementAccepted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "claimId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}], "name": "ClaimGenerated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "admin", "type": "address"}], "name": "EmergencyPaused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "admin", "type": "address"}], "name": "EmergencyUnpaused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "version", "type": "uint64"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "TokensFrozen", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "TokensUnfrozen", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "fee", "type": "uint256"}], "name": "TransferFeeCollected", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "whitelisted", "type": "bool"}], "name": "WhitelistUpdated", "type": "event"}, {"inputs": [], "name": "AGENT_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "TRANSFER_MANAGER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "acceptAgreement", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "name": "accountClaims", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "actionCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "agent", "type": "address"}], "name": "addAgent", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "agents", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "availableBalanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "recipients", "type": "address[]"}, {"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}], "name": "batchMint", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "accounts", "type": "address[]"}, {"internalType": "bool[]", "name": "statuses", "type": "bool[]"}], "name": "batchUp<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "bonusTiers", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "compliance", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "customFeePercentages", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "disableTransferFees", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "emergencyPause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "emergencyUnpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "feePercentage", "type": "uint256"}, {"internalType": "address", "name": "collector", "type": "address"}], "name": "enableTransferFees", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "feeCollector", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "feeExemptAddresses", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "forcedTransfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "freezeTokens", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "frozenBalances", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "generateClaimId", "outputs": [{"internalType": "uint256", "name": "claimId", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "getAccountClaims", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "index", "type": "uint256"}], "name": "getAgentAt", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getAgentCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "getAgreementAcceptanceTimestamp", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getAllAgents", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "hasAcceptedAgreement", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "identityRegistry", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint8", "name": "decimals_", "type": "uint8"}, {"internalType": "uint256", "name": "maxSupply_", "type": "uint256"}, {"internalType": "address", "name": "identityRegistry_", "type": "address"}, {"internalType": "address", "name": "compliance_", "type": "address"}, {"internalType": "address", "name": "admin", "type": "address"}, {"internalType": "string", "name": "tokenPrice_", "type": "string"}, {"internalType": "string", "name": "bonusTiers_", "type": "string"}, {"internalType": "string", "name": "tokenImageUrl_", "type": "string"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "isAgent", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "isEmergencyPaused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "functionSelector", "type": "bytes4"}], "name": "isFunctionPaused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "lastActionTimestamp", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "maxSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "mint", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "nextClaimId", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "functionSelector", "type": "bytes4"}], "name": "pauseFunction", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "agent", "type": "address"}], "name": "removeAgent", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "securityLevel", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "feePercentage", "type": "uint256"}], "name": "setCustomFee", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bool", "name": "exempt", "type": "bool"}], "name": "setFeeExemption", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "level", "type": "uint256"}], "name": "setSecurityLevel", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "tokenImageUrl", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "tokenPrice", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalFeesCollected", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalFrozenSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "transferFeePercentage", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "transferFeesEnabled", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "unfreezeTokens", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "functionSelector", "type": "bytes4"}], "name": "unpauseFunction", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bool", "name": "whitelisted", "type": "bool"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "usedClaimIds", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "version", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "pure", "type": "function"}], "bytecode": "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", "deployedBytecode": "0x608080604052600436101561001357600080fd5b6000358060e01c91826301ffc9a714612fd65750816305b57d8914612f9157816306fdde0314612ef4578163095ea7b314612e735781630ad9e7bf14612e395781630d392cd914612d915781630fd360ef14612d52578163134e18f414612d29578163148ea1a414612cef57816318160ddd14612cc45781631ffbb06414612c8557816322459e1814612c5c57816323b872dd14612b1e578163248a9ca314612ae257816325d998bb14612ab7578163290d10c414612a945781632dd1e5f814612a4c5781632f2ff15d14612a00578163313ce567146129df57816336568abe14612998578163380e9ae71461290a57816339bea75e146128e15781633af32abf146128a25781633e420e181461270957816340c10f191461244e578163411fe38e1461239a5781634a4e3bd51461234f5781634aeb5c09146122c15781634c532de11461227f578163513856c81461225657816351858e271461220857816354fd4d50146121ae57816358afbfff1461219057816359978f52146121725781635d2481061461213857816360c6d8ae1461211a5781636290865d146120f15781636857310714611e575781636a0f20d7146114555781636f1d90cf1461141957816370a08231146113d2578163751fd1791461138957816375524bfa146113185781637ff9b5961461126957816384e798421461118e578163860e4ac6146110e35781638f24ea9814610f4c57816391cab63e14610f2e57816391d1485414610ed4578163934592e714610e2057816395d89b4114610d5057816397a6278e14610b9d5781639d11aaaa14610b7f5781639fc1d0e714610a5e578163a0021cf114610a40578163a217fddf14610a24578163a4df6c6a146108c0578163a9059cbb1461081b578163ac64c48c146107ea578163c29ae253146107b0578163c415b95c14610787578163cc51fca71461074d578163d49466a8146106d1578163d547741f14610683578163d589c69114610649578163d5abeb011461062b578163dac96d4a14610591578163dd62ed3e14610548578163e468619d14610511578163e5e55444146104b9578163e9b2f0ad14610361575063effb3c531461033957600080fd5b3461035c57600036600319011261035c57602060ff601254166040519015158152f35b600080fd5b3461035c57604036600319011261035c5761037a613072565b336000908152600080516020613d45833981519152602090815260409091205460243593919291906103ae9060ff166132af565b60ff600e54161580610496575b6103c59150613325565b6001600160a01b0316918215158061048d575b6103e190613458565b82600052600a8252806040600020541061044a57907f9bed35cb62ad0dba04f9d5bfee4b5bc91443e77da8a65c4c84834c51bb08b0d69183600052600a825260406000206104308282546133e4565b905561043e816010546133e4565b601055604051908152a2005b60405162461bcd60e51b815260048101839052601b60248201527a496e73756666696369656e742066726f7a656e2062616c616e636560281b6044820152606490fd5b508015156103d8565b5063ffffffff60e01b16600052600f82526103c560ff60406000205416156103bb565b3461035c57604036600319011261035c576104d2613072565b6001600160a01b03166000908152600d602052604090208054602435919082101561035c57602091610503916131a9565b90546040519160031b1c8152f35b3461035c57602036600319011261035c57602061052f600435613178565b905460405160039290921b1c6001600160a01b03168152f35b3461035c57604036600319011261035c57610561613072565b61057261056c613088565b9161309e565b9060018060a01b03166000526020526020604060002054604051908152f35b3461035c57600036600319011261035c573360005260096020526040600020546105f357336000526009602052426040600020556040514281527f612c514ce908d4cf750d17dd895900f4ec4e946855ed2dbd293219e8b12eda0460203392a2005b60405162461bcd60e51b815260206004820152601060248201526f105b1c9958591e481858d8d95c1d195960821b6044820152606490fd5b3461035c57600036600319011261035c576020600154604051908152f35b3461035c57602036600319011261035c576001600160a01b0361066a613072565b1660005260096020526020604060002054604051908152f35b3461035c57604036600319011261035c576106cf6004356106a2613088565b9080600052600080516020613ce58339815191526020526106ca600160406000200154613591565b613985565b005b3461035c57604036600319011261035c576106ea613072565b602435906106f661354b565b6103e88211610719576001600160a01b0316600090815260166020526040902055005b60405162461bcd60e51b815260206004820152600c60248201526b08ccaca40e8dede40d0d2ced60a31b6044820152606490fd5b3461035c57602036600319011261035c576001600160a01b0361076e613072565b16600052601a6020526020604060002054604051908152f35b3461035c57600036600319011261035c576014546040516001600160a01b039091168152602090f35b3461035c57602036600319011261035c576001600160a01b036107d1613072565b1660005260166020526020604060002054604051908152f35b3461035c57602036600319011261035c57600435600052600c602052602060ff604060002054166040519015158152f35b3461035c57604036600319011261035c5761089360209161083a613072565b6024359160ff600e5416158061089d575b6108559150613325565b6001600160a01b0381166000908152600b855260409020546108799060ff1661335a565b61088d82610886336133f1565b1015613398565b336135e2565b6040519015158152f35b5063ffffffff60e01b16600052600f845261085560ff604060002054161561084b565b3461035c57604036600319011261035c576108d9613072565b336000908152600080516020613d458339815191526020908152604090912054602435939192919061090d9060ff166132af565b60ff600e54161580610a01575b6109249150613325565b6001600160a01b031691821515806109f8575b61094090613458565b82600052600080516020613be58339815191528252604060002054600a835261096e8260406000205461343c565b116109bc57907fa065e63c631c86f1b9f66a4a2f63f2093bf1c2168d23290259dbd969e0222a459183600052600a825260406000206109ae82825461343c565b905561043e8160105461343c565b60405162461bcd60e51b8152600481018390526014602482015273496e73756666696369656e742062616c616e636560601b6044820152606490fd5b50801515610937565b5063ffffffff60e01b16600052600f825261092460ff604060002054161561091a565b3461035c57600036600319011261035c57602060405160008152f35b3461035c57600036600319011261035c576020601154604051908152f35b3461035c57610a6c366130e6565b336000908152600080516020613c85833981519152602052604090205490929060ff1615610b4357610aed93610aa06139f6565b60ff600e54161580610b1f575b610ab79150613325565b6001600160a01b0381811615159081610b13575b5080610b0a575b610adb90613458565b610ae883610886836133f1565b613aa6565b6001600080516020613d2583398151915255602060405160018152f35b50821515610ad2565b90508216151585610acb565b5063ffffffff60e01b16600052600f602052610ab760ff6040600020541615610aad565b60405162461bcd60e51b81526020600482015260146024820152732737ba103a3930b739b332b91036b0b730b3b2b960611b6044820152606490fd5b3461035c57600036600319011261035c576020601354604051908152f35b3461035c57602036600319011261035c57610bb6613072565b610bbe61354b565b6001600160a01b0381811660008181526008602052604090205490929060ff1680610d44575b15610d0957610bf290613901565b506000828152600860205260408120805460ff191690555b60079081549182821015610d0057839285610c2484613178565b969054600397881b1c1614610c40575050600191925001610c0a565b9092916000199180830191908211610cea57610c6f84610c62610c8d94613178565b905490891b1c1691613178565b90919060018060a01b038084549260031b9316831b921b1916179055565b82548015610cd4570192610ca084613178565b81939154921b1b19169055555b7fed9c8ad8d5a0a66898ea49d2956929c93ae2e8bd50281b2ed897c5d1a6737e0b600080a2005b634e487b7160e01b600052603160045260246000fd5b634e487b7160e01b600052601160045260246000fd5b50505050610cad565b60405162461bcd60e51b815260206004820152601360248201527210d85b9b9bdd081c995b5bdd99481859d95b9d606a1b6044820152606490fd5b50600160075411610be4565b3461035c57600036600319011261035c57604051600090600080516020613c058339815191528054610d818161311b565b90818452602092600191600181169081600014610dfe5750600114610dc1575b610dbd85610db181890382613155565b60405191829182613029565b0390f35b6000908152838120939550925b828410610deb5750505081610dbd93610db1928201019385610da1565b8054858501870152928501928101610dce565b60ff191686860152505050151560051b8201019150610db181610dbd85610da1565b3461035c57604036600319011261035c57600435610e3c613088565b90610e4561354b565b6103e881111580610ec2575b15610e88576012805460ff19166001179055601355601480546001600160a01b0319166001600160a01b0392909216919091179055005b60405162461bcd60e51b8152602060048201526012602482015271496e76616c69642066656520706172616d7360701b6044820152606490fd5b506001600160a01b0382161515610e51565b3461035c57604036600319011261035c57610eed613088565b600435600052600080516020613ce583398151915260205260406000209060018060a01b0316600052602052602060ff604060002054166040519015158152f35b3461035c57600036600319011261035c576020600754604051908152f35b3461035c57604036600319011261035c576001600160401b0360043581811161035c57610f7d9036906004016131d8565b60243591821161035c573660238301121561035c57816004013591610fa1836131c1565b90610faf6040519283613155565b83825260209360248584019160051b8301019136831161035c57602401905b8282106110cb57505050600080516020613c65833981519152600052600080516020613ce58339815191528352604060002033600052835261101760ff604060002054166132af565b61102d82518251811490816110bf575b506134d6565b60005b82518110156106cf576001906001600160a01b0361105b816110528488613512565b511615156132e7565b6110896110688386613512565b511515826110768589613512565b5116600052600b8852604060002061329e565b6110938286613512565b5116600080516020613d85833981519152866110af8487613512565b511515604051908152a201611030565b60649150111585611027565b8135801515810361035c578152908501908501610fce565b3461035c57600036600319011261035c5760405180600754808352602080930180916007600052846000209060005b8682821061117157868661112882880383613155565b604051928392818401908285525180915260408401929160005b82811061115157505050500390f35b83516001600160a01b031685528695509381019392810192600101611142565b83546001600160a01b031685529093019260019283019201611112565b3461035c57602036600319011261035c576111a7613072565b6111af61354b565b6001600160a01b0381169081151580611250575b80611244575b1561120f57806111db6111e19261377a565b50613526565b8060005260086020526040600020600160ff19825416179055600080516020613ba5833981519152600080a2005b60405162461bcd60e51b815260206004820152600d60248201526c125b9d985b1a59081859d95b9d609a1b6044820152606490fd5b506032600754106111c9565b5081600052600860205260ff60406000205416156111c3565b3461035c57600036600319011261035c5760405160009060025461128c8161311b565b8083526020916001908181169081156112f857506001146112b8575b610dbd84610db181880382613155565b919350600260005283600020916000925b8284106112e55750505081610dbd93610db192820101936112a8565b80548585018701529285019281016112c9565b60ff1916858501525050151560051b8201019150610db181610dbd6112a8565b3461035c57602036600319011261035c5760043561133461354b565b60018110158061137e575b1561134957601855005b60405162461bcd60e51b815260206004820152600d60248201526c125b9d985b1a59081b195d995b609a1b6044820152606490fd5b50600581111561133f565b3461035c57604036600319011261035c576106cf6113a5613072565b6113ad6130d7565b906113b661354b565b6001600160a01b0316600090815260156020526040902061329e565b3461035c57602036600319011261035c576001600160a01b036113f3613072565b16600052600080516020613be58339815191526020526020604060002054604051908152f35b3461035c57602036600319011261035c576001600160a01b0361143a613072565b16600052600960205260206040600020541515604051908152f35b3461035c5761014036600319011261035c576004356001600160401b03811161035c57611486903690600401613248565b6024356001600160401b03811161035c576114a5903690600401613248565b906044359160ff8316830361035c576001600160a01b039060843590828216820361035c5760a43594838616860361035c578360c4351660c4350361035c5760e4356001600160401b03811161035c57611503903690600401613248565b610104356001600160401b03811161035c57611523903690600401613248565b91610124356001600160401b03811161035c57611544903690600401613248565b600080516020613d6583398151915254979094906001600160401b0389161580611e49575b6001600160401b038a166001149081611e3f575b159081611e36575b50611e24576001600160401b03198916600117600080516020613d6583398151915255604089901c60ff1615611dfb575b6115be613b55565b6115c6613b55565b8051906001600160401b0382116119f65781906115f1600080516020613bc58339815191525461311b565b601f8111611d81575b50602090601f8311600114611cfa57600092611cef575b50508160011b916000199060031b1c191617600080516020613bc5833981519152555b8051906001600160401b0382116119f65761165d600080516020613c058339815191525461311b565b601f8111611c81575b50602090601f8311600114611bf95760ff93929160009183611bee575b50508160011b916000199060031b1c191617600080516020613c05833981519152555b6116ae613b55565b6116b6613b55565b6116be613b55565b6001600080516020613d25833981519152558660c43516151580611be3575b80611bd6575b6116ec90613458565b1660ff19600054161760005560643560015580519060018060401b0382116119f657819061171b60025461311b565b601f8111611b69575b50602090601f8311600114611afc57600092611af1575b50508160011b916000199060031b1c1916176002555b8051906001600160401b0382116119f657819061176f60035461311b565b601f8111611a84575b50602090601f8311600114611a1757600092611a0c575b50508160011b916000199060031b1c1916176003555b8051906001600160401b0382116119f6576117c160045461311b565b601f8111611995575b50602090601f831160011461192857918060ff9792869460009261191d575b50508160011b916000199060031b1c1916176004555b8160018060a01b03199316836005541617600555169060065416176006556103e8601155600160185561183360c4356136fe565b5061183f60c43561377a565b5061184b60c435613806565b5061185760c435613526565b8060c435166000526008602052604060002060018419825416179055600b602052604060002060018419825416179055600080516020613d858339815191526020604051928060c43516600080516020613ba5833981519152600080a26001845260c4351692a260401c16156118c957005b60ff60401b19600080516020613d658339815191525416600080516020613d65833981519152557fc7f505b2f371ae2175ee4913f4499e1f2633a7b5936321eed1cdaeb6115181d2602060405160018152a1005b0151905088806117e9565b90601f198316916004600052816000209260005b81811061197d5750926001928592889660ff9b9610611964575b505050811b016004556117ff565b015160001960f88460031b161c19169055888080611956565b9293602060018192878601518155019501930161193c565b60046000527f8a35acfbc15ff81a39ae7d344fd709f28e8600b4aa8c65c6b64bfe7fe36bd19b601f840160051c8101602085106119ef575b601f830160051c820181106119e35750506117ca565b600081556001016119cd565b50806119cd565b634e487b7160e01b600052604160045260246000fd5b01519050878061178f565b9250600360005280600020906000935b601f1984168510611a69576001945083601f19811610611a50575b505050811b016003556117a5565b015160001960f88460031b161c19169055878080611a42565b81810151835560209485019460019093019290910190611a27565b60036000529091507fc2575a0e9e593c00f959f8c92f12db2869c3395a3b0502d05e2516446f71f85b601f840160051c810160208510611aea575b90849392915b601f830160051c82018110611adb575050611778565b60008155859450600101611ac5565b5080611abf565b01519050888061173b565b9250600260005280600020906000935b601f1984168510611b4e576001945083601f19811610611b35575b505050811b01600255611751565b015160001960f88460031b161c19169055888080611b27565b81810151835560209485019460019093019290910190611b0c565b60026000529091507f405787fa12a823e0f2b7631cc41b3ba8828b3321ca811111fa75cd3aa3bb5ace601f840160051c810160208510611bcf575b90849392915b601f830160051c82018110611bc0575050611724565b60008155859450600101611baa565b5080611ba4565b50601281831611156116e3565b5060643515156116dd565b015190508b80611683565b90600080516020613c05833981519152600052806000209160005b601f1985168110611c69575091839160019360ff9695601f19811610611c50575b505050811b01600080516020613c05833981519152556116a6565b015160001960f88460031b161c191690558b8080611c35565b91926020600181928685015181550194019201611c14565b600080516020613c058339815191526000527f46a2803e59a4de4e7a4c574b1243f25977ac4c77d5a1a4a609b5394cebb4a2aa601f840160051c810160208510611ce8575b601f830160051c82018110611cdc575050611666565b60008155600101611cc6565b5080611cc6565b015190508b80611611565b9250600080516020613bc583398151915260005280600020906000935b601f1984168510611d66576001945083601f19811610611d4d575b505050811b01600080516020613bc583398151915255611634565b015160001960f88460031b161c191690558b8080611d32565b81810151835560209485019460019093019290910190611d17565b600080516020613bc58339815191526000529091507f2ae08a8e29253f69ac5d979a101956ab8f8d9d7ded63fa7a83b16fc47648eab0601f840160051c810160208510611df4575b90849392915b601f830160051c82018110611de55750506115fa565b60008155859450600101611dcf565b5080611dc9565b6001600160481b031989166001600160401b0117600080516020613d65833981519152556115b6565b60405163f92ee8a960e01b8152600490fd5b9050158b611585565b303b15915061157d565b5060ff8960401c1615611569565b3461035c57604036600319011261035c576001600160401b039060043582811161035c57611e899036906004016131d8565b60249260243590811161035c573660238201121561035c578060040135611eaf816131c1565b91611ebd6040519384613155565b81835260209160248385019160051b8301019136831161035c576024849101915b8383106120e15750505050600080516020613c65833981519152600052600080516020613ce58339815191528152604060002033600052815260ff93611f2b60ff604060002054166132af565b611f336139f6565b60ff600e541615806120be575b611f4a9150613325565b611f5f83518351811490816120b257506134d6565b600092835b8351851015612026576001600160a01b0380611f808785613512565b511615159081612011575b81611fee575b5015611fb657611fae600191611fa78787613512565b519061343c565b940193611f64565b60405162461bcd60e51b81526004810184905260118189015270125b9d985b1a59081c9958da5c1a595b9d607a1b6044820152606490fd5b9050611ffa8684613512565b5116600052600b8352856040600020541688611f91565b905061201d8686613512565b51151590611f8b565b612054916120448592600080516020613c258339815191525461343c565b9160019384936001541015613495565b6000925b612071575b6001600080516020613d2583398151915255005b81518310156120ad57839283906120a66001600160a01b036120938387613512565b511661209f8386613512565b5190613a26565b0192612058565b61205d565b60649150111587611027565b5063ffffffff60e01b16600052600f8152611f4a60ff6040600020541615611f40565b8235815291810191849101611ede565b3461035c57600036600319011261035c576006546040516001600160a01b039091168152602090f35b3461035c57600036600319011261035c576020601754604051908152f35b3461035c57600036600319011261035c5761215161354b565b6012805460ff191690556000601355601480546001600160a01b0319169055005b3461035c57600036600319011261035c576020601054604051908152f35b3461035c57600036600319011261035c576020601854604051908152f35b3461035c57600036600319011261035c5760408051908101906001600160401b038211818310176119f657610dbd91604052600f81526e0d0b8c0b8c0b5bdc1d1a5b5a5e9959608a1b602082015260405191829182613029565b3461035c57600036600319011261035c5761222161354b565b600160ff19600e541617600e55337fb8fad2fa0ed7a383e747c309ef2c4391d7b65592a48893e57ccc1fab70791456600080a2005b3461035c57602036600319011261035c5760043560075481101561035c5761052f602091613178565b3461035c57602036600319011261035c5760043563ffffffff60e01b811680910361035c57600052600f602052602060ff604060002054166040519015158152f35b3461035c57600036600319011261035c576040516000906004546122e48161311b565b8083526020916001908181169081156112f8575060011461230f57610dbd84610db181880382613155565b919350600460005283600020916000925b82841061233c5750505081610dbd93610db192820101936112a8565b8054858501870152928501928101612320565b3461035c57600036600319011261035c5761236861354b565b60ff19600e5416600e55337ff5cbf596165cc457b2cd92e8d8450827ee314968160a5696402d75766fc52caf600080a2005b3461035c5760208060031936011261035c576001600160a01b036123bc613072565b16600052600d8152604060002060405190818382549182815201908192600052846000209060005b8682821061243a5786866123fa82880383613155565b604051928392818401908285525180915260408401929160005b82811061242357505050500390f35b835185528695509381019392810192600101612414565b8354855290930192600192830192016123e4565b3461035c57604036600319011261035c57612467613072565b336000908152600080516020613d458339815191526020908152604090912054602493843593929161249b9060ff166132af565b6124a36139f6565b60ff600e541615806126e6575b6124ba9150613325565b6001600160a01b0382166000818152600b835260409020546124de9060ff1661335a565b1515806126dd575b6124ef90613458565b61251561250b84600080516020613c258339815191525461343c565b6001541015613495565b6018546002811015612665575b60038110156125f7575b6004811015806125ee575b61258d575b5061205d9350336000526019815242604060002055601a815260406000206125648154613449565b905562015180420615612578575b50613a26565b601a9033600052526000604081205583612572565b80156125d957620f42400483116125a4578361253c565b606490600f856040519262461bcd60e51b845260048401528201526e082dadeeadce840e8dede40d0d2ced608b1b6044820152fd5b84634e487b7160e01b60005260126004526000fd5b50831515612537565b33600052601a8252604060002054811561265057816064041161252c5760405162461bcd60e51b815260048101839052601581870152741058dd1a5bdb881b1a5b5a5d08195e18d959591959605a1b6044820152606490fd5b85634e487b7160e01b60005260126004526000fd5b3360005260198252604060002054603c8202828104603c14831517156126c85761268e9161343c565b4210156125225760405162461bcd60e51b815260048101839052600c818701526b14985d19481b1a5b5a5d195960a21b6044820152606490fd5b86634e487b7160e01b60005260116004526000fd5b508215156124e6565b5063ffffffff60e01b16600052600f81526124ba60ff60406000205416156124b0565b3461035c5760208060031936011261035c57612723613072565b600080516020613c65833981519152600052600080516020613ce58339815191528252604060002033600052825260ff9061276560ff604060002054166132af565b601154600080516020613c258339815191525490604051918583019042825244604085015260018060a01b0385169460018060601b03199060601b16606085015282607485015260948401523360601b60b484015260a8835260e083019083821060018060401b038311176119f6576127e993620f4240926040525190200661343c565b80600052600c845282604060002054161561280c5761280790613449565b6127e9565b90915080600052600c83526040600020600160ff1982541617905581600052600d835260406000208054600160401b8110156119f657612851916001820181556131a9565b81549060031b9083821b91600019901b191617905560018101808211610cea5760115560405191817f549142fd6515952dbe2c6987879528371cc303e6de40861e86edd98beb9d0313600080a38152f35b3461035c57602036600319011261035c576001600160a01b036128c3613072565b16600052600b602052602060ff604060002054166040519015158152f35b3461035c57600036600319011261035c576020604051600080516020613ca58339815191528152f35b3461035c57600036600319011261035c5760405160009060035461292d8161311b565b8083526020916001908181169081156112f8575060011461295857610dbd84610db181880382613155565b919350600360005283600020916000925b8284106129855750505081610dbd93610db192820101936112a8565b8054858501870152928501928101612969565b3461035c57604036600319011261035c576129b1613088565b336001600160a01b038216036129cd576106cf90600435613985565b60405163334bd91960e11b8152600490fd5b3461035c57600036600319011261035c57602060ff60005416604051908152f35b3461035c57604036600319011261035c576106cf600435612a1f613088565b9080600052600080516020613ce5833981519152602052612a47600160406000200154613591565b61388c565b3461035c57602036600319011261035c5760043563ffffffff60e01b811680910361035c57612a7961354b565b6000908152600f60205260409020805460ff19166001179055005b3461035c57600036600319011261035c57602060ff600e54166040519015158152f35b3461035c57602036600319011261035c576020612ada612ad5613072565b6133f1565b604051908152f35b3461035c57602036600319011261035c57600435600052600080516020613ce58339815191526020526020600160406000200154604051908152f35b3461035c57612b2c366130e6565b919260ff600e54161580612c38575b612b459150613325565b6001600160a01b038181166000908152600b6020526040902054612b6b9060ff1661335a565b612b7883610886866133f1565b612b818461309e565b33600052602052604060002054906000198210612ba6575b60206108938585886135e2565b838294939210612c1757841615612bfe573315612be557838261089394612bce60209761309e565b336000528752036040600020559381929350612b99565b604051634a1406b160e11b815260006004820152602490fd5b60405163e602df0560e01b815260006004820152602490fd5b604051637dc7a0d960e11b815280612c34858733600485016135c4565b0390fd5b5063ffffffff60e01b16600052600f602052612b4560ff6040600020541615612b3b565b3461035c57600036600319011261035c576020604051600080516020613c658339815191528152f35b3461035c57602036600319011261035c576001600160a01b03612ca6613072565b166000526008602052602060ff604060002054166040519015158152f35b3461035c57600036600319011261035c576020600080516020613c2583398151915254604051908152f35b3461035c57602036600319011261035c576001600160a01b03612d10613072565b1660005260196020526020604060002054604051908152f35b3461035c57600036600319011261035c576005546040516001600160a01b039091168152602090f35b3461035c57602036600319011261035c576001600160a01b03612d73613072565b166000526015602052602060ff604060002054166040519015158152f35b3461035c57604036600319011261035c57612daa613072565b600080516020613d858339815191526020612dc36130d7565b92600080516020613c65833981519152600052600080516020613ce583398151915282526040600020336000528252612e0360ff604060002054166132af565b6001600160a01b031692612e188415156132e7565b83600052600b8252612e2e81604060002061329e565b6040519015158152a2005b3461035c57602036600319011261035c576001600160a01b03612e5a613072565b16600052600a6020526020604060002054604051908152f35b3461035c57604036600319011261035c57612e8c613072565b602435903315612bfe576001600160a01b0316908115612be557612eaf3361309e565b82600052602052806040600020556040519081527f8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b92560203392a3602060405160018152f35b3461035c57600036600319011261035c57604051600090600080516020613bc58339815191528054612f258161311b565b90818452602092600191600181169081600014610dfe5750600114612f5457610dbd85610db181890382613155565b6000908152838120939550925b828410612f7e5750505081610dbd93610db1928201019385610da1565b8054858501870152928501928101612f61565b3461035c57602036600319011261035c5760043563ffffffff60e01b811680910361035c57612fbe61354b565b6000908152600f60205260409020805460ff19169055005b3461035c57602036600319011261035c576004359063ffffffff60e01b821680920361035c57602091637965db0b60e01b8114908115613018575b5015158152f35b6301ffc9a760e01b14905083613011565b6020808252825181830181905290939260005b82811061305e57505060409293506000838284010152601f8019910116010190565b81810186015184820160400152850161303c565b600435906001600160a01b038216820361035c57565b602435906001600160a01b038216820361035c57565b6001600160a01b031660009081527f52c63247e1f47db19d5ce0460030c497f067ca4cebf71ba98eeadabe20bace016020526040902090565b60243590811515820361035c57565b606090600319011261035c576001600160a01b0390600435828116810361035c5791602435908116810361035c579060443590565b90600182811c9216801561314b575b602083101461313557565b634e487b7160e01b600052602260045260246000fd5b91607f169161312a565b601f909101601f19168101906001600160401b038211908210176119f657604052565b60075481101561319357600760005260206000200190600090565b634e487b7160e01b600052603260045260246000fd5b80548210156131935760005260206000200190600090565b6001600160401b0381116119f65760051b60200190565b9080601f8301121561035c5760209082356131f2816131c1565b936132006040519586613155565b81855260208086019260051b82010192831161035c57602001905b828210613229575050505090565b81356001600160a01b038116810361035c57815290830190830161321b565b81601f8201121561035c578035906001600160401b0382116119f6576040519261327c601f8401601f191660200185613155565b8284526020838301011161035c57816000926020809301838601378301015290565b9060ff801983541691151516179055565b156132b657565b60405162461bcd60e51b8152602060048201526009602482015268139bdd081859d95b9d60ba1b6044820152606490fd5b156132ee57565b60405162461bcd60e51b815260206004820152600f60248201526e125b9d985b1a59081858d8dbdd5b9d608a1b6044820152606490fd5b1561332c57565b60405162461bcd60e51b815260206004820152600660248201526514185d5cd95960d21b6044820152606490fd5b1561336157565b60405162461bcd60e51b815260206004820152600f60248201526e139bdd081dda1a5d195b1a5cdd1959608a1b6044820152606490fd5b1561339f57565b60405162461bcd60e51b815260206004820152601e60248201527f496e73756666696369656e7420617661696c61626c652062616c616e636500006044820152606490fd5b91908203918211610cea57565b60018060a01b0316600052600080516020613be5833981519152602052604060002054600a60205260406000205480821160001461343557613432916133e4565b90565b5050600090565b91908201809211610cea57565b6000198114610cea5760010190565b1561345f57565b60405162461bcd60e51b815260206004820152600e60248201526d496e76616c696420706172616d7360901b6044820152606490fd5b1561349c57565b60405162461bcd60e51b815260206004820152601260248201527145786365656473206d617820737570706c7960701b6044820152606490fd5b156134dd57565b60405162461bcd60e51b815260206004820152600d60248201526c092dcecc2d8d2c840c4c2e8c6d609b1b6044820152606490fd5b80518210156131935760209160051b010190565b60075490600160401b8210156119f657610c6f8260016135499401600755613178565b565b336000908152600080516020613c45833981519152602052604081205460ff16156135735750565b6044906040519063e2517d3f60e01b82523360048301526024820152fd5b80600052600080516020613ce583398151915260205260406000203360005260205260ff60406000205416156135735750565b604091949392606082019560018060a01b0316825260208201520152565b919060ff60125416806136db575b806136c7575b613609575b61360492613aa6565b600190565b6001600160a01b0383811660008181526016602052604090205491949180156136bd57945b858502958587041485151715610cea5761271061360496049182613657575b50505092506135fb565b906136b493959161366d84836014541689613aa6565b6136798460175461343c565b6017557ffb80d84775ea35878c1a64214895ca33241361eaebcc8eb2b730ebc7cc3b7eed6040805193858552866020860152881693a36133e4565b9138808061364d565b506013549461362e565b506014546001600160a01b031615156135f6565b506001600160a01b03831660009081526015602052604090205460ff16156135f0565b6001600160a01b03166000818152600080516020613c458339815191526020526040812054909190600080516020613ce58339815191529060ff16613775578280526020526040822081835260205260408220600160ff198254161790553391600080516020613b858339815191528180a4600190565b505090565b6001600160a01b03166000818152600080516020613d458339815191526020526040812054909190600080516020613c6583398151915290600080516020613ce58339815191529060ff16613800578184526020526040832082845260205260408320600160ff19825416179055600080516020613b85833981519152339380a4600190565b50505090565b6001600160a01b03166000818152600080516020613c858339815191526020526040812054909190600080516020613ca583398151915290600080516020613ce58339815191529060ff16613800578184526020526040832082845260205260408320600160ff19825416179055600080516020613b85833981519152339380a4600190565b90600091808352600080516020613ce583398151915280602052604084209260018060a01b03169283855260205260ff60408520541615600014613800578184526020526040832082845260205260408320600160ff19825416179055600080516020613b85833981519152339380a4600190565b6001600160a01b03166000818152600080516020613d458339815191526020526040812054909190600080516020613c6583398151915290600080516020613ce58339815191529060ff161561380057818452602052604083208284526020526040832060ff198154169055600080516020613d05833981519152339380a4600190565b90600091808352600080516020613ce583398151915280602052604084209260018060a01b03169283855260205260ff60408520541660001461380057818452602052604083208284526020526040832060ff198154169055600080516020613d05833981519152339380a4600190565b600080516020613d258339815191526002815414613a145760029055565b604051633ee5aeb560e01b8152600490fd5b6001600160a01b0316908115613a8d57600080516020613cc58339815191526020600092600080516020613c25833981519152613a6482825461343c565b9055848452600080516020613be5833981519152825260408420818154019055604051908152a3565b60405163ec442f0560e01b815260006004820152602490fd5b916001600160a01b03808416928315613b3c5716928315613a8d57600090838252600080516020613be583398151915280602052604083205491848310613b1d57508284600080516020613cc5833981519152959360409388602097528652038282205586815220818154019055604051908152a3565b9050612c348460405193849363391434e360e21b8552600485016135c4565b604051634b637e8f60e11b815260006004820152602490fd5b60ff600080516020613d658339815191525460401c1615613b7257565b604051631afcd79f60e31b8152600490fdfe2f8788117e7eff1d82e926ec794901d17c78024a50270940304540a733656f0df68e73cec97f2d70aa641fb26e87a4383686e2efacb648f2165aeb02ac562ec552c63247e1f47db19d5ce0460030c497f067ca4cebf71ba98eeadabe20bace0352c63247e1f47db19d5ce0460030c497f067ca4cebf71ba98eeadabe20bace0052c63247e1f47db19d5ce0460030c497f067ca4cebf71ba98eeadabe20bace0452c63247e1f47db19d5ce0460030c497f067ca4cebf71ba98eeadabe20bace02b7db2dd08fcb62d0c9e08c51941cae53c267786a0b75803fb7960902fc8ef97dcab5a0bfe0b79d2c4b1c2e02599fa044d115b7511f9659307cb42769509677099aa013cdaba1a4b8bc0cce159f7a8e62bd6fc18f8877b619e1cddb829731adb6183175b7b980894e2ea4c7ef510cf97a6819a85ee72e9e5b0186e21c7db90e84ddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef02dd7bc7dec4dceedda775e58dd541e08a116c6c53815c0bd028192f7b626800f6391f5c32d9c69d2a47ea670b442974b53935d1edc7fd64eb21e047a839171b9b779b17422d0df92223018b32b4d1fa46e071723d6817e2486d003becc55f0081cf8a8d68c3bf977ff8d17e8ef6f30dbea1776614c7d2258c5ab1b17338333ff0c57e16840df040f15088dc2f81fe391c3923bec73e23a9662efc9c229c6a00f93f9a76c1bf3444d22400a00cb9fe990e6abe9dbb333fda48859cfee864543da164736f6c6343000816000a", "linkReferences": {}, "deployedLinkReferences": {}}