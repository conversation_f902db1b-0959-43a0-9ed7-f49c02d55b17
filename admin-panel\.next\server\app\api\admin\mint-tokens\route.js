/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/mint-tokens/route";
exports.ids = ["app/api/admin/mint-tokens/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fmint-tokens%2Froute&page=%2Fapi%2Fadmin%2Fmint-tokens%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fmint-tokens%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fmint-tokens%2Froute&page=%2Fapi%2Fadmin%2Fmint-tokens%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fmint-tokens%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_tokendev_newroo_admin_panel_src_app_api_admin_mint_tokens_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/mint-tokens/route.ts */ \"(rsc)/./src/app/api/admin/mint-tokens/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/mint-tokens/route\",\n        pathname: \"/api/admin/mint-tokens\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/mint-tokens/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api\\\\admin\\\\mint-tokens\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_tokendev_newroo_admin_panel_src_app_api_admin_mint_tokens_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fmint-tokens%2Froute&page=%2Fapi%2Fadmin%2Fmint-tokens%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fmint-tokens%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/mint-tokens/route.ts":
/*!************************************************!*\
  !*** ./src/app/api/admin/mint-tokens/route.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/address/checks.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/providers/provider-jsonrpc.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/wallet/wallet.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/utils/units.js\");\n\n\nconst SECURITY_TOKEN_CORE_ABI = [\n    \"function mint(address to, uint256 amount) external\",\n    \"function balanceOf(address account) external view returns (uint256)\",\n    \"function totalSupply() external view returns (uint256)\",\n    \"function maxSupply() external view returns (uint256)\"\n];\nasync function POST(request) {\n    try {\n        const { tokenAddress, toAddress, amount } = await request.json();\n        if (!tokenAddress || !toAddress || !amount) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Token address, recipient address, and amount are required'\n            }, {\n                status: 400\n            });\n        }\n        // Validate addresses\n        if (!ethers__WEBPACK_IMPORTED_MODULE_1__.isAddress(tokenAddress) || !ethers__WEBPACK_IMPORTED_MODULE_1__.isAddress(toAddress)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid address format'\n            }, {\n                status: 400\n            });\n        }\n        // Validate amount\n        const amountNum = parseInt(amount);\n        if (isNaN(amountNum) || amountNum <= 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid amount value'\n            }, {\n                status: 400\n            });\n        }\n        // Get environment variables\n        const rpcUrl = process.env.AMOY_RPC_URL;\n        const privateKey = process.env.CONTRACT_ADMIN_PRIVATE_KEY;\n        if (!rpcUrl || !privateKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Server configuration error'\n            }, {\n                status: 500\n            });\n        }\n        // Setup provider and signer\n        const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.JsonRpcProvider(rpcUrl);\n        const signer = new ethers__WEBPACK_IMPORTED_MODULE_3__.Wallet(privateKey, provider);\n        // Get token contract\n        const tokenContract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(tokenAddress, SECURITY_TOKEN_CORE_ABI, signer);\n        // Get current values for comparison\n        const currentBalance = await tokenContract.balanceOf(toAddress);\n        const currentTotalSupply = await tokenContract.totalSupply();\n        const maxSupply = await tokenContract.maxSupply();\n        console.log('Current balance:', currentBalance.toString());\n        console.log('Current total supply:', currentTotalSupply.toString());\n        console.log('Max supply:', maxSupply.toString());\n        console.log('Amount to mint:', amount);\n        // Convert to proper units (assuming 0 decimals based on our token)\n        const amountBN = ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits(amount, 0);\n        // Check if minting would exceed max supply\n        if (currentTotalSupply + amountBN > maxSupply) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Minting would exceed maximum supply'\n            }, {\n                status: 400\n            });\n        }\n        // Mint tokens\n        const tx = await tokenContract.mint(toAddress, amountBN);\n        await tx.wait();\n        // Verify minting\n        const newBalance = await tokenContract.balanceOf(toAddress);\n        const newTotalSupply = await tokenContract.totalSupply();\n        console.log('Tokens minted successfully:', tx.hash);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Tokens minted successfully',\n            txHash: tx.hash,\n            toAddress,\n            amount: amount,\n            oldBalance: currentBalance.toString(),\n            newBalance: newBalance.toString(),\n            oldTotalSupply: currentTotalSupply.toString(),\n            newTotalSupply: newTotalSupply.toString(),\n            tokenAddress\n        });\n    } catch (error) {\n        console.error('Error minting tokens:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: `Failed to mint tokens: ${error.message}`\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/mint-tokens/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ethers","vendor-chunks/@noble","vendor-chunks/@adraffy","vendor-chunks/aes-js"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fmint-tokens%2Froute&page=%2Fapi%2Fadmin%2Fmint-tokens%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fmint-tokens%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();