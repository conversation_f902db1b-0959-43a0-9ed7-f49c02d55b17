// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";
import "../interfaces/IIdentityRegistry.sol";

/**
 * @title BaseIdentityRegistry
 * @dev Base implementation of the identity registry functionality for ERC-3643 compliant tokens
 * This contract serves as a reusable base for whitelist implementations
 */
abstract contract BaseIdentityRegistry is 
    Initializable, 
    AccessControlUpgradeable, 
    UUPSUpgradeable,
    ReentrancyGuardUpgradeable,
    IIdentityRegistry 
{
    bytes32 public constant AGENT_ROLE = keccak256("AGENT_ROLE");
    
    // Mapping of whitelisted addresses
    mapping(address => bool) private _whitelisted;
    
    // Mapping of frozen addresses
    mapping(address => bool) private _frozen;
    
    // Version of the implementation
    string private constant _VERSION = "2.0.0";
    
    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers();
    }
    
    /**
     * @dev Initialize the contract
     * @param admin The address to be granted DEFAULT_ADMIN_ROLE
     */
    function __BaseIdentityRegistry_init(address admin) internal onlyInitializing {
        __AccessControl_init();
        __UUPSUpgradeable_init();
        __ReentrancyGuard_init();
        
        require(admin != address(0), "BaseIdentityRegistry: admin cannot be zero address");
        
        _grantRole(DEFAULT_ADMIN_ROLE, admin);
    }
    
    /**
     * @dev Check if an address is whitelisted
     * @param account The address to check
     * @return bool True if the address is whitelisted, false otherwise
     */
    function isWhitelisted(address account) public view virtual override returns (bool) {
        return _whitelisted[account];
    }

    /**
     * @dev Check if an address is verified (for compatibility with SecurityToken)
     * In the base implementation, verified means whitelisted
     * @param account The address to check
     * @return bool True if the address is verified, false otherwise
     */
    function isVerified(address account) public view virtual returns (bool) {
        return _whitelisted[account];
    }

    /**
     * @dev Check if a transfer can be performed (for compliance compatibility)
     * @param from Sender address
     * @param to Recipient address
     * @return bool True if transfer is allowed
     */
    function canTransfer(address from, address to) public view virtual returns (bool) {
        return isVerified(from) && isVerified(to) && isWhitelisted(from) && isWhitelisted(to) && !isFrozen(from) && !isFrozen(to);
    }

    /**
     * @dev Get investor country (for compliance compatibility)
     * In the base implementation, returns a default country code (840 = US)
     * @return uint16 Country code
     */
    function investorCountry(address /* investor */) public view virtual returns (uint16) {
        // Default implementation returns US country code
        // This can be overridden in derived contracts
        return 840; // ISO-3166 code for United States
    }
    
    /**
     * @dev Add an address to the whitelist
     * @param account The address to add to the whitelist
     */
    function addToWhitelist(address account) public virtual override onlyRole(AGENT_ROLE) {
        require(account != address(0), "BaseIdentityRegistry: cannot whitelist zero address");
        require(!_whitelisted[account], "BaseIdentityRegistry: address already whitelisted");
        
        _whitelisted[account] = true;
        emit AddedToWhitelist(account);
    }
    
    /**
     * @dev Remove an address from the whitelist
     * @param account The address to remove from the whitelist
     */
    function removeFromWhitelist(address account) public virtual override onlyRole(AGENT_ROLE) {
        require(account != address(0), "BaseIdentityRegistry: cannot remove zero address");
        require(_whitelisted[account], "BaseIdentityRegistry: address not whitelisted");
        
        _whitelisted[account] = false;
        emit RemovedFromWhitelist(account);
    }
    
    /**
     * @dev Check if an address is frozen
     * @param account The address to check
     * @return bool True if the address is frozen, false otherwise
     */
    function isFrozen(address account) public view virtual override returns (bool) {
        return _frozen[account];
    }
    
    /**
     * @dev Freeze an address
     * @param account The address to freeze
     */
    function freezeAddress(address account) public virtual override onlyRole(AGENT_ROLE) {
        require(account != address(0), "BaseIdentityRegistry: cannot freeze zero address");
        require(!_frozen[account], "BaseIdentityRegistry: address already frozen");
        
        _frozen[account] = true;
        emit AddressFrozen(account);
    }
    
    /**
     * @dev Unfreeze an address
     * @param account The address to unfreeze
     */
    function unfreezeAddress(address account) public virtual override onlyRole(AGENT_ROLE) {
        require(account != address(0), "BaseIdentityRegistry: cannot unfreeze zero address");
        require(_frozen[account], "BaseIdentityRegistry: address not frozen");
        
        _frozen[account] = false;
        emit AddressUnfrozen(account);
    }
    
    /**
     * @dev Function to authorize an upgrade
     * @param newImplementation The address of the new implementation
     */
    function _authorizeUpgrade(address newImplementation) internal virtual override onlyRole(DEFAULT_ADMIN_ROLE) {
        // Additional validation could be added here
    }
    
    /**
     * @dev Batch add addresses to whitelist
     * @param accounts The addresses to add to the whitelist
     */
    function batchAddToWhitelist(address[] calldata accounts) external virtual override onlyRole(AGENT_ROLE) nonReentrant {
        uint256 length = accounts.length;
        require(length > 0, "BaseIdentityRegistry: empty addresses array");
        
        for (uint256 i = 0; i < length; i++) {
            address addr = accounts[i];
            if (addr != address(0) && !_whitelisted[addr]) {
                _whitelisted[addr] = true;
                emit AddedToWhitelist(addr);
            }
        }
    }
    
    /**
     * @dev Batch remove addresses from whitelist
     * @param accounts The addresses to remove from the whitelist
     */
    function batchRemoveFromWhitelist(address[] calldata accounts) external virtual override onlyRole(AGENT_ROLE) nonReentrant {
        uint256 length = accounts.length;
        require(length > 0, "BaseIdentityRegistry: empty addresses array");
        
        for (uint256 i = 0; i < length; i++) {
            address addr = accounts[i];
            if (addr != address(0) && _whitelisted[addr]) {
                _whitelisted[addr] = false;
                emit RemovedFromWhitelist(addr);
            }
        }
    }
    
    /**
     * @dev Batch freeze addresses
     * @param accounts The addresses to freeze
     */
    function batchFreezeAddresses(address[] calldata accounts) external virtual override onlyRole(AGENT_ROLE) nonReentrant {
        uint256 length = accounts.length;
        require(length > 0, "BaseIdentityRegistry: empty addresses array");
        
        for (uint256 i = 0; i < length; i++) {
            address addr = accounts[i];
            if (addr != address(0) && !_frozen[addr]) {
                _frozen[addr] = true;
                emit AddressFrozen(addr);
            }
        }
    }
    
    /**
     * @dev Batch unfreeze addresses
     * @param accounts The addresses to unfreeze
     */
    function batchUnfreezeAddresses(address[] calldata accounts) external virtual override onlyRole(AGENT_ROLE) nonReentrant {
        uint256 length = accounts.length;
        require(length > 0, "BaseIdentityRegistry: empty addresses array");
        
        for (uint256 i = 0; i < length; i++) {
            address addr = accounts[i];
            if (addr != address(0) && _frozen[addr]) {
                _frozen[addr] = false;
                emit AddressUnfrozen(addr);
            }
        }
    }

    /**
     * @dev Grant the AGENT_ROLE to an address
     * @param agent The address to grant the AGENT_ROLE to
     */
    function addAgent(address agent) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(agent != address(0), "BaseIdentityRegistry: agent cannot be zero address");
        grantRole(AGENT_ROLE, agent);
    }

    /**
     * @dev Revoke the AGENT_ROLE from an address
     * @param agent The address to revoke the AGENT_ROLE from
     */
    function removeAgent(address agent) external onlyRole(DEFAULT_ADMIN_ROLE) {
        revokeRole(AGENT_ROLE, agent);
    }
    
    /**
     * @dev Returns the version of the implementation
     * @return string The version
     */
    function version() external pure virtual returns (string memory) {
        return _VERSION;
    }
}