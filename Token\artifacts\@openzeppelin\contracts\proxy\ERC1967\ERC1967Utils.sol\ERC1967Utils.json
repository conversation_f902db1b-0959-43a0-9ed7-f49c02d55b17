{"_format": "hh-sol-artifact-1", "contractName": "ERC1967Utils", "sourceName": "@openzeppelin/contracts/proxy/ERC1967/ERC1967Utils.sol", "abi": [{"inputs": [{"internalType": "address", "name": "admin", "type": "address"}], "name": "ERC1967InvalidAdmin", "type": "error"}, {"inputs": [{"internalType": "address", "name": "beacon", "type": "address"}], "name": "ERC1967InvalidBeacon", "type": "error"}, {"inputs": [{"internalType": "address", "name": "implementation", "type": "address"}], "name": "ERC1967InvalidImplementation", "type": "error"}, {"inputs": [], "name": "ERC1967Non<PERSON>ayable", "type": "error"}], "bytecode": "0x60808060405234601757603a9081601d823930815050f35b600080fdfe600080fdfea26469706673582212201f0418215456938c149ccccb552f7d3632cad2f8cc99037de83846cc2ff391ea64736f6c63430008160033", "deployedBytecode": "0x600080fdfea26469706673582212201f0418215456938c149ccccb552f7d3632cad2f8cc99037de83846cc2ff391ea64736f6c63430008160033", "linkReferences": {}, "deployedLinkReferences": {}}