{"_format": "hh-sol-artifact-1", "contractName": "ERC1967Utils", "sourceName": "@openzeppelin/contracts/proxy/ERC1967/ERC1967Utils.sol", "abi": [{"inputs": [{"internalType": "address", "name": "admin", "type": "address"}], "name": "ERC1967InvalidAdmin", "type": "error"}, {"inputs": [{"internalType": "address", "name": "beacon", "type": "address"}], "name": "ERC1967InvalidBeacon", "type": "error"}, {"inputs": [{"internalType": "address", "name": "implementation", "type": "address"}], "name": "ERC1967InvalidImplementation", "type": "error"}, {"inputs": [], "name": "ERC1967Non<PERSON>ayable", "type": "error"}], "bytecode": "0x6080806040523460175760119081601d823930815050f35b600080fdfe600080fdfea164736f6c6343000816000a", "deployedBytecode": "0x600080fdfea164736f6c6343000816000a", "linkReferences": {}, "deployedLinkReferences": {}}