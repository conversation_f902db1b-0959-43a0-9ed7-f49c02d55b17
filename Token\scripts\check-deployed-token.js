const { ethers } = require("hardhat");

async function main() {
  const tokenAddress = "******************************************";
  
  console.log("🔍 Checking deployed token:", tokenAddress);
  
  const [deployer] = await ethers.getSigners();
  console.log("Using account:", deployer.address);
  
  // Try to connect as SecurityTokenEnhanced
  try {
    const SecurityTokenEnhanced = await ethers.getContractFactory("SecurityTokenEnhanced");
    const token = SecurityTokenEnhanced.attach(tokenAddress);
    
    console.log("\n📋 Token Information:");
    const name = await token.name();
    const symbol = await token.symbol();
    const decimals = await token.decimals();
    const totalSupply = await token.totalSupply();
    const maxSupply = await token.maxSupply();
    
    console.log("Name:", name);
    console.log("Symbol:", symbol);
    console.log("Decimals:", decimals);
    console.log("Total Supply:", totalSupply.toString());
    console.log("Max Supply:", maxSupply.toString());
    
    // Check if it has identityRegistry
    try {
      const identityRegistry = await token.identityRegistry();
      console.log("Identity Registry:", identityRegistry);
      
      if (identityRegistry && identityRegistry !== ethers.ZeroAddress) {
        console.log("✅ Has separate identity registry contract");
      } else {
        console.log("✅ Uses built-in whitelist functionality");
      }
    } catch (error) {
      console.log("❌ No identityRegistry function");
    }
    
    // Check if it has built-in whitelist functions
    try {
      const isWhitelisted = await token.isWhitelisted(deployer.address);
      console.log("✅ Has built-in isWhitelisted function");
      console.log("Admin whitelisted:", isWhitelisted);
    } catch (error) {
      console.log("❌ No built-in isWhitelisted function");
    }
    
    // Check version
    try {
      const version = await token.version();
      console.log("Version:", version);
    } catch (error) {
      console.log("No version function");
    }
    
    // Check agent functions
    try {
      const agentCount = await token.getAgentCount();
      console.log("Agent count:", agentCount.toString());
    } catch (error) {
      console.log("No agent functions");
    }
    
  } catch (error) {
    console.log("❌ Failed to connect as SecurityTokenEnhanced:", error.message);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
