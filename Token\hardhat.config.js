require("@nomicfoundation/hardhat-toolbox");
require("@openzeppelin/hardhat-upgrades");

// Make sure ethers is explicitly loaded
const { ethers } = require("ethers");

// Load environment variables
require('dotenv').config({ path: '.env.local' });

// Generate a random private key for testing if none provided
const PRIVATE_KEY = process.env.PRIVATE_KEY || (() => {
  if (!process.env.PRIVATE_KEY) {
    console.warn("⚠️  WARNING: No PRIVATE_KEY found in .env file!");
    console.warn("⚠️  Using a randomly generated key for testing.");
    console.warn("⚠️  Please create a .env file with your actual private key.");
    // Generate a random 32-byte private key
    return ethers.hexlify(ethers.randomBytes(32));
  }
  return process.env.PRIVATE_KEY;
})();
const ETHERSCAN_API_KEY = process.env.ETHERSCAN_API_KEY || "";
const POLYGONSCAN_API_KEY = process.env.POLYGONSCAN_API_KEY || ETHERSCAN_API_KEY || "";

/** @type import('hardhat/config').HardhatUserConfig */
module.exports = {
  solidity: {
    version: "0.8.22",
    settings: {
      optimizer: {
        enabled: true,
        runs: 200, // Balance between size and gas efficiency
      },
      viaIR: true, // Enable intermediate representation for better optimization
      viaIR: true,
    },
  },
  // Global gas settings
  mocha: {
    timeout: 100000
  },
  networks: {
    hardhat: {
      // Local development network
      allowUnlimitedContractSize: true,
      blockGasLimit: ********,
    },
    mumbai: {
      url: "https://polygon-mumbai.g.alchemy.com/v2/********************************",
      accounts: [PRIVATE_KEY],
      chainId: 80001,
    },
    amoy: {
      url: process.env.AMOY_RPC_URL || "https://rpc-amoy.polygon.technology",
      accounts: [PRIVATE_KEY],
      chainId: 80002, // Amoy testnet chain ID
      gasPrice: process.env.GAS_PRICE ? parseInt(process.env.GAS_PRICE) : 'auto',
      gas: process.env.GAS ? parseInt(process.env.GAS) : ********, // Increased gas limit
      timeout: 100000, // Higher timeout for testnet
      // For handling network congestion or instability
      networkCheckTimeout: 100000,
      timeoutBlocks: 200,
      // Maximum gas price to use in 'auto' mode - increased for large deployments
      maxFeePerGas: ethers.parseUnits('1000', 'gwei').toString(), // Increased from 500
      maxPriorityFeePerGas: ethers.parseUnits('100', 'gwei').toString(), // Increased from 50
      // Allow higher transaction value for large contract deployments
      allowUnlimitedContractSize: true,
      // Override Hardhat's default transaction value cap
      blockGasLimit: ********,
      initialBaseFeePerGas: 0,
      // Increase transaction value cap for large deployments
      txValueCap: ethers.parseEther('5').toString(), // Allow up to 5 ETH for deployment
      // Retry settings
      retry: {
        maxRetries: 5,
        initialBackoff: 1000, // in ms
        maxBackoff: 10000     // in ms
      }
    },
    polygon: {
      url: "https://polygon-rpc.com",
      accounts: [PRIVATE_KEY],
      chainId: 137,
    },
  },
  gasReporter: {
    enabled: process.env.REPORT_GAS !== undefined,
    currency: "USD",
  },
  etherscan: {
    apiKey: {
      polygon: POLYGONSCAN_API_KEY,
      polygonMumbai: POLYGONSCAN_API_KEY,
      // Note: Amoy doesn't use PolygonScan, it uses OKLink which doesn't have a verification API
    },
    customChains: [
      {
        network: "polygonMumbai",
        chainId: 80001,
        urls: {
          apiURL: "https://api-testnet.polygonscan.com/api",
          browserURL: "https://mumbai.polygonscan.com/"
        }
      },
      {
        network: "polygon",
        chainId: 137,
        urls: {
          apiURL: "https://api.polygonscan.com/api",
          browserURL: "https://polygonscan.com/"
        }
      }
    ]
  },
  paths: {
    sources: "./contracts",
    tests: "./test",
    cache: "./cache",
    artifacts: "./artifacts",
  },
};
