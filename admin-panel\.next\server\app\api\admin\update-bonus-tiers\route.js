/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/update-bonus-tiers/route";
exports.ids = ["app/api/admin/update-bonus-tiers/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fupdate-bonus-tiers%2Froute&page=%2Fapi%2Fadmin%2Fupdate-bonus-tiers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fupdate-bonus-tiers%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fupdate-bonus-tiers%2Froute&page=%2Fapi%2Fadmin%2Fupdate-bonus-tiers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fupdate-bonus-tiers%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_tokendev_newroo_admin_panel_src_app_api_admin_update_bonus_tiers_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/update-bonus-tiers/route.ts */ \"(rsc)/./src/app/api/admin/update-bonus-tiers/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/update-bonus-tiers/route\",\n        pathname: \"/api/admin/update-bonus-tiers\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/update-bonus-tiers/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api\\\\admin\\\\update-bonus-tiers\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_tokendev_newroo_admin_panel_src_app_api_admin_update_bonus_tiers_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fupdate-bonus-tiers%2Froute&page=%2Fapi%2Fadmin%2Fupdate-bonus-tiers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fupdate-bonus-tiers%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/update-bonus-tiers/route.ts":
/*!*******************************************************!*\
  !*** ./src/app/api/admin/update-bonus-tiers/route.ts ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/address/checks.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/providers/provider-jsonrpc.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/wallet/wallet.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/contract/contract.js\");\n\n\nconst SECURITY_TOKEN_CORE_ABI = [\n    \"function updateBonusTiers(string memory bonusTiers_) external\",\n    \"function getTokenMetadata() external view returns (string memory tokenPrice, string memory bonusTiers, string memory tokenDetails, string memory tokenImageUrl)\"\n];\nasync function POST(request) {\n    try {\n        const { tokenAddress, newBonusTiers } = await request.json();\n        if (!tokenAddress || !newBonusTiers) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Token address and new bonus tiers are required'\n            }, {\n                status: 400\n            });\n        }\n        // Validate addresses\n        if (!ethers__WEBPACK_IMPORTED_MODULE_1__.isAddress(tokenAddress)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid token address format'\n            }, {\n                status: 400\n            });\n        }\n        // Get environment variables\n        const rpcUrl = process.env.AMOY_RPC_URL;\n        const privateKey = process.env.CONTRACT_ADMIN_PRIVATE_KEY;\n        if (!rpcUrl || !privateKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Server configuration error'\n            }, {\n                status: 500\n            });\n        }\n        // Setup provider and signer\n        const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.JsonRpcProvider(rpcUrl);\n        const signer = new ethers__WEBPACK_IMPORTED_MODULE_3__.Wallet(privateKey, provider);\n        // Get token contract\n        const tokenContract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(tokenAddress, SECURITY_TOKEN_CORE_ABI, signer);\n        // Get current metadata for comparison\n        const currentMetadata = await tokenContract.getTokenMetadata();\n        console.log('Current bonus tiers:', currentMetadata[1]);\n        console.log('New bonus tiers:', newBonusTiers);\n        // Update bonus tiers\n        const tx = await tokenContract.updateBonusTiers(newBonusTiers);\n        await tx.wait();\n        // Verify update\n        const updatedMetadata = await tokenContract.getTokenMetadata();\n        console.log('Bonus tiers updated successfully:', tx.hash);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Bonus tiers updated successfully',\n            txHash: tx.hash,\n            oldBonusTiers: currentMetadata[1],\n            newBonusTiers: updatedMetadata[1],\n            tokenAddress\n        });\n    } catch (error) {\n        console.error('Error updating bonus tiers:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: `Failed to update bonus tiers: ${error.message}`\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/update-bonus-tiers/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ethers","vendor-chunks/@noble","vendor-chunks/@adraffy","vendor-chunks/aes-js"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fupdate-bonus-tiers%2Froute&page=%2Fapi%2Fadmin%2Fupdate-bonus-tiers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fupdate-bonus-tiers%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();