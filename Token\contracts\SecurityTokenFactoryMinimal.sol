// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "@openzeppelin/contracts/access/AccessControl.sol";
import "./SecurityTokenMinimal.sol";

/**
 * @title SecurityTokenFactoryMinimal
 * @dev Ultra-minimal factory for deploying security tokens with whitelist
 * Maximum size optimization while maintaining essential functionality
 */
contract SecurityTokenFactoryMinimal is AccessControl {
    bytes32 public constant DEPLOYER_ROLE = keccak256("DEPLOYER_ROLE");
    
    address[] public deployedTokens;
    mapping(string => address) public tokensBySymbol;
    mapping(address => bool) public isDeployedToken;
    
    event TokenDeployed(
        address indexed tokenAddress,
        string name,
        string symbol,
        uint8 decimals,
        uint256 maxSupply,
        address admin
    );
    
    constructor(address admin) {
        _grantRole(DEFAULT_ADMIN_ROLE, admin);
        _grantRole(DEPLOYER_ROLE, admin);
    }
    
    function deploySecurityToken(
        string memory name,
        string memory symbol,
        uint8 decimals,
        uint256 maxSupply,
        address admin,
        string memory tokenPrice,
        string memory bonusTiers,
        string memory, // tokenDetails - unused to save space
        string memory tokenImageUrl
    ) external onlyRole(DEPLOYER_ROLE) returns (address tokenAddress) {
        require(bytes(name).length > 0 && bytes(symbol).length > 0, "Invalid name/symbol");
        require(admin != address(0) && maxSupply > 0, "Invalid params");
        require(tokensBySymbol[symbol] == address(0), "Symbol exists");
        
        SecurityTokenMinimal token = new SecurityTokenMinimal(
            name,
            symbol,
            decimals,
            maxSupply,
            admin,
            tokenPrice,
            bonusTiers,
            tokenImageUrl,
            address(0), // identityRegistry placeholder
            address(0)  // compliance placeholder
        );
        
        tokenAddress = address(token);
        
        deployedTokens.push(tokenAddress);
        tokensBySymbol[symbol] = tokenAddress;
        isDeployedToken[tokenAddress] = true;
        
        emit TokenDeployed(tokenAddress, name, symbol, decimals, maxSupply, admin);
        
        return tokenAddress;
    }
    
    function addDeployer(address deployer) external onlyRole(DEFAULT_ADMIN_ROLE) {
        _grantRole(DEPLOYER_ROLE, deployer);
    }
    
    function removeDeployer(address deployer) external onlyRole(DEFAULT_ADMIN_ROLE) {
        _revokeRole(DEPLOYER_ROLE, deployer);
    }
    
    function getTokenAddressBySymbol(string memory symbol) external view returns (address) {
        return tokensBySymbol[symbol];
    }
    
    function getAllDeployedTokens() external view returns (address[] memory) {
        return deployedTokens;
    }
    
    function getDeployedToken(uint256 index) external view returns (address) {
        require(index < deployedTokens.length, "Index out of bounds");
        return deployedTokens[index];
    }
    
    function getTokenCount() external view returns (uint256) {
        return deployedTokens.length;
    }
}
