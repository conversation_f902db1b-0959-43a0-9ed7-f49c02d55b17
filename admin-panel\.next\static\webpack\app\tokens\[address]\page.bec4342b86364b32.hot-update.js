"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/tokens/[address]/page",{

/***/ "(app-pages-browser)/./src/config.ts":
/*!***********************!*\
  !*** ./src/config.ts ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   contractAddresses: () => (/* binding */ contractAddresses),\n/* harmony export */   defaultNetwork: () => (/* binding */ defaultNetwork),\n/* harmony export */   getContractAddresses: () => (/* binding */ getContractAddresses),\n/* harmony export */   getKnownTokens: () => (/* binding */ getKnownTokens),\n/* harmony export */   getNetworkConfig: () => (/* binding */ getNetworkConfig),\n/* harmony export */   isKnownToken: () => (/* binding */ isKnownToken),\n/* harmony export */   knownTokens: () => (/* binding */ knownTokens),\n/* harmony export */   networkConfig: () => (/* binding */ networkConfig),\n/* harmony export */   tokenTypes: () => (/* binding */ tokenTypes),\n/* harmony export */   verifyTokenExists: () => (/* binding */ verifyTokenExists)\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n// Network configuration\nconst networkConfig = {\n    // Amoy testnet\n    amoy: {\n        chainId: 80002,\n        name: \"Amoy\",\n        rpcUrl: \"https://rpc-amoy.polygon.technology\",\n        blockExplorer: \"https://www.oklink.com/amoy\"\n    },\n    // Polygon mainnet\n    polygon: {\n        chainId: 137,\n        name: \"Polygon\",\n        rpcUrl: \"https://polygon-rpc.com\",\n        blockExplorer: \"https://polygonscan.com\"\n    }\n};\n// Default network\nconst defaultNetwork = \"amoy\";\n// Contract addresses - using the current production-ready factory\nconst contractAddresses = {\n    // Current production factory (verified and working)\n    amoy: {\n        factory: \"0x9BC856A01A192B8c7cf86251C837282EBaA2C4af\",\n        // 🔐 SECURITY LEVEL: HIGH - ALL AUDIT FIXES IMPLEMENTED\n        // ✅ Emergency pause controls\n        // ✅ Function-specific pausing\n        // ✅ Enhanced reentrancy protection\n        // ✅ Improved input validation\n        // ✅ Role-based access control\n        // ✅ Agent management\n        // ✅ Agreement tracking\n        // ✅ Token enumeration\n        // ✅ Configurable decimals\n        // ✅ All critical security audit fixes implemented\n        // ✅ Production ready with enhanced security\n        //\n        // Previous factory (backup): \"0x69a6536629369F8948f47b897045929a57c630Fd\"\n        tokenImplementation: \"0xae2aA28708120CAA177e4c98CCCa0e152E30E506\",\n        whitelistImplementation: \"0x63eeE78ccc281413272bE68d9553Ae82680a0B09\",\n        whitelistWithKYCImplementation: \"0xf7c9C30Ad2E5b72F86489f26ab66cc1E79F44A7D\"\n    },\n    polygon: {\n        factory: process.env.NEXT_PUBLIC_FACTORY_ADDRESS_POLYGON || \"0x6543210987654321098765432109876543210987\"\n    }\n};\n// Known deployed tokens for fallback display (from memory)\nconst knownTokens = {\n    amoy: [\n        {\n            address: \"0x7544A3072FAA793e3f89048C31b794f171779544\",\n            name: \"Advanced Control Token\",\n            symbol: \"ACT\",\n            description: \"Security token with advanced transfer controls (conditional transfers, whitelisting, fees)\"\n        },\n        {\n            address: \"0xfccB88D208f5Ec7166ce2291138aaD5274C671dE\",\n            name: \"Augment_019\",\n            symbol: \"AUG019\",\n            description: \"Commodity token with 0 decimals, 1M max supply\"\n        },\n        {\n            address: \"0xe5F81d7dCeB8a8F97274C749773659B7288EcF90\",\n            name: \"Augment_01z\",\n            symbol: \"AUG01Z\",\n            description: \"Test token with custom configuration\"\n        },\n        {\n            address: \"0x391a0FA1498B869d0b9445596ed49b03aA8bf46e\",\n            name: \"Test Image Token\",\n            symbol: \"TIT2789\",\n            description: \"Test token with image URL support - deployed from upgraded factory\"\n        }\n    ]\n};\n// Token types for creating new tokens\nconst tokenTypes = [\n    {\n        id: \"equity\",\n        name: \"Equity\"\n    },\n    {\n        id: \"bond\",\n        name: \"Bond\"\n    },\n    {\n        id: \"debenture\",\n        name: \"Debenture\"\n    },\n    {\n        id: \"warrant\",\n        name: \"Warrant\"\n    },\n    {\n        id: \"realestate\",\n        name: \"Real Estate\"\n    },\n    {\n        id: \"carbon\",\n        name: \"Carbon Credit\"\n    },\n    {\n        id: \"commodity\",\n        name: \"Commodity\"\n    }\n];\n// Helper function to get contract addresses for the current network\nconst getContractAddresses = (network)=>{\n    return contractAddresses[network] || contractAddresses[defaultNetwork];\n};\n// Helper function to get network configuration for the current network\nconst getNetworkConfig = (network)=>{\n    return networkConfig[network] || networkConfig[defaultNetwork];\n};\n// Helper function to get known tokens for a network\nconst getKnownTokens = (network)=>{\n    return knownTokens[network] || [];\n};\n// Helper to check if a token exists in factory (in a real implementation, this would query the blockchain)\nconst verifyTokenExists = async (network, tokenAddress)=>{\n    // In a real implementation, this would:\n    // 1. Connect to the factory contract\n    // 2. Call a method or check events to verify the token's existence\n    // For demo purposes, we'll just return true\n    return true;\n};\n// Helper function to validate if an address is a known token\nconst isKnownToken = (network, tokenAddress)=>{\n    const tokens = getKnownTokens(network);\n    return tokens.some((token)=>token.address.toLowerCase() === tokenAddress.toLowerCase());\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/config.ts\n"));

/***/ })

});