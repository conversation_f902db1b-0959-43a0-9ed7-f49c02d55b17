{"_format": "hh-sol-artifact-1", "contractName": "Whitelist", "sourceName": "contracts/Whitelist.sol", "abi": [{"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"inputs": [{"internalType": "address", "name": "target", "type": "address"}], "name": "AddressEmptyCode", "type": "error"}, {"inputs": [{"internalType": "address", "name": "implementation", "type": "address"}], "name": "ERC1967InvalidImplementation", "type": "error"}, {"inputs": [], "name": "ERC1967Non<PERSON>ayable", "type": "error"}, {"inputs": [], "name": "FailedCall", "type": "error"}, {"inputs": [], "name": "InvalidInitialization", "type": "error"}, {"inputs": [], "name": "NotInitializing", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"inputs": [], "name": "UUPSUnauthorizedCallContext", "type": "error"}, {"inputs": [{"internalType": "bytes32", "name": "slot", "type": "bytes32"}], "name": "UUPSUnsupportedProxiableUUID", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}], "name": "AddressFrozen", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}], "name": "AddressUnfrozen", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "version", "type": "uint64"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}], "name": "KycApproved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}], "name": "KycRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "implementation", "type": "address"}], "name": "Upgraded", "type": "event"}, {"inputs": [], "name": "AGENT_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "UPGRADE_INTERFACE_VERSION", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "agent", "type": "address"}], "name": "addAgent", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "approveKyc", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "accounts", "type": "address[]"}], "name": "batchAdd<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "accounts", "type": "address[]"}], "name": "batchApproveKyc", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "accounts", "type": "address[]"}], "name": "batchFreezeAddresses", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "accounts", "type": "address[]"}], "name": "batchRemoveFrom<PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "accounts", "type": "address[]"}], "name": "batchRevokeKyc", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "accounts", "type": "address[]"}], "name": "batchUnfreezeAddresses", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}], "name": "canTransfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "freezeAddress", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "admin", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "admin", "type": "address"}], "name": "initializeWithAgent", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "investorCountry", "outputs": [{"internalType": "uint16", "name": "", "type": "uint16"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "isFrozen", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "isKycApproved", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "isVerified", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "proxiableUUID", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "agent", "type": "address"}], "name": "removeAgent", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "revokeKyc", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "unfreeze<PERSON>ddress", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "upgradeToAndCall", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "version", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "pure", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}