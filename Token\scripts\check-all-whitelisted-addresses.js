const { ethers } = require("hardhat");

async function main() {
  const tokenAddress = "******************************************";
  
  console.log("🔍 CHECKING ALL WHITELISTED ADDRESSES");
  console.log("=" .repeat(60));
  console.log("Token Address:", tokenAddress);
  
  const [deployer] = await ethers.getSigners();
  const SecurityTokenMinimal = await ethers.getContractFactory("SecurityTokenMinimal");
  const token = SecurityTokenMinimal.attach(tokenAddress);
  
  // List of known addresses to check
  const knownAddresses = [
    "******************************************", // Your main address
    "******************************************", // Address you tried to whitelist
    "******************************************", // Test address
    "******************************************", // Another test address
    deployer.address, // Deployer address
    "******************************************", // Zero address (should not be whitelisted)
  ];
  
  console.log("\n📋 CHECKING KNOWN ADDRESSES:");
  console.log("-" .repeat(60));
  
  const whitelistedAddresses = [];
  
  for (const address of knownAddresses) {
    try {
      const isWhitelisted = await token.isWhitelisted(address);
      const isKycApproved = await token.isKycApproved(address);
      const isVerified = await token.isVerified(address);
      const balance = await token.balanceOf(address);
      
      console.log(`\n${address}:`);
      console.log(`  Whitelisted: ${isWhitelisted}`);
      console.log(`  KYC Approved: ${isKycApproved}`);
      console.log(`  Verified: ${isVerified}`);
      console.log(`  Balance: ${balance}`);
      
      if (isWhitelisted) {
        whitelistedAddresses.push({
          address,
          isWhitelisted,
          isKycApproved,
          isVerified,
          balance: balance.toString()
        });
      }
      
    } catch (error) {
      console.log(`❌ Error checking ${address}:`, error.message);
    }
  }
  
  console.log("\n🎯 SUMMARY OF WHITELISTED ADDRESSES:");
  console.log("=" .repeat(60));
  console.log(`Total whitelisted addresses found: ${whitelistedAddresses.length}`);
  
  whitelistedAddresses.forEach((addr, index) => {
    console.log(`\n${index + 1}. ${addr.address}`);
    console.log(`   Whitelisted: ✅ ${addr.isWhitelisted}`);
    console.log(`   KYC Approved: ${addr.isKycApproved ? '✅' : '❌'} ${addr.isKycApproved}`);
    console.log(`   Verified: ${addr.isVerified ? '✅' : '❌'} ${addr.isVerified}`);
    console.log(`   Balance: ${addr.balance} tokens`);
  });
  
  // Try to get events to find more whitelisted addresses
  console.log("\n🔍 CHECKING WHITELIST EVENTS:");
  console.log("-" .repeat(60));
  
  try {
    // Get AddressWhitelisted events
    const filter = token.filters.AddressWhitelisted();
    const events = await token.queryFilter(filter, 0, 'latest');
    
    console.log(`Found ${events.length} AddressWhitelisted events:`);
    
    const eventAddresses = new Set();
    
    for (const event of events) {
      const address = event.args[0];
      eventAddresses.add(address);
      console.log(`  - ${address} (Block: ${event.blockNumber})`);
    }
    
    // Check current status of all addresses from events
    console.log("\n📋 CURRENT STATUS OF ADDRESSES FROM EVENTS:");
    console.log("-" .repeat(60));
    
    for (const address of eventAddresses) {
      try {
        const isWhitelisted = await token.isWhitelisted(address);
        const isKycApproved = await token.isKycApproved(address);
        const isVerified = await token.isVerified(address);
        const balance = await token.balanceOf(address);
        
        console.log(`\n${address}:`);
        console.log(`  Currently Whitelisted: ${isWhitelisted}`);
        console.log(`  KYC Approved: ${isKycApproved}`);
        console.log(`  Verified: ${isVerified}`);
        console.log(`  Balance: ${balance}`);
        
      } catch (error) {
        console.log(`❌ Error checking event address ${address}:`, error.message);
      }
    }
    
  } catch (error) {
    console.log("❌ Error fetching events:", error.message);
  }
  
  // Check if there are any other functions we can use to get whitelist info
  console.log("\n🔍 CHECKING FOR ADDITIONAL WHITELIST FUNCTIONS:");
  console.log("-" .repeat(60));
  
  const functionsToCheck = [
    "getWhitelistedAddresses",
    "getAllWhitelisted", 
    "whitelistedAddresses",
    "getWhitelistCount",
    "whitelistCount"
  ];
  
  for (const funcName of functionsToCheck) {
    try {
      const result = await token[funcName]();
      console.log(`✅ ${funcName}():`, result);
    } catch (error) {
      console.log(`❌ ${funcName}(): Not available`);
    }
  }
  
  console.log("\n🎯 FINAL ANALYSIS:");
  console.log("=" .repeat(60));
  console.log("This script checked:");
  console.log("1. Known addresses for whitelist status");
  console.log("2. Historical whitelist events");
  console.log("3. Current status of all addresses that were ever whitelisted");
  console.log("4. Available functions for getting whitelist data");
  console.log("");
  console.log("If the admin panel shows fewer addresses, the issue is likely");
  console.log("in how the admin panel fetches or displays the whitelist data.");
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
