"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/modular-tokens/page",{

/***/ "(app-pages-browser)/./src/app/modular-tokens/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/modular-tokens/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ModularTokensPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useModularToken__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../hooks/useModularToken */ \"(app-pages-browser)/./src/hooks/useModularToken.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n// Import custom hook\n\n// Contract addresses from environment\nconst SECURITY_TOKEN_CORE_ADDRESS = \"******************************************\";\nconst UPGRADE_MANAGER_ADDRESS = \"******************************************\";\nfunction ModularTokensPage() {\n    _s();\n    // Use the custom hook for modular token functionality\n    const { provider, signer, tokenInfo, upgradeInfo, pendingUpgrades, upgradeHistory, loading, error, initializeProvider, mintTokens, togglePause, scheduleUpgrade, toggleEmergencyMode, refreshData, setError } = (0,_hooks_useModularToken__WEBPACK_IMPORTED_MODULE_2__.useModularToken)();\n    // Local state for UI\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mintAmount, setMintAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [mintAddress, setMintAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [upgradeDescription, setUpgradeDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newImplementationAddress, setNewImplementationAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [actionLoading, setActionLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    // Clear success message after 5 seconds\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"ModularTokensPage.useEffect\": ()=>{\n            if (success) {\n                const timer = setTimeout({\n                    \"ModularTokensPage.useEffect.timer\": ()=>setSuccess(null)\n                }[\"ModularTokensPage.useEffect.timer\"], 5000);\n                return ({\n                    \"ModularTokensPage.useEffect\": ()=>clearTimeout(timer)\n                })[\"ModularTokensPage.useEffect\"];\n            }\n        }\n    }[\"ModularTokensPage.useEffect\"], [\n        success\n    ]);\n    // Clear error message after 10 seconds\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"ModularTokensPage.useEffect\": ()=>{\n            if (error) {\n                const timer = setTimeout({\n                    \"ModularTokensPage.useEffect.timer\": ()=>setError(null)\n                }[\"ModularTokensPage.useEffect.timer\"], 10000);\n                return ({\n                    \"ModularTokensPage.useEffect\": ()=>clearTimeout(timer)\n                })[\"ModularTokensPage.useEffect\"];\n            }\n        }\n    }[\"ModularTokensPage.useEffect\"], [\n        error,\n        setError\n    ]);\n    const handleMint = async ()=>{\n        if (!mintAddress || !mintAmount) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            await mintTokens(mintAddress, mintAmount);\n            setSuccess(\"Successfully minted \".concat(mintAmount, \" tokens to \").concat(mintAddress));\n            setMintAmount('');\n            setMintAddress('');\n            await refreshData(); // Refresh token info\n        } catch (error) {\n            console.error('Error minting tokens:', error);\n            setError(\"Failed to mint tokens: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handlePauseToggle = async ()=>{\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            await togglePause();\n            setSuccess(\"Token \".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'unpaused' : 'paused', \" successfully\"));\n            await refreshData(); // Refresh token info\n        } catch (error) {\n            console.error('Error toggling pause:', error);\n            setError(\"Failed to \".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'unpause' : 'pause', \" token: \").concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleScheduleUpgrade = async ()=>{\n        if (!newImplementationAddress || !upgradeDescription) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            await scheduleUpgrade(newImplementationAddress, upgradeDescription);\n            setSuccess('Upgrade scheduled successfully! It will be executable after the 48-hour timelock.');\n            setNewImplementationAddress('');\n            setUpgradeDescription('');\n            await refreshData(); // Refresh upgrade info\n        } catch (error) {\n            console.error('Error scheduling upgrade:', error);\n            setError(\"Failed to schedule upgrade: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleEmergencyModeToggle = async ()=>{\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            await toggleEmergencyMode();\n            setSuccess(\"Emergency mode \".concat((upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? 'deactivated' : 'activated', \" successfully\"));\n            await refreshData(); // Refresh upgrade info\n        } catch (error) {\n            console.error('Error toggling emergency mode:', error);\n            setError(\"Failed to \".concat((upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? 'deactivate' : 'activate', \" emergency mode: \").concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    // Utility functions\n    const formatAddress = (address)=>{\n        return \"\".concat(address.slice(0, 6), \"...\").concat(address.slice(-4));\n    };\n    const formatTimestamp = (timestamp)=>{\n        return new Date(timestamp * 1000).toLocaleString();\n    };\n    const getTimeUntilExecution = (executeTime)=>{\n        const now = Math.floor(Date.now() / 1000);\n        const timeLeft = executeTime - now;\n        if (timeLeft <= 0) return 'Ready to execute';\n        const hours = Math.floor(timeLeft / 3600);\n        const minutes = Math.floor(timeLeft % 3600 / 60);\n        return \"\".concat(hours, \"h \").concat(minutes, \"m remaining\");\n    };\n    if (!provider || !signer) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold mb-2\",\n                        children: \"Connect Wallet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: \"Please connect your MetaMask wallet to manage modular tokens.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: initializeProvider,\n                        className: \"bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\",\n                        children: \"Connect Wallet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                lineNumber: 157,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n            lineNumber: 156,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold\",\n                                children: \"Modular Token Management\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Manage your modular ERC-3643 security tokens and upgrades\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat((upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'),\n                                children: (upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? \"Emergency Mode Active\" : \"Normal Mode\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, this),\n                            tokenInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(tokenInfo.paused ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'),\n                                children: tokenInfo.paused ? \"Paused\" : \"Active\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: refreshData,\n                                disabled: loading,\n                                className: \"bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-1 px-3 rounded text-sm disabled:opacity-50\",\n                                children: [\n                                    loading ? '🔄' : '↻',\n                                    \" Refresh\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-red-600 mr-2\",\n                            children: \"⚠️\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                    lineNumber: 211,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                lineNumber: 210,\n                columnNumber: 9\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-green-600 mr-2\",\n                            children: \"✅\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: success\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                    lineNumber: 220,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                lineNumber: 219,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"-mb-px flex space-x-8\",\n                            children: [\n                                'overview',\n                                'operations',\n                                'upgrades',\n                                'history'\n                            ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(tab),\n                                    className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === tab ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                    children: tab.charAt(0).toUpperCase() + tab.slice(1).replace('-', ' ')\n                                }, tab, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, this),\n                    activeTab === 'overview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg shadow p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium mb-2\",\n                                                children: \"Token Information\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-4\",\n                                                children: \"Current token details and status\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: tokenInfo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Name:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 258,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: tokenInfo.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 259,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 257,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Symbol:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 262,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: tokenInfo.symbol\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 263,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 261,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Version:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 266,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: tokenInfo.version\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 267,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Total Supply:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 270,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: tokenInfo.totalSupply\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 271,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Max Supply:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 274,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: tokenInfo.maxSupply\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 275,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Decimals:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 278,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: tokenInfo.decimals\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 279,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Status:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 282,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(tokenInfo.paused ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'),\n                                                                    children: tokenInfo.paused ? \"Paused\" : \"Active\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 283,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 281,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 294,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Loading token information...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg shadow p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium mb-2\",\n                                                children: \"Contract Addresses\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-4\",\n                                                children: \"Deployed contract addresses on Amoy testnet\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"SecurityTokenCore:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 306,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500 font-mono\",\n                                                                children: SECURITY_TOKEN_CORE_ADDRESS\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 307,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 305,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"UpgradeManager:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 312,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500 font-mono\",\n                                                                children: UPGRADE_MANAGER_ADDRESS\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 313,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 311,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"pt-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>window.open(\"https://amoy.polygonscan.com/address/\".concat(SECURITY_TOKEN_CORE_ADDRESS), '_blank'),\n                                                            className: \"bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-1 px-3 rounded text-sm\",\n                                                            children: \"View on PolygonScan\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 13\n                            }, this),\n                            (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.metadata) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium mb-2\",\n                                        children: \"Token Metadata\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-4\",\n                                        children: \"Additional token information and configuration\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Price:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: tokenInfo.metadata.tokenPrice\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Bonus Tiers:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: tokenInfo.metadata.bonusTiers\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Details:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 mt-1\",\n                                                        children: tokenInfo.metadata.tokenDetails\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'operations' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium mb-2\",\n                                            children: \"Mint Tokens\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"Mint new tokens to a specified address\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"mintAddress\",\n                                                            className: \"block text-sm font-medium text-gray-700\",\n                                                            children: \"Recipient Address\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            id: \"mintAddress\",\n                                                            type: \"text\",\n                                                            placeholder: \"0x...\",\n                                                            value: mintAddress,\n                                                            onChange: (e)=>setMintAddress(e.target.value),\n                                                            className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"mintAmount\",\n                                                            className: \"block text-sm font-medium text-gray-700\",\n                                                            children: \"Amount\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 373,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            id: \"mintAmount\",\n                                                            type: \"number\",\n                                                            placeholder: \"1000\",\n                                                            value: mintAmount,\n                                                            onChange: (e)=>setMintAmount(e.target.value),\n                                                            className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleMint,\n                                                    disabled: actionLoading || !mintAddress || !mintAmount,\n                                                    className: \"w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50\",\n                                                    children: [\n                                                        actionLoading ? '🔄 ' : '',\n                                                        \"Mint Tokens\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 383,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium mb-2\",\n                                            children: \"Token Controls\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"Pause/unpause token transfers and emergency controls\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handlePauseToggle,\n                                                    disabled: actionLoading,\n                                                    className: \"w-full font-bold py-2 px-4 rounded disabled:opacity-50 \".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'bg-green-600 hover:bg-green-700 text-white' : 'bg-red-600 hover:bg-red-700 text-white'),\n                                                    children: [\n                                                        actionLoading ? '🔄 ' : '',\n                                                        (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'Unpause Token' : 'Pause Token'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleEmergencyModeToggle,\n                                                    disabled: actionLoading,\n                                                    className: \"w-full font-bold py-2 px-4 rounded disabled:opacity-50 \".concat((upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? 'bg-green-600 hover:bg-green-700 text-white' : 'bg-red-600 hover:bg-red-700 text-white'),\n                                                    children: [\n                                                        actionLoading ? '🔄 ' : '',\n                                                        (upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: \"✅ Deactivate Emergency Mode\"\n                                                        }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: \"⚡ Activate Emergency Mode\"\n                                                        }, void 0, false)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                        lineNumber: 355,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'upgrades' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium mb-2\",\n                                            children: \"Schedule Upgrade\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"Schedule a new implementation upgrade with 48-hour timelock\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"newImplementation\",\n                                                            className: \"block text-sm font-medium text-gray-700\",\n                                                            children: \"New Implementation Address\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 442,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            id: \"newImplementation\",\n                                                            type: \"text\",\n                                                            placeholder: \"0x...\",\n                                                            value: newImplementationAddress,\n                                                            onChange: (e)=>setNewImplementationAddress(e.target.value),\n                                                            className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 441,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"upgradeDescription\",\n                                                            className: \"block text-sm font-medium text-gray-700\",\n                                                            children: \"Upgrade Description\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 453,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            id: \"upgradeDescription\",\n                                                            placeholder: \"Describe the changes in this upgrade...\",\n                                                            value: upgradeDescription,\n                                                            onChange: (e)=>setUpgradeDescription(e.target.value),\n                                                            rows: 3,\n                                                            className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 454,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleScheduleUpgrade,\n                                                    disabled: actionLoading || !newImplementationAddress || !upgradeDescription,\n                                                    className: \"w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50\",\n                                                    children: [\n                                                        actionLoading ? '🔄 ' : '⏰ ',\n                                                        \"Schedule Upgrade (48h Timelock)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 463,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 440,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium mb-2\",\n                                            children: \"Pending Upgrades\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 475,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"Upgrades waiting for timelock expiration or execution\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 476,\n                                            columnNumber: 17\n                                        }, this),\n                                        pendingUpgrades.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 text-center py-4\",\n                                            children: \"No pending upgrades\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 480,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: pendingUpgrades.map((upgrade)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border rounded-lg p-3 space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-start\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: upgrade.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 488,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(upgrade.executed ? 'bg-green-100 text-green-800' : upgrade.cancelled ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'),\n                                                                    children: upgrade.executed ? \"Executed\" : upgrade.cancelled ? \"Cancelled\" : \"Pending\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 491,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 487,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-500 space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        \"Implementation: \",\n                                                                        upgrade.newImplementation.slice(0, 10),\n                                                                        \"...\",\n                                                                        upgrade.newImplementation.slice(-8)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 502,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        \"Execute Time: \",\n                                                                        new Date(upgrade.executeTime * 1000).toLocaleString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 503,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: getTimeUntilExecution(upgrade.executeTime)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 504,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 501,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, upgrade.upgradeId, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 484,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                    lineNumber: 474,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                            lineNumber: 434,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                        lineNumber: 433,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'history' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium mb-2\",\n                                    children: \"Upgrade History\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                    lineNumber: 518,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: \"Complete history of all upgrades performed on this token\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                    lineNumber: 519,\n                                    columnNumber: 15\n                                }, this),\n                                upgradeHistory.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 text-center py-4\",\n                                    children: \"No upgrade history available\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                    lineNumber: 523,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: upgradeHistory.map((record, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border rounded-lg p-4 space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium\",\n                                                                    children: record.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 532,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: [\n                                                                        new Date(record.timestamp * 1000).toLocaleString(),\n                                                                        \" by \",\n                                                                        record.executor.slice(0, 10),\n                                                                        \"...\",\n                                                                        record.executor.slice(-8)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 533,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 531,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(record.isEmergency ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'),\n                                                                    children: record.isEmergency ? \"Emergency\" : \"Scheduled\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 538,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                record.version && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800\",\n                                                                    children: [\n                                                                        \"v\",\n                                                                        record.version\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 544,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 537,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 530,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-500 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                \"From: \",\n                                                                record.oldImplementation.slice(0, 10),\n                                                                \"...\",\n                                                                record.oldImplementation.slice(-8)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 551,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                \"To: \",\n                                                                record.newImplementation.slice(0, 10),\n                                                                \"...\",\n                                                                record.newImplementation.slice(-8)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 552,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 550,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 529,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                    lineNumber: 527,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                            lineNumber: 517,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                        lineNumber: 516,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                lineNumber: 227,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n        lineNumber: 174,\n        columnNumber: 5\n    }, this);\n}\n_s(ModularTokensPage, \"BIyoDHkM4jemUjrLEtulphKCYxg=\", false, function() {\n    return [\n        _hooks_useModularToken__WEBPACK_IMPORTED_MODULE_2__.useModularToken\n    ];\n});\n_c = ModularTokensPage;\nvar _c;\n$RefreshReg$(_c, \"ModularTokensPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modular-tokens/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useModularToken.ts":
/*!**************************************!*\
  !*** ./src/hooks/useModularToken.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useModularToken: () => (/* binding */ useModularToken)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/crypto/keccak.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/utf8.js\");\n/* harmony import */ var _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contracts/SecurityTokenCore.json */ \"(app-pages-browser)/./src/contracts/SecurityTokenCore.json\");\n/* harmony import */ var _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contracts/UpgradeManager.json */ \"(app-pages-browser)/./src/contracts/UpgradeManager.json\");\n/* __next_internal_client_entry_do_not_use__ useModularToken auto */ \n\n// Import ABIs\n\n\n// Contract addresses from environment\nconst SECURITY_TOKEN_CORE_ADDRESS = \"******************************************\";\nconst UPGRADE_MANAGER_ADDRESS = \"******************************************\";\nfunction useModularToken() {\n    const [provider, setProvider] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [signer, setSigner] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [tokenInfo, setTokenInfo] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [upgradeInfo, setUpgradeInfo] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [pendingUpgrades, setPendingUpgrades] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [upgradeHistory, setUpgradeHistory] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // Initialize provider and signer\n    const initializeProvider = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[initializeProvider]\": async ()=>{\n            try {\n                if ( true && window.ethereum) {\n                    const provider = new ethers__WEBPACK_IMPORTED_MODULE_3__.BrowserProvider(window.ethereum);\n                    await provider.send('eth_requestAccounts', []);\n                    const signer = await provider.getSigner();\n                    setProvider(provider);\n                    setSigner(signer);\n                    return {\n                        provider,\n                        signer\n                    };\n                } else {\n                    throw new Error('MetaMask not found');\n                }\n            } catch (error) {\n                console.error('Error initializing provider:', error);\n                setError('Failed to connect to wallet');\n                return null;\n            }\n        }\n    }[\"useModularToken.useCallback[initializeProvider]\"], []);\n    // Load token information\n    const loadTokenInfo = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[loadTokenInfo]\": async ()=>{\n            if (!provider || !SECURITY_TOKEN_CORE_ADDRESS) return;\n            try {\n                setLoading(true);\n                const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_1__, provider);\n                const [name, symbol, version, totalSupply, maxSupply, decimals, paused, metadata] = await Promise.all([\n                    contract.name(),\n                    contract.symbol(),\n                    contract.version(),\n                    contract.totalSupply(),\n                    contract.maxSupply(),\n                    contract.decimals(),\n                    contract.paused(),\n                    contract.getTokenMetadata()\n                ]);\n                setTokenInfo({\n                    name,\n                    symbol,\n                    version,\n                    totalSupply: ethers__WEBPACK_IMPORTED_MODULE_5__.formatUnits(totalSupply, decimals),\n                    maxSupply: ethers__WEBPACK_IMPORTED_MODULE_5__.formatUnits(maxSupply, decimals),\n                    decimals,\n                    paused,\n                    metadata: {\n                        tokenPrice: metadata[0],\n                        bonusTiers: metadata[1],\n                        tokenDetails: metadata[2],\n                        tokenImageUrl: metadata[3]\n                    }\n                });\n            } catch (error) {\n                console.error('Error loading token info:', error);\n                setError('Failed to load token information');\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useModularToken.useCallback[loadTokenInfo]\"], [\n        provider\n    ]);\n    // Load upgrade information\n    const loadUpgradeInfo = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[loadUpgradeInfo]\": async ()=>{\n            if (!provider || !UPGRADE_MANAGER_ADDRESS) return;\n            try {\n                setLoading(true);\n                const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__, provider);\n                const [emergencyModeActive, registeredModules, upgradeDelay, emergencyModeDuration, pendingUpgradeIds] = await Promise.all([\n                    contract.isEmergencyModeActive(),\n                    contract.getRegisteredModules(),\n                    contract.UPGRADE_DELAY(),\n                    contract.EMERGENCY_MODE_DURATION(),\n                    contract.getPendingUpgradeIds()\n                ]);\n                setUpgradeInfo({\n                    emergencyModeActive,\n                    registeredModules,\n                    upgradeDelay: Number(upgradeDelay),\n                    emergencyModeDuration: Number(emergencyModeDuration)\n                });\n                // Load pending upgrades\n                const pendingUpgradesData = await Promise.all(pendingUpgradeIds.map({\n                    \"useModularToken.useCallback[loadUpgradeInfo]\": async (id)=>{\n                        const upgrade = await contract.pendingUpgrades(id);\n                        return {\n                            upgradeId: id,\n                            moduleId: upgrade.moduleId,\n                            proxy: upgrade.proxy,\n                            newImplementation: upgrade.newImplementation,\n                            executeTime: Number(upgrade.executeTime),\n                            executed: upgrade.executed,\n                            cancelled: upgrade.cancelled,\n                            description: upgrade.description\n                        };\n                    }\n                }[\"useModularToken.useCallback[loadUpgradeInfo]\"]));\n                setPendingUpgrades(pendingUpgradesData);\n                // Load upgrade history for SecurityTokenCore\n                const SECURITY_TOKEN_CORE_ID = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"SECURITY_TOKEN_CORE\"));\n                const history = await contract.getUpgradeHistory(SECURITY_TOKEN_CORE_ID);\n                setUpgradeHistory(history.map({\n                    \"useModularToken.useCallback[loadUpgradeInfo]\": (record)=>({\n                            oldImplementation: record.oldImplementation,\n                            newImplementation: record.newImplementation,\n                            timestamp: Number(record.timestamp),\n                            executor: record.executor,\n                            version: record.version,\n                            isEmergency: record.isEmergency,\n                            description: record.description\n                        })\n                }[\"useModularToken.useCallback[loadUpgradeInfo]\"]));\n            } catch (error) {\n                console.error('Error loading upgrade info:', error);\n                setError('Failed to load upgrade information');\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useModularToken.useCallback[loadUpgradeInfo]\"], [\n        provider\n    ]);\n    // Mint tokens\n    const mintTokens = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[mintTokens]\": async (address, amount)=>{\n            if (!signer || !SECURITY_TOKEN_CORE_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_1__, signer);\n            const decimals = (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals) || 0;\n            const amountWei = ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits(amount, decimals);\n            const tx = await contract.mint(address, amountWei);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[mintTokens]\"], [\n        signer,\n        tokenInfo\n    ]);\n    // Toggle pause state\n    const togglePause = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[togglePause]\": async ()=>{\n            if (!signer || !SECURITY_TOKEN_CORE_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_1__, signer);\n            const tx = (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? await contract.unpause() : await contract.pause();\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[togglePause]\"], [\n        signer,\n        tokenInfo\n    ]);\n    // Schedule upgrade\n    const scheduleUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[scheduleUpgrade]\": async (implementationAddress, description)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__, signer);\n            const SECURITY_TOKEN_CORE_ID = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"SECURITY_TOKEN_CORE\"));\n            const tx = await contract.scheduleUpgrade(SECURITY_TOKEN_CORE_ID, implementationAddress, description);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[scheduleUpgrade]\"], [\n        signer\n    ]);\n    // Execute upgrade\n    const executeUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[executeUpgrade]\": async (upgradeId)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__, signer);\n            const tx = await contract.executeUpgrade(upgradeId);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[executeUpgrade]\"], [\n        signer\n    ]);\n    // Emergency upgrade\n    const emergencyUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[emergencyUpgrade]\": async (implementationAddress, description)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__, signer);\n            const SECURITY_TOKEN_CORE_ID = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"SECURITY_TOKEN_CORE\"));\n            const tx = await contract.emergencyUpgrade(SECURITY_TOKEN_CORE_ID, implementationAddress, description);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[emergencyUpgrade]\"], [\n        signer\n    ]);\n    // Toggle emergency mode\n    const toggleEmergencyMode = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[toggleEmergencyMode]\": async ()=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__, signer);\n            const tx = (upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? await contract.deactivateEmergencyMode() : await contract.activateEmergencyMode();\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[toggleEmergencyMode]\"], [\n        signer,\n        upgradeInfo\n    ]);\n    // Cancel upgrade\n    const cancelUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[cancelUpgrade]\": async (upgradeId)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__, signer);\n            const tx = await contract.cancelUpgrade(upgradeId);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[cancelUpgrade]\"], [\n        signer\n    ]);\n    // Refresh all data\n    const refreshData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[refreshData]\": async ()=>{\n            await Promise.all([\n                loadTokenInfo(),\n                loadUpgradeInfo()\n            ]);\n        }\n    }[\"useModularToken.useCallback[refreshData]\"], [\n        loadTokenInfo,\n        loadUpgradeInfo\n    ]);\n    // Initialize on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useModularToken.useEffect\": ()=>{\n            initializeProvider();\n        }\n    }[\"useModularToken.useEffect\"], [\n        initializeProvider\n    ]);\n    // Load data when provider is ready\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useModularToken.useEffect\": ()=>{\n            if (provider && signer) {\n                refreshData();\n            }\n        }\n    }[\"useModularToken.useEffect\"], [\n        provider,\n        signer,\n        refreshData\n    ]);\n    return {\n        // State\n        provider,\n        signer,\n        tokenInfo,\n        upgradeInfo,\n        pendingUpgrades,\n        upgradeHistory,\n        loading,\n        error,\n        // Actions\n        initializeProvider,\n        loadTokenInfo,\n        loadUpgradeInfo,\n        mintTokens,\n        togglePause,\n        scheduleUpgrade,\n        executeUpgrade,\n        emergencyUpgrade,\n        toggleEmergencyMode,\n        cancelUpgrade,\n        refreshData,\n        // Utilities\n        setError,\n        setLoading\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useModularToken.ts\n"));

/***/ })

});