// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

/**
 * @title ICompliance
 * @dev Interface for compliance contract
 */
interface ICompliance {
    /**
     * @dev Check if a transfer is compliant
     * @param from Sender address
     * @param to Recipient address
     * @param amount Transfer amount
     * @return bool True if transfer is compliant
     */
    function canTransfer(address from, address to, uint256 amount) external view returns (bool);
    
    /**
     * @dev Get compliance rules count
     * @return uint256 Number of compliance rules
     */
    function getRulesCount() external view returns (uint256);
    
    /**
     * @dev Check if compliance is enabled
     * @return bool True if compliance is enabled
     */
    function isComplianceEnabled() external view returns (bool);
}
