// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";

/**
 * @title SecurityModule
 * @dev Handles security features like emergency controls and access management
 */
contract SecurityModule is AccessControl, ReentrancyGuard {
    // State variables
    address public securityToken;
    bool private _emergencyPaused;
    mapping(bytes4 => bool) private _functionPaused;
    
    // Constants
    uint256 private constant MAX_AGENTS = 50;
    
    // Events
    event EmergencyPaused(address indexed admin);
    event EmergencyUnpaused(address indexed admin);
    event FunctionPaused(bytes4 indexed functionSelector, address indexed admin);
    event FunctionUnpaused(bytes4 indexed functionSelector, address indexed admin);
    
    // Modifiers
    modifier onlySecurityToken() {
        require(msg.sender == securityToken, "SecurityModule: caller is not the security token");
        _;
    }
    
    modifier onlyTokenAdmin() {
        require(hasRole(DEFAULT_ADMIN_ROLE, msg.sender), "SecurityModule: caller is not admin");
        _;
    }
    
    /**
     * @dev Constructor
     */
    constructor(address _securityToken, address _admin) {
        require(_securityToken != address(0), "SecurityModule: invalid token address");
        require(_admin != address(0), "SecurityModule: invalid admin address");
        
        securityToken = _securityToken;
        _grantRole(DEFAULT_ADMIN_ROLE, _admin);
    }
    
    /**
     * @dev Emergency pause all contract functions
     */
    function emergencyPause() external onlyTokenAdmin {
        _emergencyPaused = true;
        emit EmergencyPaused(msg.sender);
    }
    
    /**
     * @dev Emergency unpause all contract functions
     */
    function emergencyUnpause() external onlyTokenAdmin {
        _emergencyPaused = false;
        emit EmergencyUnpaused(msg.sender);
    }
    
    /**
     * @dev Pause a specific function
     */
    function pauseFunction(bytes4 functionSelector) external onlyTokenAdmin {
        _functionPaused[functionSelector] = true;
        emit FunctionPaused(functionSelector, msg.sender);
    }
    
    /**
     * @dev Unpause a specific function
     */
    function unpauseFunction(bytes4 functionSelector) external onlyTokenAdmin {
        _functionPaused[functionSelector] = false;
        emit FunctionUnpaused(functionSelector, msg.sender);
    }
    
    /**
     * @dev Check if emergency pause is active
     */
    function isEmergencyPaused() external view returns (bool) {
        return _emergencyPaused;
    }
    
    /**
     * @dev Check if a specific function is paused
     */
    function isFunctionPaused(bytes4 functionSelector) external view returns (bool) {
        return _functionPaused[functionSelector];
    }
    
    /**
     * @dev Validate that operation is allowed
     */
    function validateOperation(bytes4 functionSelector) 
        external 
        view 
        onlySecurityToken 
    {
        require(!_emergencyPaused, "SecurityModule: emergency paused");
        require(!_functionPaused[functionSelector], "SecurityModule: function paused");
    }
}
