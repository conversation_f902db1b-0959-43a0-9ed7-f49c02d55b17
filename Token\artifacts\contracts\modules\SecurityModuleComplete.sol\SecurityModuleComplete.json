{"_format": "hh-sol-artifact-1", "contractName": "SecurityModuleComplete", "sourceName": "contracts/modules/SecurityModuleComplete.sol", "abi": [{"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "InvalidInitialization", "type": "error"}, {"inputs": [], "name": "NotInitializing", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "admin", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "EmergencyPaused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "admin", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "EmergencyUnpaused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes4", "name": "functionSelector", "type": "bytes4"}, {"indexed": true, "internalType": "address", "name": "admin", "type": "address"}], "name": "FunctionPaused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes4", "name": "functionSelector", "type": "bytes4"}, {"indexed": true, "internalType": "address", "name": "admin", "type": "address"}], "name": "FunctionUnpaused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "version", "type": "uint64"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "oldLevel", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newLevel", "type": "uint256"}], "name": "SecurityLevelChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": false, "internalType": "string", "name": "reason", "type": "string"}], "name": "SuspiciousActivity", "type": "event"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_AGENTS", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_BATCH_SIZE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "actionCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes4[]", "name": "functionSelectors", "type": "bytes4[]"}], "name": "batchPauseFunctions", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4[]", "name": "functionSelectors", "type": "bytes4[]"}], "name": "batchUnpauseFunctions", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "emergencyPause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "emergencyUnpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "getAccountSecurityInfo", "outputs": [{"internalType": "uint256", "name": "lastAction", "type": "uint256"}, {"internalType": "uint256", "name": "dailyActionCount", "type": "uint256"}, {"internalType": "bool", "name": "isRestricted", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getSecurityStatus", "outputs": [{"internalType": "bool", "name": "emergencyPaused", "type": "bool"}, {"internalType": "uint256", "name": "currentSecurityLevel", "type": "uint256"}, {"internalType": "uint256", "name": "totalPausedFunctions", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_securityToken", "type": "address"}, {"internalType": "address", "name": "_admin", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "isEmergencyPaused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "functionSelector", "type": "bytes4"}], "name": "isFunctionPaused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "lastActionTimestamp", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "functionSelector", "type": "bytes4"}], "name": "pauseFunction", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "string", "name": "reason", "type": "string"}], "name": "reportSuspiciousActivity", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "securityLevel", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "securityToken", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_level", "type": "uint256"}], "name": "setSecurityLevel", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "functionSelector", "type": "bytes4"}], "name": "unpauseFunction", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "functionSelector", "type": "bytes4"}, {"internalType": "address", "name": "caller", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "validateOperation", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}