// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "./IdentityRegistry.sol";

/**
 * @title Compliance
 * @dev ERC-3643 compliant Compliance contract
 * Handles transfer rules, compliance checks, and state tracking
 * Works with IdentityRegistry for comprehensive compliance management
 */
contract Compliance is
    Initializable,
    AccessControlUpgradeable,
    ReentrancyGuardUpgradeable,
    UUPSUpgradeable
{
    bytes32 public constant AGENT_ROLE = keccak256("AGENT_ROLE");
    bytes32 public constant TOKEN_ROLE = keccak256("TOKEN_ROLE");

    // Constants for default compliance rule
    uint256 private constant DEFAULT_MAX_HOLDERS = 10000;
    uint256 private constant DEFAULT_MAX_TOKENS_PER_HOLDER = type(uint256).max;
    uint256 private constant DEFAULT_MAX_TOTAL_SUPPLY = type(uint256).max;
    uint256 private constant MAX_COMPLIANCE_RULES = 50;

    // Compliance rules structure
    struct ComplianceRule {
        bool isActive;
        uint256 maxHolders;           // Maximum number of token holders
        uint256 maxTokensPerHolder;   // Maximum tokens per individual holder
        uint256 maxTotalSupply;       // Maximum total supply allowed
        mapping(uint16 => uint256) countryLimits;  // Per-country holder limits
        mapping(uint16 => bool) restrictedCountries; // Restricted countries for this rule
    }

    // Transfer tracking for compliance
    struct TransferData {
        uint256 totalTransfers;
        uint256 lastTransferTime;
        mapping(address => uint256) transferCount;
        mapping(address => uint256) lastTransferTimeByAddress;
    }

    // State variables
    IdentityRegistry public identityRegistry;
    address public tokenAddress;
    
    // Compliance rules
    mapping(bytes32 => ComplianceRule) private _complianceRules;
    bytes32[] private _activeRuleIds;
    mapping(bytes32 => bool) private _isActiveRule;
    
    // Transfer tracking
    TransferData private _transferData;
    
    // Holder tracking
    mapping(address => uint256) private _holderBalances;
    address[] private _holders;
    mapping(address => uint256) private _holderIndex;
    uint256 private _totalHolders;
    
    // Country-based holder tracking
    mapping(uint16 => uint256) private _countryHolderCount;
    mapping(uint16 => address[]) private _countryHolders;
    mapping(uint16 => mapping(address => uint256)) private _countryHolderIndex;

    // Events
    event ComplianceRuleAdded(bytes32 indexed ruleId, string name);
    event ComplianceRuleUpdated(bytes32 indexed ruleId, string name);
    event ComplianceRuleRemoved(bytes32 indexed ruleId);
    event TransferCompliance(address indexed from, address indexed to, uint256 amount, bool allowed);
    event HolderAdded(address indexed holder, uint16 indexed country);
    event HolderRemoved(address indexed holder, uint16 indexed country);
    event IdentityRegistryUpdated(address indexed oldRegistry, address indexed newRegistry);

    /**
     * @dev Initialize the contract
     * @param admin The address to be granted DEFAULT_ADMIN_ROLE
     * @param _identityRegistry Address of the IdentityRegistry contract
     */
    function initialize(address admin, address _identityRegistry) public initializer {
        require(admin != address(0), "Compliance: admin cannot be zero address");
        require(_identityRegistry != address(0), "Compliance: identity registry cannot be zero address");
        
        __AccessControl_init();
        __ReentrancyGuard_init();
        __UUPSUpgradeable_init();
        
        _grantRole(DEFAULT_ADMIN_ROLE, admin);
        _grantRole(AGENT_ROLE, admin);
        
        identityRegistry = IdentityRegistry(_identityRegistry);
        
        // Create default compliance rule
        _createDefaultRule();
    }

    /**
     * @dev Set the token address (can only be called once)
     * @param _tokenAddress Address of the security token
     */
    function setTokenAddress(address _tokenAddress) external {
        require(tokenAddress == address(0), "Compliance: token address already set");
        require(_tokenAddress != address(0), "Compliance: token address cannot be zero");

        // Allow the token to set its own address during initialization
        // or allow admin to set it
        require(
            msg.sender == _tokenAddress || hasRole(DEFAULT_ADMIN_ROLE, msg.sender),
            "Compliance: only token or admin can set token address"
        );

        tokenAddress = _tokenAddress;
        _grantRole(TOKEN_ROLE, _tokenAddress);
    }

    // ============================================================================
    // CORE COMPLIANCE FUNCTIONS (ERC-3643 Interface)
    // ============================================================================

    /**
     * @dev Check if a transfer is compliant
     * @param from Sender address
     * @param to Recipient address
     * @param amount Amount to transfer
     * @return bool True if transfer is allowed
     */
    function canTransfer(address from, address to, uint256 amount) external view returns (bool) {
        // Check identity registry compliance first
        if (!identityRegistry.canTransfer(from, to)) {
            return false;
        }
        
        // Check all active compliance rules
        for (uint256 i = 0; i < _activeRuleIds.length; i++) {
            bytes32 ruleId = _activeRuleIds[i];
            if (!_checkRuleCompliance(ruleId, from, to, amount)) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * @dev Called after a transfer is executed (for state tracking)
     * @param from Sender address
     * @param to Recipient address
     * @param amount Amount transferred
     */
    function transferred(address from, address to, uint256 amount) external onlyRole(TOKEN_ROLE) {
        // Update transfer tracking
        _transferData.totalTransfers++;
        _transferData.lastTransferTime = block.timestamp;
        _transferData.transferCount[from]++;
        _transferData.transferCount[to]++;
        _transferData.lastTransferTimeByAddress[from] = block.timestamp;
        _transferData.lastTransferTimeByAddress[to] = block.timestamp;
        
        // Update holder tracking if needed
        _updateHolderTracking(from, to, amount);
        
        emit TransferCompliance(from, to, amount, true);
    }

    /**
     * @dev Called after tokens are created (minted)
     * @param to Recipient address
     * @param amount Amount created
     */
    function created(address to, uint256 amount) external onlyRole(TOKEN_ROLE) {
        // Update holder tracking
        _updateHolderTracking(address(0), to, amount);
    }

    /**
     * @dev Called after tokens are destroyed (burned)
     * @param from Address from which tokens are destroyed
     * @param amount Amount destroyed
     */
    function destroyed(address from, uint256 amount) external onlyRole(TOKEN_ROLE) {
        // Update holder tracking
        _updateHolderTracking(from, address(0), amount);
    }

    // ============================================================================
    // COMPLIANCE RULE MANAGEMENT
    // ============================================================================

    /**
     * @dev Add a new compliance rule
     * @param ruleId Unique identifier for the rule
     * @param name Human-readable name for the rule
     * @param maxHolders Maximum number of holders allowed
     * @param maxTokensPerHolder Maximum tokens per holder
     * @param maxTotalSupply Maximum total supply
     */
    function addComplianceRule(
        bytes32 ruleId,
        string calldata name,
        uint256 maxHolders,
        uint256 maxTokensPerHolder,
        uint256 maxTotalSupply
    ) external onlyRole(AGENT_ROLE) {
        require(ruleId != bytes32(0), "Compliance: rule ID cannot be zero");
        require(!_isActiveRule[ruleId], "Compliance: rule already exists");
        require(bytes(name).length > 0, "Compliance: rule name cannot be empty");
        require(bytes(name).length <= 100, "Compliance: rule name too long");

        // Input validation for reasonable limits
        if (maxHolders > 0) {
            require(maxHolders >= 1, "Compliance: max holders must be at least 1");
            require(maxHolders <= 1000000, "Compliance: max holders limit too high");
        }

        if (maxTokensPerHolder > 0) {
            require(maxTokensPerHolder >= 1, "Compliance: max tokens per holder must be at least 1");
        }

        if (maxTotalSupply > 0) {
            require(maxTotalSupply >= 1, "Compliance: max total supply must be at least 1");
        }

        // Ensure we don't have too many rules (gas limit protection)
        require(_activeRuleIds.length < MAX_COMPLIANCE_RULES, "Compliance: too many active rules");

        ComplianceRule storage rule = _complianceRules[ruleId];
        rule.isActive = true;
        rule.maxHolders = maxHolders;
        rule.maxTokensPerHolder = maxTokensPerHolder;
        rule.maxTotalSupply = maxTotalSupply;

        _activeRuleIds.push(ruleId);
        _isActiveRule[ruleId] = true;

        emit ComplianceRuleAdded(ruleId, name);
    }

    /**
     * @dev Update an existing compliance rule
     * @param ruleId Rule identifier to update
     * @param name Updated name
     * @param maxHolders Updated max holders
     * @param maxTokensPerHolder Updated max tokens per holder
     * @param maxTotalSupply Updated max total supply
     */
    function updateComplianceRule(
        bytes32 ruleId,
        string calldata name,
        uint256 maxHolders,
        uint256 maxTokensPerHolder,
        uint256 maxTotalSupply
    ) external onlyRole(AGENT_ROLE) {
        require(_isActiveRule[ruleId], "Compliance: rule does not exist");
        
        ComplianceRule storage rule = _complianceRules[ruleId];
        rule.maxHolders = maxHolders;
        rule.maxTokensPerHolder = maxTokensPerHolder;
        rule.maxTotalSupply = maxTotalSupply;
        
        emit ComplianceRuleUpdated(ruleId, name);
    }

    /**
     * @dev Remove a compliance rule
     * @param ruleId Rule identifier to remove
     */
    function removeComplianceRule(bytes32 ruleId) external onlyRole(AGENT_ROLE) {
        require(_isActiveRule[ruleId], "Compliance: rule does not exist");
        
        // Remove from active rules array
        for (uint256 i = 0; i < _activeRuleIds.length; i++) {
            if (_activeRuleIds[i] == ruleId) {
                _activeRuleIds[i] = _activeRuleIds[_activeRuleIds.length - 1];
                _activeRuleIds.pop();
                break;
            }
        }
        
        delete _complianceRules[ruleId];
        _isActiveRule[ruleId] = false;
        
        emit ComplianceRuleRemoved(ruleId);
    }

    /**
     * @dev Set country limit for a compliance rule
     * @param ruleId Rule identifier
     * @param country ISO-3166 country code
     * @param limit Maximum holders from this country
     */
    function setCountryLimit(bytes32 ruleId, uint16 country, uint256 limit) external onlyRole(AGENT_ROLE) {
        require(_isActiveRule[ruleId], "Compliance: rule does not exist");
        
        _complianceRules[ruleId].countryLimits[country] = limit;
    }

    /**
     * @dev Restrict a country for a compliance rule
     * @param ruleId Rule identifier
     * @param country ISO-3166 country code
     * @param restricted True to restrict, false to allow
     */
    function setCountryRestriction(bytes32 ruleId, uint16 country, bool restricted) external onlyRole(AGENT_ROLE) {
        require(_isActiveRule[ruleId], "Compliance: rule does not exist");
        
        _complianceRules[ruleId].restrictedCountries[country] = restricted;
    }

    // ============================================================================
    // INTERNAL FUNCTIONS
    // ============================================================================

    /**
     * @dev Create default compliance rule
     */
    function _createDefaultRule() internal {
        bytes32 defaultRuleId = keccak256("DEFAULT_RULE");
        
        ComplianceRule storage rule = _complianceRules[defaultRuleId];
        rule.isActive = true;
        rule.maxHolders = DEFAULT_MAX_HOLDERS;
        rule.maxTokensPerHolder = DEFAULT_MAX_TOKENS_PER_HOLDER;
        rule.maxTotalSupply = DEFAULT_MAX_TOTAL_SUPPLY;
        
        _activeRuleIds.push(defaultRuleId);
        _isActiveRule[defaultRuleId] = true;
        
        emit ComplianceRuleAdded(defaultRuleId, "Default Rule");
    }

    /**
     * @dev Check if a transfer complies with a specific rule
     * @param ruleId Rule to check against
     * @param to Recipient address
     * @param amount Amount to transfer
     * @return bool True if compliant
     */
    function _checkRuleCompliance(
        bytes32 ruleId,
        address, /* from - unused but kept for interface compatibility */
        address to,
        uint256 amount
    ) internal view returns (bool) {
        ComplianceRule storage rule = _complianceRules[ruleId];

        if (!rule.isActive) return true;

        // Check if recipient country is restricted
        if (to != address(0)) {
            uint16 toCountry = identityRegistry.investorCountry(to);
            if (rule.restrictedCountries[toCountry]) {
                return false;
            }

            // Check country limits
            uint256 countryLimit = rule.countryLimits[toCountry];
            if (countryLimit > 0 && _countryHolderCount[toCountry] >= countryLimit) {
                // Only allow if recipient already holds tokens
                if (_holderBalances[to] == 0) {
                    return false;
                }
            }
        }

        // Check max holders limit
        if (rule.maxHolders > 0 && to != address(0) && _holderBalances[to] == 0) {
            if (_totalHolders >= rule.maxHolders) {
                return false;
            }
        }

        // Check max tokens per holder
        if (rule.maxTokensPerHolder > 0 && to != address(0)) {
            if (_holderBalances[to] + amount > rule.maxTokensPerHolder) {
                return false;
            }
        }

        return true;
    }

    /**
     * @dev Update holder tracking after transfer
     * @param from Sender address (address(0) for minting)
     * @param to Recipient address (address(0) for burning)
     * @param amount Amount transferred
     */
    function _updateHolderTracking(address from, address to, uint256 amount) internal {
        // Handle sender (from)
        if (from != address(0)) {
            require(_holderBalances[from] >= amount, "Compliance: insufficient balance for tracking");
            _holderBalances[from] -= amount;

            // Remove from holders if balance becomes zero
            if (_holderBalances[from] == 0) {
                _removeHolder(from);
            }
        }

        // Handle recipient (to)
        if (to != address(0)) {
            bool wasNewHolder = (_holderBalances[to] == 0);
            _holderBalances[to] += amount;

            // Add to holders if this is a new holder
            if (wasNewHolder) {
                _addHolder(to);
            }
        }
    }

    /**
     * @dev Add a new holder to tracking
     * @param holder Address to add
     */
    function _addHolder(address holder) internal {
        require(_holderBalances[holder] > 0, "Compliance: cannot add holder with zero balance");

        // Add to main holders array
        _holders.push(holder);
        _holderIndex[holder] = _holders.length - 1;
        _totalHolders++;

        // Add to country-specific tracking
        uint16 country = identityRegistry.investorCountry(holder);
        _countryHolders[country].push(holder);
        _countryHolderIndex[country][holder] = _countryHolders[country].length - 1;
        _countryHolderCount[country]++;

        emit HolderAdded(holder, country);
    }

    /**
     * @dev Remove a holder from tracking
     * @param holder Address to remove
     */
    function _removeHolder(address holder) internal {
        require(_holderBalances[holder] == 0, "Compliance: cannot remove holder with non-zero balance");

        uint16 country = identityRegistry.investorCountry(holder);

        // Remove from main holders array
        uint256 index = _holderIndex[holder];
        uint256 lastIndex = _holders.length - 1;

        if (index != lastIndex) {
            address lastHolder = _holders[lastIndex];
            _holders[index] = lastHolder;
            _holderIndex[lastHolder] = index;
        }

        _holders.pop();
        delete _holderIndex[holder];
        _totalHolders--;

        // Remove from country-specific tracking
        uint256 countryIndex = _countryHolderIndex[country][holder];
        uint256 lastCountryIndex = _countryHolders[country].length - 1;

        if (countryIndex != lastCountryIndex) {
            address lastCountryHolder = _countryHolders[country][lastCountryIndex];
            _countryHolders[country][countryIndex] = lastCountryHolder;
            _countryHolderIndex[country][lastCountryHolder] = countryIndex;
        }

        _countryHolders[country].pop();
        delete _countryHolderIndex[country][holder];
        _countryHolderCount[country]--;

        emit HolderRemoved(holder, country);
    }

    // ============================================================================
    // VIEW FUNCTIONS
    // ============================================================================

    /**
     * @dev Get compliance rule details
     * @param ruleId Rule identifier
     * @return isActive Whether rule is active
     * @return maxHolders Maximum holders allowed
     * @return maxTokensPerHolder Maximum tokens per holder
     * @return maxTotalSupply Maximum total supply
     */
    function getComplianceRule(bytes32 ruleId) external view returns (
        bool isActive,
        uint256 maxHolders,
        uint256 maxTokensPerHolder,
        uint256 maxTotalSupply
    ) {
        ComplianceRule storage rule = _complianceRules[ruleId];
        return (
            rule.isActive,
            rule.maxHolders,
            rule.maxTokensPerHolder,
            rule.maxTotalSupply
        );
    }

    /**
     * @dev Get country limit for a rule
     * @param ruleId Rule identifier
     * @param country ISO-3166 country code
     * @return uint256 Country limit
     */
    function getCountryLimit(bytes32 ruleId, uint16 country) external view returns (uint256) {
        return _complianceRules[ruleId].countryLimits[country];
    }

    /**
     * @dev Check if country is restricted for a rule
     * @param ruleId Rule identifier
     * @param country ISO-3166 country code
     * @return bool True if restricted
     */
    function isCountryRestricted(bytes32 ruleId, uint16 country) external view returns (bool) {
        return _complianceRules[ruleId].restrictedCountries[country];
    }

    /**
     * @dev Get all active rule IDs
     * @return bytes32[] Array of active rule IDs
     */
    function getActiveRuleIds() external view returns (bytes32[] memory) {
        return _activeRuleIds;
    }

    /**
     * @dev Get total number of holders
     * @return uint256 Total holders
     */
    function getTotalHolders() external view returns (uint256) {
        return _totalHolders;
    }

    /**
     * @dev Get holder count for a country
     * @param country ISO-3166 country code
     * @return uint256 Holder count
     */
    function getCountryHolderCount(uint16 country) external view returns (uint256) {
        return _countryHolderCount[country];
    }

    /**
     * @dev Get transfer statistics
     * @return totalTransfers Total number of transfers
     * @return lastTransferTime Timestamp of last transfer
     */
    function getTransferStats() external view returns (uint256 totalTransfers, uint256 lastTransferTime) {
        return (_transferData.totalTransfers, _transferData.lastTransferTime);
    }

    /**
     * @dev Update identity registry address
     * @param newIdentityRegistry New IdentityRegistry contract address
     */
    function updateIdentityRegistry(address newIdentityRegistry) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(newIdentityRegistry != address(0), "Compliance: identity registry cannot be zero address");

        address oldRegistry = address(identityRegistry);
        identityRegistry = IdentityRegistry(newIdentityRegistry);

        emit IdentityRegistryUpdated(oldRegistry, newIdentityRegistry);
    }

    // ============================================================================
    // UPGRADE AUTHORIZATION
    // ============================================================================

    /**
     * @dev Function to authorize an upgrade
     * @param newImplementation The address of the new implementation
     */
    function _authorizeUpgrade(address newImplementation)
        internal
        override
        onlyRole(DEFAULT_ADMIN_ROLE)
    {}
}
