/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/qualifications/[id]/approve/route";
exports.ids = ["app/api/qualifications/[id]/approve/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fqualifications%2F%5Bid%5D%2Fapprove%2Froute&page=%2Fapi%2Fqualifications%2F%5Bid%5D%2Fapprove%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqualifications%2F%5Bid%5D%2Fapprove%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fqualifications%2F%5Bid%5D%2Fapprove%2Froute&page=%2Fapi%2Fqualifications%2F%5Bid%5D%2Fapprove%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqualifications%2F%5Bid%5D%2Fapprove%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_tokendev_newroo_admin_panel_src_app_api_qualifications_id_approve_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/qualifications/[id]/approve/route.ts */ \"(rsc)/./src/app/api/qualifications/[id]/approve/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/qualifications/[id]/approve/route\",\n        pathname: \"/api/qualifications/[id]/approve\",\n        filename: \"route\",\n        bundlePath: \"app/api/qualifications/[id]/approve/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api\\\\qualifications\\\\[id]\\\\approve\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_tokendev_newroo_admin_panel_src_app_api_qualifications_id_approve_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fqualifications%2F%5Bid%5D%2Fapprove%2Froute&page=%2Fapi%2Fqualifications%2F%5Bid%5D%2Fapprove%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqualifications%2F%5Bid%5D%2Fapprove%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/qualifications/[id]/approve/route.ts":
/*!**********************************************************!*\
  !*** ./src/app/api/qualifications/[id]/approve/route.ts ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\nasync function POST(request, { params }) {\n    try {\n        const { id } = await params;\n        const body = await request.json();\n        const { forceApprove = false, adminId = 'system' } = body;\n        // Find the qualification\n        const qualification = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.qualificationProgress.findUnique({\n            where: {\n                id\n            },\n            include: {\n                client: true,\n                token: true\n            }\n        });\n        if (!qualification) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Qualification not found'\n            }, {\n                status: 404\n            });\n        }\n        // Check if all steps are completed (unless force approve)\n        const allStepsCompleted = qualification.countrySelected && qualification.agreementAccepted && qualification.profileCompleted && qualification.walletConnected && qualification.kycCompleted;\n        if (!forceApprove && !allStepsCompleted) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Cannot approve qualification - not all steps completed',\n                missingSteps: {\n                    country: !qualification.countrySelected,\n                    agreement: !qualification.agreementAccepted,\n                    profile: !qualification.profileCompleted,\n                    wallet: !qualification.walletConnected,\n                    kyc: !qualification.kycCompleted\n                }\n            }, {\n                status: 400\n            });\n        }\n        // Update qualification status\n        const updatedQualification = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.qualificationProgress.update({\n            where: {\n                id\n            },\n            data: {\n                qualificationStatus: forceApprove ? 'FORCE_APPROVED' : 'APPROVED',\n                approvedBy: adminId,\n                approvedAt: new Date(),\n                rejectedReason: null\n            },\n            include: {\n                client: true,\n                token: true\n            }\n        });\n        // If this is a token-specific qualification, also update the client's whitelist status\n        if (qualification.tokenId && qualification.token && qualification.client.walletAddress) {\n            try {\n                // 1. Add client to token whitelist in the database\n                await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.client.update({\n                    where: {\n                        id: qualification.clientId\n                    },\n                    data: {\n                        isWhitelisted: true,\n                        whitelistedAt: new Date()\n                    }\n                });\n                // 2. Create/update TokenClientApproval record\n                await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.tokenClientApproval.upsert({\n                    where: {\n                        tokenId_clientId: {\n                            tokenId: qualification.tokenId,\n                            clientId: qualification.clientId\n                        }\n                    },\n                    update: {\n                        approvalStatus: 'APPROVED',\n                        kycApproved: true,\n                        whitelistApproved: true,\n                        approvedBy: adminId,\n                        approvedAt: new Date(),\n                        notes: `Auto-approved via qualification ${forceApprove ? 'force approval' : 'approval'}`\n                    },\n                    create: {\n                        tokenId: qualification.tokenId,\n                        clientId: qualification.clientId,\n                        approvalStatus: 'APPROVED',\n                        kycApproved: true,\n                        whitelistApproved: true,\n                        approvedBy: adminId,\n                        approvedAt: new Date(),\n                        notes: `Auto-approved via qualification ${forceApprove ? 'force approval' : 'approval'}`\n                    }\n                });\n                // 3. Add client to blockchain whitelist (if token has whitelist address)\n                if (qualification.token.whitelistAddress && qualification.token.whitelistAddress !== '******************************************') {\n                    try {\n                        // Import whitelist utilities\n                        const WhitelistUtils = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/ethers\"), __webpack_require__.e(\"vendor-chunks/@noble\"), __webpack_require__.e(\"vendor-chunks/@adraffy\"), __webpack_require__.e(\"_rsc_src_utils_whitelist_ts\")]).then(__webpack_require__.bind(__webpack_require__, /*! ../../../../../utils/whitelist */ \"(rsc)/./src/utils/whitelist.ts\"));\n                        const result = await WhitelistUtils.addToWhitelist(qualification.token.whitelistAddress, qualification.client.walletAddress, qualification.token.network || 'amoy');\n                        console.log('Successfully added client to blockchain whitelist:', {\n                            clientEmail: qualification.client.email,\n                            walletAddress: qualification.client.walletAddress,\n                            tokenName: qualification.token.name,\n                            tokenAddress: qualification.token.address,\n                            whitelistAddress: qualification.token.whitelistAddress,\n                            txHash: result.txHash\n                        });\n                    } catch (whitelistError) {\n                        console.error('Error adding client to blockchain whitelist:', whitelistError);\n                        // Don't fail the approval if blockchain whitelist fails, but log it\n                        console.warn('Qualification approved but blockchain whitelisting failed. Client may need manual whitelisting.');\n                    }\n                } else {\n                    console.log('Token has no whitelist address, skipping blockchain whitelisting');\n                }\n                console.log('Updated client whitelist status for token:', {\n                    clientEmail: qualification.client.email,\n                    tokenName: qualification.token.name,\n                    tokenAddress: qualification.token.address\n                });\n            } catch (error) {\n                console.error('Error updating client whitelist status:', error);\n            // Don't fail the approval if whitelist update fails\n            }\n        }\n        console.log('Approved qualification:', {\n            id: updatedQualification.id,\n            clientEmail: updatedQualification.client.email,\n            tokenName: updatedQualification.token?.name || 'Global',\n            status: updatedQualification.qualificationStatus,\n            forceApprove,\n            approvedBy: adminId\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: `Qualification ${forceApprove ? 'force ' : ''}approved successfully`,\n            qualification: updatedQualification\n        });\n    } catch (error) {\n        console.error('Error approving qualification:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to approve qualification'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/qualifications/[id]/approve/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log:  true ? [\n        'query',\n        'error',\n        'warn'\n    ] : 0\n});\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFFOUMsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsQ0FBQztJQUMvREksS0FBS0MsS0FBc0MsR0FBRztRQUFDO1FBQVM7UUFBUztLQUFPLEdBQUcsQ0FBUztBQUN0RixHQUFHO0FBRUgsSUFBSUEsSUFBcUMsRUFBRUosZ0JBQWdCRSxNQUFNLEdBQUdBIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFx0b2tlbmRldi1uZXdyb29cXGFkbWluLXBhbmVsXFxzcmNcXGxpYlxccHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50JztcclxuXHJcbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XHJcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XHJcbn07XHJcblxyXG5leHBvcnQgY29uc3QgcHJpc21hID0gZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/PyBuZXcgUHJpc21hQ2xpZW50KHtcclxuICBsb2c6IHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnID8gWydxdWVyeScsICdlcnJvcicsICd3YXJuJ10gOiBbJ2Vycm9yJ10sXHJcbn0pO1xyXG5cclxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWE7XHJcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwibG9nIiwicHJvY2VzcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fqualifications%2F%5Bid%5D%2Fapprove%2Froute&page=%2Fapi%2Fqualifications%2F%5Bid%5D%2Fapprove%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqualifications%2F%5Bid%5D%2Fapprove%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();