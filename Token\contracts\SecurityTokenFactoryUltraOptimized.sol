// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "./SecurityTokenUltraOptimized.sol";
import "./IdentityRegistry.sol";
import "./Compliance.sol";
import "./ClaimRegistry.sol";

contract SecurityTokenFactoryUltraOptimized is AccessControl, ReentrancyGuard {
    bytes32 public constant DEPLOYER_ROLE = keccak256("DEPLOYER_ROLE");
    
    address[] public deployedTokens;
    mapping(string => address) public tokensBySymbol;
    mapping(address => bool) public isDeployedToken;
    mapping(address => TokenInfo) public tokenInfo;
    
    struct TokenInfo {
        address token;
        address identity;
        address compliance;
        address claim;
        string name;
        string symbol;
        uint8 decimals;
        uint256 maxSupply;
        address admin;
        bool hasKYC;
        bool hasFees;
        uint256 timestamp;
    }
    
    event TokenDeployed(
        address indexed token, address indexed identity, address indexed compliance,
        address claim, string name, string symbol, uint8 decimals, uint256 maxSupply,
        address admin, bool hasKYC, bool hasFees, string imageUrl
    );
    
    constructor(address admin) {
        require(admin != address(0), "!admin");
        _grantRole(DEFAULT_ADMIN_ROLE, admin);
        _grantRole(DEPLOYER_ROLE, admin);
    }
    
    function deployCompleteToken(
        string memory name, string memory symbol, uint8 decimals, uint256 maxSupply,
        address admin, string memory tokenPrice, string memory bonusTiers,
        string memory tokenDetails, string memory tokenImageUrl,
        bool withKYC, bool withFees
    ) external onlyRole(DEPLOYER_ROLE) nonReentrant returns (
        address token, address identity, address compliance, address claim
    ) {
        require(bytes(name).length > 0 && bytes(name).length <= 50, "!name");
        require(bytes(symbol).length > 0 && bytes(symbol).length <= 10, "!symbol");
        require(admin != address(0) && maxSupply > 0 && decimals <= 18, "!params");
        require(tokensBySymbol[symbol] == address(0), "exists");
        
        // Deploy supporting contracts
        claim = address(new ClaimRegistry());
        ClaimRegistry(claim).initialize(admin);
        
        identity = address(new IdentityRegistry());
        IdentityRegistry(identity).initialize(admin, claim);
        
        compliance = address(new Compliance());
        Compliance(compliance).initialize(admin, identity);
        
        // Deploy token
        token = address(new SecurityTokenUltraOptimized(
            name, symbol, decimals, maxSupply, identity, compliance, admin,
            tokenPrice, bonusTiers, tokenImageUrl
        ));
        
        // Enable features if requested
        if (withFees) {
            SecurityTokenUltraOptimized(token).enableFees(100, admin); // 1% default
        }
        
        // Store info
        tokenInfo[token] = TokenInfo({
            token: token, identity: identity, compliance: compliance, claim: claim,
            name: name, symbol: symbol, decimals: decimals, maxSupply: maxSupply,
            admin: admin, hasKYC: withKYC, hasFees: withFees, timestamp: block.timestamp
        });
        
        deployedTokens.push(token);
        tokensBySymbol[symbol] = token;
        isDeployedToken[token] = true;
        
        emit TokenDeployed(
            token, identity, compliance, claim, name, symbol, decimals, maxSupply,
            admin, withKYC, withFees, tokenImageUrl
        );
        
        return (token, identity, compliance, claim);
    }
    
    function deploySecurityToken(
        string memory name, string memory symbol, uint8 decimals, uint256 maxSupply,
        address admin, string memory tokenPrice, string memory bonusTiers,
        string memory tokenDetails, string memory tokenImageUrl
    ) external onlyRole(DEPLOYER_ROLE) nonReentrant returns (address token) {
        (token, , ,) = this.deployCompleteToken(
            name, symbol, decimals, maxSupply, admin, tokenPrice, bonusTiers,
            tokenDetails, tokenImageUrl, false, false
        );
        return token;
    }
    
    function addDeployer(address deployer) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(deployer != address(0), "!deployer");
        _grantRole(DEPLOYER_ROLE, deployer);
    }
    
    function removeDeployer(address deployer) external onlyRole(DEFAULT_ADMIN_ROLE) {
        _revokeRole(DEPLOYER_ROLE, deployer);
    }
    
    function getTokenInfo(address token) external view returns (TokenInfo memory) {
        require(isDeployedToken[token], "!deployed");
        return tokenInfo[token];
    }
    
    function getTokenAddressBySymbol(string memory symbol) external view returns (address) {
        return tokensBySymbol[symbol];
    }
    
    function getAllDeployedTokens() external view returns (address[] memory) {
        return deployedTokens;
    }
    
    function getDeployedToken(uint256 index) external view returns (address) {
        require(index < deployedTokens.length, "!index");
        return deployedTokens[index];
    }
    
    function getTokenCount() external view returns (uint256) {
        return deployedTokens.length;
    }
    
    function getTokensBatch(uint256 start, uint256 count) external view returns (address[] memory) {
        require(start < deployedTokens.length, "!start");
        require(count <= 10, "!count");
        
        uint256 end = start + count;
        if (end > deployedTokens.length) end = deployedTokens.length;
        
        address[] memory batch = new address[](end - start);
        for (uint256 i = start; i < end; i++) {
            batch[i - start] = deployedTokens[i];
        }
        return batch;
    }
}
