"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/create-token/page",{

/***/ "(app-pages-browser)/./src/app/create-token/hooks/useTokenDeployment.ts":
/*!**********************************************************!*\
  !*** ./src/app/create-token/hooks/useTokenDeployment.ts ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTokenDeployment: () => (/* binding */ useTokenDeployment)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/constants/addresses.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/address/checks.js\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../config */ \"(app-pages-browser)/./src/config.ts\");\n/* harmony import */ var _contracts_SecurityTokenFactory_json__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../contracts/SecurityTokenFactory.json */ \"(app-pages-browser)/./src/contracts/SecurityTokenFactory.json\");\n/* harmony import */ var _contracts_SecurityToken_json__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../contracts/SecurityToken.json */ \"(app-pages-browser)/./src/contracts/SecurityToken.json\");\n/* harmony import */ var _useERC3643Integration__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./useERC3643Integration */ \"(app-pages-browser)/./src/app/create-token/hooks/useERC3643Integration.ts\");\n\n\n\n\n\n\n/**\r\n * Custom hook for token deployment logic\r\n *\r\n * Encapsulates all the token deployment functionality including state management,\r\n * transaction handling, and error handling\r\n */ function useTokenDeployment(network, factoryAddress, hasDeployerRole, kycSupported) {\n    // State management\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [deployedToken, setDeployedToken] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [transactionHash, setTransactionHash] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [deploymentStep, setDeploymentStep] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('idle');\n    // ERC-3643 integration\n    const { setupERC3643Compliance, isERC3643Available } = (0,_useERC3643Integration__WEBPACK_IMPORTED_MODULE_4__.useERC3643Integration)();\n    /**\r\n   * Save token data to database\r\n   */ const saveTokenToDatabase = async (deployedToken, formData, transactionHash, blockNumber, network)=>{\n        // Fetch totalSupply from the blockchain\n        let totalSupply = '0';\n        try {\n            // Create a new provider instance for blockchain calls\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_5__.BrowserProvider(window.ethereum);\n            const token = new ethers__WEBPACK_IMPORTED_MODULE_6__.Contract(deployedToken.address, _contracts_SecurityToken_json__WEBPACK_IMPORTED_MODULE_3__.abi, provider);\n            const totalSupplyRaw = await token.totalSupply();\n            totalSupply = deployedToken.decimals === 0 ? totalSupplyRaw.toString() : ethers__WEBPACK_IMPORTED_MODULE_7__.formatUnits(totalSupplyRaw, deployedToken.decimals);\n        } catch (error) {\n            console.warn('Could not fetch totalSupply from blockchain, using default 0:', error);\n        }\n        const tokenData = {\n            address: deployedToken.address,\n            transactionHash: transactionHash,\n            blockNumber: blockNumber,\n            network: network,\n            name: deployedToken.name,\n            symbol: deployedToken.symbol,\n            decimals: deployedToken.decimals,\n            maxSupply: deployedToken.maxSupply,\n            totalSupply: totalSupply,\n            tokenType: formData.tokenType,\n            tokenPrice: deployedToken.tokenPrice,\n            currency: deployedToken.currency,\n            bonusTiers: deployedToken.bonusTiers,\n            tokenImageUrl: deployedToken.tokenImageUrl,\n            whitelistAddress: deployedToken.whitelistAddress,\n            adminAddress: deployedToken.admin,\n            hasKYC: deployedToken.hasKYC,\n            selectedClaims: formData.selectedClaims,\n            isActive: true,\n            deployedBy: deployedToken.admin,\n            deploymentNotes: \"\".concat(formData.tokenType, \" token deployed via admin panel\")\n        };\n        const response = await fetch('/api/tokens', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify(tokenData)\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(\"Database save failed: \".concat(errorData.error || 'Unknown error'));\n        }\n        return await response.json();\n    };\n    /**\r\n   * Deploy a new token with the provided form data\r\n   */ const deployToken = async (formData)=>{\n        setIsSubmitting(true);\n        setError(null);\n        setSuccess(null);\n        setDeployedToken(null);\n        setTransactionHash(null);\n        setDeploymentStep('preparing');\n        try {\n            // Validate form data\n            validateFormData(formData);\n            // Get network configuration\n            const networkConfig = (0,_config__WEBPACK_IMPORTED_MODULE_1__.getNetworkConfig)(network);\n            if (!factoryAddress) {\n                throw new Error(\"No factory address configured for network: \".concat(network));\n            }\n            if (!window.ethereum) {\n                throw new Error('Please install MetaMask to use this feature!');\n            }\n            setDeploymentStep('connecting');\n            // Get provider and signer\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_5__.BrowserProvider(window.ethereum);\n            const signer = await provider.getSigner();\n            // Verify network connection\n            await verifyNetworkConnection(provider, network);\n            // Connect to the factory contract\n            const factory = new ethers__WEBPACK_IMPORTED_MODULE_6__.Contract(factoryAddress, _contracts_SecurityTokenFactory_json__WEBPACK_IMPORTED_MODULE_2__.abi, signer);\n            console.log(\"Connected to factory at:\", factoryAddress);\n            // Verify deployer role\n            await verifyDeployerRole(factory, signer, hasDeployerRole);\n            // Verify KYC support\n            await verifyKYCSupport(factory, formData.enableKYC);\n            // Check if token symbol already exists\n            await checkTokenSymbolAvailability(factory, formData.symbol);\n            // Convert maxSupply to the appropriate unit based on decimals\n            const maxSupplyWei = formData.decimals === 0 ? BigInt(formData.maxSupply) : ethers__WEBPACK_IMPORTED_MODULE_7__.parseUnits(formData.maxSupply, formData.decimals);\n            setDeploymentStep('deploying');\n            console.log(\"Deploying token with params:\", {\n                name: formData.name,\n                symbol: formData.symbol,\n                decimals: formData.decimals,\n                maxSupply: formData.maxSupply,\n                admin: formData.ownerAddress,\n                tokenPrice: formData.tokenPrice,\n                bonusTiers: formData.bonusTiers,\n                enableKYC: formData.enableKYC\n            });\n            // Create the transaction\n            const tx = await createDeployTransaction(factory, formData, maxSupplyWei, network, kycSupported);\n            setTransactionHash(tx.hash);\n            console.log(\"Transaction hash:\", tx.hash);\n            setDeploymentStep('confirming');\n            // Wait for the transaction to be mined\n            const receipt = await tx.wait();\n            console.log(\"Transaction mined in block:\", receipt.blockNumber);\n            setDeploymentStep('fetching');\n            // Get the token address\n            const tokenAddress = await factory.getTokenAddressBySymbol(formData.symbol);\n            if (tokenAddress && tokenAddress !== ethers__WEBPACK_IMPORTED_MODULE_8__.ZeroAddress) {\n                // Create token deployment result\n                const deploymentResult = await getDeploymentResult(tokenAddress, provider, formData);\n                setDeployedToken(deploymentResult);\n                // Save token to database\n                try {\n                    await saveTokenToDatabase(deploymentResult, formData, tx.hash, receipt.blockNumber.toString(), network);\n                    console.log(\"Token successfully saved to database\");\n                } catch (dbError) {\n                    console.warn(\"Failed to save token to database:\", dbError);\n                // Don't fail the deployment if database save fails\n                }\n                // Setup ERC-3643 compliance if available\n                if (isERC3643Available()) {\n                    setDeploymentStep('setting_up_compliance');\n                    console.log(\"🏛️ Setting up ERC-3643 compliance...\");\n                    try {\n                        const complianceResult = await setupERC3643Compliance(tokenAddress, formData.ownerAddress, signer, {\n                            name: formData.name,\n                            symbol: formData.symbol,\n                            tokenType: formData.tokenType,\n                            country: formData.issuerCountry || 'US',\n                            selectedClaims: formData.selectedClaims\n                        });\n                        if (complianceResult.errors.length > 0) {\n                            console.warn(\"⚠️ Some ERC-3643 setup steps failed:\", complianceResult.errors);\n                        // Don't fail deployment, just warn\n                        } else {\n                            console.log(\"✅ ERC-3643 compliance setup completed successfully\");\n                        }\n                    } catch (complianceError) {\n                        console.warn(\"⚠️ ERC-3643 compliance setup failed:\", complianceError);\n                    // Don't fail deployment, just warn\n                    }\n                } else {\n                    console.log(\"ℹ️ ERC-3643 contracts not available, skipping compliance setup\");\n                }\n                setDeploymentStep('completed');\n                setSuccess('Token \"'.concat(formData.name, '\" (').concat(formData.symbol, \") successfully deployed!\"));\n            } else {\n                throw new Error(\"Token deployment failed: Could not retrieve token address\");\n            }\n        } catch (err) {\n            handleDeploymentError(err, network);\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    /**\r\n   * Validate form data before deployment\r\n   */ const validateFormData = (formData)=>{\n        if (!formData.name || !formData.symbol || !formData.maxSupply || !formData.ownerAddress) {\n            throw new Error('Please fill in all required fields');\n        }\n        if (!ethers__WEBPACK_IMPORTED_MODULE_9__.isAddress(formData.ownerAddress)) {\n            throw new Error('Invalid owner address');\n        }\n        if (formData.decimals < 0 || formData.decimals > 18) {\n            throw new Error('Decimals must be between 0 and 18');\n        }\n    };\n    /**\r\n   * Verify that the wallet is connected to the correct network\r\n   */ const verifyNetworkConnection = async (provider, network)=>{\n        const chainId = (await provider.getNetwork()).chainId;\n        if (network === 'amoy' && chainId.toString() !== '80002') {\n            throw new Error('Please connect your wallet to the Amoy network (Chain ID: 80002)');\n        } else if (network === 'polygon' && chainId.toString() !== '137') {\n            throw new Error('Please connect your wallet to the Polygon network (Chain ID: 137)');\n        }\n    };\n    /**\r\n   * Verify that the connected wallet has DEPLOYER_ROLE\r\n   */ const verifyDeployerRole = async (factory, signer, hasRole)=>{\n        if (!hasRole) {\n            const DEPLOYER_ROLE = await factory.DEPLOYER_ROLE();\n            const hasDeployerRole = await factory.hasRole(DEPLOYER_ROLE, await signer.getAddress());\n            if (!hasDeployerRole) {\n                throw new Error(\"Your wallet does not have the DEPLOYER_ROLE required to create tokens\");\n            }\n        }\n    };\n    /**\r\n   * Check if token symbol is available\r\n   */ const checkTokenSymbolAvailability = async (factory, symbol)=>{\n        try {\n            const existingTokenAddress = await factory.getTokenAddressBySymbol(symbol);\n            if (existingTokenAddress !== ethers__WEBPACK_IMPORTED_MODULE_8__.ZeroAddress) {\n                throw new Error('Token with symbol \"'.concat(symbol, '\" already exists at address ').concat(existingTokenAddress, \". Please choose a different symbol.\"));\n            }\n        } catch (err) {\n            if (err.message.includes(\"already exists\")) {\n                throw err; // Re-throw our custom error\n            }\n            // If it's a different error, log it but don't fail the deployment\n            console.warn(\"Could not check token symbol availability:\", err.message);\n        }\n    };\n    /**\r\n   * Verify KYC support in the factory contract\r\n   */ const verifyKYCSupport = async (factory, enableKYC)=>{\n        if (!enableKYC) return; // Skip verification if KYC is not enabled\n        try {\n            // Check if this is the enhanced factory with built-in KYC\n            const factoryAddress = await factory.getAddress();\n            const isEnhancedFactory = factoryAddress === \"******************************************\" || factoryAddress === \"******************************************\" || factoryAddress === \"******************************************\";\n            if (isEnhancedFactory) {\n                console.log(\"✅ Enhanced factory with built-in KYC support detected\");\n                return; // Enhanced factory has built-in KYC, no need for further checks\n            }\n            // Fallback to old KYC detection method for other factories\n            const kycImplementation = await factory.whitelistWithKYCImplementation();\n            // Check function exists by examining the ABI\n            const hasKYCFunction = factory.interface.fragments.some((fragment)=>fragment.type === \"function\" && 'name' in fragment && fragment.name === \"deploySecurityTokenWithOptions\");\n            if (!hasKYCFunction) {\n                throw new Error(\"The deployed factory contract doesn't support the KYC functionality. Please deploy an updated factory contract.\");\n            }\n            if (kycImplementation === ethers__WEBPACK_IMPORTED_MODULE_8__.ZeroAddress) {\n                throw new Error(\"KYC implementation address is not set in the factory contract.\");\n            }\n        } catch (err) {\n            if (err.message.includes(\"whitelistWithKYCImplementation\")) {\n                throw new Error(\"The deployed factory contract doesn't support the KYC functionality. Please deploy an updated factory contract.\");\n            }\n            throw err;\n        }\n    };\n    /**\r\n   * Create the deployment transaction with the appropriate gas settings\r\n   */ const createDeployTransaction = async (factory, formData, maxSupplyWei, network, supportsKYC)=>{\n        // Determine if KYC is supported\n        let canUseKYC = supportsKYC;\n        try {\n            // Check if this is the enhanced factory with built-in KYC\n            const factoryAddress = await factory.getAddress();\n            const isEnhancedFactory = factoryAddress === \"******************************************\" || factoryAddress === \"******************************************\" || factoryAddress === \"******************************************\";\n            if (isEnhancedFactory) {\n                canUseKYC = true;\n                console.log(\"✅ Using enhanced factory with built-in KYC support\");\n            } else {\n                // Fallback to old KYC detection method\n                await factory.whitelistWithKYCImplementation();\n                const hasKYCFunction = factory.interface.fragments.some((fragment)=>fragment.type === \"function\" && 'name' in fragment && fragment.name === \"deploySecurityTokenWithOptions\");\n                canUseKYC = hasKYCFunction;\n            }\n        } catch (err) {\n            console.log(\"KYC functionality not supported in this factory contract, using standard deployment\");\n            canUseKYC = false;\n            // If KYC was enabled but not supported, warn the user\n            if (formData.enableKYC) {\n                console.warn(\"KYC requested but not supported by the contract. Proceeding with standard token deployment.\");\n            }\n        }\n        // Optimized gas settings for Amoy testnet\n        if (network === 'amoy') {\n            // Optimized gas settings for Amoy testnet - 5M gas limit, 50 gwei gas price\n            const gasLimit = BigInt(5000000);\n            const gasPrice = ethers__WEBPACK_IMPORTED_MODULE_7__.parseUnits(\"50\", \"gwei\");\n            console.log(\"Using optimized gas settings for Amoy testnet:\");\n            console.log(\"Gas limit:\", gasLimit.toString());\n            console.log(\"Gas price:\", ethers__WEBPACK_IMPORTED_MODULE_7__.formatUnits(gasPrice, \"gwei\"), \"gwei\");\n            // Call the appropriate function based on factory type\n            const factoryAddress = await factory.getAddress();\n            const isEnhancedFactory = factoryAddress === \"******************************************\" || factoryAddress === \"******************************************\" || factoryAddress === \"******************************************\";\n            if (isEnhancedFactory) {\n                console.log(\"Calling enhanced factory deploySecurityToken with built-in KYC\");\n                return await factory.deploySecurityToken(formData.name, formData.symbol, formData.decimals, maxSupplyWei, formData.ownerAddress, \"\".concat(formData.tokenPrice, \" \").concat(formData.currency), formData.bonusTiers, \"\".concat(formData.tokenType, \" token deployed via admin panel\"), formData.tokenImageUrl || \"\", {\n                    gasLimit,\n                    gasPrice\n                });\n            } else if (canUseKYC) {\n                console.log(\"Calling deploySecurityTokenWithOptions with KYC:\", formData.enableKYC);\n                return await factory.deploySecurityTokenWithOptions(formData.name, formData.symbol, formData.decimals, maxSupplyWei, formData.ownerAddress, \"\".concat(formData.tokenPrice, \" \").concat(formData.currency), formData.bonusTiers, \"\".concat(formData.tokenType, \" token deployed via admin panel\"), formData.tokenImageUrl || \"\", formData.enableKYC, {\n                    gasLimit,\n                    gasPrice\n                });\n            } else {\n                console.log(\"Calling deploySecurityToken (no KYC support)\");\n                return await factory.deploySecurityToken(formData.name, formData.symbol, formData.decimals, maxSupplyWei, formData.ownerAddress, \"\".concat(formData.tokenPrice, \" \").concat(formData.currency), formData.bonusTiers, \"\".concat(formData.tokenType, \" token deployed via admin panel\"), formData.tokenImageUrl || \"\", {\n                    gasLimit,\n                    gasPrice\n                });\n            }\n        } else {\n            // For other networks, try to estimate gas\n            let gasLimit;\n            try {\n                // Estimate gas based on factory type\n                let gasEstimate;\n                const factoryAddress = await factory.getAddress();\n                const isEnhancedFactory = factoryAddress === \"******************************************\" || factoryAddress === \"******************************************\";\n                if (isEnhancedFactory || !canUseKYC) {\n                    gasEstimate = await factory.deploySecurityToken.estimateGas(formData.name, formData.symbol, formData.decimals, maxSupplyWei, formData.ownerAddress, \"\".concat(formData.tokenPrice, \" \").concat(formData.currency), formData.bonusTiers, \"\".concat(formData.tokenType, \" token deployed via admin panel\"), formData.tokenImageUrl || \"\");\n                } else {\n                    gasEstimate = await factory.deploySecurityTokenWithOptions.estimateGas(formData.name, formData.symbol, formData.decimals, maxSupplyWei, formData.ownerAddress, \"\".concat(formData.tokenPrice, \" \").concat(formData.currency), formData.bonusTiers, \"\".concat(formData.tokenType, \" token deployed via admin panel\"), formData.tokenImageUrl || \"\", formData.enableKYC);\n                }\n                console.log(\"Gas estimate:\", gasEstimate.toString());\n                // Add 50% to the gas estimate to be safer\n                gasLimit = gasEstimate * BigInt(150) / BigInt(100);\n                console.log(\"Using calculated gas limit:\", gasLimit.toString());\n            } catch (estimateErr) {\n                console.error(\"Gas estimation failed, using fixed limit:\", estimateErr);\n                // Fallback to fixed gas limit if estimation fails\n                gasLimit = BigInt(2000000); // Use 2M for non-Amoy networks\n                console.log(\"Using fallback gas limit:\", gasLimit.toString());\n            }\n            // Call without specific gas price for networks that calculate it properly\n            const factoryAddress = await factory.getAddress();\n            const isEnhancedFactory = factoryAddress === \"******************************************\" || factoryAddress === \"******************************************\";\n            if (isEnhancedFactory) {\n                return await factory.deploySecurityToken(formData.name, formData.symbol, formData.decimals, maxSupplyWei, formData.ownerAddress, \"\".concat(formData.tokenPrice, \" \").concat(formData.currency), formData.bonusTiers, \"\".concat(formData.tokenType, \" token deployed via admin panel\"), formData.tokenImageUrl || \"\", {\n                    gasLimit\n                });\n            } else if (canUseKYC) {\n                return await factory.deploySecurityTokenWithOptions(formData.name, formData.symbol, formData.decimals, maxSupplyWei, formData.ownerAddress, \"\".concat(formData.tokenPrice, \" \").concat(formData.currency), formData.bonusTiers, \"\".concat(formData.tokenType, \" token deployed via admin panel\"), formData.tokenImageUrl || \"\", formData.enableKYC, {\n                    gasLimit\n                });\n            } else {\n                return await factory.deploySecurityToken(formData.name, formData.symbol, formData.decimals, maxSupplyWei, formData.ownerAddress, \"\".concat(formData.tokenPrice, \" \").concat(formData.currency), formData.bonusTiers, \"\".concat(formData.tokenType, \" token deployed via admin panel\"), formData.tokenImageUrl || \"\", {\n                    gasLimit\n                });\n            }\n        }\n    };\n    /**\r\n   * Get deployment result after successful transaction\r\n   */ const getDeploymentResult = async (tokenAddress, provider, formData)=>{\n        console.log(\"Token successfully deployed at:\", tokenAddress);\n        // Connect to the token contract\n        const token = new ethers__WEBPACK_IMPORTED_MODULE_6__.Contract(tokenAddress, _contracts_SecurityToken_json__WEBPACK_IMPORTED_MODULE_3__.abi, provider);\n        // Get identity registry address (whitelist contract)\n        const whitelistAddress = await token.identityRegistry();\n        // Get token decimals from the contract\n        const tokenDecimals = await token.decimals();\n        const decimalsNumber = Number(tokenDecimals);\n        // Format maxSupply based on decimals\n        const maxSupplyRaw = await token.maxSupply();\n        const maxSupplyFormatted = decimalsNumber === 0 ? maxSupplyRaw.toString() : ethers__WEBPACK_IMPORTED_MODULE_7__.formatUnits(maxSupplyRaw, tokenDecimals);\n        // Try to get the image URL from the contract if supported\n        let tokenImageUrl = formData.tokenImageUrl;\n        try {\n            // Check if the token contract supports tokenImageUrl function\n            if (token.interface.fragments.some((fragment)=>fragment.type === \"function\" && 'name' in fragment && fragment.name === \"tokenImageUrl\")) {\n                tokenImageUrl = await token.tokenImageUrl();\n            }\n        } catch (error) {\n            console.log(\"Token contract doesn't support image URL, using form data\");\n        }\n        // Create deployed token object\n        return {\n            address: tokenAddress,\n            name: await token.name(),\n            symbol: await token.symbol(),\n            decimals: decimalsNumber,\n            maxSupply: maxSupplyFormatted,\n            whitelistAddress: whitelistAddress,\n            admin: formData.ownerAddress,\n            tokenPrice: \"\".concat(formData.tokenPrice, \" \").concat(formData.currency),\n            currency: formData.currency,\n            bonusTiers: formData.bonusTiers,\n            hasKYC: formData.enableKYC,\n            tokenImageUrl: tokenImageUrl\n        };\n    };\n    /**\r\n   * Handle deployment errors with detailed messages\r\n   */ const handleDeploymentError = (err, network)=>{\n        console.error('Error creating token:', err);\n        // Extract more details from the error for debugging\n        const errorDetails = typeof err === 'object' ? JSON.stringify({\n            code: err.code,\n            message: err.message,\n            data: err.data,\n            info: err.info\n        }, null, 2) : String(err);\n        // Special handling for specific contract errors\n        if (err.message.includes(\"transaction execution reverted\")) {\n            // This is likely a contract validation error\n            setError(\"Transaction failed: The contract rejected the transaction. This could be due to:\\n\\n• Token symbol already exists - try a different symbol\\n• Invalid parameters (empty name/symbol, zero max supply, etc.)\\n• Access control issues\\n\\nPlease check your inputs and try again with a unique token symbol.\\n\\nTechnical details: \".concat(err.message));\n        } else if (err.message.includes(\"gas required exceeds allowance\") || err.message.includes(\"intrinsic gas too low\") || err.message.includes(\"Internal JSON-RPC error\")) {\n            // For Amoy testnet specifically, provide CLI alternative\n            if (network === 'amoy') {\n                // Create a CLI command template - actual values will be filled in by the UI\n                const cliCommand = '# For Windows PowerShell:\\ncd Token\\n$env:NETWORK=\"amoy\"\\n$env:TOKEN_NAME=\"YourTokenName\"\\n$env:TOKEN_SYMBOL=\"YTS\"\\n$env:TOKEN_DECIMALS=\"0\"\\n$env:MAX_SUPPLY=\"1000000\"\\n$env:ADMIN_ADDRESS=\"0xYourAddress\"\\n$env:TOKEN_PRICE=\"10 USD\"\\n$env:BONUS_TIERS=\"Tier 1: 5%, Tier 2: 10%\"\\nnpx hardhat run scripts/02-deploy-token.js --network amoy';\n                setError(\"Gas estimation failed on Amoy testnet. This is a common issue with this network.\\n\\nYou can try using this command line script instead:\\n\\n\".concat(cliCommand));\n            } else {\n                setError(\"Transaction failed due to gas calculation issues: \".concat(err.message, \"\\n\\nDetails: \").concat(errorDetails));\n            }\n        } else if (err.message.includes(\"Internal JSON-RPC error\") || err.message.includes(\"could not coalesce error\")) {\n            setError(\"Transaction failed. This is likely due to gas calculation issues on the Amoy testnet. Try using the command line script instead.\");\n        } else {\n            setError(\"Transaction failed: \".concat(err.message, \"\\n\\nDetails: \").concat(errorDetails));\n        }\n        setDeploymentStep('failed');\n    };\n    return {\n        isSubmitting,\n        error,\n        success,\n        deployedToken,\n        transactionHash,\n        deploymentStep,\n        deployToken\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/create-token/hooks/useTokenDeployment.ts\n"));

/***/ })

});