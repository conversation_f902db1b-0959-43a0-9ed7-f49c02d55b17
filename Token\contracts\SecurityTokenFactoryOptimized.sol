// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol";
import "./SecurityTokenOptimized.sol";
import "./IdentityRegistry.sol";
import "./Compliance.sol";
import "./ClaimRegistry.sol";

contract SecurityTokenFactoryOptimized is AccessControl, ReentrancyGuard {
    bytes32 public constant DEPLOYER_ROLE = keccak256("DEPLOYER_ROLE");
    
    address public tokenImplementation;
    address public identityImplementation;
    address public complianceImplementation;
    address public claimImplementation;
    
    address[] public deployedTokens;
    mapping(string => address) public tokensBySymbol;
    mapping(address => bool) public isDeployedToken;
    mapping(address => TokenInfo) public tokenInfo;
    
    struct TokenInfo {
        address token;
        address identity;
        address compliance;
        address claim;
        string name;
        string symbol;
        uint8 decimals;
        uint256 maxSupply;
        address admin;
        bool hasKYC;
        bool hasFees;
        uint256 timestamp;
    }
    
    event TokenDeployed(
        address indexed token, address indexed identity, address indexed compliance,
        address claim, string name, string symbol, uint8 decimals, uint256 maxSupply,
        address admin, bool hasKYC, bool hasFees, string imageUrl
    );
    
    constructor(address admin) {
        require(admin != address(0), "Invalid admin");
        
        tokenImplementation = address(new SecurityTokenOptimized());
        identityImplementation = address(new IdentityRegistry());
        complianceImplementation = address(new Compliance());
        claimImplementation = address(new ClaimRegistry());
        
        _grantRole(DEFAULT_ADMIN_ROLE, admin);
        _grantRole(DEPLOYER_ROLE, admin);
    }
    
    function deployCompleteToken(
        string memory name, string memory symbol, uint8 decimals, uint256 maxSupply,
        address admin, string memory tokenPrice, string memory bonusTiers,
        string memory tokenDetails, string memory tokenImageUrl,
        bool withKYC, bool withFees
    ) external onlyRole(DEPLOYER_ROLE) nonReentrant returns (
        address token, address identity, address compliance, address claim
    ) {
        require(bytes(name).length > 0 && bytes(name).length <= 50, "Invalid name");
        require(bytes(symbol).length > 0 && bytes(symbol).length <= 10, "Invalid symbol");
        require(admin != address(0) && maxSupply > 0 && decimals <= 18, "Invalid params");
        require(tokensBySymbol[symbol] == address(0), "Symbol exists");
        
        // Deploy claim registry
        bytes memory claimData = abi.encodeWithSelector(ClaimRegistry(address(0)).initialize.selector, admin);
        claim = address(new ERC1967Proxy(claimImplementation, claimData));
        
        // Deploy identity registry
        bytes memory identityData = abi.encodeWithSelector(IdentityRegistry(address(0)).initialize.selector, admin, claim);
        identity = address(new ERC1967Proxy(identityImplementation, identityData));
        
        // Deploy compliance
        bytes memory complianceData = abi.encodeWithSelector(Compliance(address(0)).initialize.selector, admin, identity);
        compliance = address(new ERC1967Proxy(complianceImplementation, complianceData));
        
        // Deploy token
        bytes memory tokenData = abi.encodeWithSelector(
            SecurityTokenOptimized(address(0)).initialize.selector,
            name, symbol, decimals, maxSupply, identity, compliance, admin, tokenPrice, bonusTiers, tokenImageUrl
        );
        token = address(new ERC1967Proxy(tokenImplementation, tokenData));
        
        // Enable features if requested
        if (withFees) {
            SecurityTokenOptimized(token).enableTransferFees(100, admin); // 1% default
        }
        
        // Store info
        tokenInfo[token] = TokenInfo({
            token: token, identity: identity, compliance: compliance, claim: claim,
            name: name, symbol: symbol, decimals: decimals, maxSupply: maxSupply,
            admin: admin, hasKYC: withKYC, hasFees: withFees, timestamp: block.timestamp
        });
        
        deployedTokens.push(token);
        tokensBySymbol[symbol] = token;
        isDeployedToken[token] = true;
        
        emit TokenDeployed(
            token, identity, compliance, claim, name, symbol, decimals, maxSupply,
            admin, withKYC, withFees, tokenImageUrl
        );
        
        return (token, identity, compliance, claim);
    }
    
    function deploySecurityToken(
        string memory name, string memory symbol, uint8 decimals, uint256 maxSupply,
        address admin, string memory tokenPrice, string memory bonusTiers,
        string memory tokenDetails, string memory tokenImageUrl
    ) external onlyRole(DEPLOYER_ROLE) nonReentrant returns (address token) {
        (token, , ,) = this.deployCompleteToken(
            name, symbol, decimals, maxSupply, admin, tokenPrice, bonusTiers,
            tokenDetails, tokenImageUrl, false, false
        );
        return token;
    }
    
    function addDeployer(address deployer) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(deployer != address(0), "Invalid deployer");
        _grantRole(DEPLOYER_ROLE, deployer);
    }
    
    function removeDeployer(address deployer) external onlyRole(DEFAULT_ADMIN_ROLE) {
        _revokeRole(DEPLOYER_ROLE, deployer);
    }
    
    function getTokenInfo(address token) external view returns (TokenInfo memory) {
        require(isDeployedToken[token], "Token not deployed");
        return tokenInfo[token];
    }
    
    function getTokenAddressBySymbol(string memory symbol) external view returns (address) {
        return tokensBySymbol[symbol];
    }
    
    function getAllDeployedTokens() external view returns (address[] memory) {
        return deployedTokens;
    }
    
    function getDeployedToken(uint256 index) external view returns (address) {
        require(index < deployedTokens.length, "Index out of bounds");
        return deployedTokens[index];
    }
    
    function getTokenCount() external view returns (uint256) {
        return deployedTokens.length;
    }
    
    function getTokensBatch(uint256 start, uint256 count) external view returns (address[] memory) {
        require(start < deployedTokens.length, "Start out of bounds");
        require(count <= 10, "Batch too large");
        
        uint256 end = start + count;
        if (end > deployedTokens.length) end = deployedTokens.length;
        
        address[] memory batch = new address[](end - start);
        for (uint256 i = start; i < end; i++) {
            batch[i - start] = deployedTokens[i];
        }
        return batch;
    }
}
