{"_format": "hh-sol-artifact-1", "contractName": "TransferModuleComplete", "sourceName": "contracts/modules/TransferModuleComplete.sol", "abi": [{"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "InvalidInitialization", "type": "error"}, {"inputs": [], "name": "NotInitializing", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "feePercentage", "type": "uint256"}], "name": "CustomFeeSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "exempt", "type": "bool"}], "name": "FeeExemptionSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "version", "type": "uint64"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "fee", "type": "uint256"}], "name": "TransferFeeCollected", "type": "event"}, {"anonymous": false, "inputs": [], "name": "TransferFeesDisabled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "feePercentage", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "feeCollector", "type": "address"}], "name": "TransferFeesEnabled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "limit", "type": "uint256"}], "name": "TransferLimitSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "actualAmount", "type": "uint256"}], "name": "TransferProcessed", "type": "event"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "accountTransferVolume", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "froms", "type": "address[]"}, {"internalType": "address[]", "name": "tos", "type": "address[]"}, {"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}], "name": "batchProcessTransfers", "outputs": [{"internalType": "uint256[]", "name": "actualAmounts", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "fees", "type": "uint256[]"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "calculateTransferFee", "outputs": [{"internalType": "uint256", "name": "fee", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "customFeePercentages", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "dailyTransferAmounts", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "disableTransferFees", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "emergencyCollectFees", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_feePercentage", "type": "uint256"}, {"internalType": "address", "name": "_feeCollector", "type": "address"}], "name": "enableTransferFees", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "feeCollector", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "feeExemptAddresses", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "getAccountTransferInfo", "outputs": [{"internalType": "bool", "name": "feeExempt", "type": "bool"}, {"internalType": "uint256", "name": "customFee", "type": "uint256"}, {"internalType": "uint256", "name": "dailyLimit", "type": "uint256"}, {"internalType": "uint256", "name": "dailyUsed", "type": "uint256"}, {"internalType": "uint256", "name": "totalVolume", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getTransferFeeInfo", "outputs": [{"internalType": "bool", "name": "enabled", "type": "bool"}, {"internalType": "uint256", "name": "percentage", "type": "uint256"}, {"internalType": "address", "name": "collector", "type": "address"}, {"internalType": "uint256", "name": "totalFees", "type": "uint256"}, {"internalType": "uint256", "name": "totalVolume", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_securityToken", "type": "address"}, {"internalType": "address", "name": "_admin", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "lastTransferDay", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "processTransfer", "outputs": [{"internalType": "uint256", "name": "actualAmount", "type": "uint256"}, {"internalType": "uint256", "name": "fee", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "securityToken", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "feePercentage", "type": "uint256"}], "name": "setCustomFee", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bool", "name": "exempt", "type": "bool"}], "name": "setFeeExemption", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "limit", "type": "uint256"}], "name": "setTransferLimit", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalFeesCollected", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalTransferVolume", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "transferFeePercentage", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "transferFeesEnabled", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "transferLimits", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}