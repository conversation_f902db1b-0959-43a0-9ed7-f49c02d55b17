"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/create-token/page",{

/***/ "(app-pages-browser)/./src/app/create-token/page.tsx":
/*!***************************************!*\
  !*** ./src/app/create-token/page.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CreateTokenPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../config */ \"(app-pages-browser)/./src/config.ts\");\n/* harmony import */ var _contracts_SecurityTokenFactory_json__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../contracts/SecurityTokenFactory.json */ \"(app-pages-browser)/./src/contracts/SecurityTokenFactory.json\");\n/* harmony import */ var _components__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components */ \"(app-pages-browser)/./src/app/create-token/components/index.ts\");\n/* harmony import */ var _hooks__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./hooks */ \"(app-pages-browser)/./src/app/create-token/hooks/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n/**\r\n * CreateTokenPage Component\r\n *\r\n * Main component for the token creation page. Manages the overall flow of token\r\n * creation including form handling, deployment process, and result display.\r\n */ function CreateTokenPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams)();\n    const defaultNetwork = searchParams.get('network') || 'amoy';\n    // State Management\n    const [network, setNetwork] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultNetwork);\n    const [factoryAddress, setFactoryAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [hasDeployerRole, setHasDeployerRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [kycSupported, setKYCSupported] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        symbol: '',\n        decimals: 18,\n        maxSupply: '1000000',\n        ownerAddress: '',\n        tokenPrice: '10',\n        currency: 'USD',\n        tokenType: 'equity',\n        bonusTiers: 'Tier 1: 5%, Tier 2: 10%, Tier 3: 15%',\n        enableKYC: false,\n        tokenImageUrl: '',\n        selectedClaims: [\n            '10101010000001',\n            '10101010000004'\n        ],\n        issuerCountry: 'US' // Default country\n    });\n    // Use the custom hook for token deployment logic\n    const { isSubmitting, error, success, deployedToken, transactionHash, deploymentStep, deployToken } = (0,_hooks__WEBPACK_IMPORTED_MODULE_7__.useTokenDeployment)(network, factoryAddress, hasDeployerRole, kycSupported);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CreateTokenPage.useEffect\": ()=>{\n            // Get factory address for current network\n            const addresses = (0,_config__WEBPACK_IMPORTED_MODULE_4__.getContractAddresses)(network);\n            setFactoryAddress(addresses.factory || '');\n            // Auto-fill owner address from connected wallet and check permissions\n            if (window.ethereum) {\n                initWallet();\n            }\n        }\n    }[\"CreateTokenPage.useEffect\"], [\n        network,\n        factoryAddress\n    ]);\n    /**\r\n   * Initialize wallet and check permissions\r\n   */ const initWallet = async ()=>{\n        try {\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_8__.BrowserProvider(window.ethereum);\n            const accounts = await provider.listAccounts();\n            if (accounts.length > 0) {\n                const userAddress = accounts[0].address;\n                setFormData((prev)=>({\n                        ...prev,\n                        ownerAddress: userAddress\n                    }));\n                // Check if user has DEPLOYER_ROLE and if KYC is supported\n                if (factoryAddress) {\n                    checkDeployerPermissions(provider, userAddress);\n                }\n            }\n        } catch (err) {\n            console.error(\"Error initializing wallet:\", err);\n        }\n    };\n    /**\r\n   * Check if the user has DEPLOYER_ROLE and if KYC is supported\r\n   */ const checkDeployerPermissions = async (provider, userAddress)=>{\n        try {\n            const factory = new ethers__WEBPACK_IMPORTED_MODULE_9__.Contract(factoryAddress, _contracts_SecurityTokenFactory_json__WEBPACK_IMPORTED_MODULE_5__.abi, provider);\n            // Check if user has DEPLOYER_ROLE\n            const DEPLOYER_ROLE = await factory.DEPLOYER_ROLE();\n            const hasRole = await factory.hasRole(DEPLOYER_ROLE, userAddress);\n            setHasDeployerRole(hasRole);\n            if (!hasRole) {\n                console.warn(\"Connected wallet does not have DEPLOYER_ROLE\");\n            }\n            // Check if KYC is supported - Enhanced factory has built-in KYC\n            try {\n                // Check for enhanced factory functions\n                const hasEnhancedFunction = factory.interface.fragments.some((fragment)=>fragment.type === \"function\" && 'name' in fragment && fragment.name === \"deploySecurityToken\");\n                // Enhanced factory at this address has built-in KYC support\n                const isEnhancedFactory = factoryAddress === \"******************************************\" || factoryAddress === \"******************************************\";\n                if (isEnhancedFactory || hasEnhancedFunction) {\n                    setKYCSupported(true);\n                    console.log(\"✅ Enhanced factory with built-in KYC support detected\");\n                } else {\n                    // Fallback to old KYC detection method\n                    await factory.whitelistWithKYCImplementation();\n                    const hasKYCFunction = factory.interface.fragments.some((fragment)=>fragment.type === \"function\" && 'name' in fragment && fragment.name === \"deploySecurityTokenWithOptions\");\n                    setKYCSupported(hasKYCFunction);\n                }\n            } catch (err) {\n                console.warn(\"KYC functionality not supported in this factory contract\");\n                setKYCSupported(false);\n            }\n        } catch (err) {\n            console.error(\"Error checking deployer role:\", err);\n        }\n    };\n    /**\r\n   * Handle input changes in the form\r\n   */ const handleInputChange = (e)=>{\n        const { name, value, type } = e.target;\n        let processedValue = value;\n        // Convert decimals to number\n        if (name === 'decimals') {\n            processedValue = parseInt(value, 10);\n        } else if (type === 'checkbox') {\n            processedValue = e.target.checked;\n        }\n        setFormData({\n            ...formData,\n            [name]: processedValue\n        });\n    };\n    /**\r\n   * Handle network change\r\n   */ const handleNetworkChange = (e)=>{\n        setNetwork(e.target.value);\n        console.log(\"Switched to network: \".concat(e.target.value));\n    };\n    /**\r\n   * Handle form submission\r\n   */ const handleSubmit = async (e)=>{\n        e.preventDefault();\n        await deployToken(formData);\n    };\n    /**\r\n   * Add token manually to the dashboard\r\n   */ const addTokenManually = ()=>{\n        router.push(\"/?add=\".concat(formData.symbol));\n    };\n    /**\r\n   * Get the network label for display\r\n   */ const getNetworkLabel = (networkKey)=>{\n        switch(networkKey){\n            case 'amoy':\n                return 'Amoy Testnet';\n            case 'polygon':\n                return 'Polygon Mainnet';\n            default:\n                return networkKey;\n        }\n    };\n    /**\r\n   * Get block explorer URL for token or address\r\n   */ const getBlockExplorerUrl = (network, address)=>{\n        if (network === 'amoy') {\n            return \"https://www.oklink.com/amoy/address/\".concat(address);\n        } else if (network === 'polygon') {\n            return \"https://polygonscan.com/address/\".concat(address);\n        }\n        return '#';\n    };\n    /**\r\n   * Get transaction explorer URL\r\n   */ const getTransactionExplorerUrl = (network, txHash)=>{\n        if (network === 'amoy') {\n            return \"https://www.oklink.com/amoy/tx/\".concat(txHash);\n        } else if (network === 'polygon') {\n            return \"https://polygonscan.com/tx/\".concat(txHash);\n        }\n        return '#';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/\",\n                        className: \"text-blue-600 hover:text-blue-800 mr-4\",\n                        children: \"← Back to Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold\",\n                        children: \"Create New Security Token\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-4 px-3 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800\",\n                        children: getNetworkLabel(network)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                lineNumber: 234,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 p-4 bg-green-50 border border-green-200 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-green-600 text-xl\",\n                                children: \"\\uD83C\\uDF89\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-green-800\",\n                                    children: \"Advanced Transfer Controls Now Available!\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-1 text-sm text-green-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"All new tokens automatically include advanced transfer control features:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"mt-1 list-disc list-inside space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Conditional Transfers:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                                                            lineNumber: 257,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Require approval for all transfers\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Transfer Whitelisting:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                                                            lineNumber: 258,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Additional access control layer\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Transfer Fees:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Configurable percentage-based fees\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-xs\",\n                                            children: [\n                                                \"\\uD83D\\uDCA1 After creating your token, visit the \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Transfer Controls\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 57\n                                                }, this),\n                                                \" page to configure these features.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                lineNumber: 245,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_6__.FactoryInfo, {\n                factoryAddress: factoryAddress,\n                network: network,\n                hasDeployerRole: hasDeployerRole\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                lineNumber: 270,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_6__.NetworkBanner, {\n                network: network\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                lineNumber: 277,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_6__.StatusNotification, {\n                type: \"error\",\n                message: error,\n                deploymentStep: deploymentStep,\n                tokenSymbol: formData.symbol,\n                onAddManually: addTokenManually\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                lineNumber: 281,\n                columnNumber: 9\n            }, this),\n            deployedToken && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_6__.TokenDeployment, {\n                deployedToken: deployedToken,\n                transactionHash: transactionHash,\n                network: network,\n                getBlockExplorerUrl: getBlockExplorerUrl,\n                getTransactionExplorerUrl: getTransactionExplorerUrl\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                lineNumber: 292,\n                columnNumber: 9\n            }, this),\n            !deployedToken && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_6__.TokenForm, {\n                formData: formData,\n                handleInputChange: handleInputChange,\n                handleNetworkChange: handleNetworkChange,\n                handleSubmit: handleSubmit,\n                isSubmitting: isSubmitting,\n                network: network,\n                getNetworkLabel: getNetworkLabel,\n                deploymentStep: deploymentStep,\n                kycSupported: kycSupported\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n                lineNumber: 303,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\page.tsx\",\n        lineNumber: 233,\n        columnNumber: 5\n    }, this);\n}\n_s(CreateTokenPage, \"zljWvDNxsFNKvjWLmGn+1qG78Kc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams,\n        _hooks__WEBPACK_IMPORTED_MODULE_7__.useTokenDeployment\n    ];\n});\n_c = CreateTokenPage;\nvar _c;\n$RefreshReg$(_c, \"CreateTokenPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/create-token/page.tsx\n"));

/***/ })

});