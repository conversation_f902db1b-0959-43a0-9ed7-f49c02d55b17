"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/create-token/page",{

/***/ "(app-pages-browser)/./src/app/create-token/hooks/useTokenDeployment.ts":
/*!**********************************************************!*\
  !*** ./src/app/create-token/hooks/useTokenDeployment.ts ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTokenDeployment: () => (/* binding */ useTokenDeployment)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/constants/addresses.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/address/checks.js\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../config */ \"(app-pages-browser)/./src/config.ts\");\n/* harmony import */ var _contracts_SecurityTokenFactory_json__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../contracts/SecurityTokenFactory.json */ \"(app-pages-browser)/./src/contracts/SecurityTokenFactory.json\");\n/* harmony import */ var _contracts_SecurityToken_json__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../contracts/SecurityToken.json */ \"(app-pages-browser)/./src/contracts/SecurityToken.json\");\n/* harmony import */ var _useERC3643Integration__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./useERC3643Integration */ \"(app-pages-browser)/./src/app/create-token/hooks/useERC3643Integration.ts\");\n\n\n\n\n\n\n/**\r\n * Custom hook for token deployment logic\r\n *\r\n * Encapsulates all the token deployment functionality including state management,\r\n * transaction handling, and error handling\r\n */ function useTokenDeployment(network, factoryAddress, hasDeployerRole, kycSupported) {\n    // State management\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [deployedToken, setDeployedToken] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [transactionHash, setTransactionHash] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [deploymentStep, setDeploymentStep] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('idle');\n    // ERC-3643 integration\n    const { setupERC3643Compliance, isERC3643Available } = (0,_useERC3643Integration__WEBPACK_IMPORTED_MODULE_4__.useERC3643Integration)();\n    /**\r\n   * Save token data to database\r\n   */ const saveTokenToDatabase = async (deployedToken, formData, transactionHash, blockNumber, network)=>{\n        // Fetch totalSupply from the blockchain\n        let totalSupply = '0';\n        try {\n            // Create a new provider instance for blockchain calls\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_5__.BrowserProvider(window.ethereum);\n            const token = new ethers__WEBPACK_IMPORTED_MODULE_6__.Contract(deployedToken.address, _contracts_SecurityToken_json__WEBPACK_IMPORTED_MODULE_3__.abi, provider);\n            const totalSupplyRaw = await token.totalSupply();\n            totalSupply = deployedToken.decimals === 0 ? totalSupplyRaw.toString() : ethers__WEBPACK_IMPORTED_MODULE_7__.formatUnits(totalSupplyRaw, deployedToken.decimals);\n        } catch (error) {\n            console.warn('Could not fetch totalSupply from blockchain, using default 0:', error);\n        }\n        const tokenData = {\n            address: deployedToken.address,\n            transactionHash: transactionHash,\n            blockNumber: blockNumber,\n            network: network,\n            name: deployedToken.name,\n            symbol: deployedToken.symbol,\n            decimals: deployedToken.decimals,\n            maxSupply: deployedToken.maxSupply,\n            totalSupply: totalSupply,\n            tokenType: formData.tokenType,\n            tokenPrice: deployedToken.tokenPrice,\n            currency: deployedToken.currency,\n            bonusTiers: deployedToken.bonusTiers,\n            tokenImageUrl: deployedToken.tokenImageUrl,\n            whitelistAddress: deployedToken.whitelistAddress,\n            adminAddress: deployedToken.admin,\n            hasKYC: deployedToken.hasKYC,\n            selectedClaims: formData.selectedClaims,\n            isActive: true,\n            deployedBy: deployedToken.admin,\n            deploymentNotes: \"\".concat(formData.tokenType, \" token deployed via admin panel\")\n        };\n        const response = await fetch('/api/tokens', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify(tokenData)\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(\"Database save failed: \".concat(errorData.error || 'Unknown error'));\n        }\n        return await response.json();\n    };\n    /**\r\n   * Deploy a new token with the provided form data\r\n   */ const deployToken = async (formData)=>{\n        setIsSubmitting(true);\n        setError(null);\n        setSuccess(null);\n        setDeployedToken(null);\n        setTransactionHash(null);\n        setDeploymentStep('preparing');\n        try {\n            // Validate form data\n            validateFormData(formData);\n            // Get network configuration\n            const networkConfig = (0,_config__WEBPACK_IMPORTED_MODULE_1__.getNetworkConfig)(network);\n            if (!factoryAddress) {\n                throw new Error(\"No factory address configured for network: \".concat(network));\n            }\n            if (!window.ethereum) {\n                throw new Error('Please install MetaMask to use this feature!');\n            }\n            setDeploymentStep('connecting');\n            // Get provider and signer\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_5__.BrowserProvider(window.ethereum);\n            const signer = await provider.getSigner();\n            // Verify network connection\n            await verifyNetworkConnection(provider, network);\n            // Connect to the factory contract\n            const factory = new ethers__WEBPACK_IMPORTED_MODULE_6__.Contract(factoryAddress, _contracts_SecurityTokenFactory_json__WEBPACK_IMPORTED_MODULE_2__.abi, signer);\n            console.log(\"Connected to factory at:\", factoryAddress);\n            // Verify deployer role\n            await verifyDeployerRole(factory, signer, hasDeployerRole);\n            // Verify KYC support\n            await verifyKYCSupport(factory, formData.enableKYC);\n            // Check if token symbol already exists\n            await checkTokenSymbolAvailability(factory, formData.symbol);\n            // Convert maxSupply to the appropriate unit based on decimals\n            const maxSupplyWei = formData.decimals === 0 ? BigInt(formData.maxSupply) : ethers__WEBPACK_IMPORTED_MODULE_7__.parseUnits(formData.maxSupply, formData.decimals);\n            setDeploymentStep('deploying');\n            console.log(\"Deploying token with params:\", {\n                name: formData.name,\n                symbol: formData.symbol,\n                decimals: formData.decimals,\n                maxSupply: formData.maxSupply,\n                admin: formData.ownerAddress,\n                tokenPrice: formData.tokenPrice,\n                bonusTiers: formData.bonusTiers,\n                enableKYC: formData.enableKYC\n            });\n            // Create the transaction\n            const tx = await createDeployTransaction(factory, formData, maxSupplyWei, network, kycSupported);\n            setTransactionHash(tx.hash);\n            console.log(\"Transaction hash:\", tx.hash);\n            setDeploymentStep('confirming');\n            // Wait for the transaction to be mined\n            const receipt = await tx.wait();\n            console.log(\"Transaction mined in block:\", receipt.blockNumber);\n            setDeploymentStep('fetching');\n            // Get the token address\n            const tokenAddress = await factory.getTokenAddressBySymbol(formData.symbol);\n            if (tokenAddress && tokenAddress !== ethers__WEBPACK_IMPORTED_MODULE_8__.ZeroAddress) {\n                // Create token deployment result\n                const deploymentResult = await getDeploymentResult(tokenAddress, provider, formData);\n                setDeployedToken(deploymentResult);\n                // Save token to database\n                try {\n                    await saveTokenToDatabase(deploymentResult, formData, tx.hash, receipt.blockNumber.toString(), network);\n                    console.log(\"Token successfully saved to database\");\n                } catch (dbError) {\n                    console.warn(\"Failed to save token to database:\", dbError);\n                // Don't fail the deployment if database save fails\n                }\n                // Setup ERC-3643 compliance if available\n                if (isERC3643Available()) {\n                    setDeploymentStep('setting_up_compliance');\n                    console.log(\"🏛️ Setting up ERC-3643 compliance...\");\n                    try {\n                        const complianceResult = await setupERC3643Compliance(tokenAddress, formData.ownerAddress, signer, {\n                            name: formData.name,\n                            symbol: formData.symbol,\n                            tokenType: formData.tokenType,\n                            country: formData.issuerCountry || 'US',\n                            selectedClaims: formData.selectedClaims\n                        });\n                        if (complianceResult.errors.length > 0) {\n                            console.warn(\"⚠️ Some ERC-3643 setup steps failed:\", complianceResult.errors);\n                        // Don't fail deployment, just warn\n                        } else {\n                            console.log(\"✅ ERC-3643 compliance setup completed successfully\");\n                        }\n                    } catch (complianceError) {\n                        console.warn(\"⚠️ ERC-3643 compliance setup failed:\", complianceError);\n                    // Don't fail deployment, just warn\n                    }\n                } else {\n                    console.log(\"ℹ️ ERC-3643 contracts not available, skipping compliance setup\");\n                }\n                setDeploymentStep('completed');\n                setSuccess('Token \"'.concat(formData.name, '\" (').concat(formData.symbol, \") successfully deployed!\"));\n            } else {\n                throw new Error(\"Token deployment failed: Could not retrieve token address\");\n            }\n        } catch (err) {\n            handleDeploymentError(err, network);\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    /**\r\n   * Validate form data before deployment\r\n   */ const validateFormData = (formData)=>{\n        if (!formData.name || !formData.symbol || !formData.maxSupply || !formData.ownerAddress) {\n            throw new Error('Please fill in all required fields');\n        }\n        if (!ethers__WEBPACK_IMPORTED_MODULE_9__.isAddress(formData.ownerAddress)) {\n            throw new Error('Invalid owner address');\n        }\n        if (formData.decimals < 0 || formData.decimals > 18) {\n            throw new Error('Decimals must be between 0 and 18');\n        }\n    };\n    /**\r\n   * Verify that the wallet is connected to the correct network\r\n   */ const verifyNetworkConnection = async (provider, network)=>{\n        const chainId = (await provider.getNetwork()).chainId;\n        if (network === 'amoy' && chainId.toString() !== '80002') {\n            throw new Error('Please connect your wallet to the Amoy network (Chain ID: 80002)');\n        } else if (network === 'polygon' && chainId.toString() !== '137') {\n            throw new Error('Please connect your wallet to the Polygon network (Chain ID: 137)');\n        }\n    };\n    /**\r\n   * Verify that the connected wallet has DEPLOYER_ROLE\r\n   */ const verifyDeployerRole = async (factory, signer, hasRole)=>{\n        if (!hasRole) {\n            const DEPLOYER_ROLE = await factory.DEPLOYER_ROLE();\n            const hasDeployerRole = await factory.hasRole(DEPLOYER_ROLE, await signer.getAddress());\n            if (!hasDeployerRole) {\n                throw new Error(\"Your wallet does not have the DEPLOYER_ROLE required to create tokens\");\n            }\n        }\n    };\n    /**\r\n   * Check if token symbol is available\r\n   */ const checkTokenSymbolAvailability = async (factory, symbol)=>{\n        try {\n            const existingTokenAddress = await factory.getTokenAddressBySymbol(symbol);\n            if (existingTokenAddress !== ethers__WEBPACK_IMPORTED_MODULE_8__.ZeroAddress) {\n                throw new Error('Token with symbol \"'.concat(symbol, '\" already exists at address ').concat(existingTokenAddress, \". Please choose a different symbol.\"));\n            }\n        } catch (err) {\n            if (err.message.includes(\"already exists\")) {\n                throw err; // Re-throw our custom error\n            }\n            // If it's a different error, log it but don't fail the deployment\n            console.warn(\"Could not check token symbol availability:\", err.message);\n        }\n    };\n    /**\r\n   * Verify KYC support in the factory contract\r\n   */ const verifyKYCSupport = async (factory, enableKYC)=>{\n        if (!enableKYC) return; // Skip verification if KYC is not enabled\n        try {\n            // Check if this is the enhanced factory with built-in KYC\n            const factoryAddress = await factory.getAddress();\n            const isEnhancedFactory = factoryAddress === \"******************************************\";\n            if (isEnhancedFactory) {\n                console.log(\"✅ Enhanced factory with built-in KYC support detected\");\n                return; // Enhanced factory has built-in KYC, no need for further checks\n            }\n            // Fallback to old KYC detection method for other factories\n            const kycImplementation = await factory.whitelistWithKYCImplementation();\n            // Check function exists by examining the ABI\n            const hasKYCFunction = factory.interface.fragments.some((fragment)=>fragment.type === \"function\" && 'name' in fragment && fragment.name === \"deploySecurityTokenWithOptions\");\n            if (!hasKYCFunction) {\n                throw new Error(\"The deployed factory contract doesn't support the KYC functionality. Please deploy an updated factory contract.\");\n            }\n            if (kycImplementation === ethers__WEBPACK_IMPORTED_MODULE_8__.ZeroAddress) {\n                throw new Error(\"KYC implementation address is not set in the factory contract.\");\n            }\n        } catch (err) {\n            if (err.message.includes(\"whitelistWithKYCImplementation\")) {\n                throw new Error(\"The deployed factory contract doesn't support the KYC functionality. Please deploy an updated factory contract.\");\n            }\n            throw err;\n        }\n    };\n    /**\r\n   * Create the deployment transaction with the appropriate gas settings\r\n   */ const createDeployTransaction = async (factory, formData, maxSupplyWei, network, supportsKYC)=>{\n        // Determine if KYC is supported\n        let canUseKYC = supportsKYC;\n        try {\n            // Check if this is the enhanced factory with built-in KYC\n            const factoryAddress = await factory.getAddress();\n            const isEnhancedFactory = factoryAddress === \"******************************************\";\n            if (isEnhancedFactory) {\n                canUseKYC = true;\n                console.log(\"✅ Using enhanced factory with built-in KYC support\");\n            } else {\n                // Fallback to old KYC detection method\n                await factory.whitelistWithKYCImplementation();\n                const hasKYCFunction = factory.interface.fragments.some((fragment)=>fragment.type === \"function\" && 'name' in fragment && fragment.name === \"deploySecurityTokenWithOptions\");\n                canUseKYC = hasKYCFunction;\n            }\n        } catch (err) {\n            console.log(\"KYC functionality not supported in this factory contract, using standard deployment\");\n            canUseKYC = false;\n            // If KYC was enabled but not supported, warn the user\n            if (formData.enableKYC) {\n                console.warn(\"KYC requested but not supported by the contract. Proceeding with standard token deployment.\");\n            }\n        }\n        // Optimized gas settings for Amoy testnet\n        if (network === 'amoy') {\n            // Optimized gas settings for Amoy testnet - 5M gas limit, 50 gwei gas price\n            const gasLimit = BigInt(5000000);\n            const gasPrice = ethers__WEBPACK_IMPORTED_MODULE_7__.parseUnits(\"50\", \"gwei\");\n            console.log(\"Using optimized gas settings for Amoy testnet:\");\n            console.log(\"Gas limit:\", gasLimit.toString());\n            console.log(\"Gas price:\", ethers__WEBPACK_IMPORTED_MODULE_7__.formatUnits(gasPrice, \"gwei\"), \"gwei\");\n            // Call the appropriate function based on factory type\n            const factoryAddress = await factory.getAddress();\n            const isEnhancedFactory = factoryAddress === \"******************************************\";\n            if (isEnhancedFactory) {\n                console.log(\"Calling enhanced factory deploySecurityToken with built-in KYC\");\n                return await factory.deploySecurityToken(formData.name, formData.symbol, formData.decimals, maxSupplyWei, formData.ownerAddress, \"\".concat(formData.tokenPrice, \" \").concat(formData.currency), formData.bonusTiers, \"\".concat(formData.tokenType, \" token deployed via admin panel\"), formData.tokenImageUrl || \"\", {\n                    gasLimit,\n                    gasPrice\n                });\n            } else if (canUseKYC) {\n                console.log(\"Calling deploySecurityTokenWithOptions with KYC:\", formData.enableKYC);\n                return await factory.deploySecurityTokenWithOptions(formData.name, formData.symbol, formData.decimals, maxSupplyWei, formData.ownerAddress, \"\".concat(formData.tokenPrice, \" \").concat(formData.currency), formData.bonusTiers, \"\".concat(formData.tokenType, \" token deployed via admin panel\"), formData.tokenImageUrl || \"\", formData.enableKYC, {\n                    gasLimit,\n                    gasPrice\n                });\n            } else {\n                console.log(\"Calling deploySecurityToken (no KYC support)\");\n                return await factory.deploySecurityToken(formData.name, formData.symbol, formData.decimals, maxSupplyWei, formData.ownerAddress, \"\".concat(formData.tokenPrice, \" \").concat(formData.currency), formData.bonusTiers, \"\".concat(formData.tokenType, \" token deployed via admin panel\"), formData.tokenImageUrl || \"\", {\n                    gasLimit,\n                    gasPrice\n                });\n            }\n        } else {\n            // For other networks, try to estimate gas\n            let gasLimit;\n            try {\n                // Estimate gas based on which function we can call\n                let gasEstimate;\n                if (canUseKYC) {\n                    gasEstimate = await factory.deploySecurityTokenWithOptions.estimateGas(formData.name, formData.symbol, formData.decimals, maxSupplyWei, formData.ownerAddress, \"\".concat(formData.tokenPrice, \" \").concat(formData.currency), formData.bonusTiers, \"\".concat(formData.tokenType, \" token deployed via admin panel\"), formData.tokenImageUrl || \"\", formData.enableKYC);\n                } else {\n                    gasEstimate = await factory.deploySecurityToken.estimateGas(formData.name, formData.symbol, formData.decimals, maxSupplyWei, formData.ownerAddress, \"\".concat(formData.tokenPrice, \" \").concat(formData.currency), formData.bonusTiers, \"\".concat(formData.tokenType, \" token deployed via admin panel\"), formData.tokenImageUrl || \"\");\n                }\n                console.log(\"Gas estimate:\", gasEstimate.toString());\n                // Add 50% to the gas estimate to be safer\n                gasLimit = gasEstimate * BigInt(150) / BigInt(100);\n                console.log(\"Using calculated gas limit:\", gasLimit.toString());\n            } catch (estimateErr) {\n                console.error(\"Gas estimation failed, using fixed limit:\", estimateErr);\n                // Fallback to fixed gas limit if estimation fails\n                gasLimit = BigInt(2000000); // Use 2M for non-Amoy networks\n                console.log(\"Using fallback gas limit:\", gasLimit.toString());\n            }\n            // Call without specific gas price for networks that calculate it properly\n            if (canUseKYC) {\n                return await factory.deploySecurityTokenWithOptions(formData.name, formData.symbol, formData.decimals, maxSupplyWei, formData.ownerAddress, \"\".concat(formData.tokenPrice, \" \").concat(formData.currency), formData.bonusTiers, \"\".concat(formData.tokenType, \" token deployed via admin panel\"), formData.tokenImageUrl || \"\", formData.enableKYC, {\n                    gasLimit\n                });\n            } else {\n                return await factory.deploySecurityToken(formData.name, formData.symbol, formData.decimals, maxSupplyWei, formData.ownerAddress, \"\".concat(formData.tokenPrice, \" \").concat(formData.currency), formData.bonusTiers, \"\".concat(formData.tokenType, \" token deployed via admin panel\"), formData.tokenImageUrl || \"\", {\n                    gasLimit\n                });\n            }\n        }\n    };\n    /**\r\n   * Get deployment result after successful transaction\r\n   */ const getDeploymentResult = async (tokenAddress, provider, formData)=>{\n        console.log(\"Token successfully deployed at:\", tokenAddress);\n        // Connect to the token contract\n        const token = new ethers__WEBPACK_IMPORTED_MODULE_6__.Contract(tokenAddress, _contracts_SecurityToken_json__WEBPACK_IMPORTED_MODULE_3__.abi, provider);\n        // Get identity registry address (whitelist contract)\n        const whitelistAddress = await token.identityRegistry();\n        // Get token decimals from the contract\n        const tokenDecimals = await token.decimals();\n        const decimalsNumber = Number(tokenDecimals);\n        // Format maxSupply based on decimals\n        const maxSupplyRaw = await token.maxSupply();\n        const maxSupplyFormatted = decimalsNumber === 0 ? maxSupplyRaw.toString() : ethers__WEBPACK_IMPORTED_MODULE_7__.formatUnits(maxSupplyRaw, tokenDecimals);\n        // Try to get the image URL from the contract if supported\n        let tokenImageUrl = formData.tokenImageUrl;\n        try {\n            // Check if the token contract supports tokenImageUrl function\n            if (token.interface.fragments.some((fragment)=>fragment.type === \"function\" && 'name' in fragment && fragment.name === \"tokenImageUrl\")) {\n                tokenImageUrl = await token.tokenImageUrl();\n            }\n        } catch (error) {\n            console.log(\"Token contract doesn't support image URL, using form data\");\n        }\n        // Create deployed token object\n        return {\n            address: tokenAddress,\n            name: await token.name(),\n            symbol: await token.symbol(),\n            decimals: decimalsNumber,\n            maxSupply: maxSupplyFormatted,\n            whitelistAddress: whitelistAddress,\n            admin: formData.ownerAddress,\n            tokenPrice: \"\".concat(formData.tokenPrice, \" \").concat(formData.currency),\n            currency: formData.currency,\n            bonusTiers: formData.bonusTiers,\n            hasKYC: formData.enableKYC,\n            tokenImageUrl: tokenImageUrl\n        };\n    };\n    /**\r\n   * Handle deployment errors with detailed messages\r\n   */ const handleDeploymentError = (err, network)=>{\n        console.error('Error creating token:', err);\n        // Extract more details from the error for debugging\n        const errorDetails = typeof err === 'object' ? JSON.stringify({\n            code: err.code,\n            message: err.message,\n            data: err.data,\n            info: err.info\n        }, null, 2) : String(err);\n        // Special handling for specific contract errors\n        if (err.message.includes(\"transaction execution reverted\")) {\n            // This is likely a contract validation error\n            setError(\"Transaction failed: The contract rejected the transaction. This could be due to:\\n\\n• Token symbol already exists - try a different symbol\\n• Invalid parameters (empty name/symbol, zero max supply, etc.)\\n• Access control issues\\n\\nPlease check your inputs and try again with a unique token symbol.\\n\\nTechnical details: \".concat(err.message));\n        } else if (err.message.includes(\"gas required exceeds allowance\") || err.message.includes(\"intrinsic gas too low\") || err.message.includes(\"Internal JSON-RPC error\")) {\n            // For Amoy testnet specifically, provide CLI alternative\n            if (network === 'amoy') {\n                // Create a CLI command template - actual values will be filled in by the UI\n                const cliCommand = '# For Windows PowerShell:\\ncd Token\\n$env:NETWORK=\"amoy\"\\n$env:TOKEN_NAME=\"YourTokenName\"\\n$env:TOKEN_SYMBOL=\"YTS\"\\n$env:TOKEN_DECIMALS=\"0\"\\n$env:MAX_SUPPLY=\"1000000\"\\n$env:ADMIN_ADDRESS=\"0xYourAddress\"\\n$env:TOKEN_PRICE=\"10 USD\"\\n$env:BONUS_TIERS=\"Tier 1: 5%, Tier 2: 10%\"\\nnpx hardhat run scripts/02-deploy-token.js --network amoy';\n                setError(\"Gas estimation failed on Amoy testnet. This is a common issue with this network.\\n\\nYou can try using this command line script instead:\\n\\n\".concat(cliCommand));\n            } else {\n                setError(\"Transaction failed due to gas calculation issues: \".concat(err.message, \"\\n\\nDetails: \").concat(errorDetails));\n            }\n        } else if (err.message.includes(\"Internal JSON-RPC error\") || err.message.includes(\"could not coalesce error\")) {\n            setError(\"Transaction failed. This is likely due to gas calculation issues on the Amoy testnet. Try using the command line script instead.\");\n        } else {\n            setError(\"Transaction failed: \".concat(err.message, \"\\n\\nDetails: \").concat(errorDetails));\n        }\n        setDeploymentStep('failed');\n    };\n    return {\n        isSubmitting,\n        error,\n        success,\n        deployedToken,\n        transactionHash,\n        deploymentStep,\n        deployToken\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/create-token/hooks/useTokenDeployment.ts\n"));

/***/ })

});