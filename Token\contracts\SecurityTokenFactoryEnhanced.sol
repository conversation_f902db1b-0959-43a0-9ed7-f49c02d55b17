// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol";
import "./SecurityTokenEnhanced.sol";
import "./IdentityRegistry.sol";
import "./Compliance.sol";
import "./ClaimRegistry.sol";

/**
 * @title SecurityTokenFactoryEnhanced
 * @dev Enhanced factory with critical security fixes
 * Size-optimized for deployment while maintaining security
 */
contract SecurityTokenFactoryEnhanced is AccessControl, ReentrancyGuard {
    bytes32 public constant DEPLOYER_ROLE = keccak256("DEPLOYER_ROLE");

    // Implementation contracts
    address public securityTokenImplementation;
    address public identityRegistryImplementation;
    address public complianceImplementation;
    address public claimRegistryImplementation;

    // Deployed tokens tracking
    address[] public deployedTokens;
    mapping(string => address) public tokensBySymbol;
    mapping(address => bool) public isDeployedToken;
    mapping(address => TokenInfo) public tokenInfo;

    struct TokenInfo {
        address tokenAddress;
        address identityRegistry;
        address compliance;
        address claimRegistry;
        string name;
        string symbol;
        uint8 decimals;
        uint256 maxSupply;
        address admin;
        uint256 deploymentTimestamp;
    }
    
    // Events
    event TokenDeployed(
        address indexed tokenAddress,
        address indexed identityRegistry,
        address indexed compliance,
        address claimRegistry,
        string name,
        string symbol,
        uint8 decimals,
        uint256 maxSupply,
        address admin
    );
    
    constructor(address admin) {
        require(admin != address(0), "Invalid admin");

        // Deploy implementation contracts
        securityTokenImplementation = address(new SecurityTokenEnhanced(
            "Implementation", "IMPL", 18, 1, admin, "", "", "", address(0), address(0)
        ));

        identityRegistryImplementation = address(new IdentityRegistry());
        complianceImplementation = address(new Compliance());
        claimRegistryImplementation = address(new ClaimRegistry());

        _grantRole(DEFAULT_ADMIN_ROLE, admin);
        _grantRole(DEPLOYER_ROLE, admin);
    }
    
    function deploySecurityToken(
        string memory name,
        string memory symbol,
        uint8 decimals,
        uint256 maxSupply,
        address admin,
        string memory tokenPrice,
        string memory bonusTiers,
        string memory tokenDetails,
        string memory tokenImageUrl
    ) external onlyRole(DEPLOYER_ROLE) nonReentrant returns (address tokenAddress) {
        // Input validation (enhanced security)
        require(bytes(name).length > 0 && bytes(name).length <= 50, "Invalid name");
        require(bytes(symbol).length > 0 && bytes(symbol).length <= 10, "Invalid symbol");
        require(admin != address(0), "Invalid admin");
        require(maxSupply > 0 && maxSupply <= 1e30, "Invalid max supply");
        require(decimals <= 18, "Invalid decimals");
        require(tokensBySymbol[symbol] == address(0), "Symbol exists");
        
        // Deploy claim registry
        bytes memory claimData = abi.encodeWithSelector(
            ClaimRegistry(address(0)).initialize.selector,
            admin
        );
        ERC1967Proxy claimProxy = new ERC1967Proxy(claimRegistryImplementation, claimData);
        address claimRegistry = address(claimProxy);

        // Deploy identity registry (whitelist)
        bytes memory identityData = abi.encodeWithSelector(
            IdentityRegistry(address(0)).initialize.selector,
            admin,
            claimRegistry
        );
        ERC1967Proxy identityProxy = new ERC1967Proxy(identityRegistryImplementation, identityData);
        address identityRegistry = address(identityProxy);

        // Deploy compliance
        bytes memory complianceData = abi.encodeWithSelector(
            Compliance(address(0)).initialize.selector,
            admin,
            identityRegistry
        );
        ERC1967Proxy complianceProxy = new ERC1967Proxy(complianceImplementation, complianceData);
        address compliance = address(complianceProxy);

        // Deploy token with enhanced security
        SecurityTokenEnhanced token = new SecurityTokenEnhanced(
            name,
            symbol,
            decimals,
            maxSupply,
            admin,
            tokenPrice,
            bonusTiers,
            tokenImageUrl,
            identityRegistry,
            compliance
        );

        tokenAddress = address(token);

        // Store token info
        tokenInfo[tokenAddress] = TokenInfo({
            tokenAddress: tokenAddress,
            identityRegistry: identityRegistry,
            compliance: compliance,
            claimRegistry: claimRegistry,
            name: name,
            symbol: symbol,
            decimals: decimals,
            maxSupply: maxSupply,
            admin: admin,
            deploymentTimestamp: block.timestamp
        });
        
        // Track deployment
        deployedTokens.push(tokenAddress);
        tokensBySymbol[symbol] = tokenAddress;
        isDeployedToken[tokenAddress] = true;
        
        emit TokenDeployed(
            tokenAddress,
            identityRegistry,
            compliance,
            claimRegistry,
            name,
            symbol,
            decimals,
            maxSupply,
            admin
        );
        
        return tokenAddress;
    }
    
    function addDeployer(address deployer) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(deployer != address(0), "Invalid deployer");
        _grantRole(DEPLOYER_ROLE, deployer);
    }
    
    function removeDeployer(address deployer) external onlyRole(DEFAULT_ADMIN_ROLE) {
        _revokeRole(DEPLOYER_ROLE, deployer);
    }

    function getTokenInfo(address token) external view returns (TokenInfo memory) {
        require(isDeployedToken[token], "Token not deployed by this factory");
        return tokenInfo[token];
    }
    
    function getTokenAddressBySymbol(string memory symbol) external view returns (address) {
        return tokensBySymbol[symbol];
    }
    
    function getAllDeployedTokens() external view returns (address[] memory) {
        return deployedTokens;
    }
    
    function getDeployedToken(uint256 index) external view returns (address) {
        require(index < deployedTokens.length, "Index out of bounds");
        return deployedTokens[index];
    }
    
    function getTokenCount() external view returns (uint256) {
        return deployedTokens.length;
    }
    
    function getTokensBatch(uint256 start, uint256 count) external view returns (address[] memory) {
        require(start < deployedTokens.length, "Start out of bounds");
        require(count <= 10, "Batch too large");
        
        uint256 end = start + count;
        if (end > deployedTokens.length) {
            end = deployedTokens.length;
        }
        
        address[] memory batch = new address[](end - start);
        for (uint256 i = start; i < end; i++) {
            batch[i - start] = deployedTokens[i];
        }
        
        return batch;
    }
}
