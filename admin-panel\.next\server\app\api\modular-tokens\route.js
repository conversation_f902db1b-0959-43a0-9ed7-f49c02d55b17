/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/modular-tokens/route";
exports.ids = ["app/api/modular-tokens/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmodular-tokens%2Froute&page=%2Fapi%2Fmodular-tokens%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmodular-tokens%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmodular-tokens%2Froute&page=%2Fapi%2Fmodular-tokens%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmodular-tokens%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_tokendev_newroo_admin_panel_src_app_api_modular_tokens_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/modular-tokens/route.ts */ \"(rsc)/./src/app/api/modular-tokens/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/modular-tokens/route\",\n        pathname: \"/api/modular-tokens\",\n        filename: \"route\",\n        bundlePath: \"app/api/modular-tokens/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api\\\\modular-tokens\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_tokendev_newroo_admin_panel_src_app_api_modular_tokens_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmodular-tokens%2Froute&page=%2Fapi%2Fmodular-tokens%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmodular-tokens%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/modular-tokens/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/modular-tokens/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/providers/provider-jsonrpc.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/crypto/keccak.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/utils/utf8.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/address/checks.js\");\n/* harmony import */ var _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contracts/SecurityTokenCore.json */ \"(rsc)/./src/contracts/SecurityTokenCore.json\");\n/* harmony import */ var _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contracts/UpgradeManager.json */ \"(rsc)/./src/contracts/UpgradeManager.json\");\n\n\n// Import ABIs\n\n\n// Contract addresses from environment\nconst SECURITY_TOKEN_CORE_ADDRESS = process.env.AMOY_SECURITY_TOKEN_CORE_ADDRESS;\nconst UPGRADE_MANAGER_ADDRESS = process.env.AMOY_UPGRADE_MANAGER_ADDRESS;\nconst AMOY_RPC_URL = process.env.AMOY_RPC_URL;\n// Initialize provider\nconst provider = new ethers__WEBPACK_IMPORTED_MODULE_3__.JsonRpcProvider(AMOY_RPC_URL);\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const action = searchParams.get('action');\n        if (!SECURITY_TOKEN_CORE_ADDRESS || !UPGRADE_MANAGER_ADDRESS) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Contract addresses not configured'\n            }, {\n                status: 500\n            });\n        }\n        switch(action){\n            case 'token-info':\n                return await getTokenInfo();\n            case 'upgrade-info':\n                return await getUpgradeInfo();\n            case 'pending-upgrades':\n                return await getPendingUpgrades();\n            case 'upgrade-history':\n                return await getUpgradeHistory();\n            default:\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Invalid action parameter'\n                }, {\n                    status: 400\n                });\n        }\n    } catch (error) {\n        console.error('API Error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function getTokenInfo() {\n    try {\n        const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_1__, provider);\n        const [name, symbol, version, totalSupply, maxSupply, decimals, paused, metadata] = await Promise.all([\n            contract.name(),\n            contract.symbol(),\n            contract.version(),\n            contract.totalSupply(),\n            contract.maxSupply(),\n            contract.decimals(),\n            contract.paused(),\n            contract.getTokenMetadata()\n        ]);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            name,\n            symbol,\n            version,\n            totalSupply: ethers__WEBPACK_IMPORTED_MODULE_5__.formatUnits(totalSupply, decimals),\n            maxSupply: ethers__WEBPACK_IMPORTED_MODULE_5__.formatUnits(maxSupply, decimals),\n            decimals,\n            paused,\n            metadata: {\n                tokenPrice: metadata[0],\n                bonusTiers: metadata[1],\n                tokenDetails: metadata[2],\n                tokenImageUrl: metadata[3]\n            },\n            contractAddress: SECURITY_TOKEN_CORE_ADDRESS\n        });\n    } catch (error) {\n        console.error('Error getting token info:', error);\n        throw error;\n    }\n}\nasync function getUpgradeInfo() {\n    try {\n        const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__, provider);\n        const [emergencyModeActive, registeredModules, upgradeDelay, emergencyModeDuration] = await Promise.all([\n            contract.isEmergencyModeActive(),\n            contract.getRegisteredModules(),\n            contract.UPGRADE_DELAY(),\n            contract.EMERGENCY_MODE_DURATION()\n        ]);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            emergencyModeActive,\n            registeredModules,\n            upgradeDelay: Number(upgradeDelay),\n            emergencyModeDuration: Number(emergencyModeDuration),\n            contractAddress: UPGRADE_MANAGER_ADDRESS\n        });\n    } catch (error) {\n        console.error('Error getting upgrade info:', error);\n        throw error;\n    }\n}\nasync function getPendingUpgrades() {\n    try {\n        const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__, provider);\n        const pendingUpgradeIds = await contract.getPendingUpgradeIds();\n        const pendingUpgrades = await Promise.all(pendingUpgradeIds.map(async (id)=>{\n            const upgrade = await contract.pendingUpgrades(id);\n            return {\n                upgradeId: id,\n                moduleId: upgrade.moduleId,\n                proxy: upgrade.proxy,\n                newImplementation: upgrade.newImplementation,\n                executeTime: Number(upgrade.executeTime),\n                executed: upgrade.executed,\n                cancelled: upgrade.cancelled,\n                description: upgrade.description\n            };\n        }));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            pendingUpgrades\n        });\n    } catch (error) {\n        console.error('Error getting pending upgrades:', error);\n        throw error;\n    }\n}\nasync function getUpgradeHistory() {\n    try {\n        const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__, provider);\n        // Get upgrade history for SecurityTokenCore\n        const SECURITY_TOKEN_CORE_ID = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"SECURITY_TOKEN_CORE\"));\n        const history = await contract.getUpgradeHistory(SECURITY_TOKEN_CORE_ID);\n        const upgradeHistory = history.map((record)=>({\n                oldImplementation: record.oldImplementation,\n                newImplementation: record.newImplementation,\n                timestamp: Number(record.timestamp),\n                executor: record.executor,\n                version: record.version,\n                isEmergency: record.isEmergency,\n                description: record.description\n            }));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            upgradeHistory\n        });\n    } catch (error) {\n        console.error('Error getting upgrade history:', error);\n        throw error;\n    }\n}\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { action, ...params } = body;\n        // Note: POST operations require a private key to sign transactions\n        // For security, these should be handled client-side with user's wallet\n        // This endpoint can be used for validation or preparation\n        switch(action){\n            case 'validate-mint':\n                return await validateMint(params);\n            case 'validate-upgrade':\n                return await validateUpgrade(params);\n            default:\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Invalid action parameter'\n                }, {\n                    status: 400\n                });\n        }\n    } catch (error) {\n        console.error('API Error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function validateMint(params) {\n    try {\n        const { address, amount } = params;\n        // Validate address format\n        if (!ethers__WEBPACK_IMPORTED_MODULE_8__.isAddress(address)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                valid: false,\n                error: 'Invalid address format'\n            }, {\n                status: 400\n            });\n        }\n        // Validate amount\n        const parsedAmount = parseFloat(amount);\n        if (isNaN(parsedAmount) || parsedAmount <= 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                valid: false,\n                error: 'Invalid amount'\n            }, {\n                status: 400\n            });\n        }\n        // Check if minting would exceed max supply\n        const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_1__, provider);\n        const [totalSupply, maxSupply, decimals] = await Promise.all([\n            contract.totalSupply(),\n            contract.maxSupply(),\n            contract.decimals()\n        ]);\n        const amountWei = ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits(amount, decimals);\n        const newTotalSupply = totalSupply + amountWei;\n        if (newTotalSupply > maxSupply) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                valid: false,\n                error: 'Minting would exceed maximum supply',\n                currentSupply: ethers__WEBPACK_IMPORTED_MODULE_5__.formatUnits(totalSupply, decimals),\n                maxSupply: ethers__WEBPACK_IMPORTED_MODULE_5__.formatUnits(maxSupply, decimals),\n                requestedAmount: amount\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            valid: true,\n            currentSupply: ethers__WEBPACK_IMPORTED_MODULE_5__.formatUnits(totalSupply, decimals),\n            maxSupply: ethers__WEBPACK_IMPORTED_MODULE_5__.formatUnits(maxSupply, decimals),\n            newTotalSupply: ethers__WEBPACK_IMPORTED_MODULE_5__.formatUnits(newTotalSupply, decimals)\n        });\n    } catch (error) {\n        console.error('Error validating mint:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            valid: false,\n            error: 'Validation failed'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function validateUpgrade(params) {\n    try {\n        const { implementationAddress, description } = params;\n        // Validate implementation address format\n        if (!ethers__WEBPACK_IMPORTED_MODULE_8__.isAddress(implementationAddress)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                valid: false,\n                error: 'Invalid implementation address format'\n            }, {\n                status: 400\n            });\n        }\n        // Validate description\n        if (!description || description.trim().length < 10) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                valid: false,\n                error: 'Description must be at least 10 characters'\n            }, {\n                status: 400\n            });\n        }\n        // Check if implementation address has code\n        const code = await provider.getCode(implementationAddress);\n        if (code === '0x') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                valid: false,\n                error: 'Implementation address does not contain contract code'\n            }, {\n                status: 400\n            });\n        }\n        // Check if emergency mode is active\n        const upgradeManager = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__, provider);\n        const emergencyModeActive = await upgradeManager.isEmergencyModeActive();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            valid: true,\n            emergencyModeActive,\n            upgradeDelay: emergencyModeActive ? 0 : await upgradeManager.UPGRADE_DELAY()\n        });\n    } catch (error) {\n        console.error('Error validating upgrade:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            valid: false,\n            error: 'Validation failed'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/modular-tokens/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/contracts/SecurityTokenCore.json":
/*!**********************************************!*\
  !*** ./src/contracts/SecurityTokenCore.json ***!
  \**********************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('[{"inputs":[],"name":"AccessControlBadConfirmation","type":"error"},{"inputs":[{"internalType":"address","name":"account","type":"address"},{"internalType":"bytes32","name":"neededRole","type":"bytes32"}],"name":"AccessControlUnauthorizedAccount","type":"error"},{"inputs":[{"internalType":"address","name":"target","type":"address"}],"name":"AddressEmptyCode","type":"error"},{"inputs":[{"internalType":"address","name":"implementation","type":"address"}],"name":"ERC1967InvalidImplementation","type":"error"},{"inputs":[],"name":"ERC1967NonPayable","type":"error"},{"inputs":[{"internalType":"address","name":"spender","type":"address"},{"internalType":"uint256","name":"allowance","type":"uint256"},{"internalType":"uint256","name":"needed","type":"uint256"}],"name":"ERC20InsufficientAllowance","type":"error"},{"inputs":[{"internalType":"address","name":"sender","type":"address"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"uint256","name":"needed","type":"uint256"}],"name":"ERC20InsufficientBalance","type":"error"},{"inputs":[{"internalType":"address","name":"approver","type":"address"}],"name":"ERC20InvalidApprover","type":"error"},{"inputs":[{"internalType":"address","name":"receiver","type":"address"}],"name":"ERC20InvalidReceiver","type":"error"},{"inputs":[{"internalType":"address","name":"sender","type":"address"}],"name":"ERC20InvalidSender","type":"error"},{"inputs":[{"internalType":"address","name":"spender","type":"address"}],"name":"ERC20InvalidSpender","type":"error"},{"inputs":[],"name":"EnforcedPause","type":"error"},{"inputs":[],"name":"ExpectedPause","type":"error"},{"inputs":[],"name":"FailedCall","type":"error"},{"inputs":[],"name":"InvalidInitialization","type":"error"},{"inputs":[],"name":"NotInitializing","type":"error"},{"inputs":[],"name":"ReentrancyGuardReentrantCall","type":"error"},{"inputs":[],"name":"UUPSUnauthorizedCallContext","type":"error"},{"inputs":[{"internalType":"bytes32","name":"slot","type":"bytes32"}],"name":"UUPSUnsupportedProxiableUUID","type":"error"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"owner","type":"address"},{"indexed":true,"internalType":"address","name":"spender","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"Approval","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"from","type":"address"},{"indexed":true,"internalType":"address","name":"to","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"ForcedTransfer","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"uint64","name":"version","type":"uint64"}],"name":"Initialized","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"uint256","name":"oldMaxSupply","type":"uint256"},{"indexed":false,"internalType":"uint256","name":"newMaxSupply","type":"uint256"}],"name":"MaxSupplyUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"moduleId","type":"bytes32"},{"indexed":true,"internalType":"address","name":"moduleAddress","type":"address"}],"name":"ModuleRegistered","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"moduleId","type":"bytes32"}],"name":"ModuleUnregistered","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"address","name":"account","type":"address"}],"name":"Paused","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"bytes32","name":"previousAdminRole","type":"bytes32"},{"indexed":true,"internalType":"bytes32","name":"newAdminRole","type":"bytes32"}],"name":"RoleAdminChanged","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":true,"internalType":"address","name":"sender","type":"address"}],"name":"RoleGranted","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":true,"internalType":"address","name":"sender","type":"address"}],"name":"RoleRevoked","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"string","name":"tokenPrice","type":"string"},{"indexed":false,"internalType":"string","name":"bonusTiers","type":"string"},{"indexed":false,"internalType":"string","name":"tokenDetails","type":"string"}],"name":"TokenMetadataUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"from","type":"address"},{"indexed":true,"internalType":"address","name":"to","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"Transfer","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"address","name":"account","type":"address"}],"name":"Unpaused","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"implementation","type":"address"}],"name":"Upgraded","type":"event"},{"inputs":[],"name":"AGENT_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"DEFAULT_ADMIN_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"MODULE_MANAGER_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"TRANSFER_MANAGER_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"addToWhitelist","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"owner","type":"address"},{"internalType":"address","name":"spender","type":"address"}],"name":"allowance","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"spender","type":"address"},{"internalType":"uint256","name":"value","type":"uint256"}],"name":"approve","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"balanceOf","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"from","type":"address"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"}],"name":"canTransfer","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"decimals","outputs":[{"internalType":"uint8","name":"","type":"uint8"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"from","type":"address"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"}],"name":"forcedTransfer","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"getTokenMetadata","outputs":[{"internalType":"string","name":"tokenPrice","type":"string"},{"internalType":"string","name":"bonusTiers","type":"string"},{"internalType":"string","name":"tokenDetails","type":"string"},{"internalType":"string","name":"tokenImageUrl","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"string","name":"name_","type":"string"},{"internalType":"string","name":"symbol_","type":"string"},{"internalType":"uint8","name":"decimals_","type":"uint8"},{"internalType":"uint256","name":"maxSupply_","type":"uint256"},{"internalType":"address","name":"admin_","type":"address"},{"internalType":"string","name":"tokenPrice_","type":"string"},{"internalType":"string","name":"bonusTiers_","type":"string"},{"internalType":"string","name":"tokenDetails_","type":"string"},{"internalType":"string","name":"tokenImageUrl_","type":"string"}],"name":"initialize","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"isWhitelisted","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"maxSupply","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"}],"name":"mint","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"name","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"pause","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"paused","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"symbol","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"totalSupply","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"value","type":"uint256"}],"name":"transfer","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"unpause","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"version","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"pure","type":"function"}]');

/***/ }),

/***/ "(rsc)/./src/contracts/UpgradeManager.json":
/*!*******************************************!*\
  !*** ./src/contracts/UpgradeManager.json ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('[{"inputs":[],"name":"AccessControlBadConfirmation","type":"error"},{"inputs":[{"internalType":"address","name":"account","type":"address"},{"internalType":"bytes32","name":"neededRole","type":"bytes32"}],"name":"AccessControlUnauthorizedAccount","type":"error"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"activator","type":"address"},{"indexed":false,"internalType":"uint256","name":"expiry","type":"uint256"}],"name":"EmergencyModeActivated","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"deactivator","type":"address"}],"name":"EmergencyModeDeactivated","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"moduleId","type":"bytes32"},{"indexed":true,"internalType":"address","name":"proxy","type":"address"},{"indexed":false,"internalType":"address","name":"oldImplementation","type":"address"},{"indexed":false,"internalType":"address","name":"newImplementation","type":"address"}],"name":"EmergencyUpgradeExecuted","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"moduleId","type":"bytes32"},{"indexed":true,"internalType":"address","name":"proxy","type":"address"}],"name":"ModuleRegistered","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"moduleId","type":"bytes32"}],"name":"ModuleUnregistered","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"upgradeId","type":"bytes32"},{"indexed":true,"internalType":"bytes32","name":"moduleId","type":"bytes32"}],"name":"UpgradeCancelled","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"upgradeId","type":"bytes32"},{"indexed":true,"internalType":"bytes32","name":"moduleId","type":"bytes32"},{"indexed":true,"internalType":"address","name":"proxy","type":"address"},{"indexed":false,"internalType":"address","name":"oldImplementation","type":"address"},{"indexed":false,"internalType":"address","name":"newImplementation","type":"address"},{"indexed":false,"internalType":"string","name":"version","type":"string"}],"name":"UpgradeExecuted","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"upgradeId","type":"bytes32"},{"indexed":true,"internalType":"bytes32","name":"moduleId","type":"bytes32"},{"indexed":true,"internalType":"address","name":"proxy","type":"address"},{"indexed":false,"internalType":"address","name":"newImplementation","type":"address"},{"indexed":false,"internalType":"uint256","name":"executeTime","type":"uint256"},{"indexed":false,"internalType":"string","name":"description","type":"string"}],"name":"UpgradeScheduled","type":"event"},{"inputs":[],"name":"DEFAULT_ADMIN_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"EMERGENCY_MODE_DURATION","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"EMERGENCY_UPGRADE_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"UPGRADE_DELAY","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"UPGRADE_MANAGER_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"activateEmergencyMode","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"upgradeId","type":"bytes32"}],"name":"cancelUpgrade","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"deactivateEmergencyMode","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"emergencyMode","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"moduleId","type":"bytes32"},{"internalType":"address","name":"newImplementation","type":"address"},{"internalType":"string","name":"description","type":"string"}],"name":"emergencyUpgrade","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"upgradeId","type":"bytes32"}],"name":"executeUpgrade","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"getPendingUpgradeIds","outputs":[{"internalType":"bytes32[]","name":"","type":"bytes32[]"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"getRegisteredModules","outputs":[{"internalType":"bytes32[]","name":"","type":"bytes32[]"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"moduleId","type":"bytes32"}],"name":"getUpgradeHistory","outputs":[{"components":[{"internalType":"address","name":"oldImplementation","type":"address"},{"internalType":"address","name":"newImplementation","type":"address"},{"internalType":"uint256","name":"timestamp","type":"uint256"},{"internalType":"address","name":"executor","type":"address"},{"internalType":"string","name":"version","type":"string"},{"internalType":"bool","name":"isEmergency","type":"bool"},{"internalType":"string","name":"description","type":"string"}],"internalType":"struct UpgradeManager.UpgradeRecord[]","name":"","type":"tuple[]"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"admin","type":"address"}],"name":"initialize","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"isEmergencyModeActive","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"name":"moduleProxies","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"name":"pendingUpgrades","outputs":[{"internalType":"bytes32","name":"moduleId","type":"bytes32"},{"internalType":"address","name":"proxy","type":"address"},{"internalType":"address","name":"newImplementation","type":"address"},{"internalType":"uint256","name":"executeTime","type":"uint256"},{"internalType":"bool","name":"executed","type":"bool"},{"internalType":"bool","name":"cancelled","type":"bool"},{"internalType":"string","name":"description","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"moduleId","type":"bytes32"},{"internalType":"address","name":"proxy","type":"address"}],"name":"registerModule","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"moduleId","type":"bytes32"},{"internalType":"address","name":"newImplementation","type":"address"},{"internalType":"string","name":"description","type":"string"}],"name":"scheduleUpgrade","outputs":[{"internalType":"bytes32","name":"upgradeId","type":"bytes32"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"moduleId","type":"bytes32"}],"name":"unregisterModule","outputs":[],"stateMutability":"nonpayable","type":"function"}]');

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ethers","vendor-chunks/@noble","vendor-chunks/@adraffy"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmodular-tokens%2Froute&page=%2Fapi%2Fmodular-tokens%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmodular-tokens%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();